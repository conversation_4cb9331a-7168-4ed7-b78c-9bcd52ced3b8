package com.dxx.game.dao.dynamodb.model;

import com.dxx.game.common.aws.dynamodb.annotation.DynamoDBTableName;
import com.dxx.game.common.aws.dynamodb.model.DynamoDBBaseModel;
import lombok.Data;
import lombok.Getter;
import lombok.ToString;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbIgnore;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey;

import java.util.Map;

@Data
@ToString
@DynamoDbBean
@DynamoDBTableName("account")
public class Account extends DynamoDBBaseModel {
    @Getter(onMethod_ = {@DynamoDbPartitionKey})
    private String PK;
    // 用户ID
    @Getter(onMethod_ = {@DynamoDbSecondaryPartitionKey(indexNames = "accountId-Index")})
    private String accountId;             // 账号ID
    private String accountId2;            // 账号ID2 - ios teamPlayerID
    private String habbyId;            // habbyId
    private Boolean bindReward; //绑定后是否发过奖了
    @Getter(onMethod_ = {@DynamoDbSecondaryPartitionKey(indexNames = "deviceId-Index")})
    private String deviceId;                // 设备ID
    private Integer channelId;
    private Long lastLoginUserId;
    private Long lastLoginTime;
    /**
     * 服务器id -> 玩家id的映射。
     * <p>
     * 这里需要做一下说明：<br/>
     * 这个地方的映射，是在玩家id创建时就映射好的，之后不再更改。
     * 现代分服游戏即使合服，在玩家登录那里，被合并的服务器入口也会存在，一个玩家账号，即使在s1和s2服
     * 分别存在账号，合并后，也需要通过不同的服务器入口来使用不同的角色。因此这里的服务器id和角色id的映射
     * 一旦建立，就不再修改了。
     * <p>
     * 玩家的实际角色数据上会保存一个服务器id的标签{@link User#getServerId()}，这个标签用来标记玩家当前实际在哪个服，合服的时候会将这个
     * 标签修改成目标服务器的值。比如原先s2服的玩家，合并到s1后，会将user身上的服务器id修改为s1.
     * 服务器内部按照服务器id进行处理的逻辑都使用user身上的服务器id：比如分服的排行榜等。
     */
    private Map<Integer, Long> serverUserIdMap;

    @DynamoDbIgnore
    @Override
    public String getUniqueKey() {
        return this.PK;
    }

    @DynamoDbIgnore
    public long getLastLoginUserIdOrDefault(long defaultValue) {
        if (this.lastLoginUserId != null) return this.lastLoginUserId;
        return defaultValue;
    }
}
