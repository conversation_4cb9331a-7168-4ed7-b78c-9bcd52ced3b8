package com.dxx.game.dao.dynamodb.model;

import com.dxx.game.common.aws.dynamodb.annotation.DynamoDBTableName;
import com.dxx.game.common.aws.dynamodb.converter.Int2IntOpenHashMapConverter;
import com.dxx.game.common.aws.dynamodb.model.DynamoDBBaseModel;
import it.unimi.dsi.fastutil.ints.Int2IntOpenHashMap;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.*;

import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
@ToString
@DynamoDbBean
@DynamoDBTableName("exchange-shop")
public class ExchangeShop extends DynamoDBBaseModel {

    @Getter(onMethod_ = {@DynamoDbPartitionKey})
    private Long userId = 0L;
    /** key: shopId */
    private Map<Integer, ShopShell> shells;

    @DynamoDbIgnore
    public static ExchangeShop init(long userId) {
        ExchangeShop shop = new ExchangeShop();
        shop.setUserId(userId);
        shop.setShells(new HashMap<>());
        return shop;
    }

    @DynamoDbIgnore
    @Override
    public Long getUniqueKey() {
        return userId;
    }

    @DynamoDbIgnore
    public ShopShell getShellOrCreate(int shopId) {
        var shell = shells.get(shopId);
        if (shell == null) {
            shell = new ShopShell();
            shell.id = shopId;
            shells.put(shopId, shell);
        }
        return shell;
    }

    @Data
    @DynamoDbBean
    public static class ShopShell {

        private int id;
        @Getter(onMethod_ = {@DynamoDbAttribute("nextTime")})
        private long nextRefreshTime;
        /** key: ShopGoodsId, value: boughtCount, 为了省空间，没买过的里面不记录，后续商店有随机了再修改这套逻辑 */
        @Getter(onMethod_ = {@DynamoDbConvertedBy(Int2IntOpenHashMapConverter.class)})
        private Int2IntOpenHashMap boughtCount = new Int2IntOpenHashMap();

        @DynamoDbIgnore
        public int getBoughtCount(int goodsId) {
            return boughtCount.get(goodsId);
        }

        @DynamoDbIgnore
        public void addBoughtCount(int goodsId, int buyCount) {
            boughtCount.addTo(goodsId, buyCount);
        }
    }
}
