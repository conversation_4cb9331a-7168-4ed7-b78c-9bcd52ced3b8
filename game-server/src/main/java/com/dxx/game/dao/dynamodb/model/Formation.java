package com.dxx.game.dao.dynamodb.model;

import com.dxx.game.common.aws.dynamodb.annotation.DynamoDBTableName;
import com.dxx.game.common.aws.dynamodb.converter.Int2IntOpenHashMapConverter;
import com.dxx.game.common.aws.dynamodb.converter.IntArrayConverter;
import com.dxx.game.common.aws.dynamodb.converter.Long2IntOpenHashMapConverter;
import com.dxx.game.common.aws.dynamodb.model.DynamoDBBaseModel;
import com.dxx.game.consts.HeroFormationConsts;
import com.dxx.game.dao.dynamodb.model.formation.IFormation;
import it.unimi.dsi.fastutil.ints.Int2IntOpenHashMap;
import it.unimi.dsi.fastutil.longs.Long2IntOpenHashMap;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

/**
 * 战斗编队阵容组表
 */
@Getter
@Setter
@ToString
@DynamoDbBean
@DynamoDBTableName("formation")
public class Formation extends DynamoDBBaseModel {
    @Getter(onMethod_ = {@DynamoDbPartitionKey})
    private Long userId;            // 用户ID
    /** 战斗编队模板id */
    @Getter(onMethod_ = {@DynamoDbSortKey})
    private Integer formationId;
    private FormationArrays arrays;

    @DynamoDbIgnore
    @Override
    public String getUniqueKey() {
        return buildUniqueKey(userId, formationId);
    }

    /**
     * 战斗编队阵容组。
     */
    @Data
    @DynamoDbBean
    public static class FormationArrays {
        /** 阵容组，key是第几组，从0开始 */
        private Map<Integer, FormationModel> maps = new HashMap<>();
        /**
         * 已使用过的阵容index，不同战斗业务按照需求往里面添加。
         * <p>
         * 有一些业务拥有多组阵容，使用后就不允许修改了。
         */
        private List<Integer> usedIndex = new ArrayList<>();
        /** 英雄参加过战斗的次数，key: heroTemplateId, value: 次数 */
        @Getter(onMethod_ = {@DynamoDbConvertedBy(Int2IntOpenHashMapConverter.class), @DynamoDbAttribute("hTimes")})
        private Int2IntOpenHashMap heroBattleTimes = new Int2IntOpenHashMap();
        /** 宝物参加过战斗的次数，key: baoWu.rowId, value: 次数 */
        @Getter(onMethod_ = {@DynamoDbConvertedBy(Long2IntOpenHashMapConverter.class), @DynamoDbAttribute("bTimes")})
        private Long2IntOpenHashMap baoWuBattleTimes = new Long2IntOpenHashMap();
        /** 下次重置时间 */
        private long nextRest;

        @DynamoDbIgnore
        public boolean isFormationUsed(int index) {
            return usedIndex.contains(index);
        }

        @DynamoDbIgnore
        public void reset(long nextRest) {
            usedIndex.clear();
            heroBattleTimes.clear();
            baoWuBattleTimes.clear();
            this.nextRest = nextRest;
        }
    }

    /**
     * 一个战斗阵容。
     */
    @Data
    @DynamoDbBean
    public static class FormationModel implements IFormation {
        private int index;
        @Getter(onMethod_ = {@DynamoDbConvertedBy(IntArrayConverter.class)})
        private int[] heroIds = new int[HeroFormationConsts.FORMATION_SLOT_AMOUNT];
        private Map<Integer, Long> baoWu = new HashMap<>();

        @Override
        @DynamoDbIgnore
        public boolean hasHero(int index) {
            if (index < 0 || index >= heroIds.length) return false;
            return heroIds[index] > 0;
        }

        @Override
        @DynamoDbIgnore
        public int getHeroIdByIndex(int index) {
            return heroIds[index];
        }

        @Override
        @DynamoDbIgnore
        public void forEachBaoWu(UserExtend userExtend, Consumer<UserExtend.DressedBaoWu> consumer) {
            if (userExtend.getFormationBaoWus() == null) return;
            for (var each : baoWu.entrySet()) {
                var dressedBaoWu = userExtend.getFormationBaoWus().get(each.getValue());
                if (dressedBaoWu == null) continue;
                consumer.accept(dressedBaoWu);
            }
        }
    }
}
