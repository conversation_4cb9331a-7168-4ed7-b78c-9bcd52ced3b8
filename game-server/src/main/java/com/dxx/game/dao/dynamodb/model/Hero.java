package com.dxx.game.dao.dynamodb.model;

import com.dxx.game.common.aws.dynamodb.annotation.DynamoDBTableName;
import com.dxx.game.common.aws.dynamodb.model.DynamoDBBaseModel;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbIgnore;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/4/1 14:52
 */
@Getter
@Setter
@ToString
@FieldNameConstants
@DynamoDbBean
@DynamoDBTableName("hero")
public class Hero extends DynamoDBBaseModel {

    @Getter(onMethod_ = {@DynamoDbPartitionKey})
    private Long userId;            // 用户ID
    @Getter(onMethod_ = {@DynamoDbSortKey})
    private Integer heroId;         // 配置ID
    private Integer exp;            // 经验
    private Integer star;           // 星级
    /**
     * 升星已经提交的task。
     * 这个只有在升星时，type为2的升星cost时，需要提交配置的若干任务后，才能升星。
     * <p>
     * 当要升星的cost type为2时，需要独立提交task任务，完成后，才能点击升星；
     * 服务器这边，升星后默认将该set清空。
     * <p>
     * 注意：这个东西本来应该是个set，但是因为dynamodb不允许空的set，但是运行空list，因此这里
     * 使用list来替代set，但是还是当list来用，里面没有重复元素。
     */
    private List<Integer> finishedTask;

    /** 图鉴，0表示没有领取，1表示领取了首次获得，后续再有再扩展 */
    private Integer codex;

    /** 皮肤，null或者0表示默认 */
    private Integer skin;

    /**
     * 符文 map key:runeType value:HeroRune 策划说要留口子 之后可能要单独加功能 在英雄符文孔位上
     */
    private Map<Integer, HeroRune> rune;

    @DynamoDbIgnore
    @Override
    public Object getUniqueKey() {
        return buildUniqueKey(userId, heroId);
    }

    @DynamoDbIgnore
    public boolean isTaskFinished(Integer taskId) {
        return finishedTask.contains(taskId);
    }

    @DynamoDbIgnore
    public HeroRune getRuneMapOrCreateType(int runeType) {
        if (rune == null) {
            rune = new HashMap<>();
        }
        if (rune.get(runeType) == null) {
            rune.put(runeType, new HeroRune());
        }
        return rune.get(runeType);
    }

    @DynamoDbIgnore
    public Map<Integer, HeroRune> getRuneMapOrCreate() {
        if (rune == null) {
            rune = new HashMap<>();
        }
        return rune;
    }

    @Data
    @DynamoDbBean
    public static class HeroRune {
        //符文rowId
        private long runeRowId;

        @DynamoDbIgnore
        public boolean hasRune() {
            return runeRowId > 0;
        }
    }
}
