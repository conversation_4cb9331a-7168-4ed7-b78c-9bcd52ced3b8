package com.dxx.game.dao.dynamodb.model;

import com.dxx.game.modules.iap.type.IapType;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbIgnore;

/**
 * <AUTHOR>
 */
@DynamoDbBean
@Getter
@Setter
public class IapBattlePass extends IapModuleContent<IapBattlePass.BattlePassModel> {

    public IapBattlePass() {
        super(IapType.BATTLE_PASS);
    }

    /**
     * 用于包装实际对象的, null为暂时没有激活。
     */
    @Data
    @DynamoDbBean
    public static class BattlePassModel {
        /** 玩家参与的BattlePass id */
        private int id;
        /** 是否已购买高级 */
        private int buy;
        /** 通行证积分 */
        private int score;

        /** 已领免费的level */
        private int freeLevel;
        /** 已领付费的level */
        private int advanceLevel;

        /** 通行证领取最终奖励次数 */
        private int finalRewardCount;

        @DynamoDbIgnore
        public boolean hasBuyAdvance() {
            return buy > 0;
        }
    }
}
