package com.dxx.game.dao.dynamodb.model;

import com.dxx.game.modules.iap.type.IapType;
import lombok.Getter;
import lombok.Setter;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;

import java.util.Map;

/**
 * 循环基金:
 * <p>
 * key: cycleActivity.id. 只有正在活动中的才存在
 *
 * <AUTHOR>
 */
@DynamoDbBean
@Getter
@Setter
public class IapCycleFund extends IapModuleContent<Map<Integer, LevelFundModel>> {

    public IapCycleFund() {
        super(IapType.CYCLE_FUND);
    }
}
