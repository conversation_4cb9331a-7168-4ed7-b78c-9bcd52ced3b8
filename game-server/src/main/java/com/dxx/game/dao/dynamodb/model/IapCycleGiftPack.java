package com.dxx.game.dao.dynamodb.model;

import com.dxx.game.modules.iap.type.IapType;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;

import java.util.HashMap;
import java.util.Map;

/**
 * Map的key: cycleActivity.id，防止同时开启多组一样的活动。
 *
 * <AUTHOR>
 */
@DynamoDbBean
@Getter
@Setter
public class IapCycleGiftPack extends IapModuleContent<Map<Integer, IapCycleGiftPack.CycleGiftPackModel>> {

    public IapCycleGiftPack() {
        super(IapType.CYCLE_GIFT_PACK);
    }

    @Data
    @DynamoDbBean
    public static class CycleGiftPackModel {
        /** 购买次数 key: CycleGiftPack.id, value: boughtCount */
        private Map<Integer, Integer> bought = new HashMap<>();
    }
}
