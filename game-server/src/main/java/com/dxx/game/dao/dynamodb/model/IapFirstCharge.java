package com.dxx.game.dao.dynamodb.model;

import com.dxx.game.dao.dynamodb.model.condition.ConditionGroupModel;
import com.dxx.game.modules.iap.type.IapType;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbIgnore;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * 首充礼包。
 * <p>
 * 兼容老版本原则：<br/>
 * 只有在计算condition事件时才会初始化，其它地方都需要判断从db中获取是否为null，内部model是否为null。
 * <p>
 * 这么做，因为直接在线更新，玩家不下线，所以无法有统一的地方保证老玩家数据库中一定会存在该玩家数据。
 *
 * <AUTHOR>
 */
@DynamoDbBean
@Getter
@Setter
public class IapFirstCharge extends IapModuleContent<IapFirstCharge.FirstChargeModel> {

    public IapFirstCharge() {
        super(IapType.FIRST_CHARGE);
    }

    @Data
    @DynamoDbBean
    public static class FirstChargeModel {
        /**
         * 每个组当前的信息，可以认为是当前组的任务信息。
         * <p>
         * 这个东西在触发condition时开始计数.
         */
        private Map<Integer, ConditionGroupModel> groups = new HashMap<>();

        /**
         * 已经触发且没有领取完毕的首充礼包。
         * <p>
         * 购买并领取完毕后，会从这里删除掉。
         */
        private Map<Integer, FirstChargeInfo> infos = new HashMap<>();

        /**
         * 购买并完成所有奖励领奖的set，用于推送的时候判断
         */
        private Set<Integer> fSet = new HashSet<>();
    }

    @Data
    @DynamoDbBean
    public static class FirstChargeInfo {
        /** FirstCharge.id */
        private int id;
        /** 是否购买, 0为未购买 */
        private int buy;
        /** 已领奖天数，购买后从1开始，因为第一天直接发放 */
        private int day;
        /** 可领取天数，购买后从1开始，按照nextTs来计算是否下一天的可以领取 */
        private int rDay;
        /** 下一次可领取时间 */
        private long nextTs;

        @DynamoDbIgnore
        public boolean hasBought() {
            return buy > 0;
        }
    }
}
