package com.dxx.game.dao.dynamodb.model;

import com.dxx.game.modules.iap.type.IapType;
import lombok.Getter;
import lombok.Setter;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;

import java.util.Map;

/**
 * 等级基金:
 * <p>
 * key: LevelFund.id. 只有符合条件且需要记录数据的时候才会存在。
 *
 * <AUTHOR>
 */
@DynamoDbBean
@Getter
@Setter
public class IapLevelFund extends IapModuleContent<Map<Integer, LevelFundModel>> {

    public IapLevelFund() {
        super(IapType.LEVEL_FUND);
    }

}
