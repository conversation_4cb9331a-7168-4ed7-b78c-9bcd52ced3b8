package com.dxx.game.dao.dynamodb.model;

import com.dxx.game.common.aws.dynamodb.annotation.DynamoDBTableName;
import com.dxx.game.common.aws.dynamodb.model.DynamoDBBaseModel;
import com.dxx.game.modules.iap.type.IapType;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbIgnore;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/11/24 16:28
 */
@Getter
@Setter
@ToString
@DynamoDbBean
@DynamoDBTableName("iap-module")
public class IapModule extends DynamoDBBaseModel {

    @Getter(onMethod_ = {@DynamoDbPartitionKey})
    private Long userId;
    @Getter(onMethod_ = {@DynamoDbSortKey})
    private Integer type;
    /** 正在购买中的具体项目，这个东西不是发货的依据，只是用来限制各种类型从下单到发货之间购买限购的 */
    private Map<String, Buying> buyings;

    public IapModule() {
        //这个方法给Dynamodb的sdk反序列化用的
    }

    /**
     * 这个方法是给继承用的
     *
     * @param type
     */
    public IapModule(IapType type) {
        this.type = type.getTypeValue();
        this.buyings = new HashMap<>();
    }

    @DynamoDbIgnore
    @Override
    public String getUniqueKey() {
        return buildUniqueKey(this.userId, this.type);
    }

    /**
     * 正在购买中的订单。
     * <p>
     * 在PreOrder中去查询太费劲了，这个快，正在购买中一律走这个。
     */
    @Data
    @DynamoDbBean
    public static class Buying {
        String preOrderId;
        int pId;
        long start;
        int param;
    }
}
