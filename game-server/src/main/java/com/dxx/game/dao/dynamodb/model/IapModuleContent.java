package com.dxx.game.dao.dynamodb.model;

import com.dxx.game.modules.iap.type.IapType;
import lombok.Getter;
import lombok.Setter;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@DynamoDbBean
public class IapModuleContent<T> extends IapModule {
    private T model;

    public IapModuleContent() {
        //这个方法给Dynamodb的sdk反序列化用的
    }

    /**
     * 这个方法是给继承用的
     *
     * @param type
     */
    public IapModuleContent(IapType type) {
        super(type);
    }
}
