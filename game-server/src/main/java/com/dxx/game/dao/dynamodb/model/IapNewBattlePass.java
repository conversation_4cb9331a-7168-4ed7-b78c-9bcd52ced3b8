package com.dxx.game.dao.dynamodb.model;


import com.dxx.game.modules.iap.type.IapType;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbIgnore;

import java.util.Map;

/**
 * key: NewBattlePass id
 *
 * <AUTHOR>
 */
@DynamoDbBean
@Getter
@Setter
public class IapNewBattlePass extends IapModuleContent<Map<Integer, IapNewBattlePass.NewBattlePassModel>> {

    public IapNewBattlePass() {
        super(IapType.NEW_BATTLE_PASS);
    }

    /**
     * 用于包装实际对象的, null为暂时没有激活。
     */
    @Data
    @DynamoDbBean
    public static class NewBattlePassModel {
        /** 玩家参与的BattlePass id */
        private int id;
        /** 是否已购买进阶通行证 */
        private int buyAdvanced;
        /** 是否已购买豪华通行证 */
        private int buyLuxury;
        /** 通行证积分 */
        private int score;

        /** 已领免费的level */
        private int freeLevel;
        /** 已领进阶付费的level */
        private int advancedLevel;
        /** 已领豪华付费的level */
        private int luxuryLevel;

        /** 通行证领取最终奖励次数 */
        private int finalRewardCount;

        @DynamoDbIgnore
        public boolean hasBuyAdvanced() {
            return buyAdvanced > 0;
        }

        @DynamoDbIgnore
        public boolean hasBuyLuxury() {
            return buyLuxury > 0;
        }

        @DynamoDbIgnore
        public boolean hasBuy() {
            return buyAdvanced > 0 || buyLuxury > 0;
        }
    }


}
