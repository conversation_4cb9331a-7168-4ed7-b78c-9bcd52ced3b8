package com.dxx.game.dao.dynamodb.model;

import com.dxx.game.common.aws.dynamodb.converter.Int2IntOpenHashMapConverter;
import com.dxx.game.modules.iap.type.IapType;
import it.unimi.dsi.fastutil.ints.Int2IntOpenHashMap;
import lombok.Data;
import lombok.Getter;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbConvertedBy;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbIgnore;

import java.util.HashMap;
import java.util.Map;

/**
 * key: group
 *
 * <AUTHOR>
 */
@DynamoDbBean
public class IapSevenDay extends IapModuleContent<IapSevenDay.SevenDayModel> {

    public IapSevenDay() {
        super(IapType.SEVEN_DAY);
    }

    @Data
    @DynamoDbBean
    public static class SevenDayModel {
        /** 开始时间，由客户端发送getInfo请求开始初始化，时间一定是某天的0点 */
        private long start;
        /** 分组的活跃度数据, key: group */
        private Map<Integer, SevenDayInfo> map = new HashMap<>();
        /**
         * 上次是第几天检查的任务，比如已经接取了第几天的任务了。
         * <p>
         * 嘉年华的任务是按照开启天数接取和过期的，如果玩家隔了好几天才再次登录，中间的这些没登录的天的任务就不好接取和过期了，
         * 因此这个东西为了不让代码从第1天遍历到当天，加了这个值，方便快速定位从哪天开始接取。
         * <p>
         * 这个值从0开始，当天检查过了记录当天的天数。
         */
        private int taskDay;

        @DynamoDbIgnore
        public boolean isClose() {
            return this.map.isEmpty();
        }
    }

    /**
     * 这里没有任务数据，因为任务数据记录在task信息里了
     */
    @Data
    @DynamoDbBean
    public static class SevenDayInfo {
        private int group;
        // 当前活跃度
        private int active = 0;
        // 活跃度奖励领取记录
        private long activeLog = 0;
        /** SevenDayPay各自id的购买次数 */
        @Getter(onMethod_ = {@DynamoDbConvertedBy(Int2IntOpenHashMapConverter.class)})
        private Int2IntOpenHashMap limit = new Int2IntOpenHashMap();
    }
}
