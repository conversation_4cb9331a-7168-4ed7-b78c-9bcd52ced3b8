package com.dxx.game.dao.dynamodb.model;

import com.dxx.game.common.aws.dynamodb.annotation.DynamoDBTableName;
import com.dxx.game.common.aws.dynamodb.model.DynamoDBBaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbIgnore;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey;

/**
 * <AUTHOR>
 * @date 2020/12/17 16:00
 */
@Getter
@Setter
@ToString
@DynamoDbBean
@DynamoDBTableName("item")
public class Item extends DynamoDBBaseModel {

    @Getter(onMethod_ = {@DynamoDbPartitionKey})
    private Long userId;            // 用户ID
    @Getter(onMethod_ = {@DynamoDbSortKey})
    private Integer itemId;         // 道具配置ID
    private Integer count;          // 数量

    @DynamoDbIgnore
    @Override
    public String getUniqueKey() {
        return buildUniqueKey(this.userId, this.itemId);
    }


}
