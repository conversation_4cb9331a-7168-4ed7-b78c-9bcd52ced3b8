package com.dxx.game.dao.dynamodb.model;

import com.dxx.game.common.aws.dynamodb.annotation.DynamoDBTableName;
import com.dxx.game.common.aws.dynamodb.model.DynamoDBBaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/9 16:53
 */
@Getter
@Setter
@ToString
@DynamoDbBean
@DynamoDBTableName("log-resource")
public class LogResource extends DynamoDBBaseModel {

    @Getter(onMethod_ = {@DynamoDbPartitionKey})
    private Long userId;                // 用户ID
    @Getter(onMethod_ = {@DynamoDbSortKey})
    private Long transId;               // 操作ID
    private Short command;              // 接口ID
    private Integer itemId;               //
    private Long itemCount;              //
    private Long rowId;               //
    private Integer rewardType;               //
    private Integer resourceType;               //
    private Integer configType;               //
    private Integer configId;               //
    private Long configCount;              //
    private Integer extraData;              //
    private List<List<Integer>> items;  // [[itemId, itemNum, itemTotalNum, ...(额外参数,ps:装备等级)]]
    private List<Long> equipRowIds;     // 装备rowId
    private List<Long> baoWuRowIds;     // 宝物rowId
    private String requestId;           // 请求ID
    private Integer customType;         // 自定义类型
    private String extra;               // 额外的值
    @Getter(onMethod_ = {@DynamoDbSecondarySortKey(indexNames = "index-time")})
    private Long logTime;               // 日志产生时间
    private Long ttlTime;               // 过期时间
    private String timeStr;           //
    private Integer dayFlag;               //

    @DynamoDbIgnore
    @Override
    public String getUniqueKey() {
        return buildUniqueKey(this.userId, this.transId);
    }
}
