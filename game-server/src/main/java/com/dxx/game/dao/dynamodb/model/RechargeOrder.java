package com.dxx.game.dao.dynamodb.model;

import com.dxx.game.common.aws.dynamodb.annotation.DynamoDBTableName;
import com.dxx.game.common.aws.dynamodb.model.DynamoDBBaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbIgnore;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey;

/**
 * 这个表用来记录各个渠道商的订单信息，主键orderId就是各个渠道商的主键id。
 *
 * <AUTHOR>
 * @date 2020/10/19 15:32
 */
@Getter
@Setter
@ToString
@DynamoDbBean
@DynamoDBTableName("recharge-order")
public class RechargeOrder extends DynamoDBBaseModel {

    /**
     * 主键-渠道商的订单号
     */
    @Getter(onMethod_ = {@DynamoDbPartitionKey})
    private String orderId;
    @Getter(onMethod_ = {@DynamoDbSecondarySortKey(indexNames = "userId-Index")})
    private Long userId;                    // 用户ID
    private String productId;               // 商品ID
    private Integer money;   // 金额（分）
    private Long transId;                // 操作ID
    private String preOrderId;          // 自身的preOrderId
    /** 充值渠道 */
    private Integer channelId;
    private Boolean env;                    // 支付环境
    /** 创建时间 */
    private Long time;
    /** 状态，1：成功，2：退款 */
    private Integer state;
    private Integer purchaseId;
    /**
     * 下单时业务自身保留的上下文参数，由业务自己控制和解析.
     * <p>
     * 这个主要是用于退单使用，不需要的可以不设置。
     */
    private String extra;

    @Getter(onMethod_ = {@DynamoDbIgnore})
    private String date;

    @DynamoDbIgnore
    @Override
    public String getUniqueKey() {
        return orderId;
    }
}
