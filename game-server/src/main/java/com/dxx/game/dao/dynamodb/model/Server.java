package com.dxx.game.dao.dynamodb.model;

import com.dxx.game.common.aws.dynamodb.annotation.DynamoDBTableName;
import com.dxx.game.common.aws.dynamodb.model.DynamoDBBaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbIgnore;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey;

@Getter
@Setter
@ToString
@DynamoDbBean
@DynamoDBTableName("server")
public class Server extends DynamoDBBaseModel {
    @Getter(onMethod_ = {@DynamoDbPartitionKey})
    private Integer serverId;
    private Integer userCount;
    private Long openTime;
    private String imGroupId;
    private IMGroupStatus imGroupStatus;
    private Long createTime;
    private Long updateTime;
    private Integer status;

    @DynamoDbIgnore
    @Override
    public Object getUniqueKey() {
        return serverId;
    }
}
