package com.dxx.game.dao.dynamodb.model;

import com.dxx.game.common.aws.dynamodb.annotation.DynamoDBTableName;
import com.dxx.game.common.aws.dynamodb.model.DynamoDBBaseModel;
import com.dxx.game.common.utils.DateUtils;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbIgnore;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/11/24 16:28
 */
@Getter
@Setter
@ToString
@FieldNameConstants
@DynamoDbBean
@DynamoDBTableName("shop")
public class Shop extends DynamoDBBaseModel {

    @Getter(onMethod_ = {@DynamoDbPartitionKey})
    private Long userId = 0L;
    private Long nextResetTime;                                       // 商店相关数据重置时间戳
    private Map<Integer, GachaPoolModel> gachaPool;            //抽卡数据 map<groupId,GachaPoolModel>
    @Deprecated
    private Integer gachaChestCount;                                    // 宝箱奖励抽卡次数
    private Integer gachaChestRewardId = 1;                                 // 宝箱奖励ID
    private Map<Integer, IntegralShopModel> integralShops;   // 积分商店数据
    private Map<Integer, List<Integer>> wishData;                       // 抽卡许愿
    private Map<Integer, Integer> selectHeroData;                       // 选择英雄 map<gachaId,heroId>

    @DynamoDbIgnore
    public static Shop init(long userId) {
        Shop shop = new Shop();
        shop.setUserId(userId);
        shop.setNextResetTime(DateUtils.getNextSystemResetTime());
        shop.setGachaPool(new HashMap<>());
        shop.setGachaChestCount(0);
        shop.setGachaChestRewardId(1);
        shop.setIntegralShops(new HashMap<>());
        shop.setWishData(new HashMap<>());
        shop.setSelectHeroData(new HashMap<>());
        return shop;
    }

    @DynamoDbIgnore
    @Override
    public Long getUniqueKey() {
        return userId;
    }

    @DynamoDbIgnore
    public GachaPoolModel getGachaPoolOrCreate(int poolId) {
        GachaPoolModel gachaPoolModel = gachaPool.get(poolId);
        if (gachaPoolModel == null) {
            gachaPoolModel = new GachaPoolModel();
            gachaPool.put(poolId, gachaPoolModel);
        }
        return gachaPoolModel;
    }

    @Data
    @DynamoDbBean
    public static class GachaPoolModel {
        /**
         * 今天的次数
         */
        private int todayCount;
        /**
         * 一次重置周期内的使用过的免费次数
         */
        private int freeCount;
        /**
         * 不是高级nb卡的次数，用于判断是否走保底池子
         */
        private int noRareCount;
        /**
         * 当前池子抽卡的总次数
         */
        private int totalCount;
        /**
         * 未中Up英雄次数
         */
        private int missUpHeroCount;
        /**
         * 伪随机次数
         */
        private int pseudoRandomCount;

        @DynamoDbIgnore
        public void reset() {
            todayCount = 0;
            freeCount = 0;
        }
    }

    @Data
    @DynamoDbBean
    public static class IntegralShopModel {
        // 类型
        private Integer type = 0;
        // 刷新时间
        private Long refreshTimestamp = 0l;
        // 手动刷新次数
        private Integer refreshCount = 0;
        // 物品配置ID
        private List<Integer> configIds;
        // 已购买ID
        private List<Integer> buyIds;
    }

    @DynamoDbIgnore
    public Map<Integer, Integer> getSelectHeroDataOrCreate() {
        if (selectHeroData == null) {
            selectHeroData = new HashMap<>();
        }
        return selectHeroData;
    }

    @DynamoDbIgnore
    public int getSelectHeroIdOrZero(int gachaId) {
        if (selectHeroData == null) return 0;
        Integer heroId = selectHeroData.get(gachaId);
        return heroId == null ? 0 : heroId;
    }

}
