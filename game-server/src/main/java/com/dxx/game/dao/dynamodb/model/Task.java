package com.dxx.game.dao.dynamodb.model;

import com.dxx.game.common.aws.dynamodb.annotation.DynamoDBTableName;
import com.dxx.game.common.aws.dynamodb.converter.IntOpenHashSetConverter;
import com.dxx.game.common.aws.dynamodb.model.DynamoDBBaseModel;
import it.unimi.dsi.fastutil.ints.IntOpenHashSet;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.*;

import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
@ToString
@FieldNameConstants
@DynamoDbBean
@DynamoDBTableName("task")
public class Task extends DynamoDBBaseModel {
    @Getter(onMethod_ = {@DynamoDbPartitionKey})
    private Long userId;
    /** key: TaskType, value: TaskTypeInfo */
    @Getter(onMethod_ = {@DynamoDbAttribute("all")})
    private Map<Integer, TaskTypeInfo> taskTypeMap;
    /**
     * 用来记录从出生开始就需要计数的计数器。
     * <p>
     * key: TaskConditionType id, value: GlobalProcessModel
     */
    @Getter(onMethod_ = {@DynamoDbAttribute("g")})
    private Map<Integer, GlobalProcessModel> globalProcessMap;

    private ActiveModel dailyActive;
    private ActiveModel weeklyActive;

    /** 新手任务当前stage阶段，默认值为1 */
    private Integer mainTaskStage;

    /** 循环活动任务round，默认是0，表示未开启，大于0表示已开启。活动开启赋值为1 */
    private CycleTaskInfo cycleTask;

    /**
     * 用于临时判断是否数据被修改了，如果是，则需要使用dao来update。
     * <p>
     * 因为task系统相对复杂，无法准确的在具体的逻辑中去处理是否需要更新逻辑，
     * 因此该标记相当于一个全局变量，在一次处理中，可能会被多个地方标记，最后
     * 会在某些地方统一进行update处理。
     */
    @Getter(onMethod_ = {@DynamoDbIgnore})
    private boolean isDirty = false;

    @DynamoDbIgnore
    public void markDirty() {
        this.isDirty = true;
    }


    @DynamoDbIgnore
    @Override
    public Long getUniqueKey() {
        return this.userId;
    }

    @DynamoDbIgnore
    public int getGlobalProcess(int conditionTypeId, String globalIndex) {
        if (globalProcessMap == null) return 0;
        var model = globalProcessMap.get(conditionTypeId);
        if (model == null) {
            return 0;
        }
        var result = model.processMap.get(globalIndex);
        return result == null ? 0 : result;
    }

    @DynamoDbIgnore
    public void setGlobalProcess(int conditionTypeId, String globalIndex, int process) {
        if (globalProcessMap == null) {
            globalProcessMap = new HashMap<>();
        }
        var model = globalProcessMap.get(conditionTypeId);
        if (model == null) {
            model = new GlobalProcessModel();
            globalProcessMap.put(conditionTypeId, model);
        }
        model.processMap.put(globalIndex, process);
    }

    @Data
    @DynamoDbBean
    public static class TaskTypeInfo {
        /**
         * key: groupId, value: TaskModel
         * <p>
         * 游戏内全部按照任务组逻辑处理，既：所有TaskType都是: 组id:进度 + 完成taskId列表，客户端会根据这种方式组织界面。
         * 如果是没有组id的，比如日常任务、新手任务等，程序会自动使用taskId作为groupId，逻辑还是按照groupId的逻辑走。
         * 具体的完成和显示，交由客户端去处理。
         */
        @Getter(onMethod_ = {@DynamoDbAttribute("p")})
        private Map<Integer, TaskModel> processMap = new HashMap<>();
        /**
         * 已领取奖励的templateId（注意：这里即使上面是groupId，这里也是templateId，方便领奖时判断。
         * 另外，客户端会根据这个列表，还原每个group当前到哪个任务了。
         */
        @Getter(onMethod_ = {@DynamoDbConvertedBy(IntOpenHashSetConverter.class), @DynamoDbAttribute("f")})
        private IntOpenHashSet finishedSet = new IntOpenHashSet();

        @DynamoDbIgnore
        public TaskModel abort(int groupId) {
            var model = processMap.get(groupId);
            if (model != null) {
                model.abort = true;
            }
            return model;
        }
    }


    @Data
    @DynamoDbBean
    public static class TaskModel {
        /**
         * 进度，是否完成通过condition type去实时判断.
         * <p>
         * 注意：即使是全局计数的任务，这里也会跟着更新。
         */
        private int process;
        /**
         * 放弃标识，一旦放弃，则不再计数。如果完成的，可以领奖。
         * <p>
         * 比如嘉年华，前七天的过期了，暂时不删除，但是不再计数。
         */
        private boolean abort;
    }

    @Data
    @DynamoDbBean
    public static class GlobalProcessModel {
        /**
         * 用来记录从出生开始就需要计数的计数器。
         * <p>
         * key: globalIndex, value: process
         */
        @Getter(onMethod_ = {@DynamoDbAttribute("p")})
        private Map<String, Integer> processMap = new HashMap<>();
    }

    /**
     * 活跃度数据
     */
    @Data
    @DynamoDbBean
    public static class ActiveModel {
        private int active;
        private long nextResetTime;
        private long rewardLog;
    }

    @Data
    @DynamoDbBean
    public static class CycleTaskInfo {
        /** cycleActivity.id，为0表示没开启 */
        private int actId;
        /** 循环活动任务当前round，活动开启赋值为1，全部完成并领取后自动加一；活动关闭后清零 */
        private int round;
        /** 接下来要领取的轮次（是否可以领取需要通过轮次内的所有任务的完成情况来判断），活动开启赋值为1，领取后加一；活动关闭后清零 */
        private int rewardRound;
    }
}
