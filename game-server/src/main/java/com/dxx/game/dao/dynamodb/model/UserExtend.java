package com.dxx.game.dao.dynamodb.model;

import com.dxx.game.common.aws.dynamodb.annotation.DynamoDBTableName;
import com.dxx.game.common.aws.dynamodb.converter.Int2IntOpenHashMapConverter;
import com.dxx.game.common.aws.dynamodb.converter.IntArrayConverter;
import com.dxx.game.common.aws.dynamodb.converter.Long2IntOpenHashMapConverter;
import com.dxx.game.common.aws.dynamodb.model.DynamoDBBaseModel;
import com.dxx.game.common.utils.DateUtils;
import com.dxx.game.consts.HeroFormationConsts;
import com.dxx.game.dao.dynamodb.model.formation.IFormation;
import com.dxx.game.dto.CommonProto;
import com.google.common.collect.Maps;
import it.unimi.dsi.fastutil.ints.Int2IntOpenHashMap;
import it.unimi.dsi.fastutil.longs.Long2IntOpenHashMap;
import lombok.*;
import lombok.experimental.FieldNameConstants;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.*;

import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.IntConsumer;

/**
 * <AUTHOR>
 * @date 2020/11/16 14:16
 */

@Getter
@Setter
@ToString
@FieldNameConstants
@DynamoDbBean
@DynamoDBTableName("user-extend")
public class UserExtend extends DynamoDBBaseModel implements IFormation {

    @Getter(onMethod_ = {@DynamoDbPartitionKey})
    private Long userId;

    private CityModel city;                                // 主城
    /** 英雄阵型map，key: UserProto.HeroFormationType, value: HeroFormationModel */
    @Getter(onMethod_ = {@DynamoDbAttribute("heroFormation")})
    private Map<Integer, HeroFormationModel> heroFormationMap;
    /** 玩家已经获取的英雄id的集合，方便获取英雄时快速检查，不用再去查询hero表 */
    private Set<Integer> heroIdSet;
    // 是用的roleId 上面那个删掉
    private Integer roleId;

    private LordModel lord;                                  // 领主信息
    private List<Long> slaveIds;                             // 奴隶userId列表

    private Map<Long, ConquerRecordModel> conquerRecordModels;                // 征服记录

    private Map<Integer, IntegralShopModel> integralShopModels; // 积分商城
    /**
     * 公会商城，key：IntegralShop.data.id(必须不是1-黑市)。
     * <p>
     * 之所以有这个是因为之前策划说不要把黑市和公会写一块儿，分出来，因此上面的integralShopModels被修改了，
     * 但是现在策划说又要用回去，但是上面的字段已经上线了，无法再用。
     * <p>
     * 没办法，要怪就怪策划吧。
     */
    private Map<Integer, List<IntegralShopModel>> guildShop;

    private ShopDrawInfo shopDrawInfo;                               // 商店抽卡保底信息

    /**
     * 已经开启的Function Id列表（受dynamodb要求不能存在空set的原因，里面有个-1这个无用值）
     * <p>
     * 这个模块是客户端判断开放后，发送给服务器，服务器再验证后记录的。
     * 服务器在判断功能开放的地方，会先判断这里是否已经开放了，如果没有才会去实时检查表。
     */
    private Set<Integer> openModelId;              // 已开启的模块
    private ActiveInfo activeInfo;
    private ChestInfo chestInfo;
    private ArtifactInfo artifactInfo;
    private MountInfo mountInfo;
    private Map<Integer, AvatarModel> avatars;
    /** 公会科技等级 */
    private Integer guildTechLv;

    /** kingrush */
    /** fake level使用的，之后要删除 */
    @Getter(onMethod_ = {@DynamoDbAttribute("fakeLevel")})
    private FakeLevelModel fakeLevelModel;
    /** kingrush talent */
    @Getter(onMethod_ = {@DynamoDbAttribute("talent")})
    private KingRushTalentModel kingRushTalentModel;

    // todo qm hero 准备移除
    /** 等级信息 */
    @Getter(onMethod_ = {@DynamoDbAttribute("fLevel")})
    private FormationLevelModel formationLevelModel;
    /** 阵型装备信息 */
    @Getter(onMethod_ = {@DynamoDbAttribute("fEquip")})
    private FormationEquipModel formationEquipModel;

    /** 日常金币、经验本信息 */
    @Getter(onMethod_ = {@DynamoDbAttribute("goldExpDun")})
    private GoldExpDungeonModel goldExpDungeonModel;

    /** 装备本信息 */
    @Getter(onMethod_ = {@DynamoDbAttribute("equipDun")})
    private EquipDungeonModel equipDungeonModel;

    /** boss本信息 */
    @Getter(onMethod_ = {@DynamoDbAttribute("bossDun")})
    private BossDungeonModel bossDungeonModel;

    /** 广告信息 */
    private AdModel ad;

    /** 宝物穿戴信息 */
    private BaoWuModel baoWu;

    /** 怪物图鉴领取情况 */
    @Getter(onMethod_ = {@DynamoDbAttribute("mCodex")})
    private Map<Integer, MonsterCodex> monsterCodex;

    /** 爬塔信息 */
    private TowerModel tower;

    /**
     * 新手7天签到领奖bit位，标记哪天领取过了.
     * <p>
     * 这个东西根据建角时间来，即使中间不上线，只要上线了，中间经历的日期都可以领取，
     * 因此这里就存一个每天是否领取过的bit flag即可，使用建角时间就可以判断了。
     */
    @Getter(onMethod_ = {@DynamoDbAttribute("newSignIn")})
    private Long newPlayerSignInRewardBit;

    /** 条件副本，攒星星的 */
    @Getter(onMethod_ = {@DynamoDbAttribute("condChpt")})
    private ConditionChapterModel conditionChapterModel;

    /**
     * 已经获得的英雄皮肤。使用flag做标识。
     * <p>
     * 使用map是因为如果有某个段的flag对应的皮肤完全没有获取，则字段中不存在。
     */
    @Getter(onMethod_ = {@DynamoDbAttribute("hSkins")})
    private HeroSkinModel heroSkins;

    /** 禁赛数据 */
    private Map<Integer, Long> bannedPlay;

    /**
     * 用于编队宝物信息的。
     * <p>
     * 在编队中使用宝物时，会将信息记录在这里，之后宝物升级品质会更新；当宝物被分解，则从这里删除。
     */
    @Getter(onMethod_ = {@DynamoDbAttribute("fBaoWus")})
    private Map<Long, DressedBaoWu> formationBaoWus;

    @DynamoDbIgnore
    @Override
    public Long getUniqueKey() {
        return this.userId;
    }

    @DynamoDbIgnore
    public static UserExtend initOnCreateUser(long userId, int fakeLevelId) {
        UserExtend userExtend = new UserExtend();
        userExtend.setUserId(userId);
        userExtend.setSlaveIds(new ArrayList<>());
        userExtend.setIntegralShopModels(new HashMap<>());
        userExtend.setGuildShop(new HashMap<>());
        userExtend.setActiveInfo(new ActiveInfo());
        userExtend.setShopDrawInfo(new ShopDrawInfo());
        userExtend.setChestInfo(new ChestInfo());
        userExtend.setArtifactInfo(new ArtifactInfo());
        userExtend.setMountInfo(new MountInfo());
        var openFuncIdSet = new HashSet<Integer>();
        openFuncIdSet.add(-1); //浪费一个-1，因为dynamodb不允许空set
        userExtend.setOpenModelId(openFuncIdSet);
        var heroFormationMap = new HashMap<Integer, HeroFormationModel>();
        heroFormationMap.put(CommonProto.HeroFormationType.MAIN_LEVEL_VALUE, new HeroFormationModel());
        userExtend.setHeroFormationMap(heroFormationMap);
        userExtend.setHeroIdSet(new HashSet<>());
        var fakeLevelModel = new FakeLevelModel();
        fakeLevelModel.setMaxLevelId(fakeLevelId);
        userExtend.setFakeLevelModel(fakeLevelModel);
        userExtend.setKingRushTalentModel(new KingRushTalentModel());
        userExtend.setFormationLevelModel(new FormationLevelModel());
        userExtend.setFormationEquipModel(new FormationEquipModel());
        userExtend.setGoldExpDungeonModel(new GoldExpDungeonModel());
        userExtend.setEquipDungeonModel(new EquipDungeonModel());
        userExtend.setBossDungeonModel(new BossDungeonModel());
        userExtend.setAd(new AdModel());
        userExtend.setBaoWu(new BaoWuModel());
        userExtend.setAvatars(new HashMap<>());
        userExtend.setMonsterCodex(new HashMap<>());
        userExtend.setTower(new TowerModel());
        userExtend.setNewPlayerSignInRewardBit(0L);
        var conditionChapter = new ConditionChapterModel();
        conditionChapter.rewards.add(-1); //浪费一个-1，因为dynamodb不允许空set
        userExtend.setConditionChapterModel(conditionChapter);
        return userExtend;
    }

    @Override
    @DynamoDbIgnore
    public boolean hasHero(int index) {
        return heroFormationMap.get(CommonProto.HeroFormationType.MAIN_LEVEL_VALUE).hasHero(index);
    }

    @Override
    @DynamoDbIgnore
    public int getHeroIdByIndex(int index) {
        return heroFormationMap.get(CommonProto.HeroFormationType.MAIN_LEVEL_VALUE).heroIds[index];
    }

    @Override
    @DynamoDbIgnore
    public void forEachBaoWu(UserExtend userExtend, Consumer<DressedBaoWu> consumer) {
        this.baoWu.posMap.values().forEach(consumer);
    }

    @DynamoDbIgnore
    public HeroFormationModel getOrCreateHeroFormation(int formationType) {
        HeroFormationModel model = this.heroFormationMap.get(formationType);
        if (model == null) {
            model = new HeroFormationModel();
            this.heroFormationMap.put(formationType, model);
        }
        return model;
    }

    @DynamoDbIgnore
    public HeroFormationModel getHeroFormation(int formationType) {
        return this.heroFormationMap.get(formationType);
    }


    @Data
    @DynamoDbBean
    public static class HeroSkinModel {
        /**
         * 已经获得的英雄皮肤。使用flag做标识。
         * <p>
         * 使用map是因为如果有某个段的flag对应的皮肤完全没有获取，则字段中不存在。
         */
        private Map<Integer, HeroSkin> heroSkins = new HashMap<>();

        @DynamoDbIgnore
        public HeroSkin getOrCreate(int mapKey) {
            var skin = heroSkins.get(mapKey);
            if (skin == null) {
                skin = new UserExtend.HeroSkin();
                heroSkins.put(mapKey, skin);
            }
            return skin;
        }
    }

    /**
     * 英雄皮肤。
     * <p>
     * 为了省存储，强迫策划配置从1开始递增（服务器检查做了保证），存储时每64个存一组，
     * 这组里表示的是第n组[n*64, (n+1)*64)范围的皮肤的获取状态。
     */
    @Data
    @DynamoDbBean
    public static class HeroSkin {
        /** 已经获取的skin的bit flag */
        private long flag = 0;
    }

    @Data
    @DynamoDbBean
    public static class TowerModel {
        /** 当前等级 */
        @Getter(onMethod_ = {@DynamoDbAttribute("lv")})
        private int currentLevel;
        /** 通关过的最大等级，用于首通奖励的判断 */
        @Getter(onMethod_ = {@DynamoDbAttribute("maxLv")})
        private int passedMaxLevel;
        /** 下次刷新时间，用于每周回退逻辑的计算 */
        private long nextTime;
    }

    /**
     * 怪物图鉴。
     * <p>
     * 为了省存储，强迫策划配置从1开始递增（服务器检查做了保证），存储时每64个存一组，
     * 这组里表示的是第n组[n*64, (n+1)*64)范围的怪物图鉴的获取和领奖状态。
     */
    @Data
    @DynamoDbBean
    public static class MonsterCodex {
        /** 已经获取的monster的bit flag */
        private long monster = 0;
        /** 已经领奖的bit flag */
        private long reward = 0;
    }

    @Data
    @DynamoDbBean
    public static class BaoWuModel {
        /** key: pos位置，表里配置不为0; value: baoWu rowId */
        private Map<Integer, DressedBaoWu> posMap = new HashMap<>();
        /** key: baoWu template id, value: pos位置，用于快速判断某个baoWu是否已经装备了 */
        @Getter(onMethod_ = {@DynamoDbConvertedBy(Int2IntOpenHashMapConverter.class), @DynamoDbAttribute("tmplId2Pos")})
        private Int2IntOpenHashMap templateId2PosMap = new Int2IntOpenHashMap();
    }

    @Data
    @DynamoDbBean
    public static class DressedBaoWu {
        private long rId;
        private int tId;
        @Getter(onMethod_ = {@DynamoDbAttribute("q")})
        private int quality; //冗余放在这里，构建战斗信息时不用再去查baowu表了
    }

    @Data
    @DynamoDbBean
    public static class AdModel {
        /** 下次按日重置的时间 */
        @Getter(onMethod_ = {@DynamoDbAttribute("nextTime")})
        private long nextRefreshTime;
        @Getter(onMethod_ = {@DynamoDbConvertedBy(Int2IntOpenHashMapConverter.class)})
        private Int2IntOpenHashMap counts = new Int2IntOpenHashMap();
        /** 看广告的总次数，客户端用来打点 */
        private int total;
        /** 下次按星期重置的时间 */
        private long weekTs;
        /** 下次按月重置的时间 */
        private long monthTs;
    }

    @Data
    @DynamoDbBean
    public static class ConditionChapterModel {
        /** key: condition chapter id, value: 已经获得的星星的index */
        private Map<Integer, List<Integer>> chapters = new HashMap<>();
        private int totalStar;
        /** 已经领取的累计星星奖励的id set，里面有个-1，因为dynamodb不允许空set */
        private Set<Integer> rewards = new HashSet<>();
    }

    @Data
    @DynamoDbBean
    public static class BossDungeonModel {
        /** key: boss group id */
        private Map<Integer, BossDungeonInfo> bossInfo = new HashMap<>();
        /** 参与排名但未领奖的日期，隔天发奖后会重置为0（如果后面没有参与排名，则一直为0），格式yyyyMMdd */
        private int rankDay;

        @DynamoDbIgnore
        public BossDungeonInfo getOrCreateBossDungeon(int group) {
            var info = bossInfo.get(group);
            if (info == null) {
                info = new BossDungeonInfo();
                info.level = 1;
                bossInfo.put(group, info);
            }
            return info;
        }
    }

    @Data
    @DynamoDbBean
    public static class BossDungeonInfo {
        private int level;
        /** 历史最大伤害，level升级了清零 */
        private long maxDamage;
    }

    @Data
    @DynamoDbBean
    public static class EquipDungeonModel {
        @Getter(onMethod_ = {@DynamoDbAttribute("id")})
        private int currentId = 1;
        /** 今日挑战最大分，如果一波都没打过去，则为0，不让扫荡 */
        @Getter(onMethod_ = {@DynamoDbAttribute("mPoint")})
        private int maxPointToday;
        @Getter(onMethod_ = {@DynamoDbAttribute("tPoint")})
        private int totalPoints;
        @Getter(onMethod_ = {@DynamoDbAttribute("nextRest")})
        private long nextResetTime;
        /** 降级cd开始时间，单位：秒 */
        @Getter(onMethod_ = {@DynamoDbAttribute("downCd")})
        private long downgradeCdStartTime;
    }

    @Data
    @DynamoDbBean
    public static class GoldExpDungeonModel {
        /** 当前正在挑战且未通关的id，全部通关了，就是模板里最大ID + 1 */
        @Getter(onMethod_ = {@DynamoDbAttribute("goldId")})
        private int currentGoldId = 1;
        private int goldWave;
        @Getter(onMethod_ = {@DynamoDbAttribute("expId")})
        private int currentExpId = 1;
        private int expWave;
    }

    @Data
    @DynamoDbBean
    public static class KingRushTalentModel {
        /** 当前正在抽的talentDropTemplateId */
        @Getter(onMethod_ = {@DynamoDbAttribute("tmplId")})
        private int talentDropTemplateId;
        /** 玩家出生时为null，上面的talentDropTemplateId为0，一旦抽第一次的时候，talentDropTemplateId设值，dropPool就不会为null了，只会为空 */
        private Map<Integer, List<Integer>> dropPool;
        private Int2IntOpenHashMap groupId2LevelMap = new Int2IntOpenHashMap();

        @DynamoDbAttribute("levels")
        @DynamoDbConvertedBy(Int2IntOpenHashMapConverter.class)
        public Int2IntOpenHashMap getGroupId2LevelMap() {
            return groupId2LevelMap;
        }

        @DynamoDbIgnore
        public void deleteOneFromPool(int index) {
            var list = dropPool.get(index);
            var remainAmount = list.get(1) - 1;
            if (remainAmount <= 0) {
                dropPool.remove(index);
            } else {
                list.set(1, remainAmount);
            }
        }
    }

    @Data
    @DynamoDbBean
    public static class FakeLevelModel {
        /** 当前打到的最大关 */
        @Getter(onMethod_ = {@DynamoDbAttribute("maxId")})
        private int maxLevelId;
        /** 当前最大关levelId中，完成的最大波次的index，从0开始 */
        @Getter(onMethod_ = {@DynamoDbAttribute("maxFWave")})
        private int maxFinishedWaveIndex;
        /** 接下来要领取的奖励index，从0开始 */
        private int pveRewardIndex;

        /** 上次领取AFK奖励的时间，功能开放领取第一次奖励后设置，没有开始为0，单位秒 */
        @Getter(onMethod_ = {@DynamoDbAttribute("afkTime")})
        private long lastGetAfkRewardTime;
        /** 今天快速领取的afk奖励次数 */
        @Getter(onMethod_ = {@DynamoDbAttribute("afkCount")})
        private int todayFastGetAfkRewardCount;
        /** 上次重置akf领奖次数时间，单位秒 */
        @Getter(onMethod_ = {@DynamoDbAttribute("afkLastReset")})
        private long lastResetFastAkfRewardCountTime;
    }

    @Data
    @DynamoDbBean
    public static class CityModel {
        /**
         * 金矿等级
         */
        private int goldmineLevel;
        /**
         * 金矿初始挂机时间
         */
        private long initGoldmineTime;
        /**
         * 上次领取金矿挂机奖励时间戳
         */
        private long lastGoldmineRewardTime;
        /**
         * 主城宝箱列表
         */
        private Map<Long, CityChestModel> CityChestMap;
        /**
         * 主城宝箱初始解锁时间
         */
        private long chestInitTime;
        /**
         * 宝箱满了之后重新开始计算时间
         */
        private long chestRefreshTime;
        /**
         * 上个宝箱生成时间
         */
        private long lastGenChestTime;
        /**
         * 宝箱序列数
         */
        private int chestSeqNum;
        /**
         * 生成宝箱总数
         */
        private int chestTotalNum;
        /**
         * 开启宝箱积分
         */
        private int chestScore;
    }

    /**
     * 玩家英雄阵型
     */
    @Setter
    @DynamoDbBean
    public static class HeroFormationModel {
        private int[] heroIds = new int[HeroFormationConsts.FORMATION_SLOT_AMOUNT];

        @DynamoDbConvertedBy(IntArrayConverter.class)
        public int[] getHeroIds() {
            return heroIds;
        }

        /**
         * 这个方法使用的时候，一定要注意，游戏中阵上英雄只能替换，不能下阵。
         * 该方法内部不会做任何检查，因此在调用该方法之前，一定要做完整的判断。
         *
         * @param index
         * @param heroId
         * @param formationLevelModel
         */
        @DynamoDbIgnore
        public void set(int index, int heroId, FormationLevelModel formationLevelModel) {
            heroIds[index] = heroId;
            formationLevelModel.initLevelWhenHeroFirstSet(index);
        }

        @DynamoDbIgnore
        public boolean isFull() {
            for (int each : heroIds) {
                if (each == 0) return false;
            }
            return true;
        }

        @DynamoDbIgnore
        public int getFirstNotFullIndex() {
            for (int i = 0; i < heroIds.length; i++) {
                if (heroIds[i] == 0) return i;
            }
            return -1;
        }

        @DynamoDbIgnore
        public void forAll(@NonNull IntConsumer consumer) {
            for (int heroId : heroIds) {
                consumer.accept(heroId);
            }
        }

        @DynamoDbIgnore
        public boolean hasHero(int index) {
            if (index < 0 || index >= heroIds.length) return false;
            return heroIds[index] > 0;
        }

        @DynamoDbIgnore
        public boolean isHeroInFormation(int heroId) {
            for (int each : heroIds) {
                if (each == heroId) return true;
            }
            return false;
        }

        /**
         * 获取已上阵英雄的数量。
         *
         * @return
         */
        @DynamoDbIgnore
        public int getHeroCount() {
            int count = 0;
            for (int i = 0; i < heroIds.length; i++) {
                if (hasHero(i)) count++;
            }
            return count;
        }
    }

    @Setter
    @DynamoDbBean
    public static class FormationLevelModel {

        private int[] levels = new int[HeroFormationConsts.FORMATION_SLOT_AMOUNT];

        @DynamoDbConvertedBy(IntArrayConverter.class)
        public int[] getLevels() {
            return levels;
        }

        @DynamoDbIgnore
        public int getLevel(int index) {
            return levels[index];
        }

        @DynamoDbIgnore
        public int getMinLevelExcludes(int excludeIndex) {
            int min = Integer.MAX_VALUE;
            for (int i = 0; i < levels.length; i++) {
                if (i == excludeIndex) continue;
                if (levels[i] < min) min = levels[i];
            }
            return min;
        }

        @DynamoDbIgnore
        public int getMinLevel() {
            int min = Integer.MAX_VALUE;
            for (int i = 0; i < levels.length; i++) {
                if (levels[i] < min) min = levels[i];
            }
            return min;
        }

        @DynamoDbIgnore
        public void initLevelWhenHeroFirstSet(int index) {
            if (levels[index] == 0) {
                levels[index] = 1;
            }
        }

        @DynamoDbIgnore
        public void setLevel(int index, int addedLevel) {
            levels[index] = addedLevel;
        }
    }

    /**
     * 阵型装备信息
     */
    @Data
    @DynamoDbBean
    public static class FormationEquipModel {
        /** equipRowId to slot index映射，方便查找装备具体装在哪个slot上。因为Equip数据库里不存这些信息，为了某些逻辑不用再去查DB */
        private Long2IntOpenHashMap equipRowId2FormationSlotIndex = new Long2IntOpenHashMap();

        public FormationEquipModel() {
            equipRowId2FormationSlotIndex.defaultReturnValue(-1);
        }

        /** key: formationSlotIndex */
        private Map<Integer, FormationEquipBarModel> equipBarMap = new HashMap<>();

        @DynamoDbConvertedBy(Long2IntOpenHashMapConverter.class)
        @DynamoDbAttribute("equipRowIdIndex")
        public Long2IntOpenHashMap getEquipRowId2FormationSlotIndex() {
            equipRowId2FormationSlotIndex.defaultReturnValue(-1);
            return equipRowId2FormationSlotIndex;
        }

        @DynamoDbIgnore
        public FormationEquipBarModel getOrCreateEquipBarByIndex(int formationSlotIndex) {
            var bar = equipBarMap.get(formationSlotIndex);
            if (bar == null) {
                bar = new FormationEquipBarModel();
                equipBarMap.put(formationSlotIndex, bar);
            }
            bar.formationSlotIndex = formationSlotIndex;
            return bar;
        }

        @DynamoDbIgnore
        public FormationEquipBarModel getEquipBarByIndex(int formationSlotIndex) {
            var bar = equipBarMap.get(formationSlotIndex);
            if (bar != null) bar.formationSlotIndex = formationSlotIndex;
            return bar;
        }

        @DynamoDbIgnore
        public boolean isEquipInUse(long rowId) {
            return equipRowId2FormationSlotIndex.containsKey(rowId);
        }

    }

    /**
     * 一个槽上的装备栏
     */
    @Data
    @DynamoDbBean
    public static class FormationEquipBarModel {
        /** key: pos(也是EquipType)，这里用了map没用数组，因为数组序列化很麻烦 */
        private Map<Integer, FormationEquipPosModel> equips = new HashMap<>();
        /** slot位置，在FormationEquipModel里通过equipBarMap存储了，这里就是一个冗余，方便外面转换消息时使用 */
        @Getter(onMethod_ = {@DynamoDbAttribute("slotIndex")})
        private int formationSlotIndex;

        @DynamoDbIgnore
        public FormationEquipPosModel getOrCreateByEquipType(int pos) {
            var equip = equips.get(pos);
            if (equip == null) {
                equip = new FormationEquipPosModel();
                equip.level = 1;
                equip.equipRowId = -1;
                equip.equipTemplateId = -1;
                equips.put(pos, equip);
            }
            equip.pos = pos;
            return equip;
        }

        @DynamoDbIgnore
        public FormationEquipPosModel getByEquipType(int pos) {
            var equip = equips.get(pos);
            if (equip != null) equip.pos = pos;
            return equip;
        }

        @DynamoDbIgnore
        public void forAllEquip(BiConsumer<Integer, FormationEquipPosModel> consumer) {
            equips.forEach(consumer);
        }
    }

    /**
     * 一个装备栏槽
     */
    @Data
    @DynamoDbBean
    public static class FormationEquipPosModel {
        /** 位置，在FormationEquipBarModel里通过map存储了，这里就是一个冗余，方便外面转换消息时使用 */
        private int pos;
        /** 装备在上面的装备id，没有为-1 */
        private long equipRowId;
        /**
         * 装备模板id，用于判断快速获取装备模板数据的（比如要计算品质大师，就可以不去数据库查询了），跟着equipRowId同时设置。
         */
        private int equipTemplateId;
        private int level;
        private int purifyLevel;
        private int purifyPerAdd;

        public boolean hasEquip() {
            return equipRowId > -1;
        }
    }

    @Data
    @DynamoDbBean
    public static class LordModel {
        private long userId;
        private int avatar;
        private int avatarFrame;
        private String nickName;
    }

    @Data
    @DynamoDbBean
    public static class ConquerRecordModel {
        private long rowId;             // 征服战rowId
        private long recordRowId;       // 战报rowId
        private int configId;           // 配置id
        private boolean isRead;         // 是否已读
        List<String> paramList;         // 参数列表
        private long time;              // 时间
    }

    @Data
    @DynamoDbBean
    public static class IntegralShopModel {
        private int shopId;                         // 商店ID
        private List<Integer> goodsList;            // 商品列表
        private List<Integer> buyList;              // 已购买列表
        private Map<Integer, Integer> goodsAdCount; //  已看广告的次数， key: goods.id, value: 已看广告次数
        private int refreshNum;                     // 付费刷新次数
        private long refreshTime;                   // 刷新时间
        private int freeRefreshNum;                 // 免费刷新次数
        private int refType;
    }

    @Data
    @DynamoDbBean
    public static class CityChestModel {
        private long rowId;
        private int quality;

        public static CityChestModel valueOf(long rowId, int quality) {
            CityChestModel model = new CityChestModel();
            model.rowId = rowId;
            model.quality = quality;
            return model;
        }
    }

    @Data
    @DynamoDbBean
    public static class ActiveInfo {
        public Map<Integer, ActiveDetail> activeTypeMap = Maps.newHashMap();
    }

    @Data
    @DynamoDbBean
    public static class ShopDrawInfo {

        public Map<Integer, Integer> miniInfoMap = Maps.newHashMap();
        public Map<Integer, Integer> hardInfoMap = Maps.newHashMap();
        public Map<Integer, Integer> freeCostTimes = Maps.newHashMap();
        public long systemResetTimestamp;
        public Map<Integer, Integer> dailyCount = Maps.newHashMap();

        public ShopDrawInfo() {
            systemResetTimestamp = DateUtils.getNextSystemResetTime();
        }

        public int getDrawTimes(int type, int sumId) {
            Map<Integer, Integer> data = miniInfoMap;
            if (type == 2) {
                data = hardInfoMap;
            }
            if (data.get(sumId) == null) {
                return 0;
            }
            return data.get(sumId);
        }

        public void addDrawTimes(int type, int sumId) {
            Map<Integer, Integer> data = miniInfoMap;
            if (type == 2) {
                data = hardInfoMap;
            }
            int oldTimes = data.get(sumId) == null ? 0 : data.get(sumId);
            data.put(sumId, oldTimes + 1);
        }

        @DynamoDbIgnore
        public void addDailyCount(int sumId, int count) {
            int old = dailyCount.get(sumId) == null ? 0 : dailyCount.get(sumId);
            dailyCount.put(sumId, old + count);
        }

        public void resetDrawTimes(int type, int sumId) {
            Map<Integer, Integer> data = miniInfoMap;
            if (type == 2) {
                data = hardInfoMap;
            }
            data.put(sumId, 0);
        }

    }


    @Data
    @DynamoDbBean
    public static class ActiveDetail {
        public int activeType;
        public int activeId;
        public int score;
        public int hasRewardScore;
        public int groupId;
        public Map<Integer, Integer> hasRewardMap = Maps.newHashMap();
        public int lastRewardConfigId = 0;

        //排行积分
        public long rankKey = 0;

        //奖励
        public int lastRewardActId = 0;

        public boolean hasRankReward = false;
        public int rewardActId = 0;
        public long rewardRankKey = 0;
        public int rewardRankIndex = 0;

    }

    @Data
    @DynamoDbBean
    public static class ChestInfo {
        public int lastRewardType = 1;    // 1表示首次2表示循环
        public int lastRewardConfigId = 0;    // 宝箱奖励ID
    }

    @Data
    @DynamoDbBean
    public static class ArtifactInfo {
        private int stage;
        private int level;
        private int exp;
        private int skillId;
        private int godType;
        private int godConfigId;
    }

    @Data
    @DynamoDbBean
    public static class MountInfo {
        private int stage;
        private int level;
        private int exp;
        private int skillId;
        private int godType;
        private int godConfigId;
    }

    @Data
    @DynamoDbBean
    @NoArgsConstructor
    public static class AvatarModel {
        private int id;
        /**
         * 状态：
         * -1: 已获得未激活（这个一般在带有过期时间的上面使用，激活后status变为过期时间；也有可能解锁后永久有效变成0）
         * 0: 已获得已激活（没有过期时间的就是这个状态）
         * >0: 过期时间
         */
        private long status;

        public AvatarModel(int id, long status) {
            this.id = id;
            this.status = status;
        }

        @DynamoDbIgnore
        public boolean isUnlock(long now) {
            return status == 0 || (status > 0 && now < status);
        }

        @DynamoDbIgnore
        public void unlockForever() {
            this.status = 0;
        }
    }
}
