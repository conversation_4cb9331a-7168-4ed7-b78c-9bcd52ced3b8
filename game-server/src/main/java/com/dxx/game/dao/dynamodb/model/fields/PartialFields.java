package com.dxx.game.dao.dynamodb.model.fields;

import lombok.Getter;

import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Getter
public class PartialFields {

    private final String[] fields;

    private PartialFields(String[] fields) {
        this.fields = fields;
    }

    public Builder toBuilder() {
        var builder = new Builder();
        Collections.addAll(builder.fieldSet, fields);
        return builder;
    }

    public static Builder with(String field, String... moreFields) {
        var builder = new Builder();
        builder.with(field, moreFields);
        return builder;
    }

    public static Builder with(PartialFields... more) {
        var builder = new Builder();
        for (PartialFields each : more) {
            Collections.addAll(builder.fieldSet, each.fields);
        }
        return builder;
    }

    public static class Builder {
        private final Set<String> fieldSet = new HashSet<>();

        private Builder() {
        }

        public Builder with(String field, String... moreFields) {
            fieldSet.add(field);
            Collections.addAll(fieldSet, moreFields);
            return this;
        }

        public Builder with(PartialFields... more) {
            for (PartialFields each : more) {
                Collections.addAll(this.fieldSet, each.fields);
            }
            return this;
        }

        public PartialFields build() {
            return new PartialFields(this.fieldSet.toArray(new String[0]));
        }
    }
}
