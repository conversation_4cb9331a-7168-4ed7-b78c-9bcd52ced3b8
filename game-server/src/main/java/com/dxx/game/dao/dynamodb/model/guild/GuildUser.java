package com.dxx.game.dao.dynamodb.model.guild;

import com.dxx.game.common.aws.dynamodb.annotation.DynamoDBTableName;
import com.dxx.game.common.aws.dynamodb.model.DynamoDBBaseModel;
import com.dxx.game.dao.dynamodb.model.IMGroupUserStatus;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @authoer: lsc
 * @createDate: 2023/3/24
 * @description:
 */
@Getter
@Setter
@ToString
@DynamoDbBean
@DynamoDBTableName("guild")
public class GuildUser extends DynamoDBBaseModel {

    @Getter(onMethod_ = {@DynamoDbPartitionKey})
    private String PK;                      // 主键
    @Getter(onMethod_ = {@DynamoDbSortKey})
    private String SK;                      // 排序键

    @Getter(onMethod_ = {@DynamoDbSecondaryPartitionKey(indexNames = "guildId-index")})
    private Long guildId;
    @Getter(onMethod_ = {@DynamoDbSecondarySortKey(indexNames = "guildId-index")})
    private String SK2;

    private Long userId;
    private Integer position;                         // 职位(1 会长 , 2 副会长, 3 管理 , 4 成员)
    private List<Long> applyGuildIds;                 // 已申请的公会列表
    private Long joinTime;                            // 加入时间
    private Integer signInCnt;                        // 每日签到次数
    private Integer active;                           // 总活跃度
    private Integer weeklyActive;                     // 每周活跃度
    private Integer dailyActive;                      // 每日活跃度
    private Long dailyTM;                             // 每日数据刷新时间
    private Long weeklyTM;                            // 每周数据刷新时间
    private Map<Integer, GuildTaskModel> tasks;       // 公会任务
    private Integer taskRefreshCount;                 // 任务刷新次数
    private Map<Integer, GuildShopModel> dailyShop;   // 公会每日商店
    private Map<Integer, GuildShopModel> weeklyShop;  // 公会每周商店
    private Integer guildLevelRecord;                 // 公会等级记录,用来给客户端判断是否有公会升级的弹窗
    private String beKickedOutInfo;                   // 被踢出公会消息
    private GuildBossModel guildBossModel;            // 公会boss
    private Long quitTime;                            //退出工会时间
    private Boolean creater;                        //是否是工会创建人

    private GuildWarModel guildWarModel;              // 公会战
    private GuildWarTaskModel guildWarTaskModel; //公会战成就
    private Integer donationItemCount;                      // 每周捐赠获得道具数量
    private Long reqItemTM;                                 // 捐赠-请求道具时间
    private Integer dayContributeTimes;                     //日捐赠次数
//    private List<Long> reqItemMsgID;                      // 捐赠-请求道具msgID

    @Getter(onMethod_ = {@DynamoDbIgnore})
    private Long applyTime;                 // 不同公会的申请时间(服务器返回数据用)
    private List<Integer> bossKilledRewardRecord;                                 // 捐赠-请求道具时间

    private IMGroupUserStatus imGroupUserStatus;

    @DynamoDbIgnore
    @Override
    public Object getUniqueKey() {
        return this.PK + "_" + this.SK;
    }

    @Data
    @DynamoDbBean
    public static class GuildTaskModel {
        private int progress;   // 任务进度
        private int state;      // 奖励状态1已领取
    }

    @Data
    @DynamoDbBean
    public static class GuildShopModel {
        private int position;                   // 位置
        private int needItemId;                // 价格道具ID
        private int needItemNum;               // 价格道具数量
        private List<List<Integer>> reward;     // 奖励
        private int count;                      // 购买次数
        private int limit;                      // 限购次数
        private int discount;                   // 折扣
    }

    @Data
    @DynamoDbBean
    public static class GuildBossModel {
        private long damage;                    // 总伤害
        private int totalChallengeCnt;          // 总挑战次数
        private int challengeCnt;               // 剩余挑战次数
        private int buyCntByDiamonds;           // 每日购买次数(钻石)
        private int buyCntByCoins;              // 每日购买次数(金币)
        private Integer lastRefreshTaskSeason;  // 上次刷新任务赛季
        private Integer lastRefreshTaskCheck;  // 上次刷新任务赛季
        private List<Integer> taskLog;          // 成就奖励领取记录
        private List<Integer> boxLog;           // 击杀boss宝箱领取记录
        private long mailUniqueIdTask;          // 成就任务未领取的奖励邮件唯一ID
        private long mailUniqueIdBox;           // 击杀boss的奖励邮件唤一ID
        private List<Long> battleStartTransId;  // 开始挑战上报的transId
        private long guildId;                   // 最后参与公会boss的公会ID(用来判断奖励邮件)
        private long recoveryTime;              // 挑战次数恢复时间
        private long dailyTime;
        private long weeklyTime;
        private long guildBossRewardTime;       // 公会排名奖励领取时间  领取后第二天0点

        private int season;                  // 赛季[日期]
        private int groupDan;               // 段位
        private int lastGroupDan;               // 段位
        private long bossGroupId;             // 分组ID
        private long lastBossGroupId;             // 分组ID
    }

    @DynamoDbBean
    @Data
    public static class GuildWarModel {
        private int lastRefreshChallengeId;   // 上次刷新挑战次数配置id
        private int challengeCnt;             // 剩余挑战次数

        private int caculateConfigid; //上次个人结算时间
        private int winTurntableCnt;           //胜利转盘次数
        private int loseTurntableCnt;          //失败转盘次数
        int totalWinTurntableCnt;   //总胜利转盘次数
        int totalLoseTurntableCnt;  //总失败转盘次数
        private int turntableConfigId; //上次刷新转盘配置ID
        private int settleId; //结算id 用于计算周/月结算动画
        private List<String> weekSettleId; //周结算id集合
        private List<String> roundSettleId; //月结算id集合

        private int weekTotalCnt; //本周总结算次数  用于防止转盘次数刷，其实没有意义  翟城要求加这个

    }

    @DynamoDbBean
    @Data
    public static class GuildWarTaskModel {
        @Deprecated
        private Map<Long, Integer> guildProgress = new HashMap<>(); //公会完成进度 废弃

        private Map<Long, List<Long>> guildProgressNew = new HashMap<>(); //公会完成进度
        private int personProgress; //个人完成进度
        private List<Integer> rewardPersonIds; //已领取的任务id
        private List<Integer> rewardGuildIds; //已领取的任务id

    }
}
