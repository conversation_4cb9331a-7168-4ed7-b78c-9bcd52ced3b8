package com.dxx.game.dao.dynamodb.model.guild;

import com.dxx.game.common.aws.dynamodb.annotation.DynamoDBTableName;
import com.dxx.game.common.aws.dynamodb.model.DynamoDBBaseModel;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbIgnore;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey;

/**
 * 用户在公会中的活跃度记录
 * @authoer: lsc
 * @createDate: 2023/4/12
 * @description:
 */
@Getter
@Setter
@ToString
@DynamoDbBean
@DynamoDBTableName("guild")
public class GuildUserActiveRecord extends DynamoDBBaseModel {

    @Getter(onMethod_ = {@DynamoDbPartitionKey})
    private String PK;                      // 主键
    @Getter(onMethod_ = {@DynamoDbSortKey})
    private String SK;                      // 排序键

    private Integer active;
    private Integer dailyActive;
    private Integer weeklyActive;
    private Long dailyTM;
    private Long weeklyTM;

    @DynamoDbIgnore
    @Override
    public Object getUniqueKey() {
        return this.PK + "_" + this.SK;
    }
}
