package com.dxx.game.dao.dynamodb.repository;

import com.dxx.game.common.aws.dynamodb.BaseDynamoDBDao;
import com.dxx.game.dao.dynamodb.model.IapModule;
import com.dxx.game.modules.iap.type.IapType;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class AbstractIapModuleDao<T extends IapModule> extends BaseDynamoDBDao<T> {

    private final IapType iapType;

    protected AbstractIapModuleDao(IapType iapType) {
        this.iapType = iapType;
    }

    public T getByUserId(Long userId) {
        return getItem(userId, iapType.getTypeValue());
    }

    public void updateBuyings(T data) {
        this.updateDelta(data, delta -> delta.setBuyings(data.getBuyings()));
    }
}
