package com.dxx.game.dao.dynamodb.repository;

import com.dxx.game.common.aws.dynamodb.BaseDynamoDBDao;
import com.dxx.game.dao.dynamodb.model.Account;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbIndex;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.model.Page;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryConditional;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/10/19 10:44
 */
@Repository
public class AccountDao extends BaseDynamoDBDao<Account> {

    DynamoDbIndex<Account> accountIdIndex;
    DynamoDbIndex<Account> deviceIdIndex;

    @PostConstruct
    private void init() {
        accountIdIndex = this.mappedTable.index("accountId-Index");
        deviceIdIndex = this.mappedTable.index("deviceId-Index");
    }

    public Account getByAccountKey(String accountKey) {
        return getItem(accountKey);
    }

    /**
     * 根据账号ID获取用户
     *
     * @param accountId
     * @return
     */
    public Account getByAccountId(String accountId) {
        QueryConditional queryConditional = QueryConditional.keyEqualTo(Key.builder().partitionValue(accountId).build());
        List<Page<Account>> collect = accountIdIndex.query(queryConditional).stream().toList();
        List<Account> items = collect.getFirst().items();
        if (items.isEmpty()) {
            return null;
        }
        return items.getFirst();
    }

    /**
     * 根据设备ID获取最后一次登录的账户。
     *
     * @param deviceId
     * @return
     */
    public Account getLastLoginAccountByDeviceId(String deviceId) {
        QueryConditional queryConditional = QueryConditional.keyEqualTo(Key.builder().partitionValue(deviceId).build());
        List<Page<Account>> collect = deviceIdIndex.query(queryConditional).stream().toList();
        List<Account> items = collect.getFirst().items();
        if (items.isEmpty()) {
            return null;
        }
        items.sort((c1, c2) -> (int) (c2.getLastLoginTime() - c1.getLastLoginTime()));
        return items.getFirst();
    }

    public void updateAccInfo(Account account) {
        this.updateDelta(account, update -> {
            update.setAccountId(account.getAccountId());
            update.setAccountId2(account.getAccountId2());
            update.setDeviceId(account.getDeviceId());
            update.setChannelId(account.getChannelId());
            update.setLastLoginUserId(account.getLastLoginUserId());
            update.setLastLoginTime(account.getLastLoginTime());
            update.setServerUserIdMap(account.getServerUserIdMap());
        });
    }

    public void updateAccountId(Account account) {
        if (StringUtils.isEmpty(account.getAccountId())) {
            return;
        }
        super.updateDelta(account, update -> update.setAccountId(account.getAccountId()));
    }

    //更新device
    public void updateDeviceId(Account account) {
        if (StringUtils.isEmpty(account.getDeviceId())) {
            return;
        }
        super.updateDelta(account, update -> update.setDeviceId(account.getDeviceId()));
    }

    public void updateHabbyBind(Account account) {
        super.updateDelta(account, update -> {
            update.setHabbyId(account.getHabbyId());
            update.setBindReward(account.getBindReward());
        });
    }
}
