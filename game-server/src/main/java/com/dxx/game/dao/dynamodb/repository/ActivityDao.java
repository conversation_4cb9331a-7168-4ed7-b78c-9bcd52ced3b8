package com.dxx.game.dao.dynamodb.repository;

import com.dxx.game.common.aws.dynamodb.BaseDynamoDBDao;
import com.dxx.game.dao.dynamodb.model.Activity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;


/**
 * <AUTHOR>
 * @date 2021/7/14 10:49
 */
@Repository
@Slf4j
public class ActivityDao extends BaseDynamoDBDao<Activity> {

    public Activity getByUserId(long userId) {
        return super.getItem(userId);
    }

    public void save(Activity activity) {
        super.insert(activity);
    }

    public void updateSevenDayTask(Activity activity) {
        this.save(activity);
    }
}
