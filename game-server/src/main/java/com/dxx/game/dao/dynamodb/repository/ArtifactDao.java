package com.dxx.game.dao.dynamodb.repository;

import com.dxx.game.common.aws.dynamodb.BaseDynamoDBDao;
import com.dxx.game.dao.dynamodb.model.Artifact;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2021/11/24 16:58
 */
@Repository
public class ArtifactDao extends BaseDynamoDBDao<Artifact> {

    public Artifact getByUserId(long userId) {
        return super.getItem(userId);
    }
}
