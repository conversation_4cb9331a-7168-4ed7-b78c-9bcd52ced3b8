package com.dxx.game.dao.dynamodb.repository;

import com.dxx.game.common.aws.dynamodb.BaseDynamoDBDao;
import com.dxx.game.dao.dynamodb.model.BaoWu;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Slf4j
public class BaoWuDao extends BaseDynamoDBDao<BaoWu> {
    public BaoWu getByRowId(long userId, long rowId) {
        return super.getItem(userId, rowId);
    }


    public List<BaoWu> getListByRowIds(long userId, List<Long> rowIds) {
        return batchGetItem(userId, rowIds);
    }

    public List<BaoWu> getAllByUserId(long userId) {
        return super.getAll(userId);
    }
}
