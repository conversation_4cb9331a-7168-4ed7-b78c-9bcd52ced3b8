package com.dxx.game.dao.dynamodb.repository;

import com.dxx.game.common.aws.dynamodb.BaseDynamoDBDao;
import com.dxx.game.dao.dynamodb.model.BattleRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Slf4j
public class BattleRecordDao extends BaseDynamoDBDao<BattleRecord> {
    public BattleRecord getByBattleId(long userId, long battleId) {
        return super.getItem(userId, battleId);
    }

    public List<BattleRecord> getAllByUserId(long userId) {
        return super.getAll(userId);
    }
}
