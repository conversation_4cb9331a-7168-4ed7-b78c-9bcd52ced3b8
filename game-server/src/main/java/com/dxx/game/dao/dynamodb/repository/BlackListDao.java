package com.dxx.game.dao.dynamodb.repository;

import com.dxx.game.common.redis.RedisService;
import com.dxx.game.common.utils.DateUtils;
import com.dxx.game.consts.RedisKeys;
import com.dxx.game.dao.dynamodb.model.BlackList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Repository
@Slf4j
public class BlackListDao extends UserModuleDao<BlackList> {
    @Resource
    private RedisService redisService;

    public void addBlack(long userId, List<Long> userId2s) {
        getBlackList(userId);
        BlackList blackList = getByUserId(userId);
        long now = DateUtils.getUnixTime();
        for (Long userId2 : userId2s) {
            blackList.getBlackList().put(userId2, now);
        }
        update(blackList);
        String key = RedisKeys.getKey(RedisKeys.BLACK_LIST, userId);
        redisService.deleteKey(key);
    }

    public void removeBlack(long userId, List<Long> userId2s) {
        BlackList blackList = getByUserId(userId);
        for (Long userId2 : userId2s) {
            blackList.getBlackList().remove(userId2);
        }
        update(blackList);
        String key = RedisKeys.getKey(RedisKeys.BLACK_LIST, userId);
        redisService.deleteKey(key);
    }

    public Set<Long> getBlackList(Long userId) {
        String key = RedisKeys.getKey(RedisKeys.BLACK_LIST, userId);
        Set<String> ids = redisService.sMembers(key);
        if (ids != null && !ids.isEmpty()) {
            ids.remove("0");
            return ids.stream().map(Long::parseLong).collect(Collectors.toSet());
        }
        BlackList blackList = getByUserId(userId);
        if (blackList == null) {
            blackList = new BlackList();
            blackList.setUserId(userId);
            blackList.setBlackList(new HashMap<>());
            insert(blackList);
            //放个0进去 防止缓存穿透
            redisService.sAdd(key, String.valueOf(0L));
            redisService.expireKey(key, 3, TimeUnit.DAYS);
            return Collections.emptySet();
        }
        if (blackList.getBlackList().isEmpty()) {
            redisService.sAdd(key, String.valueOf(0L));
            redisService.expireKey(key, 3, TimeUnit.DAYS);
            return Collections.emptySet();
        }
        String[] tempIds = new String[blackList.getBlackList().size()];
        int i = 0;
        for (Long l : blackList.getBlackList().keySet()) {
            tempIds[i] = String.valueOf(l);
            i++;
        }
        redisService.sAdd(key, tempIds);
        redisService.expireKey(key, 3, TimeUnit.DAYS);
        return blackList.getBlackList().keySet();
    }


    /**
     * 是否在黑名单中
     *
     * @param userId
     * @param userId2
     * @return
     */
    public boolean isBlackListUser(Long userId, Long userId2) {
        String key = RedisKeys.getKey(RedisKeys.BLACK_LIST, userId);
        boolean exist = redisService.existsKey(key);
        if (exist) {
            return redisService.sisMember(key, String.valueOf(userId2));
        }
        BlackList blackList = getByUserId(userId);
        if (blackList == null) {
            blackList = new BlackList();
            blackList.setUserId(userId);
            blackList.setBlackList(new HashMap<>());
            insert(blackList);
            //放个0进去 防止缓存穿透
            redisService.sAdd(key, String.valueOf(0L));
            redisService.expireKey(key, 3, TimeUnit.DAYS);
            return false;
        }
        if (blackList.getBlackList().isEmpty()) {
            redisService.sAdd(key, String.valueOf(0L));
            redisService.expireKey(key, 3, TimeUnit.DAYS);
            return false;
        }
        String[] ids = new String[blackList.getBlackList().size()];
        int i = 0;
        for (Long l : blackList.getBlackList().keySet()) {
            ids[i] = String.valueOf(l);
            i++;
        }
        redisService.sAdd(key, ids);
        redisService.expireKey(key, 3, TimeUnit.DAYS);
        return blackList.getBlackList().containsKey(userId2);
    }

}
