package com.dxx.game.dao.dynamodb.repository;

import com.dxx.game.common.aws.dynamodb.BaseDynamoDBDao;
import com.dxx.game.dao.dynamodb.model.Equip;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/10/19 15:08
 */
@Repository
@Slf4j
public class EquipDao extends BaseDynamoDBDao<Equip> {
    public Equip getByRowId(long userId, long rowId) {
        return super.getItem(userId, rowId);
    }


    public List<Equip> getListByRowIds(long userId, List<Long> rowIds) {
        return batchGetItem(userId,rowIds);
    }

    public List<Equip> getAllByUserId(long userId) {
        return super.getAll(userId);
    }
}
