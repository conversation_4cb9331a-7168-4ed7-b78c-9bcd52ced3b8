package com.dxx.game.dao.dynamodb.repository;

import com.dxx.game.common.aws.dynamodb.BaseDynamoDBDao;
import com.dxx.game.dao.dynamodb.model.Formation;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class FormationDao extends BaseDynamoDBDao<Formation> {

    public Formation getByFormationId(long userId, int formationId) {
        return super.getItem(userId, formationId);
    }

    public List<Formation> getAllByUserId(long userId) {
        return super.getAll(userId);
    }
}
