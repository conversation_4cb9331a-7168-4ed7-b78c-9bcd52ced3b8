package com.dxx.game.dao.dynamodb.repository;

import com.dxx.game.common.aws.dynamodb.cache.DynamoDBCacheManager;
import com.dxx.game.common.redis.RedisService;
import com.dxx.game.common.utils.DateUtils;
import com.dxx.game.consts.RedisKeys;
import com.dxx.game.dao.dynamodb.model.Friend;
import io.jsonwebtoken.lang.Collections;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.core.RedisOperations;
import org.springframework.data.redis.core.SessionCallback;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Repository
@Slf4j
public class FriendDao extends UserModuleDao<Friend> {
    
    @Resource
    private RedisService redisService;

    /**
     * 获取好友信息
     *
     * @param userId
     */
    public Friend getFriendData(long userId) {
        var data = super.getItem(userId);
        if (data == null) {
            data = new Friend();
            data.setUserId(userId);
            data.setFriendList(new HashMap<>());
            data.setSendApplyList(new HashMap<>());
            data.setReceiveApplyList(new HashMap<>());
            super.insert(data);
        }
        var redisKey = getFriendLifeStatusKey(userId);
        var res = redisService.existsKey(redisKey);
        if (!res && data != null && !Collections.isEmpty(data.getFriendList())) {
            var lifeStatus = new HashMap<String, String>();
            data.getFriendList().forEach((key, value) -> {
                lifeStatus.put(String.valueOf(key), "0|0");
            });
            redisService.hSetAll(redisKey, lifeStatus);
            // 设置30天过期
            redisService.expireKey(redisKey, DateUtils.DAY_30_SECONDS, TimeUnit.SECONDS);
        }
        return data;
    }

    public String getFriendLifeStatusKey(long userId) {
        return RedisKeys.FRIEND_LIFE_STATUS + "{friend}:" + userId;
    }

    /**
     * 更新好友列表
     *
     * @param friend
     */
    public void updateFriendList(Friend friend) {
        var update = DynamoDBCacheManager.getUpdateObj(friend);
        update.setFriendList(friend.getFriendList());
        super.updateIgnoreNulls(update);
    }

    /**
     * 更新 已发送申请列表
     *
     * @param friend
     */
    public void updateSendApplyList(Friend friend, boolean agreeApply) {
        var update = DynamoDBCacheManager.getUpdateObj(friend);
        update.setSendApplyList(friend.getSendApplyList());
        if (agreeApply) {
            update.setFriendList(friend.getFriendList());
        }
        super.updateIgnoreNulls(update);
    }

    /**
     * 更新 已发送申请列表
     *
     * @param friend
     * @param friend
     */
    public void updateReceiveApplyList(Friend friend, boolean agreeApply) {
        var update = DynamoDBCacheManager.getUpdateObj(friend);
        update.setReceiveApplyList(friend.getReceiveApplyList());
        if (agreeApply) {
            update.setFriendList(friend.getFriendList());
        }
        super.updateIgnoreNulls(update);
    }

    /**
     * 更新 已发送/已接收 申请列表
     *
     * @param friend
     * @param friend
     */
    public void updateApplyList(Friend friend) {
        var update = DynamoDBCacheManager.getUpdateObj(friend);
        update.setReceiveApplyList(friend.getReceiveApplyList());
        update.setSendApplyList(friend.getSendApplyList());
        super.updateIgnoreNulls(update);
    }


    /**
     * 更新黑名单以外的数据
     *
     * @param friend
     */
    public void updateWithoutBlackList(Friend friend) {
        var update = DynamoDBCacheManager.getUpdateObj(friend);
        update.setFriendList(friend.getFriendList());
        update.setSendApplyList(friend.getSendApplyList());
        update.setReceiveApplyList(friend.getReceiveApplyList());
        super.updateIgnoreNulls(update);
    }

    public Map<Long, Friend> getByUserIds(List<Long> userIds) {
        Map<Long, Friend> result = new HashMap<>();
        List<Friend> datas = super.batchGetItem(userIds);
        for (Friend data : datas) {
            result.put(data.getUserId(), data);
        }
        return result;
    }

    /**
     * 从redis中获取多个玩家 赠送/接收体力数据
     *
     * @param userId
     * @return
     */
    public Map<Long, Map<Object, Object>> multiGetLifeStatus(long userId, List<Long> friendIds) {
        var result = new HashMap<Long, Map<Object, Object>>();
        var strUserId = String.valueOf(userId);
        var res = redisService.getRedisTemplate().executePipelined(new SessionCallback<>() {
            @Override
            public <K, V> Object execute(RedisOperations<K, V> redisOperations) throws DataAccessException {
                var operations = (RedisOperations<String, String>) redisOperations;
                operations.opsForHash().entries(getFriendLifeStatusKey(userId));
                friendIds.forEach(friendId -> {
                    operations.opsForHash().get(getFriendLifeStatusKey(friendId), strUserId);
                });
                return null;
            }
        });
        result.put(userId, (Map<Object, Object>) res.get(0));
        for (int i = 1; i < res.size(); i++) {
            var data = res.get(i);
            if (data != null) {
                var mapData = new HashMap<>();
                mapData.put(strUserId, data);
                result.put(friendIds.get(i - 1), mapData);
            }
        }
        return result;
    }

    public Map<Long, String> getLifeStatus(long userId) {
        Map<Long, String> result = new HashMap<>();
        var redisKey = getFriendLifeStatusKey(userId);
        var res = redisService.hGetAll(redisKey);
        if (res != null) {
            // 刷新过期时间
            redisService.expireKey(redisKey, DateUtils.DAY_30_SECONDS, TimeUnit.SECONDS);
            result = res.entrySet().stream().collect(Collectors.toMap(m1 -> Long.parseLong(String.valueOf(m1.getKey()))
                    , m2 -> String.valueOf(m2.getValue())));
        }
        return result;
    }

    @Async
    public void multiUpdateLifeStatus(Map<String, Map<Object, Object>> lifeStatus) {
        if (lifeStatus != null && !lifeStatus.isEmpty()) {
            redisService.getRedisTemplate().executePipelined(new SessionCallback<>() {
                @Override
                public <K, V> Object execute(RedisOperations<K, V> redisOperations) throws DataAccessException {
                    var operations = (RedisOperations<String, String>) redisOperations;
                    lifeStatus.forEach((k, v) -> {
                        operations.opsForHash().putAll(k, v);
                    });
                    return null;
                }
            });
        }
    }

    @Async
    public void updateLifeStatus(long userId, Map<Object, Object> lifeStatus) {
        if (lifeStatus != null && !lifeStatus.isEmpty()) {
            var target = new HashMap<String, String>();
            lifeStatus.forEach((k, v) -> {
                target.put(String.valueOf(k), String.valueOf(v));
            });
            redisService.hSetAll(getFriendLifeStatusKey(userId), target);
        }
    }

    @Async
    public void addNewLifeStatus(HashMap<String, HashMap<Object, Object>> lifeStatus) {
        if (lifeStatus != null && !lifeStatus.isEmpty()) {
            redisService.getRedisTemplate().executePipelined(new SessionCallback<>() {
                @Override
                public <K, V> Object execute(RedisOperations<K, V> redisOperations) throws DataAccessException {
                    var operations = (RedisOperations<String, String>) redisOperations;
                    lifeStatus.forEach((k, v) -> {
                        operations.opsForHash().putAll(k, v);
                    });
                    return null;
                }
            });
        }
    }

    @Async
    public void delLifeStatus(long userId, ArrayList<Long> delFriendIds) {
        var delIds = delFriendIds.stream().map(String::valueOf).toArray(String[]::new);
        var strUserId = String.valueOf(userId);
        redisService.getRedisTemplate().executePipelined(new SessionCallback<>() {
            @Override
            public <K, V> Object execute(RedisOperations<K, V> redisOperations) throws DataAccessException {
                var operations = (RedisOperations<String, String>) redisOperations;
                operations.opsForHash().delete(getFriendLifeStatusKey(userId), delIds);
                for (String id : delIds) {
                    operations.opsForHash().delete(getFriendLifeStatusKey(Long.parseLong(id)), strUserId);
                }
                return null;
            }
        });
    }
}
