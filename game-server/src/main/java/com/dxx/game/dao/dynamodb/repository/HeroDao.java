package com.dxx.game.dao.dynamodb.repository;

import com.dxx.game.common.aws.dynamodb.BaseDynamoDBDao;
import com.dxx.game.dao.dynamodb.model.Hero;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/1 14:53
 */
@Repository
public class HeroDao extends BaseDynamoDBDao<Hero> {

    public Hero getByHeroId(long userId, int heroId) {
        return super.getItem(userId, heroId);
    }

    public List<Hero> getAllByUserId(long userId) {
        return super.getAll(userId);
    }

    @SuppressWarnings("unchecked")
    public List<Hero> getListByRowIds(long userId, List<Integer> heroIds) {
        return batchGetItem(userId, heroIds);
    }
}
