package com.dxx.game.dao.dynamodb.repository;

import com.dxx.game.common.aws.dynamodb.BaseDynamoDBDao;
import com.dxx.game.dao.dynamodb.model.IapModule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

@Slf4j
@Repository
public class IapModuleDao extends BaseDynamoDBDao<IapModule> {
    public IapModule getByUserId(Long userId, int iapType) {
        var module = getItem(userId, iapType);
        if (module != null) module.setIgnoreNulls(true);
        return module;
    }
}
