package com.dxx.game.dao.dynamodb.repository;


import com.dxx.game.dao.dynamodb.model.IapNewBattlePass;
import com.dxx.game.modules.iap.type.IapType;
import org.springframework.stereotype.Repository;

import java.util.HashMap;

/**
 * <AUTHOR>
 */
@Repository
public class IapNewBattlePassDao extends AbstractIapModuleDao<IapNewBattlePass> {

    protected IapNewBattlePassDao() {
        super(IapType.NEW_BATTLE_PASS);
    }

    public IapNewBattlePass getOrCreate(Long userId) {
        var newBattlePass = getByUserId(userId);
        if (newBattlePass == null) {
            newBattlePass = new IapNewBattlePass();
            newBattlePass.setUserId(userId);
            newBattlePass.setModel(new HashMap<>());
            this.insert(newBattlePass);
        }
        return newBattlePass;
    }
}
