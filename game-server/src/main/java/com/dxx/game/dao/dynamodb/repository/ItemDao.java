package com.dxx.game.dao.dynamodb.repository;

import com.dxx.game.common.aws.dynamodb.BaseDynamoDBDao;
import com.dxx.game.dao.dynamodb.model.Item;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/12/17 16:01
 */
@Repository
public class ItemDao extends BaseDynamoDBDao<Item> {

    public Item getByItemId(long userId, int itemId) {
        return super.getItem(userId, itemId);
    }

    public List<Item> getAllByUserId(long userId) {
        return super.getAll(userId);
    }
}
