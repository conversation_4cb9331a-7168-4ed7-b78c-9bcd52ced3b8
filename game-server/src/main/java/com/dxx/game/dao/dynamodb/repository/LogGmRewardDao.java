package com.dxx.game.dao.dynamodb.repository;

import com.dxx.game.common.aws.dynamodb.BaseDynamoDBDao;
import com.dxx.game.dao.dynamodb.model.LogGmReward;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2021/4/29 11:27
 */
@Repository
public class LogGmRewardDao extends BaseDynamoDBDao<LogGmReward> {
    public LogGmReward getByUserIdAndUniqueId(long userId, String uniqueId) {
        return super.getItem(userId, uniqueId);
    }
}
