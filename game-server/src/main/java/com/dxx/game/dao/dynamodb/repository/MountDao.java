package com.dxx.game.dao.dynamodb.repository;

import com.dxx.game.common.aws.dynamodb.BaseDynamoDBDao;
import com.dxx.game.dao.dynamodb.model.Mount;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2021/11/24 16:58
 */
@Repository
public class MountDao extends BaseDynamoDBDao<Mount> {
    public Mount getByUserId(long userId) {
        return super.getItem(userId);
    }
}
