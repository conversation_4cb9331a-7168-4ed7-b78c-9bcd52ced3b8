package com.dxx.game.dao.dynamodb.repository;

import com.dxx.game.common.aws.dynamodb.BaseDynamoDBDao;
import com.dxx.game.dao.dynamodb.model.PreRechargeOrder;
import org.springframework.stereotype.Repository;
import software.amazon.awssdk.enhanced.dynamodb.Expression;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryConditional;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryEnhancedRequest;
import software.amazon.awssdk.services.dynamodb.model.AttributeValue;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2021/11/8 16:19
 */
@Repository
public class PreRechargeOrderDao extends BaseDynamoDBDao<PreRechargeOrder> {

    public PreRechargeOrder getByOrderId(long userId, String preOrderId) {
        return super.getItem(userId, preOrderId);
    }

    public List<PreRechargeOrder> getAllByUserId(long userId) {
        return super.getAll(userId);
    }

    public PreRechargeOrder getByLastProductId(long userId, String productId) {
        Key key = this.buildKey(userId);

        Map<String, AttributeValue> expressionValues = new HashMap<>();
        expressionValues.put(":productId", AttributeValue.builder().s(productId).build());
        expressionValues.put(":success", AttributeValue.builder().n("0").build());
        String filterExpression = "productId = :productId And success = :success";
        Expression expression = Expression.builder()
                .expression(filterExpression)
                .expressionValues(expressionValues)
                .build();

        QueryEnhancedRequest queryEnhancedRequest = QueryEnhancedRequest.builder()
                .queryConditional(QueryConditional.keyEqualTo(key))
                .filterExpression(expression)
                .consistentRead(true)
                .scanIndexForward(false)
                .limit(20)
                .build();
        List<PreRechargeOrder> orders = mappedTable.query(queryEnhancedRequest).stream().findFirst().get().items();
        if (!orders.isEmpty()) {
            return orders.getFirst();
        }
        return null;
    }
}
