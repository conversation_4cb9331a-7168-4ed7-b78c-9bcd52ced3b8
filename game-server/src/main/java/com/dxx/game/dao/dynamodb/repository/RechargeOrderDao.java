package com.dxx.game.dao.dynamodb.repository;

import com.dxx.game.common.aws.dynamodb.BaseDynamoDBDao;
import com.dxx.game.dao.dynamodb.model.RechargeOrder;
import org.springframework.stereotype.Repository;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbIndex;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.model.Page;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryConditional;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/10/19 15:34
 */
@Repository
public class RechargeOrderDao extends BaseDynamoDBDao<RechargeOrder> {

    DynamoDbIndex<RechargeOrder> userIdIndex;

    @PostConstruct
    private void init() {
        userIdIndex = this.mappedTable.index("userId-Index");
    }

    public RechargeOrder getByOrderId(String orderId) {
        return super.getItem(orderId);
    }

    public List<RechargeOrder> getAllByUserId(long userId) {
        QueryConditional queryConditional = QueryConditional.keyEqualTo(Key.builder().partitionValue(userId).build());
        List<Page<RechargeOrder>> collect = userIdIndex.query(queryConditional).stream().collect(Collectors.toList());
        return collect.get(0).items();
    }
}
