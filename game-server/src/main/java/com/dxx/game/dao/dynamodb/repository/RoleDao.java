package com.dxx.game.dao.dynamodb.repository;

import com.dxx.game.common.aws.dynamodb.BaseDynamoDBDao;
import com.dxx.game.dao.dynamodb.model.Role;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2021/4/1 14:53
 */
@Repository
public class Role<PERSON><PERSON> extends BaseDynamoDBDao<Role> {

    public Role getByUserId(long userId) {
        return super.getItem(userId);
    }

    public void updateRoles(Role role) {
        this.updateIgnoreNulls(role, update -> update.setRoles(role.getRoles()));
    }

    public void updateRoleSkin(Role role) {
        this.updateIgnoreNulls(role, update -> update.setRoleSkin(role.getRoleSkin()));
    }

    public void updateRoleValhallaLevel(Role role) {
        this.updateIgnoreNulls(role, update -> update.setValhallaLevel(role.getValhallaLevel()));
    }

    public void updateShowRole(Role role) {
        this.updateIgnoreNulls(role, update -> update.setShowRole(role.getShowRole()));
    }
}
