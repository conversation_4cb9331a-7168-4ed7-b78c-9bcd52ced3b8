package com.dxx.game.dao.dynamodb.repository;

import com.dxx.game.common.aws.dynamodb.BaseDynamoDBDao;
import com.dxx.game.dao.dynamodb.model.Rune;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/25 15:07
 * @description: 符文dao
 */
@Repository
@Slf4j
public class RuneDao extends BaseDynamoDBDao<Rune> {
    public Rune getByRowId(long userId, long rowId) {
        return super.getItem(userId, rowId);
    }

    public List<Rune> getListByRowIds(long userId, List<Long> rowIds) {
        return batchGetItem(userId, rowIds);
    }

    public List<Rune> getAllByUserId(long userId) {
        return super.getAll(userId);
    }
}
