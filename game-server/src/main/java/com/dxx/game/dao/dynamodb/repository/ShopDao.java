package com.dxx.game.dao.dynamodb.repository;

import com.dxx.game.common.aws.dynamodb.BaseDynamoDBDao;
import com.dxx.game.common.aws.dynamodb.cache.DynamoDBCacheManager;
import com.dxx.game.dao.dynamodb.model.Shop;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2021/11/24 16:58
 */
@Repository
public class ShopDao extends BaseDynamoDBDao<Shop> {

    public Shop getByUserId(long userId) {
        return super.getItem(userId);
    }
}
