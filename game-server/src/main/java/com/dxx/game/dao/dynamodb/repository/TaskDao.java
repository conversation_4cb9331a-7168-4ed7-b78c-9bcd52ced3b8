package com.dxx.game.dao.dynamodb.repository;

import com.dxx.game.common.aws.dynamodb.BaseDynamoDBDao;
import com.dxx.game.dao.dynamodb.model.Task;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @date 2020/12/3 18:46
 */
@Repository
public class TaskDao extends BaseDynamoDBDao<Task> {
    public Task getByUserId(long userId) {
        return super.getItem(userId);
    }

    public void updateByDirty(Task data) {
        if (!data.isDirty()) return;
        data.setDirty(false);
        this.update(data);
    }
}
