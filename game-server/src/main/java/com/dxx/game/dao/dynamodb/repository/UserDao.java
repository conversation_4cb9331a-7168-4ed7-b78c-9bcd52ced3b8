package com.dxx.game.dao.dynamodb.repository;

import com.dxx.game.common.aws.dynamodb.BaseDynamoDBDao;
import com.dxx.game.common.aws.dynamodb.utils.DefaultAttributeValue;
import com.dxx.game.consts.RedisKeys;
import com.dxx.game.dao.dynamodb.model.User;
import com.dxx.game.dao.dynamodb.repository.guild.GuildUserDao;
import com.dxx.game.dao.redis.UserInfoRedisDao;
import com.dxx.game.dao.redis.UserRequestRedisDao;
import com.dxx.game.modules.user.model.UserInfoModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Repository;
import software.amazon.awssdk.core.pagination.sync.SdkIterable;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbIndex;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.model.Page;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryConditional;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryEnhancedRequest;
import software.amazon.awssdk.services.dynamodb.model.AttributeValue;
import software.amazon.awssdk.services.dynamodb.model.KeysAndAttributes;

import javax.annotation.PostConstruct;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/10/19 10:44
 */
@Repository
public class UserDao extends BaseDynamoDBDao<User> {

    DynamoDbIndex<User> accountIdIndex;
    DynamoDbIndex<User> nickNameIndex;
    DynamoDbIndex<User> serverIdIndex;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    @Autowired
    private UserExtendDao userExtendDao;
    @Autowired
    private UserInfoRedisDao userInfoRedisDao;
    @Autowired
    private GuildUserDao guildUserDao;
    @Autowired
    private UserRequestRedisDao userRequestRedisDao;

    @PostConstruct
    private void init() {
        accountIdIndex = this.mappedTable.index("accountId-Index");
        nickNameIndex = this.mappedTable.index("nickName-Index");
        serverIdIndex = this.mappedTable.index("serverId-Index");
    }

    public User getByUserId(long userId) {
        return super.getItem(userId);
    }

    public List<User> searchUserByName(String nickName) {
        QueryConditional queryConditional = QueryConditional.keyEqualTo(Key.builder().partitionValue(nickName).build());
        QueryEnhancedRequest enhancedRequest = QueryEnhancedRequest.builder()
                .queryConditional(queryConditional)
                .limit(20) // 设置返回结果最大数
                .build();
        SdkIterable<Page<User>> pages = nickNameIndex.query(enhancedRequest);
        var users = new ArrayList<User>();
        for (Page<User> page : pages) {
            for (User user : page.items()) {
                if (users.size() >= 20) {
                    break;
                }
                users.add(user);
            }
            if (users.size() >= 20) {
                break;
            }
        }
//        QueryConditional queryConditional = QueryConditional.keyEqualTo(Key.builder().partitionValue(nickName).build());
//        List<Page<User>> collect = nickNameIndex.query(queryConditional).stream().collect(Collectors.toList());
//        var users = new ArrayList<User>();
//        collect.forEach(temp -> users.addAll(temp.items()));
//        if (users.isEmpty()) {
//            return null;
//        }
        return users;
    }

    /**
     * 根据账号ID获取用户
     *
     * @param accountId
     * @return
     */
    public User getByAccountId(String accountId, int serverId) {
        QueryConditional queryConditional = QueryConditional.keyEqualTo(Key.builder().partitionValue(accountId).sortValue(serverId).build());
        List<Page<User>> collect = accountIdIndex.query(queryConditional).stream().toList();
        List<User> items = collect.getFirst().items();
        if (items.isEmpty()) {
            return null;
        }
        return items.getFirst();

    }

    public User getByNickName(String nickName, int serverId) {
        QueryConditional queryConditional = QueryConditional.keyEqualTo(Key.builder().partitionValue(nickName).build());
        List<Page<User>> collect = nickNameIndex.query(queryConditional).stream().toList();
        List<User> items = collect.getFirst().items();
        if (items.isEmpty()) {
            return null;
        }
        return items.getFirst();
    }

    public List<User> getByUsersByNickName(String nickName) {
        QueryConditional queryConditional = QueryConditional.keyEqualTo(Key.builder().partitionValue(nickName).build());
        List<Page<User>> collect = nickNameIndex.query(queryConditional).stream().collect(Collectors.toList());
        return collect.get(0).items();
    }

    public List<User> getUsersByAccountId(String accountId) {
        QueryConditional queryConditional = QueryConditional.keyEqualTo(Key.builder().partitionValue(accountId).build());
        List<Page<User>> collect = accountIdIndex.query(queryConditional).stream().toList();
        List<User> items = collect.getFirst().items();
        if (items.isEmpty()) {
            return null;
        }
        return items;
    }

    public List<User> getUsersByServerId(int serverId) {
        QueryConditional queryConditional = QueryConditional.keyEqualTo(Key.builder().partitionValue(serverId).build());
        SdkIterable<Page<User>> query = serverIdIndex.query(queryConditional);
        List<Page<User>> collect = query.stream().toList();
        return collect.getFirst().items();
    }

    public UserInfoModel queryUserInfo(long userId, boolean withChapterId) {
        List<Long> userIds = new ArrayList<>();
        userIds.add(userId);
        Map<Long, UserInfoModel> userMap = queryUserInfo(userIds, withChapterId);
        return userMap.get(userId);
    }

    public Map<Long, UserInfoModel> queryUserInfo(List<Long> userIds, boolean withChapterId) {
        RedisCallback<Map<String, Object>> callback = redisConnection -> {
            for (Long userId : userIds) {
                String key = RedisKeys.USER_INFO + userId;
                redisConnection.hGetAll(key.getBytes(StandardCharsets.UTF_8));
            }
            return null;
        };

        Map<Long, UserInfoModel> result = new HashMap<>();

        List<Map<String, AttributeValue>> dynamodbKeyList = new ArrayList<>();
        var userIdForDb = new ArrayList<Long>();
        List<Object> redisResult = redisTemplate.executePipelined(callback);
        //需要查询的玩家工会id userIds
        List<Long> needSelectUserIds = new ArrayList<>();
        for (int i = 0, len = redisResult.size(); i < len; i++) {
            Long userId = userIds.get(i);
            Map<String, Object> redisData = (Map<String, Object>) redisResult.get(i);
            if (redisData.isEmpty() || redisData.get(UserInfoRedisDao.GUILD_ID) == null) {
                needSelectUserIds.add(userId);
            }
            if (redisData.isEmpty() || redisData.get(UserInfoRedisDao.SERVER_ID) == null) {
                dynamodbKeyList.add(super.getKey(userId));
                userIdForDb.add(userId);
                continue;
            }

            UserInfoModel model = UserInfoModel.valueOf(userId,
                    redisData.getOrDefault(UserInfoRedisDao.NICK_NAME, "").toString(),
                    Integer.parseInt(redisData.getOrDefault(UserInfoRedisDao.AVATAR, 0).toString()),
                    Integer.parseInt(redisData.getOrDefault(UserInfoRedisDao.AVATAR_FRAME, 0).toString()),
                    Integer.parseInt(redisData.getOrDefault(UserInfoRedisDao.LEVEL, 0).toString()),
                    Long.parseLong(redisData.getOrDefault(UserInfoRedisDao.ACTIVE_TM, 0).toString()),
                    Integer.parseInt(redisData.getOrDefault(UserInfoRedisDao.FAKE_LEVEL_ID, 0).toString()),
                    Long.parseLong(redisData.getOrDefault(UserInfoRedisDao.POWER, 0).toString()),
                    Integer.parseInt(redisData.getOrDefault(UserInfoRedisDao.SERVER_ID, 0).toString()),
                    Integer.parseInt(redisData.getOrDefault(UserInfoRedisDao.GUILD_ID, 0).toString())
            );
            result.put(userId, model);
        }

        // redis 没有的数据从dynamodb查询
        if (!dynamodbKeyList.isEmpty()) {
            Map<String, KeysAndAttributes> fields = new HashMap<>();
            fields.put(tableName, KeysAndAttributes.builder().keys(dynamodbKeyList).attributesToGet("userId", "nickName", "avatar", "avatarFrame", "level", "loginTimestamp", "channelId", "power", "serverId").consistentRead(true).build());

            List<Map<String, AttributeValue>> resp = super.batchGetItemsByKeys(dynamodbKeyList, fields);
            for (Map<String, AttributeValue> attributeValueMap : resp) {
                long userId = Long.parseLong(attributeValueMap.get("userId").n());
                String nickName = attributeValueMap.getOrDefault("nickName", DefaultAttributeValue.S_EMPTY).s();
                String avatar = attributeValueMap.getOrDefault("avatar", DefaultAttributeValue.N_ZERO).n();
                String avatarFrame = attributeValueMap.getOrDefault("avatarFrame", DefaultAttributeValue.N_ZERO).n();
                String loginTimestamp = attributeValueMap.getOrDefault("loginTimestamp", DefaultAttributeValue.N_ZERO).n();
                String level = attributeValueMap.getOrDefault("level", DefaultAttributeValue.N_ZERO).n();
                String power = attributeValueMap.getOrDefault("power", DefaultAttributeValue.N_ZERO).n();
                String serverId = attributeValueMap.getOrDefault("serverId", DefaultAttributeValue.N_ZERO).n();
                UserInfoModel model = UserInfoModel.valueOf(userId,
                        nickName,
                        Integer.parseInt(avatar),
                        Integer.parseInt(avatarFrame),
                        Integer.parseInt(level),
                        Long.parseLong(loginTimestamp),
                        1,
                        Long.parseLong(power),
                        Integer.parseInt(serverId),
                        0);
                result.put(userId, model);
            }

            // 查询章节ID
            if (withChapterId) {
                List<Map<String, AttributeValue>> chapterData = userExtendDao.queryMaxFakeLevelIds(userIdForDb);
                for (Map<String, AttributeValue> attributeValueMap : chapterData) {
                    long userId = Long.parseLong(attributeValueMap.get("userId").n());
                    int chapterId = Integer.parseInt(attributeValueMap.getOrDefault("maxId", DefaultAttributeValue.N_ONE).n());
                    if (result.containsKey(userId)) {
                        result.get(userId).setFakeLevelId(chapterId);
                    }
                }
            }

            for (Map.Entry<Long, UserInfoModel> entry : result.entrySet()) {
                userInfoRedisDao.updateInfo(entry.getKey(), entry.getValue());
            }
        }
        //查询玩家工会id  缓存中不存在有两种情况 1：redis 30天过期了 2:未在工会
        if (!needSelectUserIds.isEmpty()) {
            List<Map<String, AttributeValue>> queryGuildIds = guildUserDao.queryGuildId(needSelectUserIds);
            for (Map<String, AttributeValue> attributeValueMap : queryGuildIds) {
                long userId = Long.parseLong(attributeValueMap.get("userId").n());
                int guildId = Integer.parseInt(attributeValueMap.getOrDefault("guildId", DefaultAttributeValue.N_ZERO).n());
                if (guildId > 0 && result.get(userId) != null) {
                    result.get(userId).setGuildId(guildId);
                    userInfoRedisDao.updateGuild(userId, guildId);
                }
            }
        }

        return result;
    }

    public void updateUserDataWhenLogin(User user) {
        this.updateDelta(user, update -> {
            update.setLoginDays(user.getLoginDays());
            update.setLoginTimestamp(user.getLoginTimestamp());
            update.setLastLoginDeviceId(user.getLastLoginDeviceId());
            update.setClientNetVersion(user.getClientNetVersion());
            update.setBuyLifeValueCount(user.getBuyLifeValueCount());
            update.setBuyLifeValueCount(user.getBuyLifeValueCount());
            update.setAccountVerifyMd5(user.getAccountVerifyMd5());
            update.setLife(user.getLife());
            update.setLifeTimeStamp(user.getLifeTimeStamp());
            update.setMaxTransId(user.getMaxTransId());
            update.setTowerTicket(user.getTowerTicket());
            update.setCrossArenaTicket(user.getCrossArenaTicket());
            update.setCrossArenaTicketBuyTimes(user.getCrossArenaTicketBuyTimes());
            update.setPower(user.getPower());
            update.setNextDayRefTime(user.getNextDayRefTime());
            update.setDungeonTicketMap(user.getDungeonTicketMap());
            update.setAppVersion(user.getAppVersion());
            update.setClientVersion(user.getClientVersion());
        });
    }

    public void updateVersion(User user) {
        this.updateDelta(user, update -> {
            update.setAppVersion(user.getAppVersion());
            update.setClientVersion(user.getClientVersion());
        });
    }


    /**
     * 更新体力信息
     *
     * @param user
     */
    public void updateUserLifeValue(User user) {
        this.updateDelta(user, update -> {
            update.setLife(user.getLife());
            update.setLifeTimeStamp(user.getLifeTimeStamp());
            update.setBuyLifeValueCount(user.getBuyLifeValueCount());
        });
    }


    public void updateUserPower(User user, long power) {
        if (power == 0) {
            return;
        }
        this.updateDelta(user, update -> update.setPower(power));
    }

    /**
     * 更新爬塔门票
     */
    public void updateTowerTicket(User user) {
        this.updateDelta(user, update -> {
            update.setTowerTicket(user.getTowerTicket());
        });
    }

    public void updateCrossArenaTicket(User user) {
        this.updateDelta(user, update -> {
            update.setCrossArenaTicket(user.getCrossArenaTicket());
            update.setCrossArenaTicketBuyTimes(user.getCrossArenaTicketBuyTimes());
        });
    }

    /**
     * 更新账号信息
     *
     * @param user
     */
    public void updateAccount(User user) {
        this.updateDelta(user, update -> {
            update.setAccountId(user.getAccountId());
            update.setAccountId2(user.getAccountId2());
        });
    }


    public void updateUserInfo(User user) {
        this.updateDelta(user, update -> {
            update.setNickName(user.getNickName());
            update.setAvatar(user.getAvatar());
            update.setAvatarFrame(user.getAvatarFrame());
            update.setLastNickNameModifyTime(user.getLastNickNameModifyTime());
        });
    }

    public Map<Long, User> getByUserIds(List<Long> userIds) {
        Map<Long, User> result = new HashMap<>();
        List<User> datas = super.batchGetItem(userIds);
        for (User data : datas) {
            result.put(data.getUserId(), data);
        }
        return result;
    }

    /**
     * 更新 msgSeq
     *
     * @param user
     */
    public void updateMaxSeq(User user) {
        this.updateDelta(user, update -> update.setMaxSeq(user.getMaxSeq()));
    }
}
