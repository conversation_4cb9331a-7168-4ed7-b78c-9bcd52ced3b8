package com.dxx.game.dao.dynamodb.repository;

import com.dxx.game.common.aws.dynamodb.BaseDynamoDBDao;
import com.dxx.game.dao.dynamodb.model.UserExtend;
import org.springframework.stereotype.Repository;
import software.amazon.awssdk.services.dynamodb.model.AttributeValue;
import software.amazon.awssdk.services.dynamodb.model.ExecuteStatementRequest;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/11/16 14:19
 */
@Repository
public class UserExtendDao extends BaseDynamoDBDao<UserExtend> {

    public UserExtend getByUserId(long userId) {
        return super.getItem(userId);
    }

    public void updateArtifact(UserExtend userExtend) {
        this.updateDelta(userExtend, update -> update.setArtifactInfo(userExtend.getArtifactInfo()));
    }

    public void updateHeroFormationModel(UserExtend userExtend) {
        this.updateDelta(userExtend, update -> update.setHeroFormationMap(userExtend.getHeroFormationMap()));
    }

    public void updateKingRushTalent(UserExtend userExtend) {
        this.updateDelta(userExtend, update -> update.setKingRushTalentModel(userExtend.getKingRushTalentModel()));
    }

    public void updateIntegralShop(UserExtend userExtend) {
        this.updateDelta(userExtend, update -> update.setIntegralShopModels(userExtend.getIntegralShopModels()));
    }

    public void updateMount(UserExtend userExtend) {
        this.updateDelta(userExtend, update -> update.setMountInfo(userExtend.getMountInfo()));
    }

    public void updateShopDrawData(UserExtend userExtend) {
        this.updateDelta(userExtend, update -> update.setShopDrawInfo(userExtend.getShopDrawInfo()));
    }

    public void updateAvatarModel(UserExtend userExtend) {
        this.updateDelta(userExtend, update -> update.setAvatars(userExtend.getAvatars()));
    }

    public void updateGuildTechLv(UserExtend userExtend) {
        this.updateDelta(userExtend, update -> update.setGuildTechLv(userExtend.getGuildTechLv()));
    }

    // 禁赛数据
    public void updateBannedPlay(UserExtend userExtend) {
        this.updateDelta(userExtend, update -> update.setBannedPlay(userExtend.getBannedPlay()));
    }

    /**
     * 批量查询章节ID
     *
     * @param userIdList
     * @return
     */
    public List<Map<String, AttributeValue>> queryMaxFakeLevelIds(List<Long> userIdList) {
        if (userIdList == null || userIdList.isEmpty()) {
            return List.of();
        }
        var sql = """
                select userId, fakeLevel.maxId from "%s" where userId in [%s]
                """.formatted(tableName, userIdList.stream().map(Object::toString).collect(Collectors.joining(",")));
        var state = ExecuteStatementRequest.builder().statement(sql).consistentRead(true).build();
        return this.dynamoDbClient.executeStatement(state).items();
    }

    public Map<Long, UserExtend> getByUserIds(List<Long> userIds) {
        Map<Long, UserExtend> result = new HashMap<>();
        List<UserExtend> datas = super.batchGetItem(userIds);
        for (UserExtend data : datas) {
            result.put(data.getUserId(), data);
        }
        return result;
    }
}
