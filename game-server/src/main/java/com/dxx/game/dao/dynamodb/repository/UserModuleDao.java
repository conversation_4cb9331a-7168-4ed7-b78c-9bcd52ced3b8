package com.dxx.game.dao.dynamodb.repository;

import com.dxx.game.common.aws.dynamodb.BaseDynamoDBDao;
import com.dxx.game.common.aws.dynamodb.cache.DynamoDBCacheManager;
import com.dxx.game.common.aws.dynamodb.model.DynamoDBBaseModel;
import com.dxx.game.common.aws.dynamodb.transaction.DynamoDBTableInfo;
import com.dxx.game.common.aws.dynamodb.transaction.DynamoDBTransactionAspectSupport;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbTable;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.TableSchema;
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbIgnore;
import software.amazon.awssdk.enhanced.dynamodb.model.DeleteItemEnhancedRequest;
import software.amazon.awssdk.enhanced.dynamodb.model.GetItemEnhancedRequest;
import software.amazon.awssdk.services.dynamodb.model.AttributeValue;
import software.amazon.awssdk.services.dynamodb.model.QueryRequest;
import software.amazon.awssdk.services.dynamodb.model.QueryResponse;
import software.amazon.awssdk.services.dynamodb.model.ReturnConsumedCapacity;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2024/6/25 16:58
 */
@Slf4j
public abstract class UserModuleDao<T extends DynamoDBBaseModel> extends BaseDynamoDBDao<T> {

    private final String sortKey;
    private static final Map<String, Class<?>> moduleClassMap = new ConcurrentHashMap<>();

    public UserModuleDao() {
        Class<?> aClass = resolveGenericTypeName(this.getClass());
        sortKey = aClass.getSimpleName();
        moduleClassMap.putIfAbsent(sortKey, aClass);
        log.info("init user module class " + sortKey);
    }

    public Map<String, DynamoDBBaseModel> getAllModules(long userId) {
        Map<String, DynamoDBBaseModel> result = new HashMap<>();
        List<Map<String, AttributeValue>> items = getAllModuleAttributes(userId, this.tableName);
        for (Map<String, AttributeValue> item : items) {
            AttributeValue module = item.get("module");
            String moduleType = module.s();
            Class<?> aClass = moduleClassMap.get(moduleType);
            DynamoDBTableInfo<T> tableInfo = (DynamoDBTableInfo<T>) DynamoDBTransactionAspectSupport.getTableInfoMap().get(aClass.getName());
            T t = tableInfo.getTableSchema().mapToItem(item);

            result.put(moduleType, t);
            //放入缓存
            DynamoDBCacheManager.put(t);
        }
        return result;
    }

    public List<Map<String, AttributeValue>> getAllModuleAttributes(long userId, String tableName) {
        HashMap<String, AttributeValue> attributeValues = new HashMap<>();
        attributeValues.put(":userId", AttributeValue.builder().n(String.valueOf(userId)).build());

        QueryRequest queryReq = QueryRequest.builder()
                .tableName(tableName)
                .returnConsumedCapacity(ReturnConsumedCapacity.INDEXES)
                .keyConditionExpression("userId =:userId")
                .expressionAttributeValues(attributeValues)
                .consistentRead(true)
                .build();
        QueryResponse queryResponse = dynamoDbClient.query(queryReq);
        return queryResponse.items();
    }

    @DynamoDbIgnore
    private static Class<?> resolveGenericTypeName(Class<?> clazz) {
        Type superClass = clazz.getGenericSuperclass();
        // 获取调用者的泛型类型信息
        if (superClass instanceof ParameterizedType) {
            Type[] typeArguments = ((ParameterizedType) superClass).getActualTypeArguments();
            if (typeArguments.length > 0) {
                if (typeArguments[0] instanceof Class) {
                    return ((Class<?>) typeArguments[0]);
                }
            }
        }
        throw new RuntimeException("未找到泛型类型参数");
    }

    @Override
    public <E> List<T> batchGetItem(List<E> partitionKeys) {
        List<List<Object>> keys = new ArrayList<>();
        String sortKey = sortKey();
        for (E partitionKey : partitionKeys) {
            keys.add(Lists.newArrayList(partitionKey, sortKey));
        }
        return batchGetItemWithSortKeys(keys);
    }

    public Map<String, DynamoDBBaseModel> getProdAllModules(long userId) {
        Map<String, DynamoDBBaseModel> result = new HashMap<>();
        String tableName = gameConfigManager.getProdDynamoDBTableName(this.tableName);
        List<Map<String, AttributeValue>> items = getAllModuleAttributes(userId, tableName);
        for (Map<String, AttributeValue> item : items) {
            AttributeValue module = item.get("module");
            String moduleType = module.s();
            Class<?> aClass = moduleClassMap.get(moduleType);
            DynamoDBTableInfo<T> tableInfo = (DynamoDBTableInfo<T>) DynamoDBTransactionAspectSupport.getTableInfoMap().get(aClass.getName());
            T t = tableInfo.getTableSchema().mapToItem(item);

            result.put(moduleType, t);
            //放入缓存
            DynamoDBCacheManager.put(t);
        }
        return result;
    }


    public T queryProdData(long userId) {
        String tableName = gameConfigManager.getProdDynamoDBTableName(this.tableName);
        DynamoDbTable<T> propMappedTable = this.dynamoDbEnhancedClient.table(tableName, TableSchema.fromBean(tClass));
        String sortKey = sortKey();
//        log.info("queryProdData tableName:{}, userId:{}, sortKey:{}", tableName, userId, sortKey);
        Key key = this.buildKey(userId, sortKey);
        GetItemEnhancedRequest getItemEnhancedRequest = GetItemEnhancedRequest.builder().key(key).consistentRead(true).build();
        return propMappedTable.getItem(getItemEnhancedRequest);
    }


    public void deleteNow(long userId, String moduleType) {
        var key = buildKey(userId, moduleType);
        DeleteItemEnhancedRequest request = DeleteItemEnhancedRequest.builder()
                .returnConsumedCapacity(ReturnConsumedCapacity.INDEXES)
                .key(key)
                .build();
        var r = this.mappedTable.deleteItemWithResponse(request);
    }

    @Override
    public T getItem(Object partitionKey) {
        return super.getItem(partitionKey, sortKey());
    }

    public T getByUserId(long userId) {
        return super.getItem(userId, sortKey());
    }

    public String sortKey() {
        return sortKey;
    }
}
