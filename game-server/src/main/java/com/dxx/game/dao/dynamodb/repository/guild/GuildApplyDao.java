package com.dxx.game.dao.dynamodb.repository.guild;

import com.dxx.game.common.aws.dynamodb.BaseDynamoDBDao;
import com.dxx.game.dao.dynamodb.model.guild.GuildApply;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Repository;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbIndex;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.model.Page;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryConditional;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @authoer: lsc
 * @createDate: 2023/3/24
 * @description:
 */
@Repository
public class GuildApplyDao extends BaseDynamoDBDao<GuildApply> {

    DynamoDbIndex<GuildApply> guildIdIndex;

    @PostConstruct
    private void init() {
        guildIdIndex = this.mappedTable.index("guildId-index");
    }

    private String getPK(long guildId) {
        return GuildTableKeyPrefix.Apply + GuildTableKeyPrefix.Guild + guildId;
    }
    private String getSK(long userId) {
        return GuildTableKeyPrefix.Apply + GuildTableKeyPrefix.User + userId;
    }

    // 添加主键
    public void setPrimaryKey(GuildApply guildApply) {
        guildApply.setPK(this.getPK(guildApply.getGuildId()));
        guildApply.setSK(this.getSK(guildApply.getUserId()));
        guildApply.setSK2(GuildTableKeyPrefix.Apply);
    }

    // 根据用户id查询申请数据
    public GuildApply getGuildApply(long guildId, long userId) {
        return super.getItem(this.getPK(guildId), this.getSK(userId));
    }

    // 根据公会id查询所有申请数据
    public List<GuildApply> getGuildApplyListByGuildId(long guildId) {
        QueryConditional queryConditional
                = QueryConditional.keyEqualTo(Key.builder().partitionValue(guildId).sortValue(GuildTableKeyPrefix.Apply).build());
        List<Page<GuildApply>> collect = guildIdIndex.query(queryConditional).stream().collect(Collectors.toList());
        return collect.get(0).items();
    }

    // 根据用户ID列表查询所有申请数据
    public List<GuildApply> getListByUserIds(long guildId, List<Long> userIds) {
        List<List<Object>> keys = new ArrayList<>(userIds.size());
        for (Long userId : userIds) {
            keys.add(Lists.newArrayList(this.getPK(guildId), this.getSK(userId)));
        }
        return super.batchGetItemWithSortKeys(keys);
    }

    // 批量删除
    public void batchDelete(List<Long> guildIds, long userId) {
        for (Long guildId : guildIds) {
            GuildApply apply = new GuildApply();
            apply.setPK(this.getPK(guildId));
            apply.setSK(this.getSK(userId));
            super.delete(apply);
        }
    }

    // 插入数据
    public void insert(GuildApply guildApply) {
        super.insert(guildApply);
    }

    // 删除数据
    public void delete(GuildApply guildApply) {
        super.delete(guildApply);
    }
}
