package com.dxx.game.dao.dynamodb.repository.guild;

import com.dxx.game.common.aws.dynamodb.BaseDynamoDBDao;
import com.dxx.game.common.aws.dynamodb.cache.DynamoDBCacheManager;
import com.dxx.game.common.aws.dynamodb.utils.DynamoDBConvertUtil;
import com.dxx.game.consts.RedisKeys;
import com.dxx.game.dao.dynamodb.model.guild.Guild;
import com.dxx.game.dao.dynamodb.repository.guild.opensearch.GuildOpenSearchDao;
import com.google.common.collect.Lists;
import com.google.common.primitives.Longs;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Repository;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbIndex;
import software.amazon.awssdk.enhanced.dynamodb.Expression;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.model.Page;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryConditional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @authoer: lsc
 * @createDate: 2023/3/24
 * @description:
 */
@Repository
public class GuildDao extends BaseDynamoDBDao<Guild> {

    DynamoDbIndex<Guild> guildNameIndex;
    DynamoDbIndex<Guild> guildIdIndex;

    @Resource
    private GuildOpenSearchDao guildOpenSearchDao;
    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @PostConstruct
    private void init() {
        guildNameIndex = this.mappedTable.index("guildName-index2");
        guildIdIndex = this.mappedTable.index("guildId-index");

        // 清空OpenSearch数据用
//        guildOpenSearchDao.deleteIndex();
//        guildOpenSearchDao.createIndex();
    }

    private String getPK(long guildId) {
        return GuildTableKeyPrefix.Guild + guildId;
    }

    private String getSK() {
        return GuildTableKeyPrefix.GuildMetaData;
    }

    // 添加主键
    public void setPrimaryKey(Guild guild) {
        guild.setPK(this.getPK(guild.getGuildId()));
        guild.setSK(this.getSK());
        guild.setSK2(GuildTableKeyPrefix.GuildMetaData);
    }

    public void insert(Guild guild) {
        super.insert(guild);
        guildOpenSearchDao.addDocument(guild);
    }

    public void update(Guild guild) {
        super.update(guild);
        guildOpenSearchDao.addDocument(guild);
    }

    public void delete(Guild guild) {
        super.delete(guild);
        guildOpenSearchDao.deleteDocument(guild);
    }

    // 根据公会ID查询数据
    public Guild getByGuildId(long guildId) {
        return super.getItem(this.getPK(guildId), this.getSK());
    }

    public List<Guild> getListByGuildIds(Collection<Long> guildIds) {
        List<List<Object>> keys = new ArrayList<>(guildIds.size());
        for (Long guildId : guildIds) {
            keys.add(Lists.newArrayList(this.getPK(guildId), this.getSK()));
        }
        return super.batchGetItemWithSortKeys(keys);
    }

    //TODO 可以优化为只取部分数据
    public Map<Long, Guild> getMapByGuildIds(Collection<Long> guildIds) {
        if (guildIds == null || guildIds.isEmpty()) {
            return Collections.emptyMap();
        }
        Map<Long, Guild> result = new HashMap<>();
        List<Guild> guilds = this.getListByGuildIds(guildIds);
        for (Guild guild : guilds) {
            result.put(guild.getGuildId(), guild);
        }
        return result;
    }

    // 根据公会名称查询数据
    public Guild getByName(String name, int serverMin, int serMax) {
        QueryConditional queryConditional
                = QueryConditional.keyEqualTo(Key.builder().partitionValue(name).build());
        List<Page<Guild>> collect = guildNameIndex.query(queryConditional).stream().collect(Collectors.toList());
        List<Guild> items = collect.get(0).items();
        if (items.isEmpty()) {
            return null;
        }
        items = items.stream()
                .filter(temp -> temp.getServerId() >= serverMin && temp.getServerId() <= serMax)
                .collect(Collectors.toList());
        if (items.isEmpty()) {
            return null;
        }
        return items.get(0);
    }

    // 根据公会名称查询数据
    public List<Guild> searchByName(String name, List<Long> excludeGuildIds, boolean isOnlyJoinable, int condition, List<List<Integer>> serRange, int excludeServerId) {
        return guildOpenSearchDao.searchByName(name, excludeGuildIds, isOnlyJoinable, condition, serRange, excludeServerId);
    }

    // 获取公会排行榜
    public List<Guild> getRankList(int from, int limit, List<List<Integer>> serRange, int excludeServerId) {
        return guildOpenSearchDao.getRankList(from, limit, serRange, excludeServerId);
    }

    // 获得满足条件可以自动加入的公会
    public List<Guild> getAutoJoinGuilds(int condition, List<List<Integer>> serRange, int excludeServerId, long openServerTimeLimit, int lastDayMinActive) {
        // 优先查询 今天有成员上线的公会
        List<Guild> guilds = guildOpenSearchDao.getAutoJoinGuilds(true, condition, serRange, excludeServerId, openServerTimeLimit, lastDayMinActive);
        if (guilds.isEmpty()) {
            // 随机查询
            guilds = guildOpenSearchDao.getAutoJoinGuilds(false, condition, serRange, excludeServerId, 0, 0);
        }
        return guilds;
    }

    // 更新公会信息
    public void updateGuildInfo(Guild guild) {
        Guild update = DynamoDBCacheManager.getUpdateObj(guild);
        update.setGuildName(guild.getGuildName());
        update.setGuildIntro(guild.getGuildIntro());
        update.setGuildIcon(guild.getGuildIcon());
        update.setGuildIconBg(guild.getGuildIconBg());
        update.setGuildApplyType(guild.getGuildApplyType());
        update.setGuildApplyCondition(guild.getGuildApplyCondition());
        update.setGuildNotice(guild.getGuildNotice());
        update.setGuildLanguage(guild.getGuildLanguage());
        super.updateIgnoreNulls(update);
        guildOpenSearchDao.addDocument(guild);
    }

    // 更新公会昵称
    public void updateGuildName(Guild guild) {
        Guild update = DynamoDBCacheManager.getUpdateObj(guild);
        update.setGuildName(guild.getGuildName());
        super.updateIgnoreNulls(update);
        guildOpenSearchDao.addDocument(guild);
    }

    // 更新公会成员数量
    public void updateGuildMembersCount(Guild guild, int count) {
        Expression updateExpression = Expression.builder().expression("#guildMembersCount = #guildMembersCount + :changeMembersCount")
                .putExpressionName("#guildMembersCount", "guildMembersCount")
                .putExpressionValue(":changeMembersCount", DynamoDBConvertUtil.buildAttributeValue(count))
                .build();
        guild.addUpdateExpression(updateExpression);
        Expression updateCondition;
        if (count < 0) {
            updateCondition = Expression.builder().expression("#guildMembersCount > :value")
                    .putExpressionName("#guildMembersCount", "guildMembersCount")
                    .putExpressionValue(":value", DynamoDBConvertUtil.buildAttributeValue(0))
                    .build();
        } else {
            updateCondition = Expression.builder().expression("#guildMembersCount < #guildMaxMembersCount")
                    .putExpressionName("#guildMembersCount", "guildMembersCount")
                    .putExpressionName("#guildMaxMembersCount", "guildMaxMembersCount")
                    .build();
        }
        guild.addUpdateCondition(updateCondition);

        guild.setGuildMembersCount(guild.getGuildMembersCount() + count);
        this.updateGuildMembersCount(guild);
    }

    public void updateGuildMembersCount(Guild guild) {
        Guild update = DynamoDBCacheManager.getUpdateObj(guild);
        update.setGuildMembersCount(guild.getGuildMembersCount());
        super.updateIgnoreNulls(update);
        guildOpenSearchDao.addDocument(guild);
    }

    // 更新副会长数量
    public void updateGuildVicePresidentCount(Guild guild, int count, int maxCount) {
        Expression updateExpression = Expression.builder().expression("#guildVicePresidentCount = #guildVicePresidentCount + :changeVicePresidentCount")
                .putExpressionName("#guildVicePresidentCount", "guildVicePresidentCount")
                .putExpressionValue(":changeVicePresidentCount", DynamoDBConvertUtil.buildAttributeValue(count))
                .build();
        guild.addUpdateExpression(updateExpression);
        if (count < 0) {
            // 更新条件副会长人数不能小于0
            Expression updateCondition = Expression.builder().expression("#guildVicePresidentCount > :value")
                    .putExpressionName("#guildVicePresidentCount", "guildVicePresidentCount")
                    .putExpressionValue(":value", DynamoDBConvertUtil.buildAttributeValue(0))
                    .build();
            guild.addUpdateCondition(updateCondition);
        }
        if (maxCount > 0) {
            // 更新条件 副会长人数不能超过最大人数
            Expression updateCondition = Expression.builder().expression("#guildVicePresidentCount < :maxVicePresidentCount")
                    .putExpressionName("#guildVicePresidentCount", "guildVicePresidentCount")
                    .putExpressionValue(":maxVicePresidentCount", DynamoDBConvertUtil.buildAttributeValue(maxCount))
                    .build();
            guild.addUpdateCondition(updateCondition);
        }

        guild.setGuildVicePresidentCount(guild.getGuildVicePresidentCount() + count);
        this.updateGuildVicePresidentCount(guild);
    }

    public void updateGuildVicePresidentCount(Guild guild) {
        Guild update = DynamoDBCacheManager.getUpdateObj(guild);
        update.setGuildVicePresidentCount(guild.getGuildVicePresidentCount());
        super.updateIgnoreNulls(update);
        guildOpenSearchDao.addDocument(guild);
    }

    // 更新管理员数量
    public void updateGuildManagerCount(Guild guild, int count, int maxCount) {
        Expression updateExpression = Expression.builder().expression("#guildManagerCount = #guildManagerCount + :changeManagerCount")
                .putExpressionName("#guildManagerCount", "guildManagerCount")
                .putExpressionValue(":changeManagerCount", DynamoDBConvertUtil.buildAttributeValue(count))
                .build();
        guild.addUpdateExpression(updateExpression);
        if (count < 0) {
            // 更新条件管理人数不能小于0
            Expression updateCondition = Expression.builder().expression("#guildManagerCount > :guildManagerCount")
                    .putExpressionName("#guildManagerCount", "guildManagerCount")
                    .putExpressionValue(":guildManagerCount", DynamoDBConvertUtil.buildAttributeValue(0))
                    .build();
            guild.addUpdateCondition(updateCondition);
        }
        if (maxCount > 0) {
            // 更新条件 管理人数不能超过最大人数
            Expression updateCondition = Expression.builder().expression("#guildManagerCount < :maxManagerCount")
                    .putExpressionName("#guildManagerCount", "guildManagerCount")
                    .putExpressionValue(":maxManagerCount", DynamoDBConvertUtil.buildAttributeValue(maxCount))
                    .build();
            guild.addUpdateCondition(updateCondition);
        }

        guild.setGuildManagerCount(guild.getGuildManagerCount() + count);
        this.updateGuildManagerCount(guild);
    }

    public void updateGuildManagerCount(Guild guild) {
        Guild update = DynamoDBCacheManager.getUpdateObj(guild);
        update.setGuildManagerCount(guild.getGuildManagerCount());
        super.updateIgnoreNulls(update);
        guildOpenSearchDao.addDocument(guild);
    }


    // 更新公会申请数量
    public void updateGuildApplyJoinedCount(Guild guild) {
        Guild update = DynamoDBCacheManager.getUpdateObj(guild);
        update.setGuildApplyJoinedCount(guild.getGuildApplyJoinedCount());
        super.updateIgnoreNulls(update);
        guildOpenSearchDao.addDocument(guild);
    }

    // 更新解散公会标记
    public void updateIsDissolved(Guild guild) {
        Guild update = DynamoDBCacheManager.getUpdateObj(guild);
        update.setGuildIsDissolved(guild.getGuildIsDissolved());
        super.updateIgnoreNulls(update);
        guildOpenSearchDao.addDocument(guild);
    }


    // 更新公会每日活跃度
    public void updateGuildDayInfo(Guild guild) {
        Guild update = DynamoDBCacheManager.getUpdateObj(guild);
        update.setGuildDayActive(guild.getGuildDayActive());
        update.setGuildDayActiveTime(guild.getGuildDayActiveTime());
        update.setAllContributeTimes(guild.getAllContributeTimes());
        update.setLastDayActive(guild.getLastDayActive());
        super.updateIgnoreNulls(update);
        guildOpenSearchDao.addDocument(guild);
    }

    //更新工会每周活跃度
    public void updateGuildWeekActive(Guild guild) {
        Guild update = DynamoDBCacheManager.getUpdateObj(guild);
        update.setGuildWeekActive(guild.getGuildWeekActive());
        update.setGuildWeekActiveTime(guild.getGuildWeekActiveTime());
        super.updateIgnoreNulls(update);
    }


    // 更新公会会长ID
    public void updateGuildPresidentUserId(Guild guild) {
        Guild update = DynamoDBCacheManager.getUpdateObj(guild);
        update.setGuildPresidentUserId(guild.getGuildPresidentUserId());
        super.updateIgnoreNulls(update);
        guildOpenSearchDao.addDocument(guild);
    }

    // 更新活跃度
    public void updateGuildActive(Guild guild) {
        Guild update = DynamoDBCacheManager.getUpdateObj(guild);
        update.setGuildActive(guild.getGuildActive());
        super.updateIgnoreNulls(update);
        guildOpenSearchDao.addDocument(guild);
    }

    // 更新公会经验
    public void updateGuildExp(Guild guild) {
        Guild update = DynamoDBCacheManager.getUpdateObj(guild);
        update.setGuildExp(guild.getGuildExp());
        super.updateIgnoreNulls(update);
        guildOpenSearchDao.addDocument(guild);
    }

    // 更新公会等级
    public void updateGuildLevel(Guild guild) {
        Guild update = DynamoDBCacheManager.getUpdateObj(guild);
        update.setGuildLevel(guild.getGuildLevel());
        super.updateIgnoreNulls(update);
        guildOpenSearchDao.addDocument(guild);
    }

    // 更新公会成员最大人数
    public void updateGuildMaxMembersCount(Guild guild) {
        Guild update = DynamoDBCacheManager.getUpdateObj(guild);
        update.setGuildMaxMembersCount(guild.getGuildMaxMembersCount());
        super.updateIgnoreNulls(update);
        guildOpenSearchDao.addDocument(guild);
    }

    public void updateOpenServerTime(Guild guild) {
        Guild update = DynamoDBCacheManager.getUpdateObj(guild);
        update.setOpenServerTime(guild.getOpenServerTime());
        super.updateIgnoreNulls(update);
        guildOpenSearchDao.addDocument(guild);
    }

    /***更新战力*/
    public void updatePowerInfo(long guildId, long userId, long power) {
        redisTemplate.opsForHash().put(RedisKeys.GUILD_POWER_KEY + guildId, String.valueOf(userId), String.valueOf(power));
    }

    /***移除成员**/
    public boolean removePowerInfo(long guildId, long userId) {
        return redisTemplate.opsForHash().delete(RedisKeys.GUILD_POWER_KEY + guildId, String.valueOf(userId)) > 0;
    }

    /***删除联盟**/
    public boolean deletePowerInfo(long guildId) {
        return redisTemplate.delete(RedisKeys.GUILD_POWER_KEY + guildId);
    }

    /***获取总战力*/
    public long getTotalPower(long guildId) {
        return redisTemplate.opsForHash().values(RedisKeys.GUILD_POWER_KEY + guildId).stream().mapToLong(temp -> Longs.tryParse(String.valueOf(temp))).sum();
    }

    // 更新公会语言
    public void updateGuildLanguage(Guild guild) {
        Guild update = DynamoDBCacheManager.getUpdateObj(guild);
        update.setGuildLanguage(guild.getGuildLanguage());
        super.updateIgnoreNulls(update);
        guildOpenSearchDao.addDocument(guild);
    }
}
