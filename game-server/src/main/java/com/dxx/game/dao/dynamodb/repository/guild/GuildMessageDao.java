package com.dxx.game.dao.dynamodb.repository.guild;

import com.alibaba.fastjson.JSONObject;
import com.dxx.game.common.aws.dynamodb.BaseDynamoDBDao;
import com.dxx.game.common.aws.dynamodb.cache.DynamoDBCacheManager;
import com.dxx.game.common.aws.dynamodb.utils.DynamoDBConvertUtil;
import com.dxx.game.common.utils.DateUtils;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.dao.dynamodb.model.guild.GuildMessage;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbIndex;
import software.amazon.awssdk.enhanced.dynamodb.Expression;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.model.Page;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryConditional;
import software.amazon.awssdk.services.dynamodb.model.AttributeValue;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @authoer: lsc
 * @createDate: 2023/4/17
 * @description:
 */
@Repository
public class GuildMessageDao extends BaseDynamoDBDao<GuildMessage> {


    @Resource
    private GameConfigManager gameConfigManager;

    // 公会消息
    private static final int TYPE_GUILD_MESSAGE = 1;
    // 公会聊天记录
    private static final int TYPE_GUILD_CHAT = 2;
    // 公会挑战记录
    private static final int TYPE_GUILD_BOSS_CHALLENGE_RECORD = 3;
    // 捐献记录
    private static final int TYPE_GUILD_SIGN_IN_RECORD = 4;
    // 捐赠-请求道具
    private static final int TYPE_GUILD_DONATION = 5;
    // 捐赠-赠送记录
    private static final int TYPE_GUILD_DONATION_SEND_ITEM = 6;
    // 捐赠-受赠记录
    private static final int TYPE_GUILD_DONATION_RECEIVE_ITEM = 7;
    // 公会红包
    private static final int TYPE_GUILD_RED_ENVELOPE = 8;
    // 领取公会红包记录
    private static final int TYPE_GUILD_RED_ENVELOPE_OPEN_RECORDS = 9;
    // 聊天记录
    private static final int TYPE_COMMON_CHAT = 10;

    DynamoDbIndex<GuildMessage> guildIdIndex;
    DynamoDbIndex<GuildMessage> guildMessageIndex;

    @PostConstruct
    private void init() {
        guildIdIndex = this.mappedTable.index("guildId-index");
        guildMessageIndex = this.mappedTable.index("guildMessage-Index");
    }

    // 根据主键+排序键获取数据
    public GuildMessage getGuildMessageByKey(long guildId, long msgId) {
        String PK = this.getPk(guildId);
        return super.getItem(PK, String.valueOf(msgId));
    }

    public List<GuildMessage> queryMessage(long guildId, long msgId, int limit) {
        String PK = this.getPk(guildId);
        QueryCondition queryCondition = QueryCondition.buildQueryMessage(PK, msgId, limit, TYPE_GUILD_MESSAGE, TYPE_GUILD_CHAT, TYPE_GUILD_DONATION, TYPE_GUILD_RED_ENVELOPE);
        return this.queryMessageFromDdbByIndex(queryCondition);
//
//        return guildMessageOpenSearchDao.queryMessage(PK, limit, msgId, TYPE_GUILD_MESSAGE, TYPE_GUILD_CHAT, TYPE_GUILD_DONATION, TYPE_GUILD_RED_ENVELOPE);
    }

    public List<GuildMessage> queryChatMessage(String channelId, long msgId, int limit) {
        String PK = this.getChatPk(channelId);
        QueryCondition queryCondition = QueryCondition.buildQueryMessage(PK, msgId, limit);
        return this.queryMessageFromDdbByIndex(queryCondition);
//
//        return guildMessageOpenSearchDao.queryMessage(PK, limit, msgId, TYPE_GUILD_MESSAGE, TYPE_GUILD_CHAT, TYPE_GUILD_DONATION, TYPE_GUILD_RED_ENVELOPE);
    }

    public List<GuildMessage> queryChallengeRecords(long guildId, long guildBossTime) {
        String PK = this.getPk(guildId);

        QueryCondition queryCondition = QueryCondition.buildQueryChallengeRecords(PK, 20, guildBossTime, TYPE_GUILD_BOSS_CHALLENGE_RECORD);
        return this.queryMessageFromDdbByIndex(queryCondition);
//        return guildMessageOpenSearchDao.queryBossChallengeRecords(PK, guildBossTime,20, TYPE_GUILD_BOSS_CHALLENGE_RECORD);
    }

    // 捐献(签到)记录
    public List<GuildMessage> querySignInRecords(long guildId, int limit) {
        String PK = this.getPk(guildId);
        QueryCondition queryCondition = QueryCondition.buildQueryMessage(PK, 0, limit, TYPE_GUILD_SIGN_IN_RECORD);
        return this.queryMessageFromDdbByIndex(queryCondition);
//        return guildMessageOpenSearchDao.querySignInRecords(PK, limit, TYPE_GUILD_SIGN_IN_RECORD);
    }

    // 捐赠-请求道具
    public List<GuildMessage> queryDonationsReqItems(long guildId, long msgId, int limit) {
        String PK = this.getPk(guildId);
        QueryCondition queryCondition = QueryCondition.buildQueryMessage(PK, msgId, limit, TYPE_GUILD_DONATION);
        return this.queryMessageFromDdbByIndex(queryCondition);
//        return guildMessageOpenSearchDao.queryDonationReqItems(PK, limit, msgId, TYPE_GUILD_DONATION);
    }

    public List<GuildMessage> getByMsgIds(long guildId, List<Long> msgIds) {
        if (msgIds == null || msgIds.isEmpty()) {
            return null;
        }
        List<String> sks = new ArrayList<>();
        for (Long msgId : msgIds) {
            sks.add(String.valueOf(msgId));
        }
        return super.batchGetItem(this.getPk(guildId), sks);
    }

    public List<GuildMessage> getSelfDonationReqItems(long guildId, long userId) {
        QueryConditional queryConditional
                = QueryConditional.keyEqualTo(Key.builder().partitionValue(guildId).sortValue(this.getSK2(userId, TYPE_GUILD_DONATION)).build());
        List<Page<GuildMessage>> collect = guildIdIndex.query(queryConditional).stream().collect(Collectors.toList());
        return collect.get(0).items();
    }

    public List<GuildMessage> queryDonationOperations(long guildId, long userId, long msgId, int limit) {
        String PK = this.getPk(guildId);
        QueryCondition queryCondition = QueryCondition.buildQueryDonationOperations(PK, limit, userId, TYPE_GUILD_DONATION_SEND_ITEM, TYPE_GUILD_DONATION_RECEIVE_ITEM);
        return this.queryMessageFromDdbByIndex(queryCondition);
//        return guildMessageOpenSearchDao.queryDonationByUserId(PK, msgId, limit, userId, TYPE_GUILD_DONATION_SEND_ITEM, TYPE_GUILD_DONATION_RECEIVE_ITEM);
    }

    // 查询红包数据
    public List<GuildMessage> queryRedEnvelope(long guildId, int limit, long msgId) {
        String PK = this.getPk(guildId);
        QueryCondition queryCondition = QueryCondition.buildQueryMessage(PK, msgId, limit, TYPE_GUILD_RED_ENVELOPE);
        return this.queryMessageFromDdbByIndex(queryCondition);
//        return guildMessageOpenSearchDao.queryRedEnvelope(PK, limit, msgId, TYPE_GUILD_RED_ENVELOPE);
    }

    public List<GuildMessage> getMessageListByGuildId(long guildId) {
        return super.getAll(this.getPk(guildId));
    }

    private String getPk(long guildId) {
        return GuildTableKeyPrefix.GuildMessage + guildId;
    }

    private String getChatPk(String channelId) {
        return GuildTableKeyPrefix.ChatMessage + channelId;
    }

    private String getSK2(long userId, int type) {
        return GuildTableKeyPrefix.UserMessageData + userId + "#" + type;
    }

    // 保存公会消息记录
    public void addMessageRecord(long guildId, long msgId, int messageType, String messageContent) {
        GuildMessage guildMessage = new GuildMessage();
        guildMessage.setPK(this.getPk(guildId));
        guildMessage.setSK(String.valueOf(msgId));
        guildMessage.setMsgId(msgId);
        guildMessage.setType(TYPE_GUILD_MESSAGE);
        guildMessage.setMessageType(messageType);
        guildMessage.setMessageContent(messageContent);
        guildMessage.setCreateTime(DateUtils.getUnixTime());
        guildMessage.setTtlTime(this.getTTLTime());
        super.insert(guildMessage);
//        guildMessageOpenSearchDao.addDocument(guildMessage);
    }

    // 保存聊天记录
    public void addChatRecord(long guildId, long msgId, int messageType, String messageContent) {
        GuildMessage guildMessage = new GuildMessage();
        guildMessage.setPK(this.getPk(guildId));
        guildMessage.setSK(String.valueOf(msgId));
        guildMessage.setMsgId(msgId);
        guildMessage.setType(TYPE_GUILD_CHAT);
        guildMessage.setMessageType(messageType);
        guildMessage.setMessageContent(messageContent);
        guildMessage.setCreateTime(DateUtils.getUnixTime());
        guildMessage.setTtlTime(this.getTTLTime());
        super.insert(guildMessage);
//        guildMessageOpenSearchDao.addDocument(guildMessage);
    }

    // 保存聊天记录
    public void addCommonChatRecord(String channel, long msgId, int messageType, String messageContent) {
        GuildMessage message = new GuildMessage();
        message.setPK(this.getChatPk(channel));
        message.setSK(String.valueOf(msgId));
        message.setMsgId(msgId);
        message.setType(TYPE_COMMON_CHAT);
        message.setMessageType(messageType);
        message.setMessageContent(messageContent);
        message.setCreateTime(DateUtils.getUnixTime());
        message.setTtlTime(this.getTTLTime());
        super.insert(message);
    }

    // 保存boss挑战记录
    public void addBossChallengeRecord(long guildId, long guildBossTime, long msgId, String messageContent) {
        GuildMessage guildMessage = new GuildMessage();
        guildMessage.setPK(this.getPk(guildId));
        guildMessage.setSK(String.valueOf(msgId));
        guildMessage.setMsgId(msgId);
        guildMessage.setType(TYPE_GUILD_BOSS_CHALLENGE_RECORD);
        guildMessage.setMessageContent(messageContent);
        guildMessage.setGuildBossTime(guildBossTime);
        guildMessage.setCreateTime(DateUtils.getUnixTime());
        guildMessage.setTtlTime(this.getTTLTime());
        super.insert(guildMessage);
//        guildMessageOpenSearchDao.addDocument(guildMessage);
    }

    // 保存签到记录
    public void addGuildSignInRecord(long guildId, long msgId, String messageContent) {
        GuildMessage guildMessage = new GuildMessage();
        guildMessage.setPK(this.getPk(guildId));
        guildMessage.setSK(String.valueOf(msgId));
        guildMessage.setMsgId(msgId);
        guildMessage.setType(TYPE_GUILD_SIGN_IN_RECORD);
        guildMessage.setMessageContent(messageContent);
        guildMessage.setCreateTime(DateUtils.getUnixTime());
        guildMessage.setTtlTime(this.getTTLTime());
        super.insert(guildMessage);
//        guildMessageOpenSearchDao.addDocument(guildMessage);
    }

    // 捐赠-请求道具
    public void addDonation(long guildId, long userId, long msgId, int messageType, GuildMessage.RequestItemModel requestItemModel) {
        GuildMessage guildMessage = new GuildMessage();
        guildMessage.setPK(this.getPk(guildId));
        guildMessage.setSK(String.valueOf(msgId));
        guildMessage.setUserId(userId);
        guildMessage.setMsgId(msgId);
        guildMessage.setType(TYPE_GUILD_DONATION);
        guildMessage.setMessageType(messageType);
        guildMessage.setRequestItem(requestItemModel);
        guildMessage.setCreateTime(DateUtils.getUnixTime());
        guildMessage.setTtlTime(DateUtils.getUnixTime() + DateUtils.DAY_30_SECONDS);
        guildMessage.setGuildId(guildId);
        guildMessage.setSK2(this.getSK2(userId, TYPE_GUILD_DONATION));
        super.insert(guildMessage);
//        guildMessageOpenSearchDao.addDocument(guildMessage);
    }

    // 捐赠-赠送/受赠道具记录
    public void addDonationSendRecord(long guildId, long msgId, long msgId2, long userId, String nickName, long toUserId, String toUserNickName, int itemId, int itemCount) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("toUserId", toUserId);
        jsonObject.put("toUserNickName", toUserNickName);
        jsonObject.put("itemId", itemId);
        jsonObject.put("itemCount", itemCount);
        jsonObject.put("timestamp", DateUtils.getUnixTime());
        jsonObject.put("msgId", msgId);
        jsonObject.put("type", 1);      // 我赠送给别人

        GuildMessage guildMessage = new GuildMessage();
        guildMessage.setPK(this.getPk(guildId));
        guildMessage.setSK(String.valueOf(msgId));
        guildMessage.setUserId(userId);
        guildMessage.setMsgId(msgId);
        guildMessage.setType(TYPE_GUILD_DONATION_SEND_ITEM);
        guildMessage.setMessageContent(jsonObject.toJSONString());
        guildMessage.setCreateTime(DateUtils.getUnixTime());
        int ttl = gameConfigManager.getGuildConfig().getGuildConstEntity(135).getTypeInt() * 86400;
        guildMessage.setTtlTime(DateUtils.getUnixTime() + ttl);
        super.insert(guildMessage);
//        guildMessageOpenSearchDao.addDocument(guildMessage);

        jsonObject = new JSONObject();
        jsonObject.put("fromUserId", userId);
        jsonObject.put("fromNickName", nickName);
        jsonObject.put("itemId", itemId);
        jsonObject.put("itemCount", itemCount);
        jsonObject.put("msgId", msgId2);
        jsonObject.put("timestamp", DateUtils.getUnixTime());
        jsonObject.put("type", 2);      // 我赠送给别人

        GuildMessage guildMessage2 = new GuildMessage();
        guildMessage2.setPK(this.getPk(guildId));
        guildMessage2.setSK(String.valueOf(msgId2));
        guildMessage2.setUserId(toUserId);
        guildMessage2.setMsgId(msgId2);
        guildMessage2.setType(TYPE_GUILD_DONATION_RECEIVE_ITEM);
        guildMessage2.setMessageContent(jsonObject.toJSONString());
        guildMessage2.setCreateTime(DateUtils.getUnixTime());
        guildMessage2.setTtlTime(DateUtils.getUnixTime() + ttl);
        super.insert(guildMessage2);
//        guildMessageOpenSearchDao.addDocument(guildMessage2);
    }

    // 添加红包数据
    public void addRedEnvelope(long guildId, long userId, long msgId, int messageType, GuildMessage.RedEnvelopeModel redEnvelopeModel) {
        GuildMessage guildMessage = new GuildMessage();
        guildMessage.setPK(this.getPk(guildId));
        guildMessage.setSK(String.valueOf(msgId));
        guildMessage.setUserId(userId);
        guildMessage.setMsgId(msgId);
        guildMessage.setType(TYPE_GUILD_RED_ENVELOPE);
        guildMessage.setMessageType(messageType);
        guildMessage.setRedEnvelope(redEnvelopeModel);
        guildMessage.setCreateTime(DateUtils.getUnixTime());
        guildMessage.setTtlTime(redEnvelopeModel.getExpiredTM());
        guildMessage.setGuildId(guildId);
        guildMessage.setSK2(this.getSK2(userId, TYPE_GUILD_RED_ENVELOPE));
        super.insert(guildMessage);
//        guildMessageOpenSearchDao.addDocument(guildMessage);
    }

    // 更新红包领取数量
    public void addRedEnvelopeReceiveCount(GuildMessage guildMessage, int maxCount) {
        Expression updateExpression = Expression.builder().expression("redEnvelope.#field = redEnvelope.#field + :addValue")
                .putExpressionName("#field", "count")
                .putExpressionValue(":addValue", DynamoDBConvertUtil.buildAttributeValue(1))
                .build();
        guildMessage.addUpdateExpression(updateExpression);

        Expression updateCondition = Expression.builder().expression("redEnvelope.#field < :value")
                .putExpressionName("#field", "count")
                .putExpressionValue(":value", DynamoDBConvertUtil.buildAttributeValue(maxCount)).build();
        guildMessage.addUpdateCondition(updateCondition);

        guildMessage.getRedEnvelope().setCount(guildMessage.getRedEnvelope().getCount() + 1);
        GuildMessage updateObj = DynamoDBCacheManager.getUpdateObj(guildMessage);
        updateObj.setRedEnvelope(guildMessage.getRedEnvelope());
        super.updateIgnoreNulls(guildMessage);
//        guildMessageOpenSearchDao.addDocument(guildMessage);
    }

    // 获取单个红包领取记录
    public GuildMessage addOpenRedEnvelopeRecord(long packMsgId, long msgId, long userId, String content, long ttlTime) {
        GuildMessage guildMessage = new GuildMessage();
        guildMessage.setPK(GuildTableKeyPrefix.GuildRedEnvelopeOpenRecord + packMsgId);
        guildMessage.setSK(String.valueOf(msgId));
        guildMessage.setUserId(userId);
        guildMessage.setMsgId(msgId);
        guildMessage.setMessageContent(content);
        guildMessage.setType(TYPE_GUILD_RED_ENVELOPE_OPEN_RECORDS);
        guildMessage.setCreateTime(DateUtils.getUnixTime());
        guildMessage.setTtlTime(ttlTime);
        super.insert(guildMessage);
//        guildMessageOpenSearchDao.addDocument(guildMessage);
        return guildMessage;
    }

    // 获取红包领取记录
    public List<GuildMessage> getOpenRedEnvelopeRecords(long packMsgId) {
        return super.getAll(GuildTableKeyPrefix.GuildRedEnvelopeOpenRecord + packMsgId);
    }

    private long getTTLTime() {
        return gameConfigManager.getGuildConfig().getGuildConstEntity(123).getTypeInt() * 86400L + DateUtils.getUnixTime();
    }

    public void delete(GuildMessage guildMessage) {
        super.delete(guildMessage);
//        guildMessageOpenSearchDao.deleteDocument(guildMessage);
    }

    public void update(GuildMessage guildMessage) {
        super.update(guildMessage);
//        guildMessageOpenSearchDao.addDocument(guildMessage);
    }

    // 使用 guildMessage-Index 查询数据
    private List<GuildMessage> queryMessageFromDdbByIndex(QueryCondition queryCondition) {
        QueryConditional queryConditional
                = QueryConditional
                .keyEqualTo(super.buildKey(queryCondition.getPK()));
        Expression expression = Expression.builder()
                .expression(queryCondition.getExpressionStr())
                .expressionNames(queryCondition.getExpressionNames())
                .expressionValues(queryCondition.getExpressionValues())
                .build();
        return guildMessageIndex.query(builder -> {
            builder.queryConditional(queryConditional)
                    .scanIndexForward(false)
                    .exclusiveStartKey(queryCondition.getExclusiveStartKey())
                    .filterExpression(expression)
                    .limit(queryCondition.getLimit());
        }).iterator().next().items();
    }


    @Data
    private static class QueryCondition {
        private String PK;
        private int limit;
        private String expressionStr;
        private Map<String, String> expressionNames;
        private Map<String, AttributeValue> expressionValues;
        private Map<String, AttributeValue> exclusiveStartKey;

        public static QueryCondition buildQueryMessage(String PK, long msgId, int limit, int... type) {
            List<String> conditions = new ArrayList<>();
            Map<String, AttributeValue> expressionValues = new HashMap<>();
            for (int i = 0, len = type.length; i < len; i++) {
                String key = ":type" + i;
                conditions.add(key);
                expressionValues.put(key, DynamoDBConvertUtil.buildAttributeValue(type[i]));
            }

            String str = StringUtils.join(conditions, ",");
            String expressionStr = "#type in (" + str + ") and #ttlTime > :ttlTime";
            Map<String, String> expressionNames = new HashMap<>();
            expressionNames.put("#type", "type");
            expressionNames.put("#ttlTime", "ttlTime");
            expressionValues.put(":ttlTime", DynamoDBConvertUtil.buildAttributeValue(DateUtils.getUnixTime()));

            Map<String, AttributeValue> exclusiveStartKey = null;
            if (msgId > 0) {
                exclusiveStartKey = new HashMap<>();
                exclusiveStartKey.put("PK", AttributeValue.fromS(PK));
                exclusiveStartKey.put("SK", AttributeValue.fromS(String.valueOf(msgId)));
                exclusiveStartKey.put("msgId", AttributeValue.fromN(String.valueOf(msgId)));
            }

            QueryCondition result = new QueryCondition();
            result.setPK(PK);
            result.setLimit(limit);
            result.setExpressionStr(expressionStr);
            result.setExpressionNames(expressionNames);
            result.setExpressionValues(expressionValues);
            result.setExclusiveStartKey(exclusiveStartKey);
            return result;
        }

        public static QueryCondition buildQueryMessage(String PK, long msgId, int limit) {
            Map<String, AttributeValue> expressionValues = new HashMap<>();

            String expressionStr = "#ttlTime > :ttlTime";
            Map<String, String> expressionNames = new HashMap<>();
            expressionNames.put("#ttlTime", "ttlTime");
            expressionValues.put(":ttlTime", DynamoDBConvertUtil.buildAttributeValue(DateUtils.getUnixTime()));

            Map<String, AttributeValue> exclusiveStartKey = null;
            if (msgId > 0) {
                exclusiveStartKey = new HashMap<>();
                exclusiveStartKey.put("PK", AttributeValue.fromS(PK));
                exclusiveStartKey.put("SK", AttributeValue.fromS(String.valueOf(msgId)));
                exclusiveStartKey.put("msgId", AttributeValue.fromN(String.valueOf(msgId)));
            }

            QueryCondition result = new QueryCondition();
            result.setPK(PK);
            result.setLimit(limit);
            result.setExpressionStr(expressionStr);
            result.setExpressionNames(expressionNames);
            result.setExpressionValues(expressionValues);
            result.setExclusiveStartKey(exclusiveStartKey);
            return result;
        }

        public static QueryCondition buildQueryChallengeRecords(String PK, int limit, long guildBossTime, int... type) {
            QueryCondition queryCondition = buildQueryMessage(PK, 0, limit, type);
            String expressionStr = queryCondition.getExpressionStr() + " and #guildBossTime = :guildBossTime";
            queryCondition.getExpressionNames().put("#guildBossTime", "guildBossTime");
            queryCondition.getExpressionValues().put(":guildBossTime", DynamoDBConvertUtil.buildAttributeValue(guildBossTime));
            queryCondition.setExpressionStr(expressionStr);
            return queryCondition;
        }

        public static QueryCondition buildQueryDonationOperations(String PK, int limit, long userId, int... type) {
            QueryCondition queryCondition = buildQueryMessage(PK, 0, limit, type);
            String expressionStr = queryCondition.getExpressionStr() + " and #userId = :userId";
            queryCondition.getExpressionNames().put("#userId", "userId");
            queryCondition.getExpressionValues().put(":userId", DynamoDBConvertUtil.buildAttributeValue(userId));
            queryCondition.setExpressionStr(expressionStr);
            return queryCondition;
        }
    }
}




