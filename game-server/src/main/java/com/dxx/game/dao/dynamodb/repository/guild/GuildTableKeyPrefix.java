package com.dxx.game.dao.dynamodb.repository.guild;

/**
 * @authoer: lsc
 * @createDate: 2023/3/24
 * @description:
 */
public class GuildTableKeyPrefix {

    // 排序键
    public static final String GuildMetaData = "METADATA#GUILD#";
    public static final String UserMetaData = "METADATA#UESR#";
    public static final String UserMessageData = "GUILD#MESSAGE#USER";

    // ----------------------------------------- //
    // 公会主键前缀
    public static final String Guild = "GUILD#";
    // 公会用户主键前缀
    public static final String User = "USER#";
    // 公会申请列表主键前缀
    public static final String Apply = "APPLY#";
    // 公会用户活跃度记录主键前缀
    public static final String UserActiveRecord = "USER#ACTIVE#RECORD#";
    // 公会消息主键前缀
    public static final String GuildMessage = "GUILD#MESSAGE#";
    // 公会红包开启记录
    public static final String GuildRedEnvelopeOpenRecord = "GUILD#RED#ENVELOPE#OPEN#RECORD#";
    // 聊天频道前缀
    public static final String ChatMessage = "CHAT#MESSAGE#";
}
