package com.dxx.game.dao.dynamodb.repository.guild;


import com.dxx.game.common.aws.dynamodb.BaseDynamoDBDao;
import com.dxx.game.common.aws.dynamodb.cache.DynamoDBCacheManager;
import com.dxx.game.dao.dynamodb.model.guild.GuildUser;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Repository;
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbIndex;
import software.amazon.awssdk.enhanced.dynamodb.Key;
import software.amazon.awssdk.enhanced.dynamodb.model.Page;
import software.amazon.awssdk.enhanced.dynamodb.model.QueryConditional;
import software.amazon.awssdk.services.dynamodb.model.AttributeValue;
import software.amazon.awssdk.services.dynamodb.model.ExecuteStatementRequest;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @authoer: lsc
 * @createDate: 2023/3/24
 * @description:
 */
@Repository
public class GuildUserDao extends BaseDynamoDBDao<GuildUser> {

    DynamoDbIndex<GuildUser> guildIdIndex;

    @PostConstruct
    private void init() {
        guildIdIndex = this.mappedTable.index("guildId-index");
    }

    private String getPK(long userId) {
        return GuildTableKeyPrefix.User + userId;
    }

    private String getSK() {
        return GuildTableKeyPrefix.UserMetaData;
    }

    // 添加主键
    public void setPrimaryKey(GuildUser guildUser) {
        guildUser.setPK(this.getPK(guildUser.getUserId()));
        guildUser.setSK(this.getSK());
        guildUser.setSK2(GuildTableKeyPrefix.UserMetaData);
    }

    // 根据公会ID获取数据
    public List<GuildUser> getAllByGuildId(long guildId) {
        QueryConditional queryConditional
                = QueryConditional.keyEqualTo(Key.builder().partitionValue(guildId).sortValue(GuildTableKeyPrefix.UserMetaData).build());
        List<Page<GuildUser>> collect = guildIdIndex.query(queryConditional).stream().collect(Collectors.toList());
        return collect.get(0).items();
    }

    // 根据用户ID获取数据
    public GuildUser getByUserId(long userId) {
        return super.getItem(this.getPK(userId), this.getSK());
    }

    // 根据用户ID获取数据
    public List<GuildUser> getListByUserIds(Collection<Long> userIds) {
        List<List<Object>> keys = new ArrayList<>(userIds.size());
        for (Long userId : userIds) {
            keys.add(Lists.newArrayList(this.getPK(userId), this.getSK()));
        }
        return super.batchGetItemWithSortKeys(keys);
    }

    // 根据用户id获得数据
    public Map<Long, GuildUser> getGuildUsersByUserIds(List<Long> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return Collections.emptyMap();
        }
        Map<Long, GuildUser> result = new HashMap<>();
        List<GuildUser> list = getListByUserIds(userIds);
        for (GuildUser guildUser : list) {
            result.put(guildUser.getUserId(), guildUser);
        }
        return result;
    }

    public List<Map<String, AttributeValue>> queryGuildId(List<Long> userIdList) {
        if (userIdList == null || userIdList.isEmpty()) {
            return List.of();
        }
        var sql = """
                select userId, guildId from "%s" where PK in [%s]
                """.formatted(tableName, userIdList.stream().map(userId -> "'" + getPK(userId) + "'").collect(Collectors.joining(",")));
        var state = ExecuteStatementRequest.builder().statement(sql).consistentRead(true).build();
        return this.dynamoDbClient.executeStatement(state).items();
    }

    // 插入数据
    public void insert(GuildUser guildUser) {
        super.insert(guildUser);
    }

    // 更新公会ID
    public void updateGuildId(GuildUser guildUser) {
        GuildUser update = DynamoDBCacheManager.getUpdateObj(guildUser);
        update.setGuildId(guildUser.getGuildId());
        super.updateIgnoreNulls(update);
    }

    //更新退出工会时间
    public void updateQuitTime(GuildUser guildUser) {
        GuildUser update = DynamoDBCacheManager.getUpdateObj(guildUser);
        update.setQuitTime(guildUser.getQuitTime());
        super.updateIgnoreNulls(update);
    }

    //更新是否是工会创建人
    public void updateCreater(GuildUser guildUser) {
        GuildUser update = DynamoDBCacheManager.getUpdateObj(guildUser);
        update.setCreater(guildUser.getCreater());
        super.updateIgnoreNulls(update);
    }

    // 更新职位
    public void updatePosition(GuildUser guildUser) {
        GuildUser update = DynamoDBCacheManager.getUpdateObj(guildUser);
        update.setPosition(guildUser.getPosition());
        super.updateIgnoreNulls(update);
    }

    // 更新加入时间
    public void updateJoinTime(GuildUser guildUser) {
        GuildUser update = DynamoDBCacheManager.getUpdateObj(guildUser);
        update.setJoinTime(guildUser.getJoinTime());
        super.updateIgnoreNulls(update);
    }

    // 更新申请时间
    public void updateApplyTime(GuildUser guildUser) {
        GuildUser update = DynamoDBCacheManager.getUpdateObj(guildUser);
        update.setApplyTime(guildUser.getApplyTime());
        super.updateIgnoreNulls(update);
    }

    // 更新申请公会id
    public void updateApplyGuildId(GuildUser guildUser) {
        GuildUser update = DynamoDBCacheManager.getUpdateObj(guildUser);
        update.setApplyGuildIds(guildUser.getApplyGuildIds());
        super.updateIgnoreNulls(update);
    }

    public void updateApplyGuildIdNow(GuildUser guildUser) {
        GuildUser update = DynamoDBCacheManager.getUpdateObj(guildUser);
        update.setApplyGuildIds(guildUser.getApplyGuildIds());
        super.updateNowIgnoreNulls(update);
    }

    // 更新公会任务数据
    public void updateGuildTask(GuildUser guildUser) {
        GuildUser update = DynamoDBCacheManager.getUpdateObj(guildUser);
        update.setTasks(guildUser.getTasks());
        super.updateIgnoreNulls(update);
    }

    public void updateKilledRecord(GuildUser guildUser) {
        GuildUser update = DynamoDBCacheManager.getUpdateObj(guildUser);
        update.setBossKilledRewardRecord(guildUser.getBossKilledRewardRecord());
        super.updateIgnoreNulls(update);
    }

    // 更新任务刷新次数
    public void updateGuildTaskRefreshCount(GuildUser guildUser) {
        GuildUser update = DynamoDBCacheManager.getUpdateObj(guildUser);
        update.setTaskRefreshCount(guildUser.getTaskRefreshCount());
        super.updateIgnoreNulls(update);
    }

    // 更新每日商店
    public void updateDailyShop(GuildUser guildUser) {
        GuildUser update = DynamoDBCacheManager.getUpdateObj(guildUser);
        update.setDailyShop(guildUser.getDailyShop());
        super.updateIgnoreNulls(update);
    }

    // 更新每周商店
    public void updateWeeklyShop(GuildUser guildUser) {
        GuildUser update = DynamoDBCacheManager.getUpdateObj(guildUser);
        update.setWeeklyShop(guildUser.getWeeklyShop());
        super.updateIgnoreNulls(update);
    }

    // 更新签到次数
    public void updateSignIn(GuildUser guildUser) {
        GuildUser update = DynamoDBCacheManager.getUpdateObj(guildUser);
        update.setSignInCnt(guildUser.getSignInCnt());
        super.updateIgnoreNulls(update);
    }

    // 更新所有活跃度
    public void updateAllActive(GuildUser guildUser) {
        GuildUser update = DynamoDBCacheManager.getUpdateObj(guildUser);
        update.setActive(guildUser.getActive());
        update.setDailyActive(guildUser.getDailyActive());
        update.setWeeklyActive(guildUser.getWeeklyActive());
        super.updateIgnoreNulls(update);
    }

    // 更新总活跃度
    public void updateActive(GuildUser guildUser) {
        GuildUser update = DynamoDBCacheManager.getUpdateObj(guildUser);
        update.setActive(guildUser.getActive());
        super.updateIgnoreNulls(update);
    }

    // 更新每日活跃度
    public void updateDailyActive(GuildUser guildUser) {
        GuildUser update = DynamoDBCacheManager.getUpdateObj(guildUser);
        update.setDailyActive(guildUser.getDailyActive());
        super.updateIgnoreNulls(update);
    }

    // 更新每周活跃度
    public void updateWeeklyActive(GuildUser guildUser) {
        GuildUser update = DynamoDBCacheManager.getUpdateObj(guildUser);
        update.setWeeklyActive(guildUser.getWeeklyActive());
        super.updateIgnoreNulls(update);
    }

    // 更新每日数据刷新时间
    public void updateDailyTM(GuildUser guildUser) {
        GuildUser update = DynamoDBCacheManager.getUpdateObj(guildUser);
        update.setDailyTM(guildUser.getDailyTM());
        super.updateIgnoreNulls(update);
    }

    // 更新每周数据刷新时间
    public void updateWeeklyTM(GuildUser guildUser) {
        GuildUser update = DynamoDBCacheManager.getUpdateObj(guildUser);
        update.setWeeklyTM(guildUser.getWeeklyTM());
        super.updateIgnoreNulls(update);
    }

    // 更新公会等级记录
    public void updateGuildLevelRecord(GuildUser guildUser) {
        GuildUser update = DynamoDBCacheManager.getUpdateObj(guildUser);
        update.setGuildLevelRecord(guildUser.getGuildLevelRecord());
        super.updateIgnoreNulls(update);
    }

    // 更新被踢出公会信息
    public void updateBeKickedOutInfo(GuildUser guildUser) {
        GuildUser update = DynamoDBCacheManager.getUpdateObj(guildUser);
        update.setBeKickedOutInfo(guildUser.getBeKickedOutInfo());
        super.updateIgnoreNulls(update);
    }

    // 更新公会boss数据
    public void updateGuildBossModel(GuildUser guildUser) {
        GuildUser update = DynamoDBCacheManager.getUpdateObj(guildUser);
        update.setGuildBossModel(guildUser.getGuildBossModel());
        super.updateIgnoreNulls(update);
    }

    public void updateDonation(GuildUser guildUser) {
        GuildUser update = DynamoDBCacheManager.getUpdateObj(guildUser);
        update.setDonationItemCount(guildUser.getDonationItemCount());
        update.setReqItemTM(guildUser.getReqItemTM());
        super.updateIgnoreNulls(update);
    }

    public void updateGuildWar(GuildUser guildUser) {
        GuildUser update = DynamoDBCacheManager.getUpdateObj(guildUser);
        update.setGuildWarModel(guildUser.getGuildWarModel());
        super.updateIgnoreNulls(update);
    }

    public void updateGuildWarTask(GuildUser guildUser) {
        GuildUser update = DynamoDBCacheManager.getUpdateObj(guildUser);
        update.setGuildWarTaskModel(guildUser.getGuildWarTaskModel());
        super.updateIgnoreNulls(update);
    }

    public void updateContribute(GuildUser guildUser) {
        GuildUser update = DynamoDBCacheManager.getUpdateObj(guildUser);
        update.setDayContributeTimes(guildUser.getDayContributeTimes());
        super.updateIgnoreNulls(update);
    }

    public void updateImGroupUserStatus(GuildUser guildUser) {
        GuildUser update = DynamoDBCacheManager.getUpdateObj(guildUser);
        update.setImGroupUserStatus(guildUser.getImGroupUserStatus());
        super.updateIgnoreNulls(update);
    }
}
