package com.dxx.game.dao.dynamodb.repository.guild.opensearch;//package com.dxx.game.dao.dynamodb.repository.guild.opensearch;
//
//import com.dxx.game.common.aws.opensearch.OpenSearchService;
//import com.dxx.game.common.utils.DateUtils;
//import com.dxx.game.config.GameConfigManager;
//import com.dxx.game.dao.dynamodb.model.guild.GuildMessage;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.lucene.search.join.ScoreMode;
//import org.opensearch.index.query.*;
//import org.opensearch.script.Script;
//import org.opensearch.script.ScriptType;
//import org.opensearch.search.builder.SearchSourceBuilder;
//import org.opensearch.search.sort.FieldSortBuilder;
//import org.opensearch.search.sort.SortOrder;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Repository;
//
//import javax.annotation.PostConstruct;
//import javax.annotation.Resource;
//import java.util.Collections;
//import java.util.List;
//
/// **
// * @authoer: lsc
// * @createDate: 2023/4/20
// * @description:
// */
//@Slf4j
//@Repository
//public class GuildMessageOpenSearchDao {
//
//    @Value("${aws.opensearch.index.guild}")
//    private String openSearchIndex;
//    @Resource
//    private GameConfigManager gameConfigManager;
//
//    @Resource
//    private OpenSearchService openSearchService;
//
//    private String getOpenSearchIndex() {
//        return this.openSearchIndex + "-message";
//    }
//
//    @PostConstruct
//    private void init() {
//        boolean exists = openSearchService.indexExists(this.getOpenSearchIndex());
//        if (!exists) {
//            log.info("OpenSearch init-index-{}", this.getOpenSearchIndex());
//            openSearchService.createIndex(this.getOpenSearchIndex());
//        }
//    }
//
//    public void addDocument(GuildMessage guildMessage) {
//        if (gameConfigManager.isDevelop()) {
//            openSearchService.addDocument(this.getOpenSearchIndex(), String.valueOf(guildMessage.getMsgId()), guildMessage);
//        }
//    }
//
//    public List<GuildMessage> queryMessage(String pk, int limit, long msgId, int... type) {
//        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
//        searchSourceBuilder.from(0);
//        searchSourceBuilder.size(limit);
//
//        searchSourceBuilder.sort(new FieldSortBuilder("msgId").order(SortOrder.DESC).unmappedType("keyword"));
//
//        // 构建bool查询
//        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
//        if (msgId > 0) {
//            boolQueryBuilder.filter(QueryBuilders.rangeQuery("msgId").lt(msgId));
//        }
//        if (type.length > 0) {
//            boolQueryBuilder.filter(QueryBuilders.termsQuery("type", type));
//        }
//
//        boolQueryBuilder.must(QueryBuilders.termQuery("PK.keyword", pk));
//        boolQueryBuilder.filter(QueryBuilders.rangeQuery("ttlTime").gt(DateUtils.getUnixTime()));
//
//        // 如果存在requestItem则requestItem.expiredTM未过期
//        boolQueryBuilder.should(QueryBuilders.boolQuery().must(QueryBuilders.existsQuery("requestItem")).must(QueryBuilders.rangeQuery("requestItem.expiredTM").gt(DateUtils.getUnixTime())));
//        boolQueryBuilder.should(QueryBuilders.boolQuery().mustNot(QueryBuilders.existsQuery("requestItem")));
//        boolQueryBuilder.minimumShouldMatch(1);
//
//        searchSourceBuilder.query(boolQueryBuilder);
//        return openSearchService.queryDocument(searchSourceBuilder, this.getOpenSearchIndex(), GuildMessage.class);
//
//    }
//
//    public List<GuildMessage> queryBossChallengeRecords(String pk, long guildBossTime, int limit, int... type) {
//        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
//        searchSourceBuilder.from(0);
//        searchSourceBuilder.size(limit);
//
//        searchSourceBuilder.sort(new FieldSortBuilder("msgId").order(SortOrder.DESC).unmappedType("keyword"));
//
//        // 构建bool查询
//        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
//        boolQueryBuilder.must(QueryBuilders.termQuery("PK.keyword", pk));
//        boolQueryBuilder.must(QueryBuilders.termQuery("guildBossTime", guildBossTime));
//        boolQueryBuilder.filter(QueryBuilders.termsQuery("type", type));
//
//        searchSourceBuilder.query(boolQueryBuilder);
//        return openSearchService.queryDocument(searchSourceBuilder, this.getOpenSearchIndex(), GuildMessage.class);
//
//    }
//
//    public List<GuildMessage> querySignInRecords(String pk, int limit, int... type) {
//        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
//        searchSourceBuilder.from(0);
//        searchSourceBuilder.size(limit);
//
//        searchSourceBuilder.sort(new FieldSortBuilder("msgId").order(SortOrder.DESC).unmappedType("keyword"));
//
//        // 构建bool查询
//        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
//        boolQueryBuilder.must(QueryBuilders.termQuery("PK.keyword", pk));
//        boolQueryBuilder.filter(QueryBuilders.termsQuery("type", type));
//
//        searchSourceBuilder.query(boolQueryBuilder);
//        return openSearchService.queryDocument(searchSourceBuilder, this.getOpenSearchIndex(), GuildMessage.class);
//    }
//
//    public List<GuildMessage> queryDonationReqItems(String pk, int limit, long msgId, int... type) {
//        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
//        searchSourceBuilder.from(0);
//        searchSourceBuilder.size(limit);
//
//        searchSourceBuilder.sort(new FieldSortBuilder("msgId").order(SortOrder.DESC).unmappedType("keyword"));
//
//        // 构建bool查询
//        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
//        if (msgId > 0) {
//            boolQueryBuilder.filter(QueryBuilders.rangeQuery("msgId").lt(msgId));
//        }
//        if (type.length > 0) {
//            boolQueryBuilder.filter(QueryBuilders.termsQuery("type", type));
//        }
//
//        boolQueryBuilder.filter(QueryBuilders.rangeQuery("ttlTime").gt(DateUtils.getUnixTime()));
//        boolQueryBuilder.filter(QueryBuilders.rangeQuery("requestItem.expiredTM").gt(DateUtils.getUnixTime()));
//
//        boolQueryBuilder.must(QueryBuilders.termQuery("PK.keyword", pk));
//        searchSourceBuilder.query(boolQueryBuilder);
//        return openSearchService.queryDocument(searchSourceBuilder, this.getOpenSearchIndex(), GuildMessage.class);
//    }
//
//    public List<GuildMessage> queryDonationByUserId(String pk, long msgId, int limit, long userId, int ...type) {
//        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
//        searchSourceBuilder.from(0);
//        searchSourceBuilder.size(limit);
//        searchSourceBuilder.sort(new FieldSortBuilder("msgId").order(SortOrder.DESC).unmappedType("keyword"));
//
//        // 构建bool查询
//        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
//        if (msgId > 0) {
//            boolQueryBuilder.filter(QueryBuilders.rangeQuery("msgId").lt(msgId));
//        }
//        if (type.length > 0) {
//            boolQueryBuilder.filter(QueryBuilders.termsQuery("type", type));
//        }
//        boolQueryBuilder.filter(QueryBuilders.termQuery("userId", userId));
//        boolQueryBuilder.filter(QueryBuilders.rangeQuery("ttlTime").gt(DateUtils.getUnixTime()));
//
//        boolQueryBuilder.must(QueryBuilders.termQuery("PK.keyword", pk));
//        searchSourceBuilder.query(boolQueryBuilder);
//        return openSearchService.queryDocument(searchSourceBuilder, this.getOpenSearchIndex(), GuildMessage.class);
//    }
//
//    public void deleteDocument(GuildMessage guildMessage) {
//        if (gameConfigManager.isDevelop()) {
//            openSearchService.deleteDocument(this.getOpenSearchIndex(), String.valueOf(guildMessage.getMsgId()));
//        }
//    }
//
//}
