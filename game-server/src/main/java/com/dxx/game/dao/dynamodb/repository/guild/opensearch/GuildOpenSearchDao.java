package com.dxx.game.dao.dynamodb.repository.guild.opensearch;

import com.dxx.game.common.aws.opensearch.OpenSearchService;
import com.dxx.game.common.server.context.RequestContext;
import com.dxx.game.common.utils.CollectionUtils;
import com.dxx.game.common.utils.DateUtils;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.dao.dynamodb.model.guild.Guild;
import com.dxx.game.modules.server.service.ServerListService;
import com.dxx.game.modules.server.service.impl.ServerListServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.opensearch.index.query.BoolQueryBuilder;
import org.opensearch.index.query.QueryBuilders;
import org.opensearch.index.query.RangeQueryBuilder;
import org.opensearch.index.query.WildcardQueryBuilder;
import org.opensearch.index.query.functionscore.FunctionScoreQueryBuilder;
import org.opensearch.index.query.functionscore.WeightBuilder;
import org.opensearch.script.Script;
import org.opensearch.search.builder.SearchSourceBuilder;
import org.opensearch.search.sort.ScoreSortBuilder;
import org.opensearch.search.sort.ScriptSortBuilder;
import org.opensearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @authoer: lsc
 * @createDate: 2023/3/24
 * @description:
 */
@Slf4j
@Repository
public class GuildOpenSearchDao {

    @Resource
    private OpenSearchService openSearchService;
    @Resource
    private GameConfigManager gameConfigManager;
    @Value("${aws.opensearch.index.guild}")
    private String openSearchIndex;
    @Autowired
    private ServerListService serverListService;

    private String getOpenSearchIndex() {
        return this.openSearchIndex;
    }

    @PostConstruct
    private void init() {
        boolean exists = openSearchService.indexExists(getOpenSearchIndex());

        log.info("OpenSearchInit exits:{} {}", getOpenSearchIndex(), exists);
        if (!exists) {
            log.info("OpenSearch init-index-{}", getOpenSearchIndex());
            openSearchService.createIndex(getOpenSearchIndex());
        }
    }

    public void deleteIndex() {
        if (gameConfigManager.isProd() || gameConfigManager.isPre()) {
            return;
        }
        openSearchService.deleteIndex(getOpenSearchIndex());
    }

    public void createIndex() {
        openSearchService.createIndex(getOpenSearchIndex());
    }


    // 将数据从opensearch 删除
    public void deleteDocument(Guild guild) {
        if (gameConfigManager.isLocal()) {
            openSearchService.deleteDocument(this.getOpenSearchIndex(), String.valueOf(guild.getGuildId()));
        }
    }

    // 添加数据
    public void addDocument(Guild guild) {
        if (gameConfigManager.isLocal()) {
            openSearchService.addDocument(this.getOpenSearchIndex(), String.valueOf(guild.getGuildId()), guild);
        }
    }

    // 查询所有公会数据
    public List<Guild> queryAllGuilds() {
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(QueryBuilders.matchAllQuery());
        searchSourceBuilder.size(100);
        return openSearchService.queryDocument(searchSourceBuilder, this.getOpenSearchIndex(), Guild.class);
    }

    // 根据名字模糊查询
    public List<Guild> searchByName(String name, List<Long> excludeGuildIds, boolean isOnlyJoinable, int condition, List<List<Integer>> serRange, int excludeServerId) {

        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        // 设置从第 0 条记录开始返回结果
        sourceBuilder.from(0);
        // 设置返回的最大记录数
        sourceBuilder.size(20);
        // 构建查询条件

        // 模糊匹配的权重是1
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        // 构建通配符查询，忽略大小写
        WildcardQueryBuilder wildcardQuery = QueryBuilders.wildcardQuery("guildName.keyword",
                String.format("*%s*", name)).caseInsensitive(true);
        boolQuery.must(wildcardQuery);
        // 添加过滤器，只查询 guildMemberCount 大于 0 的记录
        boolQuery.filter(QueryBuilders.rangeQuery("guildMembersCount").gt(0));

        if (excludeGuildIds != null && excludeGuildIds.size() > 0) {
            boolQuery.mustNot(QueryBuilders.termsQuery("guildId", excludeGuildIds));
        }


        // 只搜索我能加入的
        if (isOnlyJoinable) {
            boolQuery.filter(QueryBuilders.scriptQuery(new Script("doc['guildMaxMembersCount'].value > doc['guildMembersCount'].value")));
            boolQuery.filter(QueryBuilders.rangeQuery("guildApplyCondition").lte(condition));
        }


        BoolQueryBuilder rangeQuery = BuildQueryBuilderByServerId(serRange);

        boolQuery.filter(rangeQuery);

        if (excludeServerId != 0) {
            boolQuery.mustNot(QueryBuilders.termsQuery("serverId", List.of(excludeServerId)));
        }


        // 完全匹配的权重是2
        WeightBuilder weightBuilder = new WeightBuilder();
        // 给完全匹配的结果加权重
        weightBuilder.setWeight(2f);
        // 构建完全匹配查询，忽略大小写
        WildcardQueryBuilder exactMatchQuery = QueryBuilders.wildcardQuery("guildName.keyword", name)
                .caseInsensitive(true);
        // 构建函数得分查询，将完全匹配结果打上加权重标记
        FunctionScoreQueryBuilder.FilterFunctionBuilder[] filterFunctionBuilders = new FunctionScoreQueryBuilder.FilterFunctionBuilder[]{
                new FunctionScoreQueryBuilder.FilterFunctionBuilder(exactMatchQuery, weightBuilder)
        };

        FunctionScoreQueryBuilder functionScoreQueryBuilder = QueryBuilders.functionScoreQuery(boolQuery, filterFunctionBuilders);
        sourceBuilder.query(functionScoreQueryBuilder);

//        sourceBuilder.query(buildSearchByNameQuery(name));
        // 先按 _score 排序
        sourceBuilder.sort(new ScoreSortBuilder().order(SortOrder.DESC));
        // 再按 guildDayActive 排序
        sourceBuilder.sort("guildDayActive", SortOrder.DESC);
        List<Guild> result = openSearchService.queryDocument(sourceBuilder, this.getOpenSearchIndex(), Guild.class);
        result.forEach(temp -> {
            log.info("guildSearchByName userId:{} serRange:{} excludeServerId:{} result:{} param:{} ", RequestContext.getUserId(), serRange, excludeServerId, temp.getGuildId() + ":" + temp.getServerId() + ":" + temp.getGuildLanguage(), RequestContext.getClientParams());
        });
        return result;
    }

    // 获取推荐列表优先从今天获得贡献值的公会中随机选出20个人员不满的公会，按照人数多少排列，多的在前
    public List<Guild> getRecommendList(int limit
            , List<Long> excludeGuildIds
            , int lastDayActiveLimit
            , boolean isOnlyJoinable
            , int selfSever
            , List<List<Integer>> serRange
            , int excludeServerId
            , long timeLimit
            , int guildLanguage) {
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.from(0);
        searchSourceBuilder.size(limit);

        // 添加随机排序脚本
        ScriptSortBuilder scriptSortBuilder = new ScriptSortBuilder(new Script("Math.random()"), ScriptSortBuilder.ScriptSortType.NUMBER);
        scriptSortBuilder.order(SortOrder.ASC);
        searchSourceBuilder.sort(scriptSortBuilder);


        // 构建bool查询
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.rangeQuery("guildId").gt(0));
        boolQueryBuilder.filter(QueryBuilders.rangeQuery("guildIsDissolved").lte(0));

        //跨服生效
        if (timeLimit > 0) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("openServerTime").lte(timeLimit));
        }

        boolQueryBuilder.filter(QueryBuilders.rangeQuery("guildMembersCount").gt(0));

        BoolQueryBuilder rangeQuery = BuildQueryBuilderByServerId(serRange);

        boolQueryBuilder.filter(rangeQuery);

        boolQueryBuilder.filter(QueryBuilders.scriptQuery(new Script("doc['guildMaxMembersCount'].value > doc['guildMembersCount'].value")));

        if (excludeServerId != 0) {
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery("serverId", List.of(excludeServerId)));
        }


        // 构建must_not子句，用于排除指定的公会ID
        if (excludeGuildIds != null && excludeGuildIds.size() > 0) {
            excludeGuildIds = CollectionUtils.distinct(excludeGuildIds);
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery("guildId", excludeGuildIds));
        }

        if (lastDayActiveLimit > 0) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("lastDayActive").gt(0));
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("guildDayActiveTime").gte(DateUtils.getNextSystemResetTime() - 86400));
        }
        if (guildLanguage != -1) {
            if (RequestContext.getCommonParams().getClientVersion() > ServerListServiceImpl.LastBattleVersion) {
                boolQueryBuilder.filter(QueryBuilders.termQuery("guildLanguage", guildLanguage));
            }
        }

        // 公会人数未满的公会
//        boolQueryBuilder.filter(QueryBuilders.scriptQuery(new Script("doc['guildMaxMembersCount'].value > doc['guildMembersCount'].value")));

        if (isOnlyJoinable) {
//            boolQueryBuilder.filter(QueryBuilders.rangeQuery("guildApplyCondition").lte(condition));
            boolQueryBuilder.filter(QueryBuilders.scriptQuery(new Script("doc['guildMaxMembersCount'].value > doc['guildMembersCount'].value")));

        }


        // 向搜索请求中添加bool查询
        searchSourceBuilder.query(boolQueryBuilder);
        List<Guild> guilds = openSearchService.queryDocument(searchSourceBuilder, this.getOpenSearchIndex(), Guild.class);
//        log.error("guildLogRecord1Size:{}",guilds.size());
//        guilds.stream().forEach(temp->{
//            log.error("guildLogRecord1Name:{}", temp.getGuildName());
//        });
//        guilds.stream().forEach(temp->{
//            log.error("guildLogRecord2Detail:{}", temp.getGuildName());
//        });
        // 根据公会人数倒序排序
        if (!guilds.isEmpty()) {
            guilds = guilds.stream()
                    .sorted(Comparator.comparingInt(Guild::getGuildMembersCount).reversed())
                    .collect(Collectors.toList());
        }
        log.info("guideRecommendListLog userId:{} guildLanguage:{} isOnlyJoinable:{} selfServer:{} serverRange:{}  excludeServerId:{} timeLimit:{} lastDayActiveLimit:{} results:{}"
                , RequestContext.getUserId()
                , guildLanguage
                , isOnlyJoinable
                , selfSever
                , serRange
                , excludeServerId
                , timeLimit
                , lastDayActiveLimit
                , guilds.size());
        return guilds;
    }

    private static BoolQueryBuilder BuildQueryBuilderByServerId
            (List<List<Integer>> serRange) {
        BoolQueryBuilder rangeQuery = QueryBuilders.boolQuery();
        serRange.stream().forEach(temp -> {
            RangeQueryBuilder tempQuery = QueryBuilders.rangeQuery("serverId")
                    .gte(temp.get(0))
                    .lte(temp.get(1));
            rangeQuery.should(tempQuery);
        });
        rangeQuery.minimumShouldMatch(1);
        return rangeQuery;
    }

    // 获得满足条件可以自动加入的公会
    public List<Guild> getAutoJoinGuilds(boolean isActiveFirst, int condition, List<List<Integer>> serRange, int excludeServerId, long openServerTimeLimit, int lastDayMinActive) {
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

        // 设置from和size参数
        searchSourceBuilder.from(0);
        searchSourceBuilder.size(10);

        // 添加随机排序脚本
        ScriptSortBuilder scriptSortBuilder = new ScriptSortBuilder(new Script("Math.random()"), ScriptSortBuilder.ScriptSortType.NUMBER);
        scriptSortBuilder.order(SortOrder.ASC);
        searchSourceBuilder.sort(scriptSortBuilder);

        // 构建bool查询
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        boolQueryBuilder.filter(QueryBuilders.rangeQuery("guildMembersCount").gt(0));
        if (isActiveFirst) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("guildDayActive").gt(0));
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("guildDayActiveTime").gt(DateUtils.getNextSystemResetTime() - 86400));
        }


        BoolQueryBuilder rangeQuery = BuildQueryBuilderByServerId(serRange);

        boolQueryBuilder.filter(rangeQuery);


        if (excludeServerId != 0) {
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery("serverId", List.of(excludeServerId)));
        }
        //跨服生效
        if (openServerTimeLimit > 0) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("openServerTime").gte(openServerTimeLimit));
        }
        if (lastDayMinActive > 0) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("lastDayActive").gt(lastDayMinActive));
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("guildDayActiveTime").gte(DateUtils.getNextSystemResetTime() - 86400));
        }

        boolQueryBuilder.filter(QueryBuilders.scriptQuery(new Script("doc['guildMaxMembersCount'].value > doc['guildMembersCount'].value")));
        boolQueryBuilder.filter(QueryBuilders.termQuery("guildApplyType", 0));
        boolQueryBuilder.filter(QueryBuilders.rangeQuery("guildApplyCondition").lte(condition));

        // 向搜索请求中添加bool查询
        searchSourceBuilder.query(boolQueryBuilder);
        return openSearchService.queryDocument(searchSourceBuilder, this.getOpenSearchIndex(), Guild.class);
    }

    // 获取排行榜, 根据等级排序
    public List<Guild> getRankList(int from, int limit, List<List<Integer>> serRange, int excludeServerId) {
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        // 设置from和size参数
        searchSourceBuilder.from(from);
        searchSourceBuilder.size(limit);

        // 根据公会等级倒序
        searchSourceBuilder.sort("guildLevel", SortOrder.DESC);

        // 构建bool查询
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        boolQueryBuilder.filter(QueryBuilders.rangeQuery("guildMembersCount").gt(0));


        BuildQueryBuilderByServerId(serRange);


        if (excludeServerId != 0) {
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery("serverId", List.of(excludeServerId)));
        }
        // 向搜索请求中添加bool查询
        searchSourceBuilder.query(boolQueryBuilder);
        return openSearchService.queryDocument(searchSourceBuilder, this.getOpenSearchIndex(), Guild.class);
    }

}

