package com.dxx.game.dao.dynamodb.repository.kactivity;

import com.dxx.game.common.aws.dynamodb.BaseDynamoDBDao;
import com.dxx.game.dao.dynamodb.model.kactivity.KActivity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.HashSet;

@Repository
@Slf4j
public class KActivityDao extends BaseDynamoDBDao<KActivity> {
    public KActivity getOrCreateByUserId(long userId) {
        var kActivity = super.getItem(userId);
        if (kActivity == null) {
            kActivity = new KActivity();
            kActivity.setUserId(userId);
            kActivity.setOpens(new HashMap<>());
            var closeSet = new HashSet<Integer>();
            closeSet.add(-1); //sb dynamodb不允许空set
            kActivity.setClose(closeSet);
            super.insert(kActivity);
        }
        return kActivity;
    }
}
