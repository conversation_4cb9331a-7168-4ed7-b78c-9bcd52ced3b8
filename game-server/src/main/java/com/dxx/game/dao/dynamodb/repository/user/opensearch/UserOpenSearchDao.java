package com.dxx.game.dao.dynamodb.repository.user.opensearch;

import com.dxx.game.common.aws.opensearch.OpenSearchService;
import com.dxx.game.config.GameConfigManager;
import com.dxx.game.dao.dynamodb.model.User;
import lombok.extern.slf4j.Slf4j;
import org.opensearch.index.query.BoolQueryBuilder;
import org.opensearch.index.query.QueryBuilders;
import org.opensearch.index.query.WildcardQueryBuilder;
import org.opensearch.index.query.functionscore.FunctionScoreQueryBuilder;
import org.opensearch.index.query.functionscore.WeightBuilder;
import org.opensearch.search.builder.SearchSourceBuilder;
import org.opensearch.search.sort.ScoreSortBuilder;
import org.opensearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Repository;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;

/**
 * @authoer: lsc
 * @createDate: 2023/3/24
 * @description:
 */
@Slf4j
@Repository
public class UserOpenSearchDao {

    @Resource
    private GameConfigManager gameConfigManager;
    @Value("${aws.opensearch.index.user}")
    private String openSearchIndex;
    @Autowired
    private OpenSearchService openSearchService;

    private String getOpenSearchIndex() {
        return this.openSearchIndex;
    }


    @PostConstruct
    private void init() {
        boolean exists = openSearchService.indexExists(getOpenSearchIndex());
        if (!exists) {
            log.info("OpenSearch init-index-{}", getOpenSearchIndex());
            openSearchService.createIndex(getOpenSearchIndex());
        }
    }

    public void deleteIndex() {
        if (gameConfigManager.isProd() || gameConfigManager.isPre()) {
            return;
        }
        openSearchService.deleteIndex(getOpenSearchIndex());
    }

    public void createIndex() {
        openSearchService.createIndex(getOpenSearchIndex());
    }

    /**
     * 异步添加数据到 opensearch
     * @param user
     */
    @Async
    public void addDocument(User user) {
        try {
            openSearchService.addDocument(this.getOpenSearchIndex(), String.valueOf(user.getUserId()), buildOpenSearchUser(user));
        } catch (Exception e) {
            log.error("UserOpenSearchDao addDocument error", e);
        }
    }

    // 查询所有公会数据
    public List<UserOpenSearchEntity> queryAllUsers() {
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(QueryBuilders.matchAllQuery());
        return openSearchService.queryDocument(searchSourceBuilder, this.getOpenSearchIndex(), UserOpenSearchEntity.class);
    }

    // 根据名字模糊查询
    public List<UserOpenSearchEntity> searchByNickName(String name) {

        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        // 设置从第 0 条记录开始返回结果
        sourceBuilder.from(0);
        // 设置返回的最大记录数
        sourceBuilder.size(20);
        // 构建查询条件

        // 模糊匹配的权重是1
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        // 构建通配符查询，忽略大小写
        WildcardQueryBuilder wildcardQuery = QueryBuilders.wildcardQuery("nickName.keyword",
                String.format("*%s*", name)).caseInsensitive(true);
        boolQuery.must(wildcardQuery);

        // 完全匹配的权重是2
        WeightBuilder weightBuilder = new WeightBuilder();
        // 给完全匹配的结果加权重
        weightBuilder.setWeight(2f);
        // 构建完全匹配查询，忽略大小写
        WildcardQueryBuilder exactMatchQuery = QueryBuilders.wildcardQuery("nickName.keyword", name)
                .caseInsensitive(true);
        // 构建函数得分查询，将完全匹配结果打上加权重标记
        FunctionScoreQueryBuilder.FilterFunctionBuilder[] filterFunctionBuilders = new FunctionScoreQueryBuilder.FilterFunctionBuilder[]{
                new FunctionScoreQueryBuilder.FilterFunctionBuilder(exactMatchQuery, weightBuilder)
        };

        FunctionScoreQueryBuilder functionScoreQueryBuilder = QueryBuilders.functionScoreQuery(boolQuery, filterFunctionBuilders);
        sourceBuilder.query(functionScoreQueryBuilder);

        // 先按 _score 排序
        sourceBuilder.sort(new ScoreSortBuilder().order(SortOrder.DESC));
        return openSearchService.queryDocument(sourceBuilder, this.getOpenSearchIndex(), UserOpenSearchEntity.class);
    }

    private UserOpenSearchEntity buildOpenSearchUser(User user) {
        var openSearchUser = new UserOpenSearchEntity();
        openSearchUser.setUserId(user.getUserId());
        openSearchUser.setNickName(user.getNickName());
        openSearchUser.setServerId(user.getServerId());
        return openSearchUser;
    }

}

