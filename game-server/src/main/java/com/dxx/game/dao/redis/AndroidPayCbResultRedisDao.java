package com.dxx.game.dao.redis;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.dxx.game.common.redis.RedisService;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2021/12/27 11:00
 */
@Repository
public class AndroidPayCbResultRedisDao {

    @Data
    public static class AndroidPayCbModel {
        private int code;
        private String commonData;
        private int rechargeId;
        private double totalRechargeAmount;

    }

    @Autowired
    private RedisService redisService;

    // 保存数据
    public void setPayCbResult(long userId, long preOrderId, AndroidPayCbModel androidPayCbModel) {
        String value = JSON.toJSON(androidPayCbModel).toString();
        String key = this.getKey(userId, preOrderId);
        redisService.set(key, value);
        // 内购结果保留1小时
        redisService.expireKey(key, 3600, TimeUnit.SECONDS);
    }

    // 获取数据
    public AndroidPayCbModel getPayCbResult(long userId, long preOrderId) {
        String key = this.getKey(userId, preOrderId);
        String value = redisService.get(key);
        if (value != null) {
            return JSONObject.parseObject(value, AndroidPayCbModel.class);
        }
        return null;
    }


    private String getKey(long userId, long preOrderId) {
        return "user_android_pay_result:" + userId + ":" + preOrderId;
    }
}
