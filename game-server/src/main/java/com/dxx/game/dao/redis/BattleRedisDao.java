package com.dxx.game.dao.redis;

import com.alibaba.fastjson2.JSONObject;
import com.dxx.game.common.redis.RedisService;
import com.dxx.game.common.utils.DateUtils;
import com.dxx.game.consts.RedisKeys;
import com.dxx.game.dao.dynamodb.model.Hero;
import com.dxx.game.modules.common.model.EquipData;
import com.dxx.game.modules.common.model.HeroData;
import com.dxx.game.modules.common.model.TalentSystemData;
import com.dxx.game.modules.common.support.CommonHelper;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 战斗数据
 */
@Repository
public class BattleRedisDao {
    @Autowired
    private RedisService redisService;

    @Data
    public static class DataMod {
        private List<EquipData> wearEquips;
        private List<HeroData> formationHeroes;
        private List<TalentSystemData> talents;
        private Map<Integer, List<Long>> formationHeroIds;
        private int guildTechLv;
    }

    public DataMod getData(long userId) {
        String value = redisService.get(this.getKey(userId));
        if (StringUtils.isEmpty(value)) {
            return null;
        }
        return JSONObject.parseObject(value, DataMod.class);
    }

    public void updateData(long userId, DataMod data) {
        redisService.set(this.getKey(userId), JSONObject.toJSONString(data));
        redisService.expireKey(this.getKey(userId), DateUtils.DAY_30_SECONDS, TimeUnit.SECONDS);
    }

    public void updateWearEquip(long userId, List<EquipData> equips) {
        DataMod dataModel = this.getData(userId);
        if (dataModel == null) {
            return;
        }

        dataModel.setWearEquips(equips);
        this.updateData(userId, dataModel);

        cal(dataModel);
    }

    public void updateHero(long userId, Hero hero) {
        DataMod dataModel = this.getData(userId);
        if (dataModel == null) {
            return;
        }

        if (!dataModel.getFormationHeroes().removeIf(v -> v.getHeroId() == hero.getHeroId())) {
            return;
        }
        dataModel.getFormationHeroes().add(CommonHelper.buildHeroData(hero));

        this.updateData(userId, dataModel);
        cal(dataModel);
    }

    public void updateFormationHero(long userId, Map<Integer, List<Long>> formationHeroIds, List<HeroData> heroes) {
        DataMod dataModel = this.getData(userId);
        if (dataModel == null) {
            return;
        }

        dataModel.setFormationHeroes(heroes);
        dataModel.setFormationHeroIds(formationHeroIds);

        this.updateData(userId, dataModel);

        cal(dataModel);
    }

    public void updateGuildTechLv(long userId, int lv) {
        DataMod dataModel = this.getData(userId);
        if (dataModel == null) {
            return;
        }

        dataModel.setGuildTechLv(lv);

        this.updateData(userId, dataModel);
        cal(dataModel);
    }

    /**
     * TODO 本地计算战力OR属性
     */
    public long cal(DataMod data) {
        return 0L;
    }

    private String getKey(long userId) {
        return RedisKeys.USER_BATTLE_DATA + userId;
    }
}
