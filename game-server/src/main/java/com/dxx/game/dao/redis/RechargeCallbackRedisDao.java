package com.dxx.game.dao.redis;

import com.dxx.game.common.redis.RedisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.concurrent.TimeUnit;

@Repository
public class RechargeCallbackRedisDao {
    @Autowired
    private RedisService redisService;

    /**
     * 设置preOrderId已经成功callback了，记录渠道orderId到redis，供客户端查询用。
     *
     * @param userId
     * @param preOrderId
     * @param channelOrderId
     */
    public void setCallBack(long userId, String preOrderId, String channelOrderId) {
        String key = this.getKey(userId, preOrderId);
        redisService.set(key, channelOrderId);
        // 内购结果保留1小时
        redisService.expireKey(key, 3600, TimeUnit.SECONDS);
    }

    /**
     * 根据preOrderId获取渠道商orderId，如果不为null，则说明已经callback成功了。
     *
     * @param userId
     * @param preOrderId
     * @return
     */
    public String getChannelOrderId(long userId, String preOrderId) {
        String key = this.getKey(userId, preOrderId);
        return redisService.get(key);
    }


    private String getKey(long userId, String preOrderId) {
        return "recharge_callback:" + userId + ":" + preOrderId;
    }
}
