package com.dxx.game.dao.redis;

import com.dxx.game.common.redis.RedisService;
import com.dxx.game.common.utils.DateUtils;
import com.dxx.game.consts.RedisKeys;
import com.dxx.game.modules.server.ServerListConstants;
import com.dxx.game.modules.server.info.ServerInfo;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.core.RedisOperations;
import org.springframework.data.redis.core.SessionCallback;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/8/28 21:13
 */
@Repository
public class ServerInfoRedisDao {
    public static final String OPEN_TIME = "openTime";
    public static final String COUNT = "count";
    public static final String STATUS = "status";
    @Resource
    private RedisService redisService;
    public static final String CUR_SERVER = "cur";


    public String getKey(int serverId) {
        return RedisKeys.SERVER_INFO + serverId;
    }

    public String getZoneKey(int zoneId) {
        return RedisKeys.SERVER_ZONE_INFO + zoneId;
    }

    public long getOpenTime(int serverId) {
        return this.redisService.hGetLong(getKey(serverId), OPEN_TIME);
    }

    public ServerInfo getServerInfo(int serverId) {
        var dataMap = redisService.hGetAll(getKey(serverId));
        if (dataMap.isEmpty()) {
            return null;
        }
        return mapServerInfo(serverId, dataMap);
    }

    private static ServerInfo mapServerInfo(int serverId, Map<Object, Object> dataMap) {
        long openTime = Long.parseLong(dataMap.getOrDefault(OPEN_TIME, "0").toString());
        int count = Integer.parseInt(dataMap.getOrDefault(COUNT, "0").toString());
        int status = Integer.parseInt(dataMap.getOrDefault(STATUS, "0").toString());
        return ServerInfo.valueOf(serverId, status, count, openTime);
    }

    public long addCount(int serverId, int delta) {
        return redisService.hIncrBy(getKey(serverId), COUNT, delta);
    }

    public void updateInfo(int serverId, String infoKey, Object value) {
        String key = this.getKey(serverId);
        redisService.hSet(key, infoKey, String.valueOf(value));
    }

    public void updateInfo(ServerInfo serverInfo) {
        var map = Map.of(OPEN_TIME, "" + serverInfo.getOpenTime(), COUNT, "" + serverInfo.getCount(), STATUS, "" + serverInfo.getStatus());
        redisService.hSetAll(getKey(serverInfo.getServerId()), map);
    }

    public int getCurServerId(int zoneId) {
        var serverId = redisService.hGet(getZoneKey(zoneId), CUR_SERVER);
        return serverId == null ? 0 : Integer.parseInt(serverId);
    }

    public void initZone(int serverId, int zoneId) {
        redisService.getRedisTemplate().opsForHash().putIfAbsent(getZoneKey(zoneId), CUR_SERVER, String.valueOf(serverId));
    }

    public void setCurServerId(int serverId, int zoneId) {
        redisService.getRedisTemplate().opsForHash().putIfAbsent(getZoneKey(zoneId), CUR_SERVER, String.valueOf(serverId));
    }

    public boolean tryOpenServer(int serverId, int zoneId) {
        boolean open = redisService.getRedisTemplate().opsForHash().putIfAbsent(getKey(serverId), OPEN_TIME, String.valueOf(DateUtils.getUnixTime()));
        if (open) {
            var maxServerId = getCurServerId(zoneId);
            if (maxServerId < serverId) {
                List<Object> results = redisService.getRedisTemplate().executePipelined(new SessionCallback<>() {
                    @Override
                    public <K, V> Object execute(RedisOperations<K, V> oper) throws DataAccessException {
                        var operations = (RedisOperations<String, String>) oper;
                        operations.opsForHash().put(getZoneKey(zoneId), CUR_SERVER, String.valueOf(serverId));
                        operations.opsForHash().put(getKey(serverId), STATUS, String.valueOf(ServerListConstants.SERVER_STATUS_NEW));
                        return null;
                    }
                });
            }
        }
        return open;
    }

    public List<ServerInfo> getServerList(List<Integer> serverList) {
        var list = new ArrayList<ServerInfo>();
        List<Object> results = redisService.getRedisTemplate().executePipelined(new SessionCallback<>() {
            @Override
            public <K, V> Object execute(RedisOperations<K, V> oper) throws DataAccessException {
                var operations = (RedisOperations<String, String>) oper;
                for (Integer serverId : serverList) {
                    operations.opsForHash().entries(getKey(serverId));
                }
                return null;
            }
        });
        //TODO redis没有从数据库拿
        for (int i = 0; i < serverList.size(); i++) {
            list.add(mapServerInfo(serverList.get(i), (Map<Object, Object>) results.get(i)));
        }
        return list;
    }


}
