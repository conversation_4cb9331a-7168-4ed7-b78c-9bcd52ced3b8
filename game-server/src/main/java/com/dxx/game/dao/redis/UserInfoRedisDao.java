package com.dxx.game.dao.redis;

import com.dxx.game.common.redis.RedisService;
import com.dxx.game.common.utils.DateUtils;
import com.dxx.game.consts.RedisKeys;
import com.dxx.game.dao.dynamodb.model.User;
import com.dxx.game.modules.user.model.UserInfoModel;
import com.google.common.primitives.Longs;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 玩家redis中的冗余信息，用于排行榜获取等。
 */
@Repository
public class UserInfoRedisDao {

    @Resource
    private RedisService redisService;

    public static final String NICK_NAME = "nickName";
    public static final String AVATAR = "avatar";
    public static final String AVATAR_FRAME = "avatarFrame";
    public static final String LEVEL = "level";
    public static final String ACTIVE_TM = "activeTM";
    public static final String FAKE_LEVEL_ID = "fakeLevelId";
    public static final String POWER = "power";
    public static final String GUILD_ID = "guildId";
    public static final String SERVER_ID = "serverId";

    public void updateNickName(long userId, String nickName) {
        String key = this.getKey(userId);
        redisService.hSet(key, NICK_NAME, nickName);
        this.expireKey(key);
    }

    public void updateAvatar(long userId, int avatar) {
        String key = this.getKey(userId);
        redisService.hSet(key, AVATAR, String.valueOf(avatar));
        this.expireKey(key);
    }

    public void updateAvatarFrame(long userId, int avatarFrame) {
        String key = this.getKey(userId);
        redisService.hSet(key, AVATAR_FRAME, String.valueOf(avatarFrame));
        this.expireKey(key);
    }

    public void updateLevel(long userId, int level) {
        String key = this.getKey(userId);
        redisService.hSet(key, LEVEL, String.valueOf(level));
        this.expireKey(key);
    }

    public void updateActiveTM(long userId) {
        String key = this.getKey(userId);
        redisService.hSet(key, ACTIVE_TM, String.valueOf(DateUtils.getUnixTime()));
        this.expireKey(key);
    }

    public long getActiveTM(long userId) {
        String key = this.getKey(userId);
        String timeStr = redisService.hGet(key, ACTIVE_TM);
        if (timeStr == null) {
            return 0;
        }
        return Longs.tryParse(timeStr);
    }

    public void updateFakeLevelId(long userId, int fakeLevelId) {
        String key = this.getKey(userId);
        redisService.hSet(key, FAKE_LEVEL_ID, String.valueOf(fakeLevelId));
        this.expireKey(key);
    }

    public void updateInfo(User user, int fakeLevelId) {
        String key = this.getKey(user.getUserId());
        Map<String, String> values = new HashMap<>();
        values.put(NICK_NAME, user.getNickName() == null ? "" : user.getNickName());
        values.put(AVATAR, user.getAvatar() == null ? "0" : String.valueOf(user.getAvatar()));
        values.put(AVATAR_FRAME, user.getAvatarFrame() == null ? "0" : String.valueOf(user.getAvatarFrame()));
        values.put(LEVEL, String.valueOf(user.getLevel()));
        values.put(ACTIVE_TM, String.valueOf(user.getLoginTimestamp()));
        values.put(FAKE_LEVEL_ID, String.valueOf(fakeLevelId));
        values.put(POWER, String.valueOf(user.getPower()));
        values.put(SERVER_ID, String.valueOf(user.getServerId() == null ? 0 : user.getServerId()));
        redisService.hSetAll(key, values);
        this.expireKey(key);
    }

    public void updateInfo(long userId, UserInfoModel model) {
        String key = this.getKey(userId);
        Map<String, String> values = new HashMap<>();
        values.put(NICK_NAME, model.getNickName());
        values.put(AVATAR, String.valueOf(model.getAvatar()));
        values.put(AVATAR_FRAME, String.valueOf(model.getAvatarFrame()));
        values.put(LEVEL, String.valueOf(model.getLevel()));
        values.put(ACTIVE_TM, String.valueOf(model.getActiveTM()));
        values.put(FAKE_LEVEL_ID, String.valueOf(model.getFakeLevelId()));
        values.put(POWER, String.valueOf(model.getPower()));
        values.put(SERVER_ID, String.valueOf(model.getServerId()));
        redisService.hSetAll(key, values);

        this.expireKey(key);
    }

    private void expireKey(String key) {
        redisService.expireKey(key, DateUtils.DAY_30_SECONDS, TimeUnit.SECONDS);
    }

    public String getKey(long userId) {
        return RedisKeys.USER_INFO + userId;
    }

    public void updatePower(long userId, long power) {
        String key = this.getKey(userId);
        redisService.hSet(key, POWER, String.valueOf(power));
        this.expireKey(key);
    }

    public long getPower(long userId) {
        String key = this.getKey(userId);
        String value = redisService.hGet(key, POWER);
        if (StringUtils.isEmpty(value)) {
            return 0;
        }
        return Long.parseLong(value);
    }

    public void deleteGuild(long userId) {
        updateGuild(userId, 0);
    }

    public void updateGuild(long userId, long guildId) {
        String key = this.getKey(userId);
        if (guildId <= 0) {
            redisService.hDel(key, GUILD_ID);
        } else {
            redisService.hSet(key, GUILD_ID, guildId + "");
            this.expireKey(key);
        }
    }
}
