package com.dxx.game.dao.redis;

import com.dxx.game.common.redis.RedisService;
import com.dxx.game.common.server.util.MsgSeqUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Base64;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/4/17 16:06
 */
@Repository
@Slf4j
public class UserRequestRedisDao {
    // 默认3分钟过期时间
    private static final int DEFAULT_EXPIRE_SECONDS = 180;
    // transId 过期时间 15天
    private static final int TRANS_ID_EXPIRE_SECONDS = 1296000;

    @Autowired
    private RedisService redisService;

    /**
     * seq 自增
     * <p>
     * 只有客户端发的比客户端小的情况，说明玩家数据被多端操作，所以会increa一个给客户端，让客户端用新的
     *
     * @return
     */
    public long increaseMsgSeq(long userId, int addValue) {
        String redisKey = MsgSeqUtil.getMsgSeqRedisKey(userId);
        long value = redisService.incrBy(redisKey, addValue);
        redisService.expireKey(redisKey, TRANS_ID_EXPIRE_SECONDS, TimeUnit.SECONDS);
        return value;
    }

    public byte[] getResponse(long userId, short cmd, String requestId) {
        String redisKey = MsgSeqUtil.getRequestIdRedisKey(userId, cmd, requestId);
        var responseString = redisService.get(redisKey);
        if (StringUtils.isNotEmpty(responseString)) {
            return Base64.getDecoder().decode(responseString);
        }
        return null;
    }

    public void saveResponse(long userId, short cmd, String requestId, byte[] response) {
        var responseString = Base64.getEncoder().encodeToString(response);
        if (response.length > 102400) {
            log.error("saveResponse result bigger than 100k on CMD:{} requestId :{},content :{}", cmd, requestId, responseString);
        }
        String redisKey = MsgSeqUtil.getRequestIdRedisKey(userId, cmd, requestId);
        redisService.set(redisKey, responseString, DEFAULT_EXPIRE_SECONDS);
    }
}
