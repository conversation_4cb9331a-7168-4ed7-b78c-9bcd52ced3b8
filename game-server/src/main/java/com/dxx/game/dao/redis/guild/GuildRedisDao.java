package com.dxx.game.dao.redis.guild;

import com.dxx.game.common.redis.RedisService;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

/**
 * @authoer: lsc
 * @createDate: 2023/3/25
 * @description:
 */
@Repository
public class GuildRedisDao {

    private static final String GUILD_MEMBER_KEY = "guild_member:";

    @Resource
    private RedisService redisService;

    // 增加公会成员数量
    public int incrGuildMemberCount(long guildId, int count) {
        String key = this.getGuildMemberKey(guildId);
        return (int)redisService.incrBy(key, count);
    }
    // 更新公会成员数量
    public void updateGuildMemberCount(long guildId, int count) {
        String key = this.getGuildMemberKey(guildId);
        redisService.set(key, count);
    }
    // 移除公会数量
    public void removeGuildMember(long guildId) {
        String key = this.getGuildMemberKey(guildId);
        redisService.deleteKey(key);
    }



    private String getGuildMemberKey(long guild) {
        return GUILD_MEMBER_KEY + guild;
    }

}








