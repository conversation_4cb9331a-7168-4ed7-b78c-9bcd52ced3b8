// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: activity.proto
// Protobuf Java Version: 4.29.0

package com.dxx.game.dto;

public final class ActivityProto {
  private ActivityProto() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 29,
      /* patch= */ 0,
      /* suffix= */ "",
      ActivityProto.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ActivityGetListRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Activity.ActivityGetListRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();
  }
  /**
   * <pre>
   * CMD PackageId=11401 活动-获取所有活动数据(登录时调用)
   * </pre>
   *
   * Protobuf type {@code Proto.Activity.ActivityGetListRequest}
   */
  public static final class ActivityGetListRequest extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Activity.ActivityGetListRequest)
      ActivityGetListRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        ActivityGetListRequest.class.getName());
    }
    // Use ActivityGetListRequest.newBuilder() to construct.
    private ActivityGetListRequest(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private ActivityGetListRequest() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.ActivityProto.internal_static_Proto_Activity_ActivityGetListRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.ActivityProto.internal_static_Proto_Activity_ActivityGetListRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.ActivityProto.ActivityGetListRequest.class, com.dxx.game.dto.ActivityProto.ActivityGetListRequest.Builder.class);
    }

    private int bitField0_;
    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getCommonParams());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.ActivityProto.ActivityGetListRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.ActivityProto.ActivityGetListRequest other = (com.dxx.game.dto.ActivityProto.ActivityGetListRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.ActivityProto.ActivityGetListRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ActivityProto.ActivityGetListRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ActivityProto.ActivityGetListRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ActivityProto.ActivityGetListRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ActivityProto.ActivityGetListRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ActivityProto.ActivityGetListRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ActivityProto.ActivityGetListRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ActivityProto.ActivityGetListRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.ActivityProto.ActivityGetListRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.ActivityProto.ActivityGetListRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.ActivityProto.ActivityGetListRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ActivityProto.ActivityGetListRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.ActivityProto.ActivityGetListRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=11401 活动-获取所有活动数据(登录时调用)
     * </pre>
     *
     * Protobuf type {@code Proto.Activity.ActivityGetListRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Activity.ActivityGetListRequest)
        com.dxx.game.dto.ActivityProto.ActivityGetListRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.ActivityProto.internal_static_Proto_Activity_ActivityGetListRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.ActivityProto.internal_static_Proto_Activity_ActivityGetListRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.ActivityProto.ActivityGetListRequest.class, com.dxx.game.dto.ActivityProto.ActivityGetListRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.ActivityProto.ActivityGetListRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getCommonParamsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.ActivityProto.internal_static_Proto_Activity_ActivityGetListRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.ActivityProto.ActivityGetListRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.ActivityProto.ActivityGetListRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.ActivityProto.ActivityGetListRequest build() {
        com.dxx.game.dto.ActivityProto.ActivityGetListRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.ActivityProto.ActivityGetListRequest buildPartial() {
        com.dxx.game.dto.ActivityProto.ActivityGetListRequest result = new com.dxx.game.dto.ActivityProto.ActivityGetListRequest(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.ActivityProto.ActivityGetListRequest result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.commonParams_ = commonParamsBuilder_ == null
              ? commonParams_
              : commonParamsBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.ActivityProto.ActivityGetListRequest) {
          return mergeFrom((com.dxx.game.dto.ActivityProto.ActivityGetListRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.ActivityProto.ActivityGetListRequest other) {
        if (other == com.dxx.game.dto.ActivityProto.ActivityGetListRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getCommonParamsFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
        } else {
          commonParamsBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
            commonParams_ != null &&
            commonParams_ != com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance()) {
            getCommonParamsBuilder().mergeFrom(value);
          } else {
            commonParams_ = value;
          }
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }
        if (commonParams_ != null) {
          bitField0_ |= 0x00000001;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        bitField0_ = (bitField0_ & ~0x00000001);
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Activity.ActivityGetListRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.Activity.ActivityGetListRequest)
    private static final com.dxx.game.dto.ActivityProto.ActivityGetListRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.ActivityProto.ActivityGetListRequest();
    }

    public static com.dxx.game.dto.ActivityProto.ActivityGetListRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ActivityGetListRequest>
        PARSER = new com.google.protobuf.AbstractParser<ActivityGetListRequest>() {
      @java.lang.Override
      public ActivityGetListRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<ActivityGetListRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ActivityGetListRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.ActivityProto.ActivityGetListRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ActivityGetListResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Activity.ActivityGetListResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <pre>
     * 签到数据
     * </pre>
     *
     * <code>.Proto.SignIn.SignInData signInData = 2;</code>
     * @return Whether the signInData field is set.
     */
    boolean hasSignInData();
    /**
     * <pre>
     * 签到数据
     * </pre>
     *
     * <code>.Proto.SignIn.SignInData signInData = 2;</code>
     * @return The signInData.
     */
    com.dxx.game.dto.SignInProto.SignInData getSignInData();
    /**
     * <pre>
     * 签到数据
     * </pre>
     *
     * <code>.Proto.SignIn.SignInData signInData = 2;</code>
     */
    com.dxx.game.dto.SignInProto.SignInDataOrBuilder getSignInDataOrBuilder();

    /**
     * <pre>
     * 消耗活动数据
     * </pre>
     *
     * <code>repeated .Proto.Common.Consume consumeData = 3;</code>
     */
    java.util.List<com.dxx.game.dto.CommonProto.Consume> 
        getConsumeDataList();
    /**
     * <pre>
     * 消耗活动数据
     * </pre>
     *
     * <code>repeated .Proto.Common.Consume consumeData = 3;</code>
     */
    com.dxx.game.dto.CommonProto.Consume getConsumeData(int index);
    /**
     * <pre>
     * 消耗活动数据
     * </pre>
     *
     * <code>repeated .Proto.Common.Consume consumeData = 3;</code>
     */
    int getConsumeDataCount();
    /**
     * <pre>
     * 消耗活动数据
     * </pre>
     *
     * <code>repeated .Proto.Common.Consume consumeData = 3;</code>
     */
    java.util.List<? extends com.dxx.game.dto.CommonProto.ConsumeOrBuilder> 
        getConsumeDataOrBuilderList();
    /**
     * <pre>
     * 消耗活动数据
     * </pre>
     *
     * <code>repeated .Proto.Common.Consume consumeData = 3;</code>
     */
    com.dxx.game.dto.CommonProto.ConsumeOrBuilder getConsumeDataOrBuilder(
        int index);

    /**
     * <pre>
     * 商城活动数据
     * </pre>
     *
     * <code>repeated .Proto.Common.Shop shopData = 4;</code>
     */
    java.util.List<com.dxx.game.dto.CommonProto.Shop> 
        getShopDataList();
    /**
     * <pre>
     * 商城活动数据
     * </pre>
     *
     * <code>repeated .Proto.Common.Shop shopData = 4;</code>
     */
    com.dxx.game.dto.CommonProto.Shop getShopData(int index);
    /**
     * <pre>
     * 商城活动数据
     * </pre>
     *
     * <code>repeated .Proto.Common.Shop shopData = 4;</code>
     */
    int getShopDataCount();
    /**
     * <pre>
     * 商城活动数据
     * </pre>
     *
     * <code>repeated .Proto.Common.Shop shopData = 4;</code>
     */
    java.util.List<? extends com.dxx.game.dto.CommonProto.ShopOrBuilder> 
        getShopDataOrBuilderList();
    /**
     * <pre>
     * 商城活动数据
     * </pre>
     *
     * <code>repeated .Proto.Common.Shop shopData = 4;</code>
     */
    com.dxx.game.dto.CommonProto.ShopOrBuilder getShopDataOrBuilder(
        int index);

    /**
     * <pre>
     * 掉落活动数据
     * </pre>
     *
     * <code>repeated .Proto.Common.Drop dropData = 5;</code>
     */
    java.util.List<com.dxx.game.dto.CommonProto.Drop> 
        getDropDataList();
    /**
     * <pre>
     * 掉落活动数据
     * </pre>
     *
     * <code>repeated .Proto.Common.Drop dropData = 5;</code>
     */
    com.dxx.game.dto.CommonProto.Drop getDropData(int index);
    /**
     * <pre>
     * 掉落活动数据
     * </pre>
     *
     * <code>repeated .Proto.Common.Drop dropData = 5;</code>
     */
    int getDropDataCount();
    /**
     * <pre>
     * 掉落活动数据
     * </pre>
     *
     * <code>repeated .Proto.Common.Drop dropData = 5;</code>
     */
    java.util.List<? extends com.dxx.game.dto.CommonProto.DropOrBuilder> 
        getDropDataOrBuilderList();
    /**
     * <pre>
     * 掉落活动数据
     * </pre>
     *
     * <code>repeated .Proto.Common.Drop dropData = 5;</code>
     */
    com.dxx.game.dto.CommonProto.DropOrBuilder getDropDataOrBuilder(
        int index);

    /**
     * <pre>
     * 付费活动数据
     * </pre>
     *
     * <code>repeated .Proto.Common.Pay payData = 6;</code>
     */
    java.util.List<com.dxx.game.dto.CommonProto.Pay> 
        getPayDataList();
    /**
     * <pre>
     * 付费活动数据
     * </pre>
     *
     * <code>repeated .Proto.Common.Pay payData = 6;</code>
     */
    com.dxx.game.dto.CommonProto.Pay getPayData(int index);
    /**
     * <pre>
     * 付费活动数据
     * </pre>
     *
     * <code>repeated .Proto.Common.Pay payData = 6;</code>
     */
    int getPayDataCount();
    /**
     * <pre>
     * 付费活动数据
     * </pre>
     *
     * <code>repeated .Proto.Common.Pay payData = 6;</code>
     */
    java.util.List<? extends com.dxx.game.dto.CommonProto.PayOrBuilder> 
        getPayDataOrBuilderList();
    /**
     * <pre>
     * 付费活动数据
     * </pre>
     *
     * <code>repeated .Proto.Common.Pay payData = 6;</code>
     */
    com.dxx.game.dto.CommonProto.PayOrBuilder getPayDataOrBuilder(
        int index);

    /**
     * <pre>
     * 章节数据
     * </pre>
     *
     * <code>repeated .Proto.Common.Chapter chapter = 7;</code>
     */
    java.util.List<com.dxx.game.dto.CommonProto.Chapter> 
        getChapterList();
    /**
     * <pre>
     * 章节数据
     * </pre>
     *
     * <code>repeated .Proto.Common.Chapter chapter = 7;</code>
     */
    com.dxx.game.dto.CommonProto.Chapter getChapter(int index);
    /**
     * <pre>
     * 章节数据
     * </pre>
     *
     * <code>repeated .Proto.Common.Chapter chapter = 7;</code>
     */
    int getChapterCount();
    /**
     * <pre>
     * 章节数据
     * </pre>
     *
     * <code>repeated .Proto.Common.Chapter chapter = 7;</code>
     */
    java.util.List<? extends com.dxx.game.dto.CommonProto.ChapterOrBuilder> 
        getChapterOrBuilderList();
    /**
     * <pre>
     * 章节数据
     * </pre>
     *
     * <code>repeated .Proto.Common.Chapter chapter = 7;</code>
     */
    com.dxx.game.dto.CommonProto.ChapterOrBuilder getChapterOrBuilder(
        int index);
  }
  /**
   * <pre>
   * CMD PackageId=11402 
   * </pre>
   *
   * Protobuf type {@code Proto.Activity.ActivityGetListResponse}
   */
  public static final class ActivityGetListResponse extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Activity.ActivityGetListResponse)
      ActivityGetListResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        ActivityGetListResponse.class.getName());
    }
    // Use ActivityGetListResponse.newBuilder() to construct.
    private ActivityGetListResponse(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private ActivityGetListResponse() {
      consumeData_ = java.util.Collections.emptyList();
      shopData_ = java.util.Collections.emptyList();
      dropData_ = java.util.Collections.emptyList();
      payData_ = java.util.Collections.emptyList();
      chapter_ = java.util.Collections.emptyList();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.ActivityProto.internal_static_Proto_Activity_ActivityGetListResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.ActivityProto.internal_static_Proto_Activity_ActivityGetListResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.ActivityProto.ActivityGetListResponse.class, com.dxx.game.dto.ActivityProto.ActivityGetListResponse.Builder.class);
    }

    private int bitField0_;
    public static final int CODE_FIELD_NUMBER = 1;
    private int code_ = 0;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int SIGNINDATA_FIELD_NUMBER = 2;
    private com.dxx.game.dto.SignInProto.SignInData signInData_;
    /**
     * <pre>
     * 签到数据
     * </pre>
     *
     * <code>.Proto.SignIn.SignInData signInData = 2;</code>
     * @return Whether the signInData field is set.
     */
    @java.lang.Override
    public boolean hasSignInData() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 签到数据
     * </pre>
     *
     * <code>.Proto.SignIn.SignInData signInData = 2;</code>
     * @return The signInData.
     */
    @java.lang.Override
    public com.dxx.game.dto.SignInProto.SignInData getSignInData() {
      return signInData_ == null ? com.dxx.game.dto.SignInProto.SignInData.getDefaultInstance() : signInData_;
    }
    /**
     * <pre>
     * 签到数据
     * </pre>
     *
     * <code>.Proto.SignIn.SignInData signInData = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.SignInProto.SignInDataOrBuilder getSignInDataOrBuilder() {
      return signInData_ == null ? com.dxx.game.dto.SignInProto.SignInData.getDefaultInstance() : signInData_;
    }

    public static final int CONSUMEDATA_FIELD_NUMBER = 3;
    @SuppressWarnings("serial")
    private java.util.List<com.dxx.game.dto.CommonProto.Consume> consumeData_;
    /**
     * <pre>
     * 消耗活动数据
     * </pre>
     *
     * <code>repeated .Proto.Common.Consume consumeData = 3;</code>
     */
    @java.lang.Override
    public java.util.List<com.dxx.game.dto.CommonProto.Consume> getConsumeDataList() {
      return consumeData_;
    }
    /**
     * <pre>
     * 消耗活动数据
     * </pre>
     *
     * <code>repeated .Proto.Common.Consume consumeData = 3;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.dxx.game.dto.CommonProto.ConsumeOrBuilder> 
        getConsumeDataOrBuilderList() {
      return consumeData_;
    }
    /**
     * <pre>
     * 消耗活动数据
     * </pre>
     *
     * <code>repeated .Proto.Common.Consume consumeData = 3;</code>
     */
    @java.lang.Override
    public int getConsumeDataCount() {
      return consumeData_.size();
    }
    /**
     * <pre>
     * 消耗活动数据
     * </pre>
     *
     * <code>repeated .Proto.Common.Consume consumeData = 3;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.Consume getConsumeData(int index) {
      return consumeData_.get(index);
    }
    /**
     * <pre>
     * 消耗活动数据
     * </pre>
     *
     * <code>repeated .Proto.Common.Consume consumeData = 3;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.ConsumeOrBuilder getConsumeDataOrBuilder(
        int index) {
      return consumeData_.get(index);
    }

    public static final int SHOPDATA_FIELD_NUMBER = 4;
    @SuppressWarnings("serial")
    private java.util.List<com.dxx.game.dto.CommonProto.Shop> shopData_;
    /**
     * <pre>
     * 商城活动数据
     * </pre>
     *
     * <code>repeated .Proto.Common.Shop shopData = 4;</code>
     */
    @java.lang.Override
    public java.util.List<com.dxx.game.dto.CommonProto.Shop> getShopDataList() {
      return shopData_;
    }
    /**
     * <pre>
     * 商城活动数据
     * </pre>
     *
     * <code>repeated .Proto.Common.Shop shopData = 4;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.dxx.game.dto.CommonProto.ShopOrBuilder> 
        getShopDataOrBuilderList() {
      return shopData_;
    }
    /**
     * <pre>
     * 商城活动数据
     * </pre>
     *
     * <code>repeated .Proto.Common.Shop shopData = 4;</code>
     */
    @java.lang.Override
    public int getShopDataCount() {
      return shopData_.size();
    }
    /**
     * <pre>
     * 商城活动数据
     * </pre>
     *
     * <code>repeated .Proto.Common.Shop shopData = 4;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.Shop getShopData(int index) {
      return shopData_.get(index);
    }
    /**
     * <pre>
     * 商城活动数据
     * </pre>
     *
     * <code>repeated .Proto.Common.Shop shopData = 4;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.ShopOrBuilder getShopDataOrBuilder(
        int index) {
      return shopData_.get(index);
    }

    public static final int DROPDATA_FIELD_NUMBER = 5;
    @SuppressWarnings("serial")
    private java.util.List<com.dxx.game.dto.CommonProto.Drop> dropData_;
    /**
     * <pre>
     * 掉落活动数据
     * </pre>
     *
     * <code>repeated .Proto.Common.Drop dropData = 5;</code>
     */
    @java.lang.Override
    public java.util.List<com.dxx.game.dto.CommonProto.Drop> getDropDataList() {
      return dropData_;
    }
    /**
     * <pre>
     * 掉落活动数据
     * </pre>
     *
     * <code>repeated .Proto.Common.Drop dropData = 5;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.dxx.game.dto.CommonProto.DropOrBuilder> 
        getDropDataOrBuilderList() {
      return dropData_;
    }
    /**
     * <pre>
     * 掉落活动数据
     * </pre>
     *
     * <code>repeated .Proto.Common.Drop dropData = 5;</code>
     */
    @java.lang.Override
    public int getDropDataCount() {
      return dropData_.size();
    }
    /**
     * <pre>
     * 掉落活动数据
     * </pre>
     *
     * <code>repeated .Proto.Common.Drop dropData = 5;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.Drop getDropData(int index) {
      return dropData_.get(index);
    }
    /**
     * <pre>
     * 掉落活动数据
     * </pre>
     *
     * <code>repeated .Proto.Common.Drop dropData = 5;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.DropOrBuilder getDropDataOrBuilder(
        int index) {
      return dropData_.get(index);
    }

    public static final int PAYDATA_FIELD_NUMBER = 6;
    @SuppressWarnings("serial")
    private java.util.List<com.dxx.game.dto.CommonProto.Pay> payData_;
    /**
     * <pre>
     * 付费活动数据
     * </pre>
     *
     * <code>repeated .Proto.Common.Pay payData = 6;</code>
     */
    @java.lang.Override
    public java.util.List<com.dxx.game.dto.CommonProto.Pay> getPayDataList() {
      return payData_;
    }
    /**
     * <pre>
     * 付费活动数据
     * </pre>
     *
     * <code>repeated .Proto.Common.Pay payData = 6;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.dxx.game.dto.CommonProto.PayOrBuilder> 
        getPayDataOrBuilderList() {
      return payData_;
    }
    /**
     * <pre>
     * 付费活动数据
     * </pre>
     *
     * <code>repeated .Proto.Common.Pay payData = 6;</code>
     */
    @java.lang.Override
    public int getPayDataCount() {
      return payData_.size();
    }
    /**
     * <pre>
     * 付费活动数据
     * </pre>
     *
     * <code>repeated .Proto.Common.Pay payData = 6;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.Pay getPayData(int index) {
      return payData_.get(index);
    }
    /**
     * <pre>
     * 付费活动数据
     * </pre>
     *
     * <code>repeated .Proto.Common.Pay payData = 6;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.PayOrBuilder getPayDataOrBuilder(
        int index) {
      return payData_.get(index);
    }

    public static final int CHAPTER_FIELD_NUMBER = 7;
    @SuppressWarnings("serial")
    private java.util.List<com.dxx.game.dto.CommonProto.Chapter> chapter_;
    /**
     * <pre>
     * 章节数据
     * </pre>
     *
     * <code>repeated .Proto.Common.Chapter chapter = 7;</code>
     */
    @java.lang.Override
    public java.util.List<com.dxx.game.dto.CommonProto.Chapter> getChapterList() {
      return chapter_;
    }
    /**
     * <pre>
     * 章节数据
     * </pre>
     *
     * <code>repeated .Proto.Common.Chapter chapter = 7;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.dxx.game.dto.CommonProto.ChapterOrBuilder> 
        getChapterOrBuilderList() {
      return chapter_;
    }
    /**
     * <pre>
     * 章节数据
     * </pre>
     *
     * <code>repeated .Proto.Common.Chapter chapter = 7;</code>
     */
    @java.lang.Override
    public int getChapterCount() {
      return chapter_.size();
    }
    /**
     * <pre>
     * 章节数据
     * </pre>
     *
     * <code>repeated .Proto.Common.Chapter chapter = 7;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.Chapter getChapter(int index) {
      return chapter_.get(index);
    }
    /**
     * <pre>
     * 章节数据
     * </pre>
     *
     * <code>repeated .Proto.Common.Chapter chapter = 7;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.ChapterOrBuilder getChapterOrBuilder(
        int index) {
      return chapter_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(2, getSignInData());
      }
      for (int i = 0; i < consumeData_.size(); i++) {
        output.writeMessage(3, consumeData_.get(i));
      }
      for (int i = 0; i < shopData_.size(); i++) {
        output.writeMessage(4, shopData_.get(i));
      }
      for (int i = 0; i < dropData_.size(); i++) {
        output.writeMessage(5, dropData_.get(i));
      }
      for (int i = 0; i < payData_.size(); i++) {
        output.writeMessage(6, payData_.get(i));
      }
      for (int i = 0; i < chapter_.size(); i++) {
        output.writeMessage(7, chapter_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getSignInData());
      }
      for (int i = 0; i < consumeData_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, consumeData_.get(i));
      }
      for (int i = 0; i < shopData_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, shopData_.get(i));
      }
      for (int i = 0; i < dropData_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, dropData_.get(i));
      }
      for (int i = 0; i < payData_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(6, payData_.get(i));
      }
      for (int i = 0; i < chapter_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(7, chapter_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.ActivityProto.ActivityGetListResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.ActivityProto.ActivityGetListResponse other = (com.dxx.game.dto.ActivityProto.ActivityGetListResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (hasSignInData() != other.hasSignInData()) return false;
      if (hasSignInData()) {
        if (!getSignInData()
            .equals(other.getSignInData())) return false;
      }
      if (!getConsumeDataList()
          .equals(other.getConsumeDataList())) return false;
      if (!getShopDataList()
          .equals(other.getShopDataList())) return false;
      if (!getDropDataList()
          .equals(other.getDropDataList())) return false;
      if (!getPayDataList()
          .equals(other.getPayDataList())) return false;
      if (!getChapterList()
          .equals(other.getChapterList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      if (hasSignInData()) {
        hash = (37 * hash) + SIGNINDATA_FIELD_NUMBER;
        hash = (53 * hash) + getSignInData().hashCode();
      }
      if (getConsumeDataCount() > 0) {
        hash = (37 * hash) + CONSUMEDATA_FIELD_NUMBER;
        hash = (53 * hash) + getConsumeDataList().hashCode();
      }
      if (getShopDataCount() > 0) {
        hash = (37 * hash) + SHOPDATA_FIELD_NUMBER;
        hash = (53 * hash) + getShopDataList().hashCode();
      }
      if (getDropDataCount() > 0) {
        hash = (37 * hash) + DROPDATA_FIELD_NUMBER;
        hash = (53 * hash) + getDropDataList().hashCode();
      }
      if (getPayDataCount() > 0) {
        hash = (37 * hash) + PAYDATA_FIELD_NUMBER;
        hash = (53 * hash) + getPayDataList().hashCode();
      }
      if (getChapterCount() > 0) {
        hash = (37 * hash) + CHAPTER_FIELD_NUMBER;
        hash = (53 * hash) + getChapterList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.ActivityProto.ActivityGetListResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ActivityProto.ActivityGetListResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ActivityProto.ActivityGetListResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ActivityProto.ActivityGetListResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ActivityProto.ActivityGetListResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ActivityProto.ActivityGetListResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ActivityProto.ActivityGetListResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ActivityProto.ActivityGetListResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.ActivityProto.ActivityGetListResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.ActivityProto.ActivityGetListResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.ActivityProto.ActivityGetListResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ActivityProto.ActivityGetListResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.ActivityProto.ActivityGetListResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=11402 
     * </pre>
     *
     * Protobuf type {@code Proto.Activity.ActivityGetListResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Activity.ActivityGetListResponse)
        com.dxx.game.dto.ActivityProto.ActivityGetListResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.ActivityProto.internal_static_Proto_Activity_ActivityGetListResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.ActivityProto.internal_static_Proto_Activity_ActivityGetListResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.ActivityProto.ActivityGetListResponse.class, com.dxx.game.dto.ActivityProto.ActivityGetListResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.ActivityProto.ActivityGetListResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getSignInDataFieldBuilder();
          getConsumeDataFieldBuilder();
          getShopDataFieldBuilder();
          getDropDataFieldBuilder();
          getPayDataFieldBuilder();
          getChapterFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        code_ = 0;
        signInData_ = null;
        if (signInDataBuilder_ != null) {
          signInDataBuilder_.dispose();
          signInDataBuilder_ = null;
        }
        if (consumeDataBuilder_ == null) {
          consumeData_ = java.util.Collections.emptyList();
        } else {
          consumeData_ = null;
          consumeDataBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        if (shopDataBuilder_ == null) {
          shopData_ = java.util.Collections.emptyList();
        } else {
          shopData_ = null;
          shopDataBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        if (dropDataBuilder_ == null) {
          dropData_ = java.util.Collections.emptyList();
        } else {
          dropData_ = null;
          dropDataBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000010);
        if (payDataBuilder_ == null) {
          payData_ = java.util.Collections.emptyList();
        } else {
          payData_ = null;
          payDataBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000020);
        if (chapterBuilder_ == null) {
          chapter_ = java.util.Collections.emptyList();
        } else {
          chapter_ = null;
          chapterBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000040);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.ActivityProto.internal_static_Proto_Activity_ActivityGetListResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.ActivityProto.ActivityGetListResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.ActivityProto.ActivityGetListResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.ActivityProto.ActivityGetListResponse build() {
        com.dxx.game.dto.ActivityProto.ActivityGetListResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.ActivityProto.ActivityGetListResponse buildPartial() {
        com.dxx.game.dto.ActivityProto.ActivityGetListResponse result = new com.dxx.game.dto.ActivityProto.ActivityGetListResponse(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(com.dxx.game.dto.ActivityProto.ActivityGetListResponse result) {
        if (consumeDataBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0)) {
            consumeData_ = java.util.Collections.unmodifiableList(consumeData_);
            bitField0_ = (bitField0_ & ~0x00000004);
          }
          result.consumeData_ = consumeData_;
        } else {
          result.consumeData_ = consumeDataBuilder_.build();
        }
        if (shopDataBuilder_ == null) {
          if (((bitField0_ & 0x00000008) != 0)) {
            shopData_ = java.util.Collections.unmodifiableList(shopData_);
            bitField0_ = (bitField0_ & ~0x00000008);
          }
          result.shopData_ = shopData_;
        } else {
          result.shopData_ = shopDataBuilder_.build();
        }
        if (dropDataBuilder_ == null) {
          if (((bitField0_ & 0x00000010) != 0)) {
            dropData_ = java.util.Collections.unmodifiableList(dropData_);
            bitField0_ = (bitField0_ & ~0x00000010);
          }
          result.dropData_ = dropData_;
        } else {
          result.dropData_ = dropDataBuilder_.build();
        }
        if (payDataBuilder_ == null) {
          if (((bitField0_ & 0x00000020) != 0)) {
            payData_ = java.util.Collections.unmodifiableList(payData_);
            bitField0_ = (bitField0_ & ~0x00000020);
          }
          result.payData_ = payData_;
        } else {
          result.payData_ = payDataBuilder_.build();
        }
        if (chapterBuilder_ == null) {
          if (((bitField0_ & 0x00000040) != 0)) {
            chapter_ = java.util.Collections.unmodifiableList(chapter_);
            bitField0_ = (bitField0_ & ~0x00000040);
          }
          result.chapter_ = chapter_;
        } else {
          result.chapter_ = chapterBuilder_.build();
        }
      }

      private void buildPartial0(com.dxx.game.dto.ActivityProto.ActivityGetListResponse result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.code_ = code_;
        }
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.signInData_ = signInDataBuilder_ == null
              ? signInData_
              : signInDataBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.ActivityProto.ActivityGetListResponse) {
          return mergeFrom((com.dxx.game.dto.ActivityProto.ActivityGetListResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.ActivityProto.ActivityGetListResponse other) {
        if (other == com.dxx.game.dto.ActivityProto.ActivityGetListResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.hasSignInData()) {
          mergeSignInData(other.getSignInData());
        }
        if (consumeDataBuilder_ == null) {
          if (!other.consumeData_.isEmpty()) {
            if (consumeData_.isEmpty()) {
              consumeData_ = other.consumeData_;
              bitField0_ = (bitField0_ & ~0x00000004);
            } else {
              ensureConsumeDataIsMutable();
              consumeData_.addAll(other.consumeData_);
            }
            onChanged();
          }
        } else {
          if (!other.consumeData_.isEmpty()) {
            if (consumeDataBuilder_.isEmpty()) {
              consumeDataBuilder_.dispose();
              consumeDataBuilder_ = null;
              consumeData_ = other.consumeData_;
              bitField0_ = (bitField0_ & ~0x00000004);
              consumeDataBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getConsumeDataFieldBuilder() : null;
            } else {
              consumeDataBuilder_.addAllMessages(other.consumeData_);
            }
          }
        }
        if (shopDataBuilder_ == null) {
          if (!other.shopData_.isEmpty()) {
            if (shopData_.isEmpty()) {
              shopData_ = other.shopData_;
              bitField0_ = (bitField0_ & ~0x00000008);
            } else {
              ensureShopDataIsMutable();
              shopData_.addAll(other.shopData_);
            }
            onChanged();
          }
        } else {
          if (!other.shopData_.isEmpty()) {
            if (shopDataBuilder_.isEmpty()) {
              shopDataBuilder_.dispose();
              shopDataBuilder_ = null;
              shopData_ = other.shopData_;
              bitField0_ = (bitField0_ & ~0x00000008);
              shopDataBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getShopDataFieldBuilder() : null;
            } else {
              shopDataBuilder_.addAllMessages(other.shopData_);
            }
          }
        }
        if (dropDataBuilder_ == null) {
          if (!other.dropData_.isEmpty()) {
            if (dropData_.isEmpty()) {
              dropData_ = other.dropData_;
              bitField0_ = (bitField0_ & ~0x00000010);
            } else {
              ensureDropDataIsMutable();
              dropData_.addAll(other.dropData_);
            }
            onChanged();
          }
        } else {
          if (!other.dropData_.isEmpty()) {
            if (dropDataBuilder_.isEmpty()) {
              dropDataBuilder_.dispose();
              dropDataBuilder_ = null;
              dropData_ = other.dropData_;
              bitField0_ = (bitField0_ & ~0x00000010);
              dropDataBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getDropDataFieldBuilder() : null;
            } else {
              dropDataBuilder_.addAllMessages(other.dropData_);
            }
          }
        }
        if (payDataBuilder_ == null) {
          if (!other.payData_.isEmpty()) {
            if (payData_.isEmpty()) {
              payData_ = other.payData_;
              bitField0_ = (bitField0_ & ~0x00000020);
            } else {
              ensurePayDataIsMutable();
              payData_.addAll(other.payData_);
            }
            onChanged();
          }
        } else {
          if (!other.payData_.isEmpty()) {
            if (payDataBuilder_.isEmpty()) {
              payDataBuilder_.dispose();
              payDataBuilder_ = null;
              payData_ = other.payData_;
              bitField0_ = (bitField0_ & ~0x00000020);
              payDataBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getPayDataFieldBuilder() : null;
            } else {
              payDataBuilder_.addAllMessages(other.payData_);
            }
          }
        }
        if (chapterBuilder_ == null) {
          if (!other.chapter_.isEmpty()) {
            if (chapter_.isEmpty()) {
              chapter_ = other.chapter_;
              bitField0_ = (bitField0_ & ~0x00000040);
            } else {
              ensureChapterIsMutable();
              chapter_.addAll(other.chapter_);
            }
            onChanged();
          }
        } else {
          if (!other.chapter_.isEmpty()) {
            if (chapterBuilder_.isEmpty()) {
              chapterBuilder_.dispose();
              chapterBuilder_ = null;
              chapter_ = other.chapter_;
              bitField0_ = (bitField0_ & ~0x00000040);
              chapterBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getChapterFieldBuilder() : null;
            } else {
              chapterBuilder_.addAllMessages(other.chapter_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                code_ = input.readInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 18: {
                input.readMessage(
                    getSignInDataFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              case 26: {
                com.dxx.game.dto.CommonProto.Consume m =
                    input.readMessage(
                        com.dxx.game.dto.CommonProto.Consume.parser(),
                        extensionRegistry);
                if (consumeDataBuilder_ == null) {
                  ensureConsumeDataIsMutable();
                  consumeData_.add(m);
                } else {
                  consumeDataBuilder_.addMessage(m);
                }
                break;
              } // case 26
              case 34: {
                com.dxx.game.dto.CommonProto.Shop m =
                    input.readMessage(
                        com.dxx.game.dto.CommonProto.Shop.parser(),
                        extensionRegistry);
                if (shopDataBuilder_ == null) {
                  ensureShopDataIsMutable();
                  shopData_.add(m);
                } else {
                  shopDataBuilder_.addMessage(m);
                }
                break;
              } // case 34
              case 42: {
                com.dxx.game.dto.CommonProto.Drop m =
                    input.readMessage(
                        com.dxx.game.dto.CommonProto.Drop.parser(),
                        extensionRegistry);
                if (dropDataBuilder_ == null) {
                  ensureDropDataIsMutable();
                  dropData_.add(m);
                } else {
                  dropDataBuilder_.addMessage(m);
                }
                break;
              } // case 42
              case 50: {
                com.dxx.game.dto.CommonProto.Pay m =
                    input.readMessage(
                        com.dxx.game.dto.CommonProto.Pay.parser(),
                        extensionRegistry);
                if (payDataBuilder_ == null) {
                  ensurePayDataIsMutable();
                  payData_.add(m);
                } else {
                  payDataBuilder_.addMessage(m);
                }
                break;
              } // case 50
              case 58: {
                com.dxx.game.dto.CommonProto.Chapter m =
                    input.readMessage(
                        com.dxx.game.dto.CommonProto.Chapter.parser(),
                        extensionRegistry);
                if (chapterBuilder_ == null) {
                  ensureChapterIsMutable();
                  chapter_.add(m);
                } else {
                  chapterBuilder_.addMessage(m);
                }
                break;
              } // case 58
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {

        code_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        bitField0_ = (bitField0_ & ~0x00000001);
        code_ = 0;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.SignInProto.SignInData signInData_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.SignInProto.SignInData, com.dxx.game.dto.SignInProto.SignInData.Builder, com.dxx.game.dto.SignInProto.SignInDataOrBuilder> signInDataBuilder_;
      /**
       * <pre>
       * 签到数据
       * </pre>
       *
       * <code>.Proto.SignIn.SignInData signInData = 2;</code>
       * @return Whether the signInData field is set.
       */
      public boolean hasSignInData() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 签到数据
       * </pre>
       *
       * <code>.Proto.SignIn.SignInData signInData = 2;</code>
       * @return The signInData.
       */
      public com.dxx.game.dto.SignInProto.SignInData getSignInData() {
        if (signInDataBuilder_ == null) {
          return signInData_ == null ? com.dxx.game.dto.SignInProto.SignInData.getDefaultInstance() : signInData_;
        } else {
          return signInDataBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 签到数据
       * </pre>
       *
       * <code>.Proto.SignIn.SignInData signInData = 2;</code>
       */
      public Builder setSignInData(com.dxx.game.dto.SignInProto.SignInData value) {
        if (signInDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          signInData_ = value;
        } else {
          signInDataBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 签到数据
       * </pre>
       *
       * <code>.Proto.SignIn.SignInData signInData = 2;</code>
       */
      public Builder setSignInData(
          com.dxx.game.dto.SignInProto.SignInData.Builder builderForValue) {
        if (signInDataBuilder_ == null) {
          signInData_ = builderForValue.build();
        } else {
          signInDataBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 签到数据
       * </pre>
       *
       * <code>.Proto.SignIn.SignInData signInData = 2;</code>
       */
      public Builder mergeSignInData(com.dxx.game.dto.SignInProto.SignInData value) {
        if (signInDataBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
            signInData_ != null &&
            signInData_ != com.dxx.game.dto.SignInProto.SignInData.getDefaultInstance()) {
            getSignInDataBuilder().mergeFrom(value);
          } else {
            signInData_ = value;
          }
        } else {
          signInDataBuilder_.mergeFrom(value);
        }
        if (signInData_ != null) {
          bitField0_ |= 0x00000002;
          onChanged();
        }
        return this;
      }
      /**
       * <pre>
       * 签到数据
       * </pre>
       *
       * <code>.Proto.SignIn.SignInData signInData = 2;</code>
       */
      public Builder clearSignInData() {
        bitField0_ = (bitField0_ & ~0x00000002);
        signInData_ = null;
        if (signInDataBuilder_ != null) {
          signInDataBuilder_.dispose();
          signInDataBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 签到数据
       * </pre>
       *
       * <code>.Proto.SignIn.SignInData signInData = 2;</code>
       */
      public com.dxx.game.dto.SignInProto.SignInData.Builder getSignInDataBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getSignInDataFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 签到数据
       * </pre>
       *
       * <code>.Proto.SignIn.SignInData signInData = 2;</code>
       */
      public com.dxx.game.dto.SignInProto.SignInDataOrBuilder getSignInDataOrBuilder() {
        if (signInDataBuilder_ != null) {
          return signInDataBuilder_.getMessageOrBuilder();
        } else {
          return signInData_ == null ?
              com.dxx.game.dto.SignInProto.SignInData.getDefaultInstance() : signInData_;
        }
      }
      /**
       * <pre>
       * 签到数据
       * </pre>
       *
       * <code>.Proto.SignIn.SignInData signInData = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.SignInProto.SignInData, com.dxx.game.dto.SignInProto.SignInData.Builder, com.dxx.game.dto.SignInProto.SignInDataOrBuilder> 
          getSignInDataFieldBuilder() {
        if (signInDataBuilder_ == null) {
          signInDataBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.SignInProto.SignInData, com.dxx.game.dto.SignInProto.SignInData.Builder, com.dxx.game.dto.SignInProto.SignInDataOrBuilder>(
                  getSignInData(),
                  getParentForChildren(),
                  isClean());
          signInData_ = null;
        }
        return signInDataBuilder_;
      }

      private java.util.List<com.dxx.game.dto.CommonProto.Consume> consumeData_ =
        java.util.Collections.emptyList();
      private void ensureConsumeDataIsMutable() {
        if (!((bitField0_ & 0x00000004) != 0)) {
          consumeData_ = new java.util.ArrayList<com.dxx.game.dto.CommonProto.Consume>(consumeData_);
          bitField0_ |= 0x00000004;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          com.dxx.game.dto.CommonProto.Consume, com.dxx.game.dto.CommonProto.Consume.Builder, com.dxx.game.dto.CommonProto.ConsumeOrBuilder> consumeDataBuilder_;

      /**
       * <pre>
       * 消耗活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Consume consumeData = 3;</code>
       */
      public java.util.List<com.dxx.game.dto.CommonProto.Consume> getConsumeDataList() {
        if (consumeDataBuilder_ == null) {
          return java.util.Collections.unmodifiableList(consumeData_);
        } else {
          return consumeDataBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 消耗活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Consume consumeData = 3;</code>
       */
      public int getConsumeDataCount() {
        if (consumeDataBuilder_ == null) {
          return consumeData_.size();
        } else {
          return consumeDataBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 消耗活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Consume consumeData = 3;</code>
       */
      public com.dxx.game.dto.CommonProto.Consume getConsumeData(int index) {
        if (consumeDataBuilder_ == null) {
          return consumeData_.get(index);
        } else {
          return consumeDataBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 消耗活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Consume consumeData = 3;</code>
       */
      public Builder setConsumeData(
          int index, com.dxx.game.dto.CommonProto.Consume value) {
        if (consumeDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureConsumeDataIsMutable();
          consumeData_.set(index, value);
          onChanged();
        } else {
          consumeDataBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 消耗活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Consume consumeData = 3;</code>
       */
      public Builder setConsumeData(
          int index, com.dxx.game.dto.CommonProto.Consume.Builder builderForValue) {
        if (consumeDataBuilder_ == null) {
          ensureConsumeDataIsMutable();
          consumeData_.set(index, builderForValue.build());
          onChanged();
        } else {
          consumeDataBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 消耗活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Consume consumeData = 3;</code>
       */
      public Builder addConsumeData(com.dxx.game.dto.CommonProto.Consume value) {
        if (consumeDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureConsumeDataIsMutable();
          consumeData_.add(value);
          onChanged();
        } else {
          consumeDataBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 消耗活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Consume consumeData = 3;</code>
       */
      public Builder addConsumeData(
          int index, com.dxx.game.dto.CommonProto.Consume value) {
        if (consumeDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureConsumeDataIsMutable();
          consumeData_.add(index, value);
          onChanged();
        } else {
          consumeDataBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 消耗活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Consume consumeData = 3;</code>
       */
      public Builder addConsumeData(
          com.dxx.game.dto.CommonProto.Consume.Builder builderForValue) {
        if (consumeDataBuilder_ == null) {
          ensureConsumeDataIsMutable();
          consumeData_.add(builderForValue.build());
          onChanged();
        } else {
          consumeDataBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 消耗活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Consume consumeData = 3;</code>
       */
      public Builder addConsumeData(
          int index, com.dxx.game.dto.CommonProto.Consume.Builder builderForValue) {
        if (consumeDataBuilder_ == null) {
          ensureConsumeDataIsMutable();
          consumeData_.add(index, builderForValue.build());
          onChanged();
        } else {
          consumeDataBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 消耗活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Consume consumeData = 3;</code>
       */
      public Builder addAllConsumeData(
          java.lang.Iterable<? extends com.dxx.game.dto.CommonProto.Consume> values) {
        if (consumeDataBuilder_ == null) {
          ensureConsumeDataIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, consumeData_);
          onChanged();
        } else {
          consumeDataBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 消耗活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Consume consumeData = 3;</code>
       */
      public Builder clearConsumeData() {
        if (consumeDataBuilder_ == null) {
          consumeData_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000004);
          onChanged();
        } else {
          consumeDataBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 消耗活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Consume consumeData = 3;</code>
       */
      public Builder removeConsumeData(int index) {
        if (consumeDataBuilder_ == null) {
          ensureConsumeDataIsMutable();
          consumeData_.remove(index);
          onChanged();
        } else {
          consumeDataBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 消耗活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Consume consumeData = 3;</code>
       */
      public com.dxx.game.dto.CommonProto.Consume.Builder getConsumeDataBuilder(
          int index) {
        return getConsumeDataFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 消耗活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Consume consumeData = 3;</code>
       */
      public com.dxx.game.dto.CommonProto.ConsumeOrBuilder getConsumeDataOrBuilder(
          int index) {
        if (consumeDataBuilder_ == null) {
          return consumeData_.get(index);  } else {
          return consumeDataBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 消耗活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Consume consumeData = 3;</code>
       */
      public java.util.List<? extends com.dxx.game.dto.CommonProto.ConsumeOrBuilder> 
           getConsumeDataOrBuilderList() {
        if (consumeDataBuilder_ != null) {
          return consumeDataBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(consumeData_);
        }
      }
      /**
       * <pre>
       * 消耗活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Consume consumeData = 3;</code>
       */
      public com.dxx.game.dto.CommonProto.Consume.Builder addConsumeDataBuilder() {
        return getConsumeDataFieldBuilder().addBuilder(
            com.dxx.game.dto.CommonProto.Consume.getDefaultInstance());
      }
      /**
       * <pre>
       * 消耗活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Consume consumeData = 3;</code>
       */
      public com.dxx.game.dto.CommonProto.Consume.Builder addConsumeDataBuilder(
          int index) {
        return getConsumeDataFieldBuilder().addBuilder(
            index, com.dxx.game.dto.CommonProto.Consume.getDefaultInstance());
      }
      /**
       * <pre>
       * 消耗活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Consume consumeData = 3;</code>
       */
      public java.util.List<com.dxx.game.dto.CommonProto.Consume.Builder> 
           getConsumeDataBuilderList() {
        return getConsumeDataFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          com.dxx.game.dto.CommonProto.Consume, com.dxx.game.dto.CommonProto.Consume.Builder, com.dxx.game.dto.CommonProto.ConsumeOrBuilder> 
          getConsumeDataFieldBuilder() {
        if (consumeDataBuilder_ == null) {
          consumeDataBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              com.dxx.game.dto.CommonProto.Consume, com.dxx.game.dto.CommonProto.Consume.Builder, com.dxx.game.dto.CommonProto.ConsumeOrBuilder>(
                  consumeData_,
                  ((bitField0_ & 0x00000004) != 0),
                  getParentForChildren(),
                  isClean());
          consumeData_ = null;
        }
        return consumeDataBuilder_;
      }

      private java.util.List<com.dxx.game.dto.CommonProto.Shop> shopData_ =
        java.util.Collections.emptyList();
      private void ensureShopDataIsMutable() {
        if (!((bitField0_ & 0x00000008) != 0)) {
          shopData_ = new java.util.ArrayList<com.dxx.game.dto.CommonProto.Shop>(shopData_);
          bitField0_ |= 0x00000008;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          com.dxx.game.dto.CommonProto.Shop, com.dxx.game.dto.CommonProto.Shop.Builder, com.dxx.game.dto.CommonProto.ShopOrBuilder> shopDataBuilder_;

      /**
       * <pre>
       * 商城活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Shop shopData = 4;</code>
       */
      public java.util.List<com.dxx.game.dto.CommonProto.Shop> getShopDataList() {
        if (shopDataBuilder_ == null) {
          return java.util.Collections.unmodifiableList(shopData_);
        } else {
          return shopDataBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 商城活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Shop shopData = 4;</code>
       */
      public int getShopDataCount() {
        if (shopDataBuilder_ == null) {
          return shopData_.size();
        } else {
          return shopDataBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 商城活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Shop shopData = 4;</code>
       */
      public com.dxx.game.dto.CommonProto.Shop getShopData(int index) {
        if (shopDataBuilder_ == null) {
          return shopData_.get(index);
        } else {
          return shopDataBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 商城活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Shop shopData = 4;</code>
       */
      public Builder setShopData(
          int index, com.dxx.game.dto.CommonProto.Shop value) {
        if (shopDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureShopDataIsMutable();
          shopData_.set(index, value);
          onChanged();
        } else {
          shopDataBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 商城活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Shop shopData = 4;</code>
       */
      public Builder setShopData(
          int index, com.dxx.game.dto.CommonProto.Shop.Builder builderForValue) {
        if (shopDataBuilder_ == null) {
          ensureShopDataIsMutable();
          shopData_.set(index, builderForValue.build());
          onChanged();
        } else {
          shopDataBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 商城活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Shop shopData = 4;</code>
       */
      public Builder addShopData(com.dxx.game.dto.CommonProto.Shop value) {
        if (shopDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureShopDataIsMutable();
          shopData_.add(value);
          onChanged();
        } else {
          shopDataBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 商城活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Shop shopData = 4;</code>
       */
      public Builder addShopData(
          int index, com.dxx.game.dto.CommonProto.Shop value) {
        if (shopDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureShopDataIsMutable();
          shopData_.add(index, value);
          onChanged();
        } else {
          shopDataBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 商城活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Shop shopData = 4;</code>
       */
      public Builder addShopData(
          com.dxx.game.dto.CommonProto.Shop.Builder builderForValue) {
        if (shopDataBuilder_ == null) {
          ensureShopDataIsMutable();
          shopData_.add(builderForValue.build());
          onChanged();
        } else {
          shopDataBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 商城活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Shop shopData = 4;</code>
       */
      public Builder addShopData(
          int index, com.dxx.game.dto.CommonProto.Shop.Builder builderForValue) {
        if (shopDataBuilder_ == null) {
          ensureShopDataIsMutable();
          shopData_.add(index, builderForValue.build());
          onChanged();
        } else {
          shopDataBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 商城活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Shop shopData = 4;</code>
       */
      public Builder addAllShopData(
          java.lang.Iterable<? extends com.dxx.game.dto.CommonProto.Shop> values) {
        if (shopDataBuilder_ == null) {
          ensureShopDataIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, shopData_);
          onChanged();
        } else {
          shopDataBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 商城活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Shop shopData = 4;</code>
       */
      public Builder clearShopData() {
        if (shopDataBuilder_ == null) {
          shopData_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000008);
          onChanged();
        } else {
          shopDataBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 商城活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Shop shopData = 4;</code>
       */
      public Builder removeShopData(int index) {
        if (shopDataBuilder_ == null) {
          ensureShopDataIsMutable();
          shopData_.remove(index);
          onChanged();
        } else {
          shopDataBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 商城活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Shop shopData = 4;</code>
       */
      public com.dxx.game.dto.CommonProto.Shop.Builder getShopDataBuilder(
          int index) {
        return getShopDataFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 商城活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Shop shopData = 4;</code>
       */
      public com.dxx.game.dto.CommonProto.ShopOrBuilder getShopDataOrBuilder(
          int index) {
        if (shopDataBuilder_ == null) {
          return shopData_.get(index);  } else {
          return shopDataBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 商城活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Shop shopData = 4;</code>
       */
      public java.util.List<? extends com.dxx.game.dto.CommonProto.ShopOrBuilder> 
           getShopDataOrBuilderList() {
        if (shopDataBuilder_ != null) {
          return shopDataBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(shopData_);
        }
      }
      /**
       * <pre>
       * 商城活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Shop shopData = 4;</code>
       */
      public com.dxx.game.dto.CommonProto.Shop.Builder addShopDataBuilder() {
        return getShopDataFieldBuilder().addBuilder(
            com.dxx.game.dto.CommonProto.Shop.getDefaultInstance());
      }
      /**
       * <pre>
       * 商城活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Shop shopData = 4;</code>
       */
      public com.dxx.game.dto.CommonProto.Shop.Builder addShopDataBuilder(
          int index) {
        return getShopDataFieldBuilder().addBuilder(
            index, com.dxx.game.dto.CommonProto.Shop.getDefaultInstance());
      }
      /**
       * <pre>
       * 商城活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Shop shopData = 4;</code>
       */
      public java.util.List<com.dxx.game.dto.CommonProto.Shop.Builder> 
           getShopDataBuilderList() {
        return getShopDataFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          com.dxx.game.dto.CommonProto.Shop, com.dxx.game.dto.CommonProto.Shop.Builder, com.dxx.game.dto.CommonProto.ShopOrBuilder> 
          getShopDataFieldBuilder() {
        if (shopDataBuilder_ == null) {
          shopDataBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              com.dxx.game.dto.CommonProto.Shop, com.dxx.game.dto.CommonProto.Shop.Builder, com.dxx.game.dto.CommonProto.ShopOrBuilder>(
                  shopData_,
                  ((bitField0_ & 0x00000008) != 0),
                  getParentForChildren(),
                  isClean());
          shopData_ = null;
        }
        return shopDataBuilder_;
      }

      private java.util.List<com.dxx.game.dto.CommonProto.Drop> dropData_ =
        java.util.Collections.emptyList();
      private void ensureDropDataIsMutable() {
        if (!((bitField0_ & 0x00000010) != 0)) {
          dropData_ = new java.util.ArrayList<com.dxx.game.dto.CommonProto.Drop>(dropData_);
          bitField0_ |= 0x00000010;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          com.dxx.game.dto.CommonProto.Drop, com.dxx.game.dto.CommonProto.Drop.Builder, com.dxx.game.dto.CommonProto.DropOrBuilder> dropDataBuilder_;

      /**
       * <pre>
       * 掉落活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Drop dropData = 5;</code>
       */
      public java.util.List<com.dxx.game.dto.CommonProto.Drop> getDropDataList() {
        if (dropDataBuilder_ == null) {
          return java.util.Collections.unmodifiableList(dropData_);
        } else {
          return dropDataBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 掉落活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Drop dropData = 5;</code>
       */
      public int getDropDataCount() {
        if (dropDataBuilder_ == null) {
          return dropData_.size();
        } else {
          return dropDataBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 掉落活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Drop dropData = 5;</code>
       */
      public com.dxx.game.dto.CommonProto.Drop getDropData(int index) {
        if (dropDataBuilder_ == null) {
          return dropData_.get(index);
        } else {
          return dropDataBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 掉落活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Drop dropData = 5;</code>
       */
      public Builder setDropData(
          int index, com.dxx.game.dto.CommonProto.Drop value) {
        if (dropDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDropDataIsMutable();
          dropData_.set(index, value);
          onChanged();
        } else {
          dropDataBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 掉落活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Drop dropData = 5;</code>
       */
      public Builder setDropData(
          int index, com.dxx.game.dto.CommonProto.Drop.Builder builderForValue) {
        if (dropDataBuilder_ == null) {
          ensureDropDataIsMutable();
          dropData_.set(index, builderForValue.build());
          onChanged();
        } else {
          dropDataBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 掉落活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Drop dropData = 5;</code>
       */
      public Builder addDropData(com.dxx.game.dto.CommonProto.Drop value) {
        if (dropDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDropDataIsMutable();
          dropData_.add(value);
          onChanged();
        } else {
          dropDataBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 掉落活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Drop dropData = 5;</code>
       */
      public Builder addDropData(
          int index, com.dxx.game.dto.CommonProto.Drop value) {
        if (dropDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureDropDataIsMutable();
          dropData_.add(index, value);
          onChanged();
        } else {
          dropDataBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 掉落活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Drop dropData = 5;</code>
       */
      public Builder addDropData(
          com.dxx.game.dto.CommonProto.Drop.Builder builderForValue) {
        if (dropDataBuilder_ == null) {
          ensureDropDataIsMutable();
          dropData_.add(builderForValue.build());
          onChanged();
        } else {
          dropDataBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 掉落活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Drop dropData = 5;</code>
       */
      public Builder addDropData(
          int index, com.dxx.game.dto.CommonProto.Drop.Builder builderForValue) {
        if (dropDataBuilder_ == null) {
          ensureDropDataIsMutable();
          dropData_.add(index, builderForValue.build());
          onChanged();
        } else {
          dropDataBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 掉落活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Drop dropData = 5;</code>
       */
      public Builder addAllDropData(
          java.lang.Iterable<? extends com.dxx.game.dto.CommonProto.Drop> values) {
        if (dropDataBuilder_ == null) {
          ensureDropDataIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, dropData_);
          onChanged();
        } else {
          dropDataBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 掉落活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Drop dropData = 5;</code>
       */
      public Builder clearDropData() {
        if (dropDataBuilder_ == null) {
          dropData_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000010);
          onChanged();
        } else {
          dropDataBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 掉落活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Drop dropData = 5;</code>
       */
      public Builder removeDropData(int index) {
        if (dropDataBuilder_ == null) {
          ensureDropDataIsMutable();
          dropData_.remove(index);
          onChanged();
        } else {
          dropDataBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 掉落活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Drop dropData = 5;</code>
       */
      public com.dxx.game.dto.CommonProto.Drop.Builder getDropDataBuilder(
          int index) {
        return getDropDataFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 掉落活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Drop dropData = 5;</code>
       */
      public com.dxx.game.dto.CommonProto.DropOrBuilder getDropDataOrBuilder(
          int index) {
        if (dropDataBuilder_ == null) {
          return dropData_.get(index);  } else {
          return dropDataBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 掉落活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Drop dropData = 5;</code>
       */
      public java.util.List<? extends com.dxx.game.dto.CommonProto.DropOrBuilder> 
           getDropDataOrBuilderList() {
        if (dropDataBuilder_ != null) {
          return dropDataBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(dropData_);
        }
      }
      /**
       * <pre>
       * 掉落活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Drop dropData = 5;</code>
       */
      public com.dxx.game.dto.CommonProto.Drop.Builder addDropDataBuilder() {
        return getDropDataFieldBuilder().addBuilder(
            com.dxx.game.dto.CommonProto.Drop.getDefaultInstance());
      }
      /**
       * <pre>
       * 掉落活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Drop dropData = 5;</code>
       */
      public com.dxx.game.dto.CommonProto.Drop.Builder addDropDataBuilder(
          int index) {
        return getDropDataFieldBuilder().addBuilder(
            index, com.dxx.game.dto.CommonProto.Drop.getDefaultInstance());
      }
      /**
       * <pre>
       * 掉落活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Drop dropData = 5;</code>
       */
      public java.util.List<com.dxx.game.dto.CommonProto.Drop.Builder> 
           getDropDataBuilderList() {
        return getDropDataFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          com.dxx.game.dto.CommonProto.Drop, com.dxx.game.dto.CommonProto.Drop.Builder, com.dxx.game.dto.CommonProto.DropOrBuilder> 
          getDropDataFieldBuilder() {
        if (dropDataBuilder_ == null) {
          dropDataBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              com.dxx.game.dto.CommonProto.Drop, com.dxx.game.dto.CommonProto.Drop.Builder, com.dxx.game.dto.CommonProto.DropOrBuilder>(
                  dropData_,
                  ((bitField0_ & 0x00000010) != 0),
                  getParentForChildren(),
                  isClean());
          dropData_ = null;
        }
        return dropDataBuilder_;
      }

      private java.util.List<com.dxx.game.dto.CommonProto.Pay> payData_ =
        java.util.Collections.emptyList();
      private void ensurePayDataIsMutable() {
        if (!((bitField0_ & 0x00000020) != 0)) {
          payData_ = new java.util.ArrayList<com.dxx.game.dto.CommonProto.Pay>(payData_);
          bitField0_ |= 0x00000020;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          com.dxx.game.dto.CommonProto.Pay, com.dxx.game.dto.CommonProto.Pay.Builder, com.dxx.game.dto.CommonProto.PayOrBuilder> payDataBuilder_;

      /**
       * <pre>
       * 付费活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Pay payData = 6;</code>
       */
      public java.util.List<com.dxx.game.dto.CommonProto.Pay> getPayDataList() {
        if (payDataBuilder_ == null) {
          return java.util.Collections.unmodifiableList(payData_);
        } else {
          return payDataBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 付费活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Pay payData = 6;</code>
       */
      public int getPayDataCount() {
        if (payDataBuilder_ == null) {
          return payData_.size();
        } else {
          return payDataBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 付费活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Pay payData = 6;</code>
       */
      public com.dxx.game.dto.CommonProto.Pay getPayData(int index) {
        if (payDataBuilder_ == null) {
          return payData_.get(index);
        } else {
          return payDataBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 付费活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Pay payData = 6;</code>
       */
      public Builder setPayData(
          int index, com.dxx.game.dto.CommonProto.Pay value) {
        if (payDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePayDataIsMutable();
          payData_.set(index, value);
          onChanged();
        } else {
          payDataBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 付费活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Pay payData = 6;</code>
       */
      public Builder setPayData(
          int index, com.dxx.game.dto.CommonProto.Pay.Builder builderForValue) {
        if (payDataBuilder_ == null) {
          ensurePayDataIsMutable();
          payData_.set(index, builderForValue.build());
          onChanged();
        } else {
          payDataBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 付费活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Pay payData = 6;</code>
       */
      public Builder addPayData(com.dxx.game.dto.CommonProto.Pay value) {
        if (payDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePayDataIsMutable();
          payData_.add(value);
          onChanged();
        } else {
          payDataBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 付费活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Pay payData = 6;</code>
       */
      public Builder addPayData(
          int index, com.dxx.game.dto.CommonProto.Pay value) {
        if (payDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensurePayDataIsMutable();
          payData_.add(index, value);
          onChanged();
        } else {
          payDataBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 付费活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Pay payData = 6;</code>
       */
      public Builder addPayData(
          com.dxx.game.dto.CommonProto.Pay.Builder builderForValue) {
        if (payDataBuilder_ == null) {
          ensurePayDataIsMutable();
          payData_.add(builderForValue.build());
          onChanged();
        } else {
          payDataBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 付费活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Pay payData = 6;</code>
       */
      public Builder addPayData(
          int index, com.dxx.game.dto.CommonProto.Pay.Builder builderForValue) {
        if (payDataBuilder_ == null) {
          ensurePayDataIsMutable();
          payData_.add(index, builderForValue.build());
          onChanged();
        } else {
          payDataBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 付费活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Pay payData = 6;</code>
       */
      public Builder addAllPayData(
          java.lang.Iterable<? extends com.dxx.game.dto.CommonProto.Pay> values) {
        if (payDataBuilder_ == null) {
          ensurePayDataIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, payData_);
          onChanged();
        } else {
          payDataBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 付费活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Pay payData = 6;</code>
       */
      public Builder clearPayData() {
        if (payDataBuilder_ == null) {
          payData_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000020);
          onChanged();
        } else {
          payDataBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 付费活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Pay payData = 6;</code>
       */
      public Builder removePayData(int index) {
        if (payDataBuilder_ == null) {
          ensurePayDataIsMutable();
          payData_.remove(index);
          onChanged();
        } else {
          payDataBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 付费活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Pay payData = 6;</code>
       */
      public com.dxx.game.dto.CommonProto.Pay.Builder getPayDataBuilder(
          int index) {
        return getPayDataFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 付费活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Pay payData = 6;</code>
       */
      public com.dxx.game.dto.CommonProto.PayOrBuilder getPayDataOrBuilder(
          int index) {
        if (payDataBuilder_ == null) {
          return payData_.get(index);  } else {
          return payDataBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 付费活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Pay payData = 6;</code>
       */
      public java.util.List<? extends com.dxx.game.dto.CommonProto.PayOrBuilder> 
           getPayDataOrBuilderList() {
        if (payDataBuilder_ != null) {
          return payDataBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(payData_);
        }
      }
      /**
       * <pre>
       * 付费活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Pay payData = 6;</code>
       */
      public com.dxx.game.dto.CommonProto.Pay.Builder addPayDataBuilder() {
        return getPayDataFieldBuilder().addBuilder(
            com.dxx.game.dto.CommonProto.Pay.getDefaultInstance());
      }
      /**
       * <pre>
       * 付费活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Pay payData = 6;</code>
       */
      public com.dxx.game.dto.CommonProto.Pay.Builder addPayDataBuilder(
          int index) {
        return getPayDataFieldBuilder().addBuilder(
            index, com.dxx.game.dto.CommonProto.Pay.getDefaultInstance());
      }
      /**
       * <pre>
       * 付费活动数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Pay payData = 6;</code>
       */
      public java.util.List<com.dxx.game.dto.CommonProto.Pay.Builder> 
           getPayDataBuilderList() {
        return getPayDataFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          com.dxx.game.dto.CommonProto.Pay, com.dxx.game.dto.CommonProto.Pay.Builder, com.dxx.game.dto.CommonProto.PayOrBuilder> 
          getPayDataFieldBuilder() {
        if (payDataBuilder_ == null) {
          payDataBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              com.dxx.game.dto.CommonProto.Pay, com.dxx.game.dto.CommonProto.Pay.Builder, com.dxx.game.dto.CommonProto.PayOrBuilder>(
                  payData_,
                  ((bitField0_ & 0x00000020) != 0),
                  getParentForChildren(),
                  isClean());
          payData_ = null;
        }
        return payDataBuilder_;
      }

      private java.util.List<com.dxx.game.dto.CommonProto.Chapter> chapter_ =
        java.util.Collections.emptyList();
      private void ensureChapterIsMutable() {
        if (!((bitField0_ & 0x00000040) != 0)) {
          chapter_ = new java.util.ArrayList<com.dxx.game.dto.CommonProto.Chapter>(chapter_);
          bitField0_ |= 0x00000040;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          com.dxx.game.dto.CommonProto.Chapter, com.dxx.game.dto.CommonProto.Chapter.Builder, com.dxx.game.dto.CommonProto.ChapterOrBuilder> chapterBuilder_;

      /**
       * <pre>
       * 章节数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Chapter chapter = 7;</code>
       */
      public java.util.List<com.dxx.game.dto.CommonProto.Chapter> getChapterList() {
        if (chapterBuilder_ == null) {
          return java.util.Collections.unmodifiableList(chapter_);
        } else {
          return chapterBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 章节数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Chapter chapter = 7;</code>
       */
      public int getChapterCount() {
        if (chapterBuilder_ == null) {
          return chapter_.size();
        } else {
          return chapterBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 章节数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Chapter chapter = 7;</code>
       */
      public com.dxx.game.dto.CommonProto.Chapter getChapter(int index) {
        if (chapterBuilder_ == null) {
          return chapter_.get(index);
        } else {
          return chapterBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 章节数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Chapter chapter = 7;</code>
       */
      public Builder setChapter(
          int index, com.dxx.game.dto.CommonProto.Chapter value) {
        if (chapterBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureChapterIsMutable();
          chapter_.set(index, value);
          onChanged();
        } else {
          chapterBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 章节数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Chapter chapter = 7;</code>
       */
      public Builder setChapter(
          int index, com.dxx.game.dto.CommonProto.Chapter.Builder builderForValue) {
        if (chapterBuilder_ == null) {
          ensureChapterIsMutable();
          chapter_.set(index, builderForValue.build());
          onChanged();
        } else {
          chapterBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 章节数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Chapter chapter = 7;</code>
       */
      public Builder addChapter(com.dxx.game.dto.CommonProto.Chapter value) {
        if (chapterBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureChapterIsMutable();
          chapter_.add(value);
          onChanged();
        } else {
          chapterBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 章节数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Chapter chapter = 7;</code>
       */
      public Builder addChapter(
          int index, com.dxx.game.dto.CommonProto.Chapter value) {
        if (chapterBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureChapterIsMutable();
          chapter_.add(index, value);
          onChanged();
        } else {
          chapterBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 章节数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Chapter chapter = 7;</code>
       */
      public Builder addChapter(
          com.dxx.game.dto.CommonProto.Chapter.Builder builderForValue) {
        if (chapterBuilder_ == null) {
          ensureChapterIsMutable();
          chapter_.add(builderForValue.build());
          onChanged();
        } else {
          chapterBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 章节数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Chapter chapter = 7;</code>
       */
      public Builder addChapter(
          int index, com.dxx.game.dto.CommonProto.Chapter.Builder builderForValue) {
        if (chapterBuilder_ == null) {
          ensureChapterIsMutable();
          chapter_.add(index, builderForValue.build());
          onChanged();
        } else {
          chapterBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 章节数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Chapter chapter = 7;</code>
       */
      public Builder addAllChapter(
          java.lang.Iterable<? extends com.dxx.game.dto.CommonProto.Chapter> values) {
        if (chapterBuilder_ == null) {
          ensureChapterIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, chapter_);
          onChanged();
        } else {
          chapterBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 章节数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Chapter chapter = 7;</code>
       */
      public Builder clearChapter() {
        if (chapterBuilder_ == null) {
          chapter_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000040);
          onChanged();
        } else {
          chapterBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 章节数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Chapter chapter = 7;</code>
       */
      public Builder removeChapter(int index) {
        if (chapterBuilder_ == null) {
          ensureChapterIsMutable();
          chapter_.remove(index);
          onChanged();
        } else {
          chapterBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 章节数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Chapter chapter = 7;</code>
       */
      public com.dxx.game.dto.CommonProto.Chapter.Builder getChapterBuilder(
          int index) {
        return getChapterFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 章节数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Chapter chapter = 7;</code>
       */
      public com.dxx.game.dto.CommonProto.ChapterOrBuilder getChapterOrBuilder(
          int index) {
        if (chapterBuilder_ == null) {
          return chapter_.get(index);  } else {
          return chapterBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 章节数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Chapter chapter = 7;</code>
       */
      public java.util.List<? extends com.dxx.game.dto.CommonProto.ChapterOrBuilder> 
           getChapterOrBuilderList() {
        if (chapterBuilder_ != null) {
          return chapterBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(chapter_);
        }
      }
      /**
       * <pre>
       * 章节数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Chapter chapter = 7;</code>
       */
      public com.dxx.game.dto.CommonProto.Chapter.Builder addChapterBuilder() {
        return getChapterFieldBuilder().addBuilder(
            com.dxx.game.dto.CommonProto.Chapter.getDefaultInstance());
      }
      /**
       * <pre>
       * 章节数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Chapter chapter = 7;</code>
       */
      public com.dxx.game.dto.CommonProto.Chapter.Builder addChapterBuilder(
          int index) {
        return getChapterFieldBuilder().addBuilder(
            index, com.dxx.game.dto.CommonProto.Chapter.getDefaultInstance());
      }
      /**
       * <pre>
       * 章节数据
       * </pre>
       *
       * <code>repeated .Proto.Common.Chapter chapter = 7;</code>
       */
      public java.util.List<com.dxx.game.dto.CommonProto.Chapter.Builder> 
           getChapterBuilderList() {
        return getChapterFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          com.dxx.game.dto.CommonProto.Chapter, com.dxx.game.dto.CommonProto.Chapter.Builder, com.dxx.game.dto.CommonProto.ChapterOrBuilder> 
          getChapterFieldBuilder() {
        if (chapterBuilder_ == null) {
          chapterBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              com.dxx.game.dto.CommonProto.Chapter, com.dxx.game.dto.CommonProto.Chapter.Builder, com.dxx.game.dto.CommonProto.ChapterOrBuilder>(
                  chapter_,
                  ((bitField0_ & 0x00000040) != 0),
                  getParentForChildren(),
                  isClean());
          chapter_ = null;
        }
        return chapterBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Activity.ActivityGetListResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.Activity.ActivityGetListResponse)
    private static final com.dxx.game.dto.ActivityProto.ActivityGetListResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.ActivityProto.ActivityGetListResponse();
    }

    public static com.dxx.game.dto.ActivityProto.ActivityGetListResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ActivityGetListResponse>
        PARSER = new com.google.protobuf.AbstractParser<ActivityGetListResponse>() {
      @java.lang.Override
      public ActivityGetListResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<ActivityGetListResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ActivityGetListResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.ActivityProto.ActivityGetListResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Activity_ActivityGetListRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Activity_ActivityGetListRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Activity_ActivityGetListResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Activity_ActivityGetListResponse_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\016activity.proto\022\016Proto.Activity\032\014common" +
      ".proto\032\014signin.proto\"J\n\026ActivityGetListR" +
      "equest\0220\n\014commonParams\030\001 \001(\0132\032.Proto.Com" +
      "mon.CommonParams\"\231\002\n\027ActivityGetListResp" +
      "onse\022\014\n\004code\030\001 \001(\005\022,\n\nsignInData\030\002 \001(\0132\030" +
      ".Proto.SignIn.SignInData\022*\n\013consumeData\030" +
      "\003 \003(\0132\025.Proto.Common.Consume\022$\n\010shopData" +
      "\030\004 \003(\0132\022.Proto.Common.Shop\022$\n\010dropData\030\005" +
      " \003(\0132\022.Proto.Common.Drop\022\"\n\007payData\030\006 \003(" +
      "\0132\021.Proto.Common.Pay\022&\n\007chapter\030\007 \003(\0132\025." +
      "Proto.Common.ChapterB!\n\020com.dxx.game.dto" +
      "B\rActivityProtob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.dxx.game.dto.CommonProto.getDescriptor(),
          com.dxx.game.dto.SignInProto.getDescriptor(),
        });
    internal_static_Proto_Activity_ActivityGetListRequest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_Proto_Activity_ActivityGetListRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Activity_ActivityGetListRequest_descriptor,
        new java.lang.String[] { "CommonParams", });
    internal_static_Proto_Activity_ActivityGetListResponse_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_Proto_Activity_ActivityGetListResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Activity_ActivityGetListResponse_descriptor,
        new java.lang.String[] { "Code", "SignInData", "ConsumeData", "ShopData", "DropData", "PayData", "Chapter", });
    descriptor.resolveAllFeaturesImmutable();
    com.dxx.game.dto.CommonProto.getDescriptor();
    com.dxx.game.dto.SignInProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
