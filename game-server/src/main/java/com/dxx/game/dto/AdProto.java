// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: ad.proto
// Protobuf Java Version: 4.29.0

package com.dxx.game.dto;

public final class AdProto {
  private AdProto() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 29,
      /* patch= */ 0,
      /* suffix= */ "",
      AdProto.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Ad_WatchAdRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Ad_WatchAdRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Ad_WatchAdResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Ad_WatchAdResponse_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\010ad.proto\022\010Proto.Ad\032\014common.proto\"X\n\016Wa" +
      "tchAdRequest\0220\n\014commonParams\030\001 \001(\0132\032.Pro" +
      "to.Common.CommonParams\022\024\n\014adTemplateId\030\002" +
      " \001(\005\"5\n\017WatchAdResponse\022\014\n\004code\030\001 \001(\005\022\024\n" +
      "\014adTemplateId\030\002 \001(\005B\035\n\020com.dxx.game.dtoB" +
      "\007AdProtoP\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.dxx.game.dto.CommonProto.getDescriptor(),
        });
    internal_static_Proto_Ad_WatchAdRequest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_Proto_Ad_WatchAdRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Ad_WatchAdRequest_descriptor,
        new java.lang.String[] { "CommonParams", "AdTemplateId", });
    internal_static_Proto_Ad_WatchAdResponse_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_Proto_Ad_WatchAdResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Ad_WatchAdResponse_descriptor,
        new java.lang.String[] { "Code", "AdTemplateId", });
    descriptor.resolveAllFeaturesImmutable();
    com.dxx.game.dto.CommonProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
