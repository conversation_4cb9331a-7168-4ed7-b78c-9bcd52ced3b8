// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: baowu.proto
// Protobuf Java Version: 4.29.0

package com.dxx.game.dto;

public final class BaoWuProto {
  private BaoWuProto() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 29,
      /* patch= */ 0,
      /* suffix= */ "",
      BaoWuProto.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_BaoWu_BaoWuPutOnRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_BaoWu_BaoWuPutOnRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_BaoWu_BaoWuPutOnResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_BaoWu_BaoWuPutOnResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_BaoWu_BaoWuPutOnResponse_BaoWuPosEntry_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_BaoWu_BaoWuPutOnResponse_BaoWuPosEntry_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_BaoWu_BaoWuTakeOffRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_BaoWu_BaoWuTakeOffRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_BaoWu_BaoWuTakeOffResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_BaoWu_BaoWuTakeOffResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_BaoWu_MergeBaoWu_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_BaoWu_MergeBaoWu_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_BaoWu_BaoWuMergeRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_BaoWu_BaoWuMergeRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_BaoWu_BaoWuMergeResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_BaoWu_BaoWuMergeResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_BaoWu_BaoWuDowngradeRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_BaoWu_BaoWuDowngradeRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_BaoWu_BaoWuDowngradeResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_BaoWu_BaoWuDowngradeResponse_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\013baowu.proto\022\013Proto.BaoWu\032\014common.proto" +
      "\"a\n\021BaoWuPutOnRequest\0220\n\014commonParams\030\001 " +
      "\001(\0132\032.Proto.Common.CommonParams\022\r\n\005rowId" +
      "\030\002 \001(\003\022\013\n\003pos\030\003 \001(\005\"\224\001\n\022BaoWuPutOnRespon" +
      "se\022\014\n\004code\030\001 \001(\005\022?\n\010baoWuPos\030\002 \003(\0132-.Pro" +
      "to.BaoWu.BaoWuPutOnResponse.BaoWuPosEntr" +
      "y\032/\n\rBaoWuPosEntry\022\013\n\003key\030\001 \001(\005\022\r\n\005value" +
      "\030\002 \001(\003:\0028\001\"T\n\023BaoWuTakeOffRequest\0220\n\014com" +
      "monParams\030\001 \001(\0132\032.Proto.Common.CommonPar" +
      "ams\022\013\n\003pos\030\002 \001(\005\"1\n\024BaoWuTakeOffResponse" +
      "\022\014\n\004code\030\001 \001(\005\022\013\n\003pos\030\002 \001(\005\"c\n\nMergeBaoW" +
      "u\022\023\n\013targetRowId\030\001 \001(\003\022\025\n\rdeletedRowIds\030" +
      "\002 \003(\003\022)\n\npingTiItem\030\003 \001(\0132\025.Proto.Common" +
      ".ItemDto\"q\n\021BaoWuMergeRequest\0220\n\014commonP" +
      "arams\030\001 \001(\0132\032.Proto.Common.CommonParams\022" +
      "*\n\tmergeList\030\002 \003(\0132\027.Proto.BaoWu.MergeBa" +
      "oWu\"a\n\022BaoWuMergeResponse\022\014\n\004code\030\001 \001(\005\022" +
      "&\n\006result\030\002 \003(\0132\026.Proto.Common.BaoWuDto\022" +
      "\025\n\rdeletedRowIds\030\003 \003(\003\"X\n\025BaoWuDowngrade" +
      "Request\0220\n\014commonParams\030\001 \001(\0132\032.Proto.Co" +
      "mmon.CommonParams\022\r\n\005rowId\030\002 \001(\003\"z\n\026BaoW" +
      "uDowngradeResponse\022\014\n\004code\030\001 \001(\005\022&\n\006resu" +
      "lt\030\002 \001(\0132\026.Proto.Common.BaoWuDto\022*\n\nback" +
      "BaoWus\030\003 \003(\0132\026.Proto.Common.BaoWuDtoB \n\020" +
      "com.dxx.game.dtoB\nBaoWuProtoP\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.dxx.game.dto.CommonProto.getDescriptor(),
        });
    internal_static_Proto_BaoWu_BaoWuPutOnRequest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_Proto_BaoWu_BaoWuPutOnRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_BaoWu_BaoWuPutOnRequest_descriptor,
        new java.lang.String[] { "CommonParams", "RowId", "Pos", });
    internal_static_Proto_BaoWu_BaoWuPutOnResponse_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_Proto_BaoWu_BaoWuPutOnResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_BaoWu_BaoWuPutOnResponse_descriptor,
        new java.lang.String[] { "Code", "BaoWuPos", });
    internal_static_Proto_BaoWu_BaoWuPutOnResponse_BaoWuPosEntry_descriptor =
      internal_static_Proto_BaoWu_BaoWuPutOnResponse_descriptor.getNestedTypes().get(0);
    internal_static_Proto_BaoWu_BaoWuPutOnResponse_BaoWuPosEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_BaoWu_BaoWuPutOnResponse_BaoWuPosEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_Proto_BaoWu_BaoWuTakeOffRequest_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_Proto_BaoWu_BaoWuTakeOffRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_BaoWu_BaoWuTakeOffRequest_descriptor,
        new java.lang.String[] { "CommonParams", "Pos", });
    internal_static_Proto_BaoWu_BaoWuTakeOffResponse_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_Proto_BaoWu_BaoWuTakeOffResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_BaoWu_BaoWuTakeOffResponse_descriptor,
        new java.lang.String[] { "Code", "Pos", });
    internal_static_Proto_BaoWu_MergeBaoWu_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_Proto_BaoWu_MergeBaoWu_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_BaoWu_MergeBaoWu_descriptor,
        new java.lang.String[] { "TargetRowId", "DeletedRowIds", "PingTiItem", });
    internal_static_Proto_BaoWu_BaoWuMergeRequest_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_Proto_BaoWu_BaoWuMergeRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_BaoWu_BaoWuMergeRequest_descriptor,
        new java.lang.String[] { "CommonParams", "MergeList", });
    internal_static_Proto_BaoWu_BaoWuMergeResponse_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_Proto_BaoWu_BaoWuMergeResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_BaoWu_BaoWuMergeResponse_descriptor,
        new java.lang.String[] { "Code", "Result", "DeletedRowIds", });
    internal_static_Proto_BaoWu_BaoWuDowngradeRequest_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_Proto_BaoWu_BaoWuDowngradeRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_BaoWu_BaoWuDowngradeRequest_descriptor,
        new java.lang.String[] { "CommonParams", "RowId", });
    internal_static_Proto_BaoWu_BaoWuDowngradeResponse_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_Proto_BaoWu_BaoWuDowngradeResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_BaoWu_BaoWuDowngradeResponse_descriptor,
        new java.lang.String[] { "Code", "Result", "BackBaoWus", });
    descriptor.resolveAllFeaturesImmutable();
    com.dxx.game.dto.CommonProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
