// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: battle.proto
// Protobuf Java Version: 4.29.0

package com.dxx.game.dto;

public final class BattleProto {
  private BattleProto() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 29,
      /* patch= */ 0,
      /* suffix= */ "",
      BattleProto.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Battle_EchoTestReq_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Battle_EchoTestReq_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Battle_EchoTestResp_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Battle_EchoTestResp_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Battle_BattleEquipInfo_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Battle_BattleEquipInfo_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Battle_BattleCardInfo_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Battle_BattleCardInfo_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Battle_BattleUserInfo_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Battle_BattleUserInfo_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Battle_BattleUserInfo_TalentLevelMapEntry_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Battle_BattleUserInfo_TalentLevelMapEntry_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Battle_BattleUserInfo_PrivilegeEntry_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Battle_BattleUserInfo_PrivilegeEntry_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Battle_BattleUserInfo_HeroSkinsEntry_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Battle_BattleUserInfo_HeroSkinsEntry_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Battle_BattleBaoWu_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Battle_BattleBaoWu_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Battle_HeroStatisticData_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Battle_HeroStatisticData_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Battle_BattleStatistics_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Battle_BattleStatistics_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Battle_BattleResultDto_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Battle_BattleResultDto_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Battle_BattleCommandArgs_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Battle_BattleCommandArgs_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Battle_BattleCommand_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Battle_BattleCommand_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Battle_BattleCommandAll_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Battle_BattleCommandAll_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Battle_BattleStartInfo_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Battle_BattleStartInfo_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Battle_BattleCheckReq_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Battle_BattleCheckReq_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Battle_BattleCheckResp_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Battle_BattleCheckResp_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Battle_PowerReq_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Battle_PowerReq_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Battle_PowerResp_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Battle_PowerResp_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Battle_BattleValidTask_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Battle_BattleValidTask_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Battle_BattleStartSnapshot_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Battle_BattleStartSnapshot_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\014battle.proto\022\014Proto.Battle\032\014common.pro" +
      "to\"\036\n\013EchoTestReq\022\017\n\007content\030\001 \001(\t\"\037\n\014Ec" +
      "hoTestResp\022\017\n\007content\030\002 \001(\t\"X\n\017BattleEqu" +
      "ipInfo\022\n\n\002id\030\001 \001(\005\022\r\n\005level\030\002 \001(\005\022\024\n\014pur" +
      "itfyLevel\030\003 \001(\005\022\024\n\014PurifyIdList\030\004 \003(\005\"\303\001" +
      "\n\016BattleCardInfo\022\n\n\002id\030\001 \001(\005\022\r\n\005level\030\002 " +
      "\001(\005\022\014\n\004star\030\003 \001(\005\022\r\n\005index\030\004 \001(\005\022\023\n\013star" +
      "TaskIds\030\005 \003(\005\022-\n\006equips\030\006 \003(\0132\035.Proto.Ba" +
      "ttle.BattleEquipInfo\022\014\n\004skin\030\007 \001(\005\022\'\n\010ru" +
      "neDtos\030\010 \003(\0132\025.Proto.Common.RuneDto\"\370\003\n\016" +
      "BattleUserInfo\022+\n\005cards\030\001 \003(\0132\034.Proto.Ba" +
      "ttle.BattleCardInfo\022\027\n\017mainTaskStageId\030\002" +
      " \001(\005\022H\n\016talentLevelMap\030\003 \003(\01320.Proto.Bat" +
      "tle.BattleUserInfo.TalentLevelMapEntry\022)" +
      "\n\006baoWus\030\004 \003(\0132\031.Proto.Battle.BattleBaoW" +
      "u\022\020\n\010dropBase\030\005 \001(\005\022>\n\tprivilege\030\006 \003(\0132+" +
      ".Proto.Battle.BattleUserInfo.PrivilegeEn" +
      "try\022>\n\theroSkins\030\007 \003(\0132+.Proto.Battle.Ba" +
      "ttleUserInfo.HeroSkinsEntry\0325\n\023TalentLev" +
      "elMapEntry\022\013\n\003key\030\001 \001(\005\022\r\n\005value\030\002 \001(\005:\002" +
      "8\001\0320\n\016PrivilegeEntry\022\013\n\003key\030\001 \001(\005\022\r\n\005val" +
      "ue\030\002 \001(\005:\0028\001\0320\n\016HeroSkinsEntry\022\013\n\003key\030\001 " +
      "\001(\005\022\r\n\005value\030\002 \001(\003:\0028\001\"*\n\013BattleBaoWu\022\n\n" +
      "\002id\030\001 \001(\005\022\017\n\007quality\030\002 \001(\005\"]\n\021HeroStatis" +
      "ticData\022\016\n\006heroId\030\001 \001(\005\022\023\n\013totalDamage\030\002" +
      " \001(\005\022\021\n\ttotalHeal\030\003 \001(\005\022\020\n\010totalDef\030\004 \001(" +
      "\005\"A\n\020BattleStatistics\022\027\n\017BossHpChangeAll" +
      "\030\001 \001(\003\022\024\n\014bossRemainHp\030\002 \001(\003\"\241\002\n\017BattleR" +
      "esultDto\022\025\n\rbattleVersion\030\001 \001(\t\022.\n\tlevel" +
      "Info\030\002 \001(\0132\033.Proto.Common.BattleInfoDto\022" +
      "0\n\tendReason\030\003 \001(\0162\035.Proto.Common.Battle" +
      "EndReason\022\017\n\007maxWave\030\004 \001(\005\022.\n\rbattleRewa" +
      "rds\030\005 \003(\0132\027.Proto.Common.RewardDto\0221\n\tst" +
      "atistic\030\006 \001(\0132\036.Proto.Battle.BattleStati" +
      "stics\022!\n\031conditionChapterStarIndex\030\007 \003(\005" +
      "\"M\n\021BattleCommandArgs\022\020\n\010ValueInt\030\001 \001(\005\022" +
      "\025\n\rValueIntArray\030\002 \003(\005\022\017\n\007ValueFp\030\003 \001(\003\"" +
      "c\n\rBattleCommand\022\017\n\007frameId\030\001 \001(\005\022\021\n\tcom" +
      "mandId\030\002 \001(\005\022.\n\005Value\030\003 \001(\0132\037.Proto.Batt" +
      "le.BattleCommandArgs\"m\n\020BattleCommandAll" +
      "\022-\n\010commands\030\001 \003(\0132\033.Proto.Battle.Battle" +
      "Command\022\022\n\ntotalFrame\030\002 \001(\005\022\026\n\016resultHas" +
      "hCode\030\003 \001(\005\"\244\001\n\017BattleStartInfo\022.\n\tlevel" +
      "Info\030\001 \001(\0132\033.Proto.Common.BattleInfoDto\022" +
      ".\n\010userInfo\030\002 \001(\0132\034.Proto.Battle.BattleU" +
      "serInfo\022\032\n\022conditionChapterId\030\003 \001(\005\022\025\n\rr" +
      "uneDungeonId\030\004 \001(\005\"r\n\016BattleCheckReq\022\016\n\006" +
      "userId\030\001 \001(\003\0220\n\tstartInfo\030\002 \001(\0132\035.Proto." +
      "Battle.BattleStartInfo\022\020\n\010commands\030\003 \001(\014" +
      "\022\014\n\004time\030\004 \001(\003\"o\n\017BattleCheckResp\022\014\n\004cod" +
      "e\030\001 \001(\005\022\016\n\006userId\030\002 \001(\003\0220\n\tresultDto\030\003 \001" +
      "(\0132\035.Proto.Battle.BattleResultDto\022\014\n\004tim" +
      "e\030\004 \001(\003\":\n\010PowerReq\022.\n\010userInfo\030\001 \001(\0132\034." +
      "Proto.Battle.BattleUserInfo\"(\n\tPowerResp" +
      "\022\014\n\004code\030\001 \001(\005\022\r\n\005power\030\002 \001(\005\"R\n\017BattleV" +
      "alidTask\022\023\n\013record_name\030\001 \001(\t\022\024\n\014use_cal" +
      "lback\030\002 \001(\010\022\024\n\014callback_url\030\003 \001(\t\"U\n\023Bat" +
      "tleStartSnapshot\0220\n\tstartInfo\030\001 \001(\0132\035.Pr" +
      "oto.Battle.BattleStartInfo\022\014\n\004time\030\002 \001(\003" +
      "2\360\001\n\rBattleService\022R\n\021handleBattleCheck\022" +
      "\034.Proto.Battle.BattleCheckReq\032\035.Proto.Ba" +
      "ttle.BattleCheckResp\"\000\022@\n\013handlePower\022\026." +
      "Proto.Battle.PowerReq\032\027.Proto.Battle.Pow" +
      "erResp\"\000\022I\n\016handleEchoTest\022\031.Proto.Battl" +
      "e.EchoTestReq\032\032.Proto.Battle.EchoTestRes" +
      "p\"\000B!\n\020com.dxx.game.dtoB\013BattleProtoP\001b\006" +
      "proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.dxx.game.dto.CommonProto.getDescriptor(),
        });
    internal_static_Proto_Battle_EchoTestReq_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_Proto_Battle_EchoTestReq_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Battle_EchoTestReq_descriptor,
        new java.lang.String[] { "Content", });
    internal_static_Proto_Battle_EchoTestResp_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_Proto_Battle_EchoTestResp_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Battle_EchoTestResp_descriptor,
        new java.lang.String[] { "Content", });
    internal_static_Proto_Battle_BattleEquipInfo_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_Proto_Battle_BattleEquipInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Battle_BattleEquipInfo_descriptor,
        new java.lang.String[] { "Id", "Level", "PuritfyLevel", "PurifyIdList", });
    internal_static_Proto_Battle_BattleCardInfo_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_Proto_Battle_BattleCardInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Battle_BattleCardInfo_descriptor,
        new java.lang.String[] { "Id", "Level", "Star", "Index", "StarTaskIds", "Equips", "Skin", "RuneDtos", });
    internal_static_Proto_Battle_BattleUserInfo_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_Proto_Battle_BattleUserInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Battle_BattleUserInfo_descriptor,
        new java.lang.String[] { "Cards", "MainTaskStageId", "TalentLevelMap", "BaoWus", "DropBase", "Privilege", "HeroSkins", });
    internal_static_Proto_Battle_BattleUserInfo_TalentLevelMapEntry_descriptor =
      internal_static_Proto_Battle_BattleUserInfo_descriptor.getNestedTypes().get(0);
    internal_static_Proto_Battle_BattleUserInfo_TalentLevelMapEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Battle_BattleUserInfo_TalentLevelMapEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_Proto_Battle_BattleUserInfo_PrivilegeEntry_descriptor =
      internal_static_Proto_Battle_BattleUserInfo_descriptor.getNestedTypes().get(1);
    internal_static_Proto_Battle_BattleUserInfo_PrivilegeEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Battle_BattleUserInfo_PrivilegeEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_Proto_Battle_BattleUserInfo_HeroSkinsEntry_descriptor =
      internal_static_Proto_Battle_BattleUserInfo_descriptor.getNestedTypes().get(2);
    internal_static_Proto_Battle_BattleUserInfo_HeroSkinsEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Battle_BattleUserInfo_HeroSkinsEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_Proto_Battle_BattleBaoWu_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_Proto_Battle_BattleBaoWu_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Battle_BattleBaoWu_descriptor,
        new java.lang.String[] { "Id", "Quality", });
    internal_static_Proto_Battle_HeroStatisticData_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_Proto_Battle_HeroStatisticData_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Battle_HeroStatisticData_descriptor,
        new java.lang.String[] { "HeroId", "TotalDamage", "TotalHeal", "TotalDef", });
    internal_static_Proto_Battle_BattleStatistics_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_Proto_Battle_BattleStatistics_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Battle_BattleStatistics_descriptor,
        new java.lang.String[] { "BossHpChangeAll", "BossRemainHp", });
    internal_static_Proto_Battle_BattleResultDto_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_Proto_Battle_BattleResultDto_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Battle_BattleResultDto_descriptor,
        new java.lang.String[] { "BattleVersion", "LevelInfo", "EndReason", "MaxWave", "BattleRewards", "Statistic", "ConditionChapterStarIndex", });
    internal_static_Proto_Battle_BattleCommandArgs_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_Proto_Battle_BattleCommandArgs_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Battle_BattleCommandArgs_descriptor,
        new java.lang.String[] { "ValueInt", "ValueIntArray", "ValueFp", });
    internal_static_Proto_Battle_BattleCommand_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_Proto_Battle_BattleCommand_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Battle_BattleCommand_descriptor,
        new java.lang.String[] { "FrameId", "CommandId", "Value", });
    internal_static_Proto_Battle_BattleCommandAll_descriptor =
      getDescriptor().getMessageTypes().get(11);
    internal_static_Proto_Battle_BattleCommandAll_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Battle_BattleCommandAll_descriptor,
        new java.lang.String[] { "Commands", "TotalFrame", "ResultHashCode", });
    internal_static_Proto_Battle_BattleStartInfo_descriptor =
      getDescriptor().getMessageTypes().get(12);
    internal_static_Proto_Battle_BattleStartInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Battle_BattleStartInfo_descriptor,
        new java.lang.String[] { "LevelInfo", "UserInfo", "ConditionChapterId", "RuneDungeonId", });
    internal_static_Proto_Battle_BattleCheckReq_descriptor =
      getDescriptor().getMessageTypes().get(13);
    internal_static_Proto_Battle_BattleCheckReq_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Battle_BattleCheckReq_descriptor,
        new java.lang.String[] { "UserId", "StartInfo", "Commands", "Time", });
    internal_static_Proto_Battle_BattleCheckResp_descriptor =
      getDescriptor().getMessageTypes().get(14);
    internal_static_Proto_Battle_BattleCheckResp_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Battle_BattleCheckResp_descriptor,
        new java.lang.String[] { "Code", "UserId", "ResultDto", "Time", });
    internal_static_Proto_Battle_PowerReq_descriptor =
      getDescriptor().getMessageTypes().get(15);
    internal_static_Proto_Battle_PowerReq_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Battle_PowerReq_descriptor,
        new java.lang.String[] { "UserInfo", });
    internal_static_Proto_Battle_PowerResp_descriptor =
      getDescriptor().getMessageTypes().get(16);
    internal_static_Proto_Battle_PowerResp_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Battle_PowerResp_descriptor,
        new java.lang.String[] { "Code", "Power", });
    internal_static_Proto_Battle_BattleValidTask_descriptor =
      getDescriptor().getMessageTypes().get(17);
    internal_static_Proto_Battle_BattleValidTask_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Battle_BattleValidTask_descriptor,
        new java.lang.String[] { "RecordName", "UseCallback", "CallbackUrl", });
    internal_static_Proto_Battle_BattleStartSnapshot_descriptor =
      getDescriptor().getMessageTypes().get(18);
    internal_static_Proto_Battle_BattleStartSnapshot_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Battle_BattleStartSnapshot_descriptor,
        new java.lang.String[] { "StartInfo", "Time", });
    descriptor.resolveAllFeaturesImmutable();
    com.dxx.game.dto.CommonProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
