// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: chest.proto
// Protobuf Java Version: 4.29.0

package com.dxx.game.dto;

public final class ChestProto {
  private ChestProto() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 29,
      /* patch= */ 0,
      /* suffix= */ "",
      ChestProto.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ChestUseRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Collection.ChestUseRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();

    /**
     * <pre>
     * 道具rowId
     * </pre>
     *
     * <code>int32 itemId = 2;</code>
     * @return The itemId.
     */
    int getItemId();

    /**
     * <pre>
     * 使用数量
     * </pre>
     *
     * <code>int32 count = 3;</code>
     * @return The count.
     */
    int getCount();
  }
  /**
   * <pre>
   * CMD PackageId=20231 使用
   * </pre>
   *
   * Protobuf type {@code Proto.Collection.ChestUseRequest}
   */
  public static final class ChestUseRequest extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Collection.ChestUseRequest)
      ChestUseRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        ChestUseRequest.class.getName());
    }
    // Use ChestUseRequest.newBuilder() to construct.
    private ChestUseRequest(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private ChestUseRequest() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.ChestProto.internal_static_Proto_Collection_ChestUseRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.ChestProto.internal_static_Proto_Collection_ChestUseRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.ChestProto.ChestUseRequest.class, com.dxx.game.dto.ChestProto.ChestUseRequest.Builder.class);
    }

    private int bitField0_;
    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }

    public static final int ITEMID_FIELD_NUMBER = 2;
    private int itemId_ = 0;
    /**
     * <pre>
     * 道具rowId
     * </pre>
     *
     * <code>int32 itemId = 2;</code>
     * @return The itemId.
     */
    @java.lang.Override
    public int getItemId() {
      return itemId_;
    }

    public static final int COUNT_FIELD_NUMBER = 3;
    private int count_ = 0;
    /**
     * <pre>
     * 使用数量
     * </pre>
     *
     * <code>int32 count = 3;</code>
     * @return The count.
     */
    @java.lang.Override
    public int getCount() {
      return count_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getCommonParams());
      }
      if (itemId_ != 0) {
        output.writeInt32(2, itemId_);
      }
      if (count_ != 0) {
        output.writeInt32(3, count_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      if (itemId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, itemId_);
      }
      if (count_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, count_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.ChestProto.ChestUseRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.ChestProto.ChestUseRequest other = (com.dxx.game.dto.ChestProto.ChestUseRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (getItemId()
          != other.getItemId()) return false;
      if (getCount()
          != other.getCount()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (37 * hash) + ITEMID_FIELD_NUMBER;
      hash = (53 * hash) + getItemId();
      hash = (37 * hash) + COUNT_FIELD_NUMBER;
      hash = (53 * hash) + getCount();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.ChestProto.ChestUseRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ChestProto.ChestUseRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ChestProto.ChestUseRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ChestProto.ChestUseRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ChestProto.ChestUseRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ChestProto.ChestUseRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ChestProto.ChestUseRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ChestProto.ChestUseRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.ChestProto.ChestUseRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.ChestProto.ChestUseRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.ChestProto.ChestUseRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ChestProto.ChestUseRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.ChestProto.ChestUseRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=20231 使用
     * </pre>
     *
     * Protobuf type {@code Proto.Collection.ChestUseRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Collection.ChestUseRequest)
        com.dxx.game.dto.ChestProto.ChestUseRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.ChestProto.internal_static_Proto_Collection_ChestUseRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.ChestProto.internal_static_Proto_Collection_ChestUseRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.ChestProto.ChestUseRequest.class, com.dxx.game.dto.ChestProto.ChestUseRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.ChestProto.ChestUseRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getCommonParamsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        itemId_ = 0;
        count_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.ChestProto.internal_static_Proto_Collection_ChestUseRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.ChestProto.ChestUseRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.ChestProto.ChestUseRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.ChestProto.ChestUseRequest build() {
        com.dxx.game.dto.ChestProto.ChestUseRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.ChestProto.ChestUseRequest buildPartial() {
        com.dxx.game.dto.ChestProto.ChestUseRequest result = new com.dxx.game.dto.ChestProto.ChestUseRequest(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.ChestProto.ChestUseRequest result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.commonParams_ = commonParamsBuilder_ == null
              ? commonParams_
              : commonParamsBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.itemId_ = itemId_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.count_ = count_;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.ChestProto.ChestUseRequest) {
          return mergeFrom((com.dxx.game.dto.ChestProto.ChestUseRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.ChestProto.ChestUseRequest other) {
        if (other == com.dxx.game.dto.ChestProto.ChestUseRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        if (other.getItemId() != 0) {
          setItemId(other.getItemId());
        }
        if (other.getCount() != 0) {
          setCount(other.getCount());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getCommonParamsFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 16: {
                itemId_ = input.readInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 24: {
                count_ = input.readInt32();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
        } else {
          commonParamsBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
            commonParams_ != null &&
            commonParams_ != com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance()) {
            getCommonParamsBuilder().mergeFrom(value);
          } else {
            commonParams_ = value;
          }
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }
        if (commonParams_ != null) {
          bitField0_ |= 0x00000001;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        bitField0_ = (bitField0_ & ~0x00000001);
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }

      private int itemId_ ;
      /**
       * <pre>
       * 道具rowId
       * </pre>
       *
       * <code>int32 itemId = 2;</code>
       * @return The itemId.
       */
      @java.lang.Override
      public int getItemId() {
        return itemId_;
      }
      /**
       * <pre>
       * 道具rowId
       * </pre>
       *
       * <code>int32 itemId = 2;</code>
       * @param value The itemId to set.
       * @return This builder for chaining.
       */
      public Builder setItemId(int value) {

        itemId_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 道具rowId
       * </pre>
       *
       * <code>int32 itemId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearItemId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        itemId_ = 0;
        onChanged();
        return this;
      }

      private int count_ ;
      /**
       * <pre>
       * 使用数量
       * </pre>
       *
       * <code>int32 count = 3;</code>
       * @return The count.
       */
      @java.lang.Override
      public int getCount() {
        return count_;
      }
      /**
       * <pre>
       * 使用数量
       * </pre>
       *
       * <code>int32 count = 3;</code>
       * @param value The count to set.
       * @return This builder for chaining.
       */
      public Builder setCount(int value) {

        count_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 使用数量
       * </pre>
       *
       * <code>int32 count = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearCount() {
        bitField0_ = (bitField0_ & ~0x00000004);
        count_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Collection.ChestUseRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.Collection.ChestUseRequest)
    private static final com.dxx.game.dto.ChestProto.ChestUseRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.ChestProto.ChestUseRequest();
    }

    public static com.dxx.game.dto.ChestProto.ChestUseRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ChestUseRequest>
        PARSER = new com.google.protobuf.AbstractParser<ChestUseRequest>() {
      @java.lang.Override
      public ChestUseRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<ChestUseRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ChestUseRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.ChestProto.ChestUseRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ChestUseResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Collection.ChestUseResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    boolean hasCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    com.dxx.game.dto.CommonProto.CommonData getCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder();
  }
  /**
   * <pre>
   * CMD PackageId=20232
   * </pre>
   *
   * Protobuf type {@code Proto.Collection.ChestUseResponse}
   */
  public static final class ChestUseResponse extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Collection.ChestUseResponse)
      ChestUseResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        ChestUseResponse.class.getName());
    }
    // Use ChestUseResponse.newBuilder() to construct.
    private ChestUseResponse(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private ChestUseResponse() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.ChestProto.internal_static_Proto_Collection_ChestUseResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.ChestProto.internal_static_Proto_Collection_ChestUseResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.ChestProto.ChestUseResponse.class, com.dxx.game.dto.ChestProto.ChestUseResponse.Builder.class);
    }

    private int bitField0_;
    public static final int CODE_FIELD_NUMBER = 1;
    private int code_ = 0;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int COMMONDATA_FIELD_NUMBER = 2;
    private com.dxx.game.dto.CommonProto.CommonData commonData_;
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    @java.lang.Override
    public boolean hasCommonData() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(2, getCommonData());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getCommonData());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.ChestProto.ChestUseResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.ChestProto.ChestUseResponse other = (com.dxx.game.dto.ChestProto.ChestUseResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (hasCommonData() != other.hasCommonData()) return false;
      if (hasCommonData()) {
        if (!getCommonData()
            .equals(other.getCommonData())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      if (hasCommonData()) {
        hash = (37 * hash) + COMMONDATA_FIELD_NUMBER;
        hash = (53 * hash) + getCommonData().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.ChestProto.ChestUseResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ChestProto.ChestUseResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ChestProto.ChestUseResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ChestProto.ChestUseResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ChestProto.ChestUseResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ChestProto.ChestUseResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ChestProto.ChestUseResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ChestProto.ChestUseResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.ChestProto.ChestUseResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.ChestProto.ChestUseResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.ChestProto.ChestUseResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ChestProto.ChestUseResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.ChestProto.ChestUseResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=20232
     * </pre>
     *
     * Protobuf type {@code Proto.Collection.ChestUseResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Collection.ChestUseResponse)
        com.dxx.game.dto.ChestProto.ChestUseResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.ChestProto.internal_static_Proto_Collection_ChestUseResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.ChestProto.internal_static_Proto_Collection_ChestUseResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.ChestProto.ChestUseResponse.class, com.dxx.game.dto.ChestProto.ChestUseResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.ChestProto.ChestUseResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getCommonDataFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        code_ = 0;
        commonData_ = null;
        if (commonDataBuilder_ != null) {
          commonDataBuilder_.dispose();
          commonDataBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.ChestProto.internal_static_Proto_Collection_ChestUseResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.ChestProto.ChestUseResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.ChestProto.ChestUseResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.ChestProto.ChestUseResponse build() {
        com.dxx.game.dto.ChestProto.ChestUseResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.ChestProto.ChestUseResponse buildPartial() {
        com.dxx.game.dto.ChestProto.ChestUseResponse result = new com.dxx.game.dto.ChestProto.ChestUseResponse(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.ChestProto.ChestUseResponse result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.code_ = code_;
        }
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.commonData_ = commonDataBuilder_ == null
              ? commonData_
              : commonDataBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.ChestProto.ChestUseResponse) {
          return mergeFrom((com.dxx.game.dto.ChestProto.ChestUseResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.ChestProto.ChestUseResponse other) {
        if (other == com.dxx.game.dto.ChestProto.ChestUseResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.hasCommonData()) {
          mergeCommonData(other.getCommonData());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                code_ = input.readInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 18: {
                input.readMessage(
                    getCommonDataFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {

        code_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        bitField0_ = (bitField0_ & ~0x00000001);
        code_ = 0;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonData commonData_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> commonDataBuilder_;
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return Whether the commonData field is set.
       */
      public boolean hasCommonData() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return The commonData.
       */
      public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
        if (commonDataBuilder_ == null) {
          return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        } else {
          return commonDataBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonData_ = value;
        } else {
          commonDataBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(
          com.dxx.game.dto.CommonProto.CommonData.Builder builderForValue) {
        if (commonDataBuilder_ == null) {
          commonData_ = builderForValue.build();
        } else {
          commonDataBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder mergeCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
            commonData_ != null &&
            commonData_ != com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance()) {
            getCommonDataBuilder().mergeFrom(value);
          } else {
            commonData_ = value;
          }
        } else {
          commonDataBuilder_.mergeFrom(value);
        }
        if (commonData_ != null) {
          bitField0_ |= 0x00000002;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder clearCommonData() {
        bitField0_ = (bitField0_ & ~0x00000002);
        commonData_ = null;
        if (commonDataBuilder_ != null) {
          commonDataBuilder_.dispose();
          commonDataBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonData.Builder getCommonDataBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getCommonDataFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
        if (commonDataBuilder_ != null) {
          return commonDataBuilder_.getMessageOrBuilder();
        } else {
          return commonData_ == null ?
              com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> 
          getCommonDataFieldBuilder() {
        if (commonDataBuilder_ == null) {
          commonDataBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder>(
                  getCommonData(),
                  getParentForChildren(),
                  isClean());
          commonData_ = null;
        }
        return commonDataBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Collection.ChestUseResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.Collection.ChestUseResponse)
    private static final com.dxx.game.dto.ChestProto.ChestUseResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.ChestProto.ChestUseResponse();
    }

    public static com.dxx.game.dto.ChestProto.ChestUseResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ChestUseResponse>
        PARSER = new com.google.protobuf.AbstractParser<ChestUseResponse>() {
      @java.lang.Override
      public ChestUseResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<ChestUseResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ChestUseResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.ChestProto.ChestUseResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ChestRewardRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Collection.ChestRewardRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();

    /**
     * <pre>
     * 0领取一个，1表示全部领取
     * </pre>
     *
     * <code>int32 optType = 2;</code>
     * @return The optType.
     */
    int getOptType();
  }
  /**
   * <pre>
   * CMD PackageId=20233 领取奖励
   * </pre>
   *
   * Protobuf type {@code Proto.Collection.ChestRewardRequest}
   */
  public static final class ChestRewardRequest extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Collection.ChestRewardRequest)
      ChestRewardRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        ChestRewardRequest.class.getName());
    }
    // Use ChestRewardRequest.newBuilder() to construct.
    private ChestRewardRequest(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private ChestRewardRequest() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.ChestProto.internal_static_Proto_Collection_ChestRewardRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.ChestProto.internal_static_Proto_Collection_ChestRewardRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.ChestProto.ChestRewardRequest.class, com.dxx.game.dto.ChestProto.ChestRewardRequest.Builder.class);
    }

    private int bitField0_;
    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }

    public static final int OPTTYPE_FIELD_NUMBER = 2;
    private int optType_ = 0;
    /**
     * <pre>
     * 0领取一个，1表示全部领取
     * </pre>
     *
     * <code>int32 optType = 2;</code>
     * @return The optType.
     */
    @java.lang.Override
    public int getOptType() {
      return optType_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getCommonParams());
      }
      if (optType_ != 0) {
        output.writeInt32(2, optType_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      if (optType_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, optType_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.ChestProto.ChestRewardRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.ChestProto.ChestRewardRequest other = (com.dxx.game.dto.ChestProto.ChestRewardRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (getOptType()
          != other.getOptType()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (37 * hash) + OPTTYPE_FIELD_NUMBER;
      hash = (53 * hash) + getOptType();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.ChestProto.ChestRewardRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ChestProto.ChestRewardRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ChestProto.ChestRewardRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ChestProto.ChestRewardRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ChestProto.ChestRewardRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ChestProto.ChestRewardRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ChestProto.ChestRewardRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ChestProto.ChestRewardRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.ChestProto.ChestRewardRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.ChestProto.ChestRewardRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.ChestProto.ChestRewardRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ChestProto.ChestRewardRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.ChestProto.ChestRewardRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=20233 领取奖励
     * </pre>
     *
     * Protobuf type {@code Proto.Collection.ChestRewardRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Collection.ChestRewardRequest)
        com.dxx.game.dto.ChestProto.ChestRewardRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.ChestProto.internal_static_Proto_Collection_ChestRewardRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.ChestProto.internal_static_Proto_Collection_ChestRewardRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.ChestProto.ChestRewardRequest.class, com.dxx.game.dto.ChestProto.ChestRewardRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.ChestProto.ChestRewardRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getCommonParamsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        optType_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.ChestProto.internal_static_Proto_Collection_ChestRewardRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.ChestProto.ChestRewardRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.ChestProto.ChestRewardRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.ChestProto.ChestRewardRequest build() {
        com.dxx.game.dto.ChestProto.ChestRewardRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.ChestProto.ChestRewardRequest buildPartial() {
        com.dxx.game.dto.ChestProto.ChestRewardRequest result = new com.dxx.game.dto.ChestProto.ChestRewardRequest(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.ChestProto.ChestRewardRequest result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.commonParams_ = commonParamsBuilder_ == null
              ? commonParams_
              : commonParamsBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.optType_ = optType_;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.ChestProto.ChestRewardRequest) {
          return mergeFrom((com.dxx.game.dto.ChestProto.ChestRewardRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.ChestProto.ChestRewardRequest other) {
        if (other == com.dxx.game.dto.ChestProto.ChestRewardRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        if (other.getOptType() != 0) {
          setOptType(other.getOptType());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getCommonParamsFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 16: {
                optType_ = input.readInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
        } else {
          commonParamsBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
            commonParams_ != null &&
            commonParams_ != com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance()) {
            getCommonParamsBuilder().mergeFrom(value);
          } else {
            commonParams_ = value;
          }
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }
        if (commonParams_ != null) {
          bitField0_ |= 0x00000001;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        bitField0_ = (bitField0_ & ~0x00000001);
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }

      private int optType_ ;
      /**
       * <pre>
       * 0领取一个，1表示全部领取
       * </pre>
       *
       * <code>int32 optType = 2;</code>
       * @return The optType.
       */
      @java.lang.Override
      public int getOptType() {
        return optType_;
      }
      /**
       * <pre>
       * 0领取一个，1表示全部领取
       * </pre>
       *
       * <code>int32 optType = 2;</code>
       * @param value The optType to set.
       * @return This builder for chaining.
       */
      public Builder setOptType(int value) {

        optType_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 0领取一个，1表示全部领取
       * </pre>
       *
       * <code>int32 optType = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearOptType() {
        bitField0_ = (bitField0_ & ~0x00000002);
        optType_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Collection.ChestRewardRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.Collection.ChestRewardRequest)
    private static final com.dxx.game.dto.ChestProto.ChestRewardRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.ChestProto.ChestRewardRequest();
    }

    public static com.dxx.game.dto.ChestProto.ChestRewardRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ChestRewardRequest>
        PARSER = new com.google.protobuf.AbstractParser<ChestRewardRequest>() {
      @java.lang.Override
      public ChestRewardRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<ChestRewardRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ChestRewardRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.ChestProto.ChestRewardRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ChestRewardResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Collection.ChestRewardResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    boolean hasCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    com.dxx.game.dto.CommonProto.CommonData getCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder();

    /**
     * <code>.Proto.Common.ChestInfo chestInfo = 3;</code>
     * @return Whether the chestInfo field is set.
     */
    boolean hasChestInfo();
    /**
     * <code>.Proto.Common.ChestInfo chestInfo = 3;</code>
     * @return The chestInfo.
     */
    com.dxx.game.dto.CommonProto.ChestInfo getChestInfo();
    /**
     * <code>.Proto.Common.ChestInfo chestInfo = 3;</code>
     */
    com.dxx.game.dto.CommonProto.ChestInfoOrBuilder getChestInfoOrBuilder();
  }
  /**
   * <pre>
   * CMD PackageId=20234
   * </pre>
   *
   * Protobuf type {@code Proto.Collection.ChestRewardResponse}
   */
  public static final class ChestRewardResponse extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Collection.ChestRewardResponse)
      ChestRewardResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        ChestRewardResponse.class.getName());
    }
    // Use ChestRewardResponse.newBuilder() to construct.
    private ChestRewardResponse(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private ChestRewardResponse() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.ChestProto.internal_static_Proto_Collection_ChestRewardResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.ChestProto.internal_static_Proto_Collection_ChestRewardResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.ChestProto.ChestRewardResponse.class, com.dxx.game.dto.ChestProto.ChestRewardResponse.Builder.class);
    }

    private int bitField0_;
    public static final int CODE_FIELD_NUMBER = 1;
    private int code_ = 0;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int COMMONDATA_FIELD_NUMBER = 2;
    private com.dxx.game.dto.CommonProto.CommonData commonData_;
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    @java.lang.Override
    public boolean hasCommonData() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }

    public static final int CHESTINFO_FIELD_NUMBER = 3;
    private com.dxx.game.dto.CommonProto.ChestInfo chestInfo_;
    /**
     * <code>.Proto.Common.ChestInfo chestInfo = 3;</code>
     * @return Whether the chestInfo field is set.
     */
    @java.lang.Override
    public boolean hasChestInfo() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>.Proto.Common.ChestInfo chestInfo = 3;</code>
     * @return The chestInfo.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.ChestInfo getChestInfo() {
      return chestInfo_ == null ? com.dxx.game.dto.CommonProto.ChestInfo.getDefaultInstance() : chestInfo_;
    }
    /**
     * <code>.Proto.Common.ChestInfo chestInfo = 3;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.ChestInfoOrBuilder getChestInfoOrBuilder() {
      return chestInfo_ == null ? com.dxx.game.dto.CommonProto.ChestInfo.getDefaultInstance() : chestInfo_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(2, getCommonData());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(3, getChestInfo());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getCommonData());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getChestInfo());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.ChestProto.ChestRewardResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.ChestProto.ChestRewardResponse other = (com.dxx.game.dto.ChestProto.ChestRewardResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (hasCommonData() != other.hasCommonData()) return false;
      if (hasCommonData()) {
        if (!getCommonData()
            .equals(other.getCommonData())) return false;
      }
      if (hasChestInfo() != other.hasChestInfo()) return false;
      if (hasChestInfo()) {
        if (!getChestInfo()
            .equals(other.getChestInfo())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      if (hasCommonData()) {
        hash = (37 * hash) + COMMONDATA_FIELD_NUMBER;
        hash = (53 * hash) + getCommonData().hashCode();
      }
      if (hasChestInfo()) {
        hash = (37 * hash) + CHESTINFO_FIELD_NUMBER;
        hash = (53 * hash) + getChestInfo().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.ChestProto.ChestRewardResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ChestProto.ChestRewardResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ChestProto.ChestRewardResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ChestProto.ChestRewardResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ChestProto.ChestRewardResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ChestProto.ChestRewardResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ChestProto.ChestRewardResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ChestProto.ChestRewardResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.ChestProto.ChestRewardResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.ChestProto.ChestRewardResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.ChestProto.ChestRewardResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ChestProto.ChestRewardResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.ChestProto.ChestRewardResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=20234
     * </pre>
     *
     * Protobuf type {@code Proto.Collection.ChestRewardResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Collection.ChestRewardResponse)
        com.dxx.game.dto.ChestProto.ChestRewardResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.ChestProto.internal_static_Proto_Collection_ChestRewardResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.ChestProto.internal_static_Proto_Collection_ChestRewardResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.ChestProto.ChestRewardResponse.class, com.dxx.game.dto.ChestProto.ChestRewardResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.ChestProto.ChestRewardResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getCommonDataFieldBuilder();
          getChestInfoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        code_ = 0;
        commonData_ = null;
        if (commonDataBuilder_ != null) {
          commonDataBuilder_.dispose();
          commonDataBuilder_ = null;
        }
        chestInfo_ = null;
        if (chestInfoBuilder_ != null) {
          chestInfoBuilder_.dispose();
          chestInfoBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.ChestProto.internal_static_Proto_Collection_ChestRewardResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.ChestProto.ChestRewardResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.ChestProto.ChestRewardResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.ChestProto.ChestRewardResponse build() {
        com.dxx.game.dto.ChestProto.ChestRewardResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.ChestProto.ChestRewardResponse buildPartial() {
        com.dxx.game.dto.ChestProto.ChestRewardResponse result = new com.dxx.game.dto.ChestProto.ChestRewardResponse(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.ChestProto.ChestRewardResponse result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.code_ = code_;
        }
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.commonData_ = commonDataBuilder_ == null
              ? commonData_
              : commonDataBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.chestInfo_ = chestInfoBuilder_ == null
              ? chestInfo_
              : chestInfoBuilder_.build();
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.ChestProto.ChestRewardResponse) {
          return mergeFrom((com.dxx.game.dto.ChestProto.ChestRewardResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.ChestProto.ChestRewardResponse other) {
        if (other == com.dxx.game.dto.ChestProto.ChestRewardResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.hasCommonData()) {
          mergeCommonData(other.getCommonData());
        }
        if (other.hasChestInfo()) {
          mergeChestInfo(other.getChestInfo());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                code_ = input.readInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 18: {
                input.readMessage(
                    getCommonDataFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              case 26: {
                input.readMessage(
                    getChestInfoFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000004;
                break;
              } // case 26
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {

        code_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        bitField0_ = (bitField0_ & ~0x00000001);
        code_ = 0;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonData commonData_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> commonDataBuilder_;
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return Whether the commonData field is set.
       */
      public boolean hasCommonData() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return The commonData.
       */
      public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
        if (commonDataBuilder_ == null) {
          return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        } else {
          return commonDataBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonData_ = value;
        } else {
          commonDataBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(
          com.dxx.game.dto.CommonProto.CommonData.Builder builderForValue) {
        if (commonDataBuilder_ == null) {
          commonData_ = builderForValue.build();
        } else {
          commonDataBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder mergeCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
            commonData_ != null &&
            commonData_ != com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance()) {
            getCommonDataBuilder().mergeFrom(value);
          } else {
            commonData_ = value;
          }
        } else {
          commonDataBuilder_.mergeFrom(value);
        }
        if (commonData_ != null) {
          bitField0_ |= 0x00000002;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder clearCommonData() {
        bitField0_ = (bitField0_ & ~0x00000002);
        commonData_ = null;
        if (commonDataBuilder_ != null) {
          commonDataBuilder_.dispose();
          commonDataBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonData.Builder getCommonDataBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getCommonDataFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
        if (commonDataBuilder_ != null) {
          return commonDataBuilder_.getMessageOrBuilder();
        } else {
          return commonData_ == null ?
              com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> 
          getCommonDataFieldBuilder() {
        if (commonDataBuilder_ == null) {
          commonDataBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder>(
                  getCommonData(),
                  getParentForChildren(),
                  isClean());
          commonData_ = null;
        }
        return commonDataBuilder_;
      }

      private com.dxx.game.dto.CommonProto.ChestInfo chestInfo_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.ChestInfo, com.dxx.game.dto.CommonProto.ChestInfo.Builder, com.dxx.game.dto.CommonProto.ChestInfoOrBuilder> chestInfoBuilder_;
      /**
       * <code>.Proto.Common.ChestInfo chestInfo = 3;</code>
       * @return Whether the chestInfo field is set.
       */
      public boolean hasChestInfo() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>.Proto.Common.ChestInfo chestInfo = 3;</code>
       * @return The chestInfo.
       */
      public com.dxx.game.dto.CommonProto.ChestInfo getChestInfo() {
        if (chestInfoBuilder_ == null) {
          return chestInfo_ == null ? com.dxx.game.dto.CommonProto.ChestInfo.getDefaultInstance() : chestInfo_;
        } else {
          return chestInfoBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.ChestInfo chestInfo = 3;</code>
       */
      public Builder setChestInfo(com.dxx.game.dto.CommonProto.ChestInfo value) {
        if (chestInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          chestInfo_ = value;
        } else {
          chestInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.ChestInfo chestInfo = 3;</code>
       */
      public Builder setChestInfo(
          com.dxx.game.dto.CommonProto.ChestInfo.Builder builderForValue) {
        if (chestInfoBuilder_ == null) {
          chestInfo_ = builderForValue.build();
        } else {
          chestInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.ChestInfo chestInfo = 3;</code>
       */
      public Builder mergeChestInfo(com.dxx.game.dto.CommonProto.ChestInfo value) {
        if (chestInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0) &&
            chestInfo_ != null &&
            chestInfo_ != com.dxx.game.dto.CommonProto.ChestInfo.getDefaultInstance()) {
            getChestInfoBuilder().mergeFrom(value);
          } else {
            chestInfo_ = value;
          }
        } else {
          chestInfoBuilder_.mergeFrom(value);
        }
        if (chestInfo_ != null) {
          bitField0_ |= 0x00000004;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Proto.Common.ChestInfo chestInfo = 3;</code>
       */
      public Builder clearChestInfo() {
        bitField0_ = (bitField0_ & ~0x00000004);
        chestInfo_ = null;
        if (chestInfoBuilder_ != null) {
          chestInfoBuilder_.dispose();
          chestInfoBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.ChestInfo chestInfo = 3;</code>
       */
      public com.dxx.game.dto.CommonProto.ChestInfo.Builder getChestInfoBuilder() {
        bitField0_ |= 0x00000004;
        onChanged();
        return getChestInfoFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.ChestInfo chestInfo = 3;</code>
       */
      public com.dxx.game.dto.CommonProto.ChestInfoOrBuilder getChestInfoOrBuilder() {
        if (chestInfoBuilder_ != null) {
          return chestInfoBuilder_.getMessageOrBuilder();
        } else {
          return chestInfo_ == null ?
              com.dxx.game.dto.CommonProto.ChestInfo.getDefaultInstance() : chestInfo_;
        }
      }
      /**
       * <code>.Proto.Common.ChestInfo chestInfo = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.ChestInfo, com.dxx.game.dto.CommonProto.ChestInfo.Builder, com.dxx.game.dto.CommonProto.ChestInfoOrBuilder> 
          getChestInfoFieldBuilder() {
        if (chestInfoBuilder_ == null) {
          chestInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.ChestInfo, com.dxx.game.dto.CommonProto.ChestInfo.Builder, com.dxx.game.dto.CommonProto.ChestInfoOrBuilder>(
                  getChestInfo(),
                  getParentForChildren(),
                  isClean());
          chestInfo_ = null;
        }
        return chestInfoBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Collection.ChestRewardResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.Collection.ChestRewardResponse)
    private static final com.dxx.game.dto.ChestProto.ChestRewardResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.ChestProto.ChestRewardResponse();
    }

    public static com.dxx.game.dto.ChestProto.ChestRewardResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ChestRewardResponse>
        PARSER = new com.google.protobuf.AbstractParser<ChestRewardResponse>() {
      @java.lang.Override
      public ChestRewardResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<ChestRewardResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ChestRewardResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.ChestProto.ChestRewardResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Collection_ChestUseRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Collection_ChestUseRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Collection_ChestUseResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Collection_ChestUseResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Collection_ChestRewardRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Collection_ChestRewardRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Collection_ChestRewardResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Collection_ChestRewardResponse_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\013chest.proto\022\020Proto.Collection\032\014common." +
      "proto\"b\n\017ChestUseRequest\0220\n\014commonParams" +
      "\030\001 \001(\0132\032.Proto.Common.CommonParams\022\016\n\006it" +
      "emId\030\002 \001(\005\022\r\n\005count\030\003 \001(\005\"N\n\020ChestUseRes" +
      "ponse\022\014\n\004code\030\001 \001(\005\022,\n\ncommonData\030\002 \001(\0132" +
      "\030.Proto.Common.CommonData\"W\n\022ChestReward" +
      "Request\0220\n\014commonParams\030\001 \001(\0132\032.Proto.Co" +
      "mmon.CommonParams\022\017\n\007optType\030\002 \001(\005\"}\n\023Ch" +
      "estRewardResponse\022\014\n\004code\030\001 \001(\005\022,\n\ncommo" +
      "nData\030\002 \001(\0132\030.Proto.Common.CommonData\022*\n" +
      "\tchestInfo\030\003 \001(\0132\027.Proto.Common.ChestInf" +
      "oB\036\n\020com.dxx.game.dtoB\nChestProtob\006proto" +
      "3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.dxx.game.dto.CommonProto.getDescriptor(),
        });
    internal_static_Proto_Collection_ChestUseRequest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_Proto_Collection_ChestUseRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Collection_ChestUseRequest_descriptor,
        new java.lang.String[] { "CommonParams", "ItemId", "Count", });
    internal_static_Proto_Collection_ChestUseResponse_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_Proto_Collection_ChestUseResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Collection_ChestUseResponse_descriptor,
        new java.lang.String[] { "Code", "CommonData", });
    internal_static_Proto_Collection_ChestRewardRequest_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_Proto_Collection_ChestRewardRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Collection_ChestRewardRequest_descriptor,
        new java.lang.String[] { "CommonParams", "OptType", });
    internal_static_Proto_Collection_ChestRewardResponse_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_Proto_Collection_ChestRewardResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Collection_ChestRewardResponse_descriptor,
        new java.lang.String[] { "Code", "CommonData", "ChestInfo", });
    descriptor.resolveAllFeaturesImmutable();
    com.dxx.game.dto.CommonProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
