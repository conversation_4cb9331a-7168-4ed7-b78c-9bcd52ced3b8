// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: city.proto
// Protobuf Java Version: 4.29.0

package com.dxx.game.dto;

public final class CityProto {
  private CityProto() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 29,
      /* patch= */ 0,
      /* suffix= */ "",
      CityProto.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface CityGoldmineLevelUpRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Actor.CityGoldmineLevelUpRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();
  }
  /**
   * <pre>
   * CMD PackageId=12601 主城-金矿升级
   * </pre>
   *
   * Protobuf type {@code Proto.Actor.CityGoldmineLevelUpRequest}
   */
  public static final class CityGoldmineLevelUpRequest extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Actor.CityGoldmineLevelUpRequest)
      CityGoldmineLevelUpRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        CityGoldmineLevelUpRequest.class.getName());
    }
    // Use CityGoldmineLevelUpRequest.newBuilder() to construct.
    private CityGoldmineLevelUpRequest(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private CityGoldmineLevelUpRequest() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityGoldmineLevelUpRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityGoldmineLevelUpRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.CityProto.CityGoldmineLevelUpRequest.class, com.dxx.game.dto.CityProto.CityGoldmineLevelUpRequest.Builder.class);
    }

    private int bitField0_;
    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getCommonParams());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.CityProto.CityGoldmineLevelUpRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.CityProto.CityGoldmineLevelUpRequest other = (com.dxx.game.dto.CityProto.CityGoldmineLevelUpRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.CityProto.CityGoldmineLevelUpRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.CityProto.CityGoldmineLevelUpRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.CityProto.CityGoldmineLevelUpRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.CityProto.CityGoldmineLevelUpRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.CityProto.CityGoldmineLevelUpRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.CityProto.CityGoldmineLevelUpRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.CityProto.CityGoldmineLevelUpRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.CityProto.CityGoldmineLevelUpRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.CityProto.CityGoldmineLevelUpRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.CityProto.CityGoldmineLevelUpRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.CityProto.CityGoldmineLevelUpRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.CityProto.CityGoldmineLevelUpRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.CityProto.CityGoldmineLevelUpRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=12601 主城-金矿升级
     * </pre>
     *
     * Protobuf type {@code Proto.Actor.CityGoldmineLevelUpRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Actor.CityGoldmineLevelUpRequest)
        com.dxx.game.dto.CityProto.CityGoldmineLevelUpRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityGoldmineLevelUpRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityGoldmineLevelUpRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.CityProto.CityGoldmineLevelUpRequest.class, com.dxx.game.dto.CityProto.CityGoldmineLevelUpRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.CityProto.CityGoldmineLevelUpRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getCommonParamsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityGoldmineLevelUpRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.CityProto.CityGoldmineLevelUpRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.CityProto.CityGoldmineLevelUpRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.CityProto.CityGoldmineLevelUpRequest build() {
        com.dxx.game.dto.CityProto.CityGoldmineLevelUpRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.CityProto.CityGoldmineLevelUpRequest buildPartial() {
        com.dxx.game.dto.CityProto.CityGoldmineLevelUpRequest result = new com.dxx.game.dto.CityProto.CityGoldmineLevelUpRequest(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.CityProto.CityGoldmineLevelUpRequest result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.commonParams_ = commonParamsBuilder_ == null
              ? commonParams_
              : commonParamsBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.CityProto.CityGoldmineLevelUpRequest) {
          return mergeFrom((com.dxx.game.dto.CityProto.CityGoldmineLevelUpRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.CityProto.CityGoldmineLevelUpRequest other) {
        if (other == com.dxx.game.dto.CityProto.CityGoldmineLevelUpRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getCommonParamsFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
        } else {
          commonParamsBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
            commonParams_ != null &&
            commonParams_ != com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance()) {
            getCommonParamsBuilder().mergeFrom(value);
          } else {
            commonParams_ = value;
          }
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }
        if (commonParams_ != null) {
          bitField0_ |= 0x00000001;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        bitField0_ = (bitField0_ & ~0x00000001);
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Actor.CityGoldmineLevelUpRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.Actor.CityGoldmineLevelUpRequest)
    private static final com.dxx.game.dto.CityProto.CityGoldmineLevelUpRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.CityProto.CityGoldmineLevelUpRequest();
    }

    public static com.dxx.game.dto.CityProto.CityGoldmineLevelUpRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<CityGoldmineLevelUpRequest>
        PARSER = new com.google.protobuf.AbstractParser<CityGoldmineLevelUpRequest>() {
      @java.lang.Override
      public CityGoldmineLevelUpRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<CityGoldmineLevelUpRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CityGoldmineLevelUpRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.CityProto.CityGoldmineLevelUpRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface CityGoldmineLevelUpResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Actor.CityGoldmineLevelUpResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <code>int32 level = 2;</code>
     * @return The level.
     */
    int getLevel();

    /**
     * <code>.Proto.Common.CommonData commonData = 3;</code>
     * @return Whether the commonData field is set.
     */
    boolean hasCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 3;</code>
     * @return The commonData.
     */
    com.dxx.game.dto.CommonProto.CommonData getCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 3;</code>
     */
    com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder();
  }
  /**
   * <pre>
   * CMD PackageId=12602
   * </pre>
   *
   * Protobuf type {@code Proto.Actor.CityGoldmineLevelUpResponse}
   */
  public static final class CityGoldmineLevelUpResponse extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Actor.CityGoldmineLevelUpResponse)
      CityGoldmineLevelUpResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        CityGoldmineLevelUpResponse.class.getName());
    }
    // Use CityGoldmineLevelUpResponse.newBuilder() to construct.
    private CityGoldmineLevelUpResponse(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private CityGoldmineLevelUpResponse() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityGoldmineLevelUpResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityGoldmineLevelUpResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.CityProto.CityGoldmineLevelUpResponse.class, com.dxx.game.dto.CityProto.CityGoldmineLevelUpResponse.Builder.class);
    }

    private int bitField0_;
    public static final int CODE_FIELD_NUMBER = 1;
    private int code_ = 0;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int LEVEL_FIELD_NUMBER = 2;
    private int level_ = 0;
    /**
     * <code>int32 level = 2;</code>
     * @return The level.
     */
    @java.lang.Override
    public int getLevel() {
      return level_;
    }

    public static final int COMMONDATA_FIELD_NUMBER = 3;
    private com.dxx.game.dto.CommonProto.CommonData commonData_;
    /**
     * <code>.Proto.Common.CommonData commonData = 3;</code>
     * @return Whether the commonData field is set.
     */
    @java.lang.Override
    public boolean hasCommonData() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 3;</code>
     * @return The commonData.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 3;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (level_ != 0) {
        output.writeInt32(2, level_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(3, getCommonData());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (level_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, level_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getCommonData());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.CityProto.CityGoldmineLevelUpResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.CityProto.CityGoldmineLevelUpResponse other = (com.dxx.game.dto.CityProto.CityGoldmineLevelUpResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (getLevel()
          != other.getLevel()) return false;
      if (hasCommonData() != other.hasCommonData()) return false;
      if (hasCommonData()) {
        if (!getCommonData()
            .equals(other.getCommonData())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      hash = (37 * hash) + LEVEL_FIELD_NUMBER;
      hash = (53 * hash) + getLevel();
      if (hasCommonData()) {
        hash = (37 * hash) + COMMONDATA_FIELD_NUMBER;
        hash = (53 * hash) + getCommonData().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.CityProto.CityGoldmineLevelUpResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.CityProto.CityGoldmineLevelUpResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.CityProto.CityGoldmineLevelUpResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.CityProto.CityGoldmineLevelUpResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.CityProto.CityGoldmineLevelUpResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.CityProto.CityGoldmineLevelUpResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.CityProto.CityGoldmineLevelUpResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.CityProto.CityGoldmineLevelUpResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.CityProto.CityGoldmineLevelUpResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.CityProto.CityGoldmineLevelUpResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.CityProto.CityGoldmineLevelUpResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.CityProto.CityGoldmineLevelUpResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.CityProto.CityGoldmineLevelUpResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=12602
     * </pre>
     *
     * Protobuf type {@code Proto.Actor.CityGoldmineLevelUpResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Actor.CityGoldmineLevelUpResponse)
        com.dxx.game.dto.CityProto.CityGoldmineLevelUpResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityGoldmineLevelUpResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityGoldmineLevelUpResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.CityProto.CityGoldmineLevelUpResponse.class, com.dxx.game.dto.CityProto.CityGoldmineLevelUpResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.CityProto.CityGoldmineLevelUpResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getCommonDataFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        code_ = 0;
        level_ = 0;
        commonData_ = null;
        if (commonDataBuilder_ != null) {
          commonDataBuilder_.dispose();
          commonDataBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityGoldmineLevelUpResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.CityProto.CityGoldmineLevelUpResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.CityProto.CityGoldmineLevelUpResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.CityProto.CityGoldmineLevelUpResponse build() {
        com.dxx.game.dto.CityProto.CityGoldmineLevelUpResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.CityProto.CityGoldmineLevelUpResponse buildPartial() {
        com.dxx.game.dto.CityProto.CityGoldmineLevelUpResponse result = new com.dxx.game.dto.CityProto.CityGoldmineLevelUpResponse(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.CityProto.CityGoldmineLevelUpResponse result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.code_ = code_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.level_ = level_;
        }
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.commonData_ = commonDataBuilder_ == null
              ? commonData_
              : commonDataBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.CityProto.CityGoldmineLevelUpResponse) {
          return mergeFrom((com.dxx.game.dto.CityProto.CityGoldmineLevelUpResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.CityProto.CityGoldmineLevelUpResponse other) {
        if (other == com.dxx.game.dto.CityProto.CityGoldmineLevelUpResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.getLevel() != 0) {
          setLevel(other.getLevel());
        }
        if (other.hasCommonData()) {
          mergeCommonData(other.getCommonData());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                code_ = input.readInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                level_ = input.readInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 26: {
                input.readMessage(
                    getCommonDataFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000004;
                break;
              } // case 26
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {

        code_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        bitField0_ = (bitField0_ & ~0x00000001);
        code_ = 0;
        onChanged();
        return this;
      }

      private int level_ ;
      /**
       * <code>int32 level = 2;</code>
       * @return The level.
       */
      @java.lang.Override
      public int getLevel() {
        return level_;
      }
      /**
       * <code>int32 level = 2;</code>
       * @param value The level to set.
       * @return This builder for chaining.
       */
      public Builder setLevel(int value) {

        level_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>int32 level = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearLevel() {
        bitField0_ = (bitField0_ & ~0x00000002);
        level_ = 0;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonData commonData_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> commonDataBuilder_;
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       * @return Whether the commonData field is set.
       */
      public boolean hasCommonData() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       * @return The commonData.
       */
      public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
        if (commonDataBuilder_ == null) {
          return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        } else {
          return commonDataBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       */
      public Builder setCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonData_ = value;
        } else {
          commonDataBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       */
      public Builder setCommonData(
          com.dxx.game.dto.CommonProto.CommonData.Builder builderForValue) {
        if (commonDataBuilder_ == null) {
          commonData_ = builderForValue.build();
        } else {
          commonDataBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       */
      public Builder mergeCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0) &&
            commonData_ != null &&
            commonData_ != com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance()) {
            getCommonDataBuilder().mergeFrom(value);
          } else {
            commonData_ = value;
          }
        } else {
          commonDataBuilder_.mergeFrom(value);
        }
        if (commonData_ != null) {
          bitField0_ |= 0x00000004;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       */
      public Builder clearCommonData() {
        bitField0_ = (bitField0_ & ~0x00000004);
        commonData_ = null;
        if (commonDataBuilder_ != null) {
          commonDataBuilder_.dispose();
          commonDataBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonData.Builder getCommonDataBuilder() {
        bitField0_ |= 0x00000004;
        onChanged();
        return getCommonDataFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
        if (commonDataBuilder_ != null) {
          return commonDataBuilder_.getMessageOrBuilder();
        } else {
          return commonData_ == null ?
              com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> 
          getCommonDataFieldBuilder() {
        if (commonDataBuilder_ == null) {
          commonDataBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder>(
                  getCommonData(),
                  getParentForChildren(),
                  isClean());
          commonData_ = null;
        }
        return commonDataBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Actor.CityGoldmineLevelUpResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.Actor.CityGoldmineLevelUpResponse)
    private static final com.dxx.game.dto.CityProto.CityGoldmineLevelUpResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.CityProto.CityGoldmineLevelUpResponse();
    }

    public static com.dxx.game.dto.CityProto.CityGoldmineLevelUpResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<CityGoldmineLevelUpResponse>
        PARSER = new com.google.protobuf.AbstractParser<CityGoldmineLevelUpResponse>() {
      @java.lang.Override
      public CityGoldmineLevelUpResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<CityGoldmineLevelUpResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CityGoldmineLevelUpResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.CityProto.CityGoldmineLevelUpResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface CityGoldmineHangRewardRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Actor.CityGoldmineHangRewardRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();
  }
  /**
   * <pre>
   * CMD PackageId=12603 主城-金矿挂机奖励
   * </pre>
   *
   * Protobuf type {@code Proto.Actor.CityGoldmineHangRewardRequest}
   */
  public static final class CityGoldmineHangRewardRequest extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Actor.CityGoldmineHangRewardRequest)
      CityGoldmineHangRewardRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        CityGoldmineHangRewardRequest.class.getName());
    }
    // Use CityGoldmineHangRewardRequest.newBuilder() to construct.
    private CityGoldmineHangRewardRequest(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private CityGoldmineHangRewardRequest() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityGoldmineHangRewardRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityGoldmineHangRewardRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.CityProto.CityGoldmineHangRewardRequest.class, com.dxx.game.dto.CityProto.CityGoldmineHangRewardRequest.Builder.class);
    }

    private int bitField0_;
    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getCommonParams());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.CityProto.CityGoldmineHangRewardRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.CityProto.CityGoldmineHangRewardRequest other = (com.dxx.game.dto.CityProto.CityGoldmineHangRewardRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.CityProto.CityGoldmineHangRewardRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.CityProto.CityGoldmineHangRewardRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.CityProto.CityGoldmineHangRewardRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.CityProto.CityGoldmineHangRewardRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.CityProto.CityGoldmineHangRewardRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.CityProto.CityGoldmineHangRewardRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.CityProto.CityGoldmineHangRewardRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.CityProto.CityGoldmineHangRewardRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.CityProto.CityGoldmineHangRewardRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.CityProto.CityGoldmineHangRewardRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.CityProto.CityGoldmineHangRewardRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.CityProto.CityGoldmineHangRewardRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.CityProto.CityGoldmineHangRewardRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=12603 主城-金矿挂机奖励
     * </pre>
     *
     * Protobuf type {@code Proto.Actor.CityGoldmineHangRewardRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Actor.CityGoldmineHangRewardRequest)
        com.dxx.game.dto.CityProto.CityGoldmineHangRewardRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityGoldmineHangRewardRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityGoldmineHangRewardRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.CityProto.CityGoldmineHangRewardRequest.class, com.dxx.game.dto.CityProto.CityGoldmineHangRewardRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.CityProto.CityGoldmineHangRewardRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getCommonParamsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityGoldmineHangRewardRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.CityProto.CityGoldmineHangRewardRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.CityProto.CityGoldmineHangRewardRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.CityProto.CityGoldmineHangRewardRequest build() {
        com.dxx.game.dto.CityProto.CityGoldmineHangRewardRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.CityProto.CityGoldmineHangRewardRequest buildPartial() {
        com.dxx.game.dto.CityProto.CityGoldmineHangRewardRequest result = new com.dxx.game.dto.CityProto.CityGoldmineHangRewardRequest(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.CityProto.CityGoldmineHangRewardRequest result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.commonParams_ = commonParamsBuilder_ == null
              ? commonParams_
              : commonParamsBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.CityProto.CityGoldmineHangRewardRequest) {
          return mergeFrom((com.dxx.game.dto.CityProto.CityGoldmineHangRewardRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.CityProto.CityGoldmineHangRewardRequest other) {
        if (other == com.dxx.game.dto.CityProto.CityGoldmineHangRewardRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getCommonParamsFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
        } else {
          commonParamsBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
            commonParams_ != null &&
            commonParams_ != com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance()) {
            getCommonParamsBuilder().mergeFrom(value);
          } else {
            commonParams_ = value;
          }
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }
        if (commonParams_ != null) {
          bitField0_ |= 0x00000001;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        bitField0_ = (bitField0_ & ~0x00000001);
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Actor.CityGoldmineHangRewardRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.Actor.CityGoldmineHangRewardRequest)
    private static final com.dxx.game.dto.CityProto.CityGoldmineHangRewardRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.CityProto.CityGoldmineHangRewardRequest();
    }

    public static com.dxx.game.dto.CityProto.CityGoldmineHangRewardRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<CityGoldmineHangRewardRequest>
        PARSER = new com.google.protobuf.AbstractParser<CityGoldmineHangRewardRequest>() {
      @java.lang.Override
      public CityGoldmineHangRewardRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<CityGoldmineHangRewardRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CityGoldmineHangRewardRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.CityProto.CityGoldmineHangRewardRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface CityGoldmineHangRewardResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Actor.CityGoldmineHangRewardResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <code>uint64 lastGoldmineRewardTime = 2;</code>
     * @return The lastGoldmineRewardTime.
     */
    long getLastGoldmineRewardTime();

    /**
     * <code>.Proto.Common.CommonData commonData = 3;</code>
     * @return Whether the commonData field is set.
     */
    boolean hasCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 3;</code>
     * @return The commonData.
     */
    com.dxx.game.dto.CommonProto.CommonData getCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 3;</code>
     */
    com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder();
  }
  /**
   * <pre>
   * CMD PackageId=12604
   * </pre>
   *
   * Protobuf type {@code Proto.Actor.CityGoldmineHangRewardResponse}
   */
  public static final class CityGoldmineHangRewardResponse extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Actor.CityGoldmineHangRewardResponse)
      CityGoldmineHangRewardResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        CityGoldmineHangRewardResponse.class.getName());
    }
    // Use CityGoldmineHangRewardResponse.newBuilder() to construct.
    private CityGoldmineHangRewardResponse(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private CityGoldmineHangRewardResponse() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityGoldmineHangRewardResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityGoldmineHangRewardResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.CityProto.CityGoldmineHangRewardResponse.class, com.dxx.game.dto.CityProto.CityGoldmineHangRewardResponse.Builder.class);
    }

    private int bitField0_;
    public static final int CODE_FIELD_NUMBER = 1;
    private int code_ = 0;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int LASTGOLDMINEREWARDTIME_FIELD_NUMBER = 2;
    private long lastGoldmineRewardTime_ = 0L;
    /**
     * <code>uint64 lastGoldmineRewardTime = 2;</code>
     * @return The lastGoldmineRewardTime.
     */
    @java.lang.Override
    public long getLastGoldmineRewardTime() {
      return lastGoldmineRewardTime_;
    }

    public static final int COMMONDATA_FIELD_NUMBER = 3;
    private com.dxx.game.dto.CommonProto.CommonData commonData_;
    /**
     * <code>.Proto.Common.CommonData commonData = 3;</code>
     * @return Whether the commonData field is set.
     */
    @java.lang.Override
    public boolean hasCommonData() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 3;</code>
     * @return The commonData.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 3;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (lastGoldmineRewardTime_ != 0L) {
        output.writeUInt64(2, lastGoldmineRewardTime_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(3, getCommonData());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (lastGoldmineRewardTime_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(2, lastGoldmineRewardTime_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getCommonData());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.CityProto.CityGoldmineHangRewardResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.CityProto.CityGoldmineHangRewardResponse other = (com.dxx.game.dto.CityProto.CityGoldmineHangRewardResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (getLastGoldmineRewardTime()
          != other.getLastGoldmineRewardTime()) return false;
      if (hasCommonData() != other.hasCommonData()) return false;
      if (hasCommonData()) {
        if (!getCommonData()
            .equals(other.getCommonData())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      hash = (37 * hash) + LASTGOLDMINEREWARDTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getLastGoldmineRewardTime());
      if (hasCommonData()) {
        hash = (37 * hash) + COMMONDATA_FIELD_NUMBER;
        hash = (53 * hash) + getCommonData().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.CityProto.CityGoldmineHangRewardResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.CityProto.CityGoldmineHangRewardResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.CityProto.CityGoldmineHangRewardResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.CityProto.CityGoldmineHangRewardResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.CityProto.CityGoldmineHangRewardResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.CityProto.CityGoldmineHangRewardResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.CityProto.CityGoldmineHangRewardResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.CityProto.CityGoldmineHangRewardResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.CityProto.CityGoldmineHangRewardResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.CityProto.CityGoldmineHangRewardResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.CityProto.CityGoldmineHangRewardResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.CityProto.CityGoldmineHangRewardResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.CityProto.CityGoldmineHangRewardResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=12604
     * </pre>
     *
     * Protobuf type {@code Proto.Actor.CityGoldmineHangRewardResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Actor.CityGoldmineHangRewardResponse)
        com.dxx.game.dto.CityProto.CityGoldmineHangRewardResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityGoldmineHangRewardResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityGoldmineHangRewardResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.CityProto.CityGoldmineHangRewardResponse.class, com.dxx.game.dto.CityProto.CityGoldmineHangRewardResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.CityProto.CityGoldmineHangRewardResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getCommonDataFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        code_ = 0;
        lastGoldmineRewardTime_ = 0L;
        commonData_ = null;
        if (commonDataBuilder_ != null) {
          commonDataBuilder_.dispose();
          commonDataBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityGoldmineHangRewardResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.CityProto.CityGoldmineHangRewardResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.CityProto.CityGoldmineHangRewardResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.CityProto.CityGoldmineHangRewardResponse build() {
        com.dxx.game.dto.CityProto.CityGoldmineHangRewardResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.CityProto.CityGoldmineHangRewardResponse buildPartial() {
        com.dxx.game.dto.CityProto.CityGoldmineHangRewardResponse result = new com.dxx.game.dto.CityProto.CityGoldmineHangRewardResponse(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.CityProto.CityGoldmineHangRewardResponse result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.code_ = code_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.lastGoldmineRewardTime_ = lastGoldmineRewardTime_;
        }
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.commonData_ = commonDataBuilder_ == null
              ? commonData_
              : commonDataBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.CityProto.CityGoldmineHangRewardResponse) {
          return mergeFrom((com.dxx.game.dto.CityProto.CityGoldmineHangRewardResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.CityProto.CityGoldmineHangRewardResponse other) {
        if (other == com.dxx.game.dto.CityProto.CityGoldmineHangRewardResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.getLastGoldmineRewardTime() != 0L) {
          setLastGoldmineRewardTime(other.getLastGoldmineRewardTime());
        }
        if (other.hasCommonData()) {
          mergeCommonData(other.getCommonData());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                code_ = input.readInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                lastGoldmineRewardTime_ = input.readUInt64();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 26: {
                input.readMessage(
                    getCommonDataFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000004;
                break;
              } // case 26
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {

        code_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        bitField0_ = (bitField0_ & ~0x00000001);
        code_ = 0;
        onChanged();
        return this;
      }

      private long lastGoldmineRewardTime_ ;
      /**
       * <code>uint64 lastGoldmineRewardTime = 2;</code>
       * @return The lastGoldmineRewardTime.
       */
      @java.lang.Override
      public long getLastGoldmineRewardTime() {
        return lastGoldmineRewardTime_;
      }
      /**
       * <code>uint64 lastGoldmineRewardTime = 2;</code>
       * @param value The lastGoldmineRewardTime to set.
       * @return This builder for chaining.
       */
      public Builder setLastGoldmineRewardTime(long value) {

        lastGoldmineRewardTime_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 lastGoldmineRewardTime = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearLastGoldmineRewardTime() {
        bitField0_ = (bitField0_ & ~0x00000002);
        lastGoldmineRewardTime_ = 0L;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonData commonData_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> commonDataBuilder_;
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       * @return Whether the commonData field is set.
       */
      public boolean hasCommonData() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       * @return The commonData.
       */
      public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
        if (commonDataBuilder_ == null) {
          return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        } else {
          return commonDataBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       */
      public Builder setCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonData_ = value;
        } else {
          commonDataBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       */
      public Builder setCommonData(
          com.dxx.game.dto.CommonProto.CommonData.Builder builderForValue) {
        if (commonDataBuilder_ == null) {
          commonData_ = builderForValue.build();
        } else {
          commonDataBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       */
      public Builder mergeCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0) &&
            commonData_ != null &&
            commonData_ != com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance()) {
            getCommonDataBuilder().mergeFrom(value);
          } else {
            commonData_ = value;
          }
        } else {
          commonDataBuilder_.mergeFrom(value);
        }
        if (commonData_ != null) {
          bitField0_ |= 0x00000004;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       */
      public Builder clearCommonData() {
        bitField0_ = (bitField0_ & ~0x00000004);
        commonData_ = null;
        if (commonDataBuilder_ != null) {
          commonDataBuilder_.dispose();
          commonDataBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonData.Builder getCommonDataBuilder() {
        bitField0_ |= 0x00000004;
        onChanged();
        return getCommonDataFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
        if (commonDataBuilder_ != null) {
          return commonDataBuilder_.getMessageOrBuilder();
        } else {
          return commonData_ == null ?
              com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> 
          getCommonDataFieldBuilder() {
        if (commonDataBuilder_ == null) {
          commonDataBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder>(
                  getCommonData(),
                  getParentForChildren(),
                  isClean());
          commonData_ = null;
        }
        return commonDataBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Actor.CityGoldmineHangRewardResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.Actor.CityGoldmineHangRewardResponse)
    private static final com.dxx.game.dto.CityProto.CityGoldmineHangRewardResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.CityProto.CityGoldmineHangRewardResponse();
    }

    public static com.dxx.game.dto.CityProto.CityGoldmineHangRewardResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<CityGoldmineHangRewardResponse>
        PARSER = new com.google.protobuf.AbstractParser<CityGoldmineHangRewardResponse>() {
      @java.lang.Override
      public CityGoldmineHangRewardResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<CityGoldmineHangRewardResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CityGoldmineHangRewardResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.CityProto.CityGoldmineHangRewardResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface CityGetChestInfoRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Actor.CityGetChestInfoRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();
  }
  /**
   * <pre>
   * CMD PackageId=12605 主城-宝箱信息
   * </pre>
   *
   * Protobuf type {@code Proto.Actor.CityGetChestInfoRequest}
   */
  public static final class CityGetChestInfoRequest extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Actor.CityGetChestInfoRequest)
      CityGetChestInfoRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        CityGetChestInfoRequest.class.getName());
    }
    // Use CityGetChestInfoRequest.newBuilder() to construct.
    private CityGetChestInfoRequest(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private CityGetChestInfoRequest() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityGetChestInfoRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityGetChestInfoRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.CityProto.CityGetChestInfoRequest.class, com.dxx.game.dto.CityProto.CityGetChestInfoRequest.Builder.class);
    }

    private int bitField0_;
    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getCommonParams());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.CityProto.CityGetChestInfoRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.CityProto.CityGetChestInfoRequest other = (com.dxx.game.dto.CityProto.CityGetChestInfoRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.CityProto.CityGetChestInfoRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.CityProto.CityGetChestInfoRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.CityProto.CityGetChestInfoRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.CityProto.CityGetChestInfoRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.CityProto.CityGetChestInfoRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.CityProto.CityGetChestInfoRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.CityProto.CityGetChestInfoRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.CityProto.CityGetChestInfoRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.CityProto.CityGetChestInfoRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.CityProto.CityGetChestInfoRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.CityProto.CityGetChestInfoRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.CityProto.CityGetChestInfoRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.CityProto.CityGetChestInfoRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=12605 主城-宝箱信息
     * </pre>
     *
     * Protobuf type {@code Proto.Actor.CityGetChestInfoRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Actor.CityGetChestInfoRequest)
        com.dxx.game.dto.CityProto.CityGetChestInfoRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityGetChestInfoRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityGetChestInfoRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.CityProto.CityGetChestInfoRequest.class, com.dxx.game.dto.CityProto.CityGetChestInfoRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.CityProto.CityGetChestInfoRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getCommonParamsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityGetChestInfoRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.CityProto.CityGetChestInfoRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.CityProto.CityGetChestInfoRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.CityProto.CityGetChestInfoRequest build() {
        com.dxx.game.dto.CityProto.CityGetChestInfoRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.CityProto.CityGetChestInfoRequest buildPartial() {
        com.dxx.game.dto.CityProto.CityGetChestInfoRequest result = new com.dxx.game.dto.CityProto.CityGetChestInfoRequest(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.CityProto.CityGetChestInfoRequest result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.commonParams_ = commonParamsBuilder_ == null
              ? commonParams_
              : commonParamsBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.CityProto.CityGetChestInfoRequest) {
          return mergeFrom((com.dxx.game.dto.CityProto.CityGetChestInfoRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.CityProto.CityGetChestInfoRequest other) {
        if (other == com.dxx.game.dto.CityProto.CityGetChestInfoRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getCommonParamsFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
        } else {
          commonParamsBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
            commonParams_ != null &&
            commonParams_ != com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance()) {
            getCommonParamsBuilder().mergeFrom(value);
          } else {
            commonParams_ = value;
          }
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }
        if (commonParams_ != null) {
          bitField0_ |= 0x00000001;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        bitField0_ = (bitField0_ & ~0x00000001);
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Actor.CityGetChestInfoRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.Actor.CityGetChestInfoRequest)
    private static final com.dxx.game.dto.CityProto.CityGetChestInfoRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.CityProto.CityGetChestInfoRequest();
    }

    public static com.dxx.game.dto.CityProto.CityGetChestInfoRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<CityGetChestInfoRequest>
        PARSER = new com.google.protobuf.AbstractParser<CityGetChestInfoRequest>() {
      @java.lang.Override
      public CityGetChestInfoRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<CityGetChestInfoRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CityGetChestInfoRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.CityProto.CityGetChestInfoRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface CityGetChestInfoResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Actor.CityGetChestInfoResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    boolean hasCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    com.dxx.game.dto.CommonProto.CommonData getCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder();

    /**
     * <code>repeated .Proto.Common.CityChestDto cityChest = 3;</code>
     */
    java.util.List<com.dxx.game.dto.CommonProto.CityChestDto> 
        getCityChestList();
    /**
     * <code>repeated .Proto.Common.CityChestDto cityChest = 3;</code>
     */
    com.dxx.game.dto.CommonProto.CityChestDto getCityChest(int index);
    /**
     * <code>repeated .Proto.Common.CityChestDto cityChest = 3;</code>
     */
    int getCityChestCount();
    /**
     * <code>repeated .Proto.Common.CityChestDto cityChest = 3;</code>
     */
    java.util.List<? extends com.dxx.game.dto.CommonProto.CityChestDtoOrBuilder> 
        getCityChestOrBuilderList();
    /**
     * <code>repeated .Proto.Common.CityChestDto cityChest = 3;</code>
     */
    com.dxx.game.dto.CommonProto.CityChestDtoOrBuilder getCityChestOrBuilder(
        int index);

    /**
     * <code>uint64 startTime = 4;</code>
     * @return The startTime.
     */
    long getStartTime();

    /**
     * <code>uint64 refreshTime = 5;</code>
     * @return The refreshTime.
     */
    long getRefreshTime();
  }
  /**
   * <pre>
   * CMD PackageId=12606
   * </pre>
   *
   * Protobuf type {@code Proto.Actor.CityGetChestInfoResponse}
   */
  public static final class CityGetChestInfoResponse extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Actor.CityGetChestInfoResponse)
      CityGetChestInfoResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        CityGetChestInfoResponse.class.getName());
    }
    // Use CityGetChestInfoResponse.newBuilder() to construct.
    private CityGetChestInfoResponse(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private CityGetChestInfoResponse() {
      cityChest_ = java.util.Collections.emptyList();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityGetChestInfoResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityGetChestInfoResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.CityProto.CityGetChestInfoResponse.class, com.dxx.game.dto.CityProto.CityGetChestInfoResponse.Builder.class);
    }

    private int bitField0_;
    public static final int CODE_FIELD_NUMBER = 1;
    private int code_ = 0;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int COMMONDATA_FIELD_NUMBER = 2;
    private com.dxx.game.dto.CommonProto.CommonData commonData_;
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    @java.lang.Override
    public boolean hasCommonData() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }

    public static final int CITYCHEST_FIELD_NUMBER = 3;
    @SuppressWarnings("serial")
    private java.util.List<com.dxx.game.dto.CommonProto.CityChestDto> cityChest_;
    /**
     * <code>repeated .Proto.Common.CityChestDto cityChest = 3;</code>
     */
    @java.lang.Override
    public java.util.List<com.dxx.game.dto.CommonProto.CityChestDto> getCityChestList() {
      return cityChest_;
    }
    /**
     * <code>repeated .Proto.Common.CityChestDto cityChest = 3;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.dxx.game.dto.CommonProto.CityChestDtoOrBuilder> 
        getCityChestOrBuilderList() {
      return cityChest_;
    }
    /**
     * <code>repeated .Proto.Common.CityChestDto cityChest = 3;</code>
     */
    @java.lang.Override
    public int getCityChestCount() {
      return cityChest_.size();
    }
    /**
     * <code>repeated .Proto.Common.CityChestDto cityChest = 3;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CityChestDto getCityChest(int index) {
      return cityChest_.get(index);
    }
    /**
     * <code>repeated .Proto.Common.CityChestDto cityChest = 3;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CityChestDtoOrBuilder getCityChestOrBuilder(
        int index) {
      return cityChest_.get(index);
    }

    public static final int STARTTIME_FIELD_NUMBER = 4;
    private long startTime_ = 0L;
    /**
     * <code>uint64 startTime = 4;</code>
     * @return The startTime.
     */
    @java.lang.Override
    public long getStartTime() {
      return startTime_;
    }

    public static final int REFRESHTIME_FIELD_NUMBER = 5;
    private long refreshTime_ = 0L;
    /**
     * <code>uint64 refreshTime = 5;</code>
     * @return The refreshTime.
     */
    @java.lang.Override
    public long getRefreshTime() {
      return refreshTime_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(2, getCommonData());
      }
      for (int i = 0; i < cityChest_.size(); i++) {
        output.writeMessage(3, cityChest_.get(i));
      }
      if (startTime_ != 0L) {
        output.writeUInt64(4, startTime_);
      }
      if (refreshTime_ != 0L) {
        output.writeUInt64(5, refreshTime_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getCommonData());
      }
      for (int i = 0; i < cityChest_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, cityChest_.get(i));
      }
      if (startTime_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(4, startTime_);
      }
      if (refreshTime_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(5, refreshTime_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.CityProto.CityGetChestInfoResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.CityProto.CityGetChestInfoResponse other = (com.dxx.game.dto.CityProto.CityGetChestInfoResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (hasCommonData() != other.hasCommonData()) return false;
      if (hasCommonData()) {
        if (!getCommonData()
            .equals(other.getCommonData())) return false;
      }
      if (!getCityChestList()
          .equals(other.getCityChestList())) return false;
      if (getStartTime()
          != other.getStartTime()) return false;
      if (getRefreshTime()
          != other.getRefreshTime()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      if (hasCommonData()) {
        hash = (37 * hash) + COMMONDATA_FIELD_NUMBER;
        hash = (53 * hash) + getCommonData().hashCode();
      }
      if (getCityChestCount() > 0) {
        hash = (37 * hash) + CITYCHEST_FIELD_NUMBER;
        hash = (53 * hash) + getCityChestList().hashCode();
      }
      hash = (37 * hash) + STARTTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getStartTime());
      hash = (37 * hash) + REFRESHTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getRefreshTime());
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.CityProto.CityGetChestInfoResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.CityProto.CityGetChestInfoResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.CityProto.CityGetChestInfoResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.CityProto.CityGetChestInfoResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.CityProto.CityGetChestInfoResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.CityProto.CityGetChestInfoResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.CityProto.CityGetChestInfoResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.CityProto.CityGetChestInfoResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.CityProto.CityGetChestInfoResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.CityProto.CityGetChestInfoResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.CityProto.CityGetChestInfoResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.CityProto.CityGetChestInfoResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.CityProto.CityGetChestInfoResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=12606
     * </pre>
     *
     * Protobuf type {@code Proto.Actor.CityGetChestInfoResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Actor.CityGetChestInfoResponse)
        com.dxx.game.dto.CityProto.CityGetChestInfoResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityGetChestInfoResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityGetChestInfoResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.CityProto.CityGetChestInfoResponse.class, com.dxx.game.dto.CityProto.CityGetChestInfoResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.CityProto.CityGetChestInfoResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getCommonDataFieldBuilder();
          getCityChestFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        code_ = 0;
        commonData_ = null;
        if (commonDataBuilder_ != null) {
          commonDataBuilder_.dispose();
          commonDataBuilder_ = null;
        }
        if (cityChestBuilder_ == null) {
          cityChest_ = java.util.Collections.emptyList();
        } else {
          cityChest_ = null;
          cityChestBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        startTime_ = 0L;
        refreshTime_ = 0L;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityGetChestInfoResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.CityProto.CityGetChestInfoResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.CityProto.CityGetChestInfoResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.CityProto.CityGetChestInfoResponse build() {
        com.dxx.game.dto.CityProto.CityGetChestInfoResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.CityProto.CityGetChestInfoResponse buildPartial() {
        com.dxx.game.dto.CityProto.CityGetChestInfoResponse result = new com.dxx.game.dto.CityProto.CityGetChestInfoResponse(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(com.dxx.game.dto.CityProto.CityGetChestInfoResponse result) {
        if (cityChestBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0)) {
            cityChest_ = java.util.Collections.unmodifiableList(cityChest_);
            bitField0_ = (bitField0_ & ~0x00000004);
          }
          result.cityChest_ = cityChest_;
        } else {
          result.cityChest_ = cityChestBuilder_.build();
        }
      }

      private void buildPartial0(com.dxx.game.dto.CityProto.CityGetChestInfoResponse result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.code_ = code_;
        }
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.commonData_ = commonDataBuilder_ == null
              ? commonData_
              : commonDataBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.startTime_ = startTime_;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.refreshTime_ = refreshTime_;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.CityProto.CityGetChestInfoResponse) {
          return mergeFrom((com.dxx.game.dto.CityProto.CityGetChestInfoResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.CityProto.CityGetChestInfoResponse other) {
        if (other == com.dxx.game.dto.CityProto.CityGetChestInfoResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.hasCommonData()) {
          mergeCommonData(other.getCommonData());
        }
        if (cityChestBuilder_ == null) {
          if (!other.cityChest_.isEmpty()) {
            if (cityChest_.isEmpty()) {
              cityChest_ = other.cityChest_;
              bitField0_ = (bitField0_ & ~0x00000004);
            } else {
              ensureCityChestIsMutable();
              cityChest_.addAll(other.cityChest_);
            }
            onChanged();
          }
        } else {
          if (!other.cityChest_.isEmpty()) {
            if (cityChestBuilder_.isEmpty()) {
              cityChestBuilder_.dispose();
              cityChestBuilder_ = null;
              cityChest_ = other.cityChest_;
              bitField0_ = (bitField0_ & ~0x00000004);
              cityChestBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getCityChestFieldBuilder() : null;
            } else {
              cityChestBuilder_.addAllMessages(other.cityChest_);
            }
          }
        }
        if (other.getStartTime() != 0L) {
          setStartTime(other.getStartTime());
        }
        if (other.getRefreshTime() != 0L) {
          setRefreshTime(other.getRefreshTime());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                code_ = input.readInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 18: {
                input.readMessage(
                    getCommonDataFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              case 26: {
                com.dxx.game.dto.CommonProto.CityChestDto m =
                    input.readMessage(
                        com.dxx.game.dto.CommonProto.CityChestDto.parser(),
                        extensionRegistry);
                if (cityChestBuilder_ == null) {
                  ensureCityChestIsMutable();
                  cityChest_.add(m);
                } else {
                  cityChestBuilder_.addMessage(m);
                }
                break;
              } // case 26
              case 32: {
                startTime_ = input.readUInt64();
                bitField0_ |= 0x00000008;
                break;
              } // case 32
              case 40: {
                refreshTime_ = input.readUInt64();
                bitField0_ |= 0x00000010;
                break;
              } // case 40
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {

        code_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        bitField0_ = (bitField0_ & ~0x00000001);
        code_ = 0;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonData commonData_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> commonDataBuilder_;
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return Whether the commonData field is set.
       */
      public boolean hasCommonData() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return The commonData.
       */
      public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
        if (commonDataBuilder_ == null) {
          return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        } else {
          return commonDataBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonData_ = value;
        } else {
          commonDataBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(
          com.dxx.game.dto.CommonProto.CommonData.Builder builderForValue) {
        if (commonDataBuilder_ == null) {
          commonData_ = builderForValue.build();
        } else {
          commonDataBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder mergeCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
            commonData_ != null &&
            commonData_ != com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance()) {
            getCommonDataBuilder().mergeFrom(value);
          } else {
            commonData_ = value;
          }
        } else {
          commonDataBuilder_.mergeFrom(value);
        }
        if (commonData_ != null) {
          bitField0_ |= 0x00000002;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder clearCommonData() {
        bitField0_ = (bitField0_ & ~0x00000002);
        commonData_ = null;
        if (commonDataBuilder_ != null) {
          commonDataBuilder_.dispose();
          commonDataBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonData.Builder getCommonDataBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getCommonDataFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
        if (commonDataBuilder_ != null) {
          return commonDataBuilder_.getMessageOrBuilder();
        } else {
          return commonData_ == null ?
              com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> 
          getCommonDataFieldBuilder() {
        if (commonDataBuilder_ == null) {
          commonDataBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder>(
                  getCommonData(),
                  getParentForChildren(),
                  isClean());
          commonData_ = null;
        }
        return commonDataBuilder_;
      }

      private java.util.List<com.dxx.game.dto.CommonProto.CityChestDto> cityChest_ =
        java.util.Collections.emptyList();
      private void ensureCityChestIsMutable() {
        if (!((bitField0_ & 0x00000004) != 0)) {
          cityChest_ = new java.util.ArrayList<com.dxx.game.dto.CommonProto.CityChestDto>(cityChest_);
          bitField0_ |= 0x00000004;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          com.dxx.game.dto.CommonProto.CityChestDto, com.dxx.game.dto.CommonProto.CityChestDto.Builder, com.dxx.game.dto.CommonProto.CityChestDtoOrBuilder> cityChestBuilder_;

      /**
       * <code>repeated .Proto.Common.CityChestDto cityChest = 3;</code>
       */
      public java.util.List<com.dxx.game.dto.CommonProto.CityChestDto> getCityChestList() {
        if (cityChestBuilder_ == null) {
          return java.util.Collections.unmodifiableList(cityChest_);
        } else {
          return cityChestBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .Proto.Common.CityChestDto cityChest = 3;</code>
       */
      public int getCityChestCount() {
        if (cityChestBuilder_ == null) {
          return cityChest_.size();
        } else {
          return cityChestBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .Proto.Common.CityChestDto cityChest = 3;</code>
       */
      public com.dxx.game.dto.CommonProto.CityChestDto getCityChest(int index) {
        if (cityChestBuilder_ == null) {
          return cityChest_.get(index);
        } else {
          return cityChestBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .Proto.Common.CityChestDto cityChest = 3;</code>
       */
      public Builder setCityChest(
          int index, com.dxx.game.dto.CommonProto.CityChestDto value) {
        if (cityChestBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureCityChestIsMutable();
          cityChest_.set(index, value);
          onChanged();
        } else {
          cityChestBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .Proto.Common.CityChestDto cityChest = 3;</code>
       */
      public Builder setCityChest(
          int index, com.dxx.game.dto.CommonProto.CityChestDto.Builder builderForValue) {
        if (cityChestBuilder_ == null) {
          ensureCityChestIsMutable();
          cityChest_.set(index, builderForValue.build());
          onChanged();
        } else {
          cityChestBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .Proto.Common.CityChestDto cityChest = 3;</code>
       */
      public Builder addCityChest(com.dxx.game.dto.CommonProto.CityChestDto value) {
        if (cityChestBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureCityChestIsMutable();
          cityChest_.add(value);
          onChanged();
        } else {
          cityChestBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .Proto.Common.CityChestDto cityChest = 3;</code>
       */
      public Builder addCityChest(
          int index, com.dxx.game.dto.CommonProto.CityChestDto value) {
        if (cityChestBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureCityChestIsMutable();
          cityChest_.add(index, value);
          onChanged();
        } else {
          cityChestBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .Proto.Common.CityChestDto cityChest = 3;</code>
       */
      public Builder addCityChest(
          com.dxx.game.dto.CommonProto.CityChestDto.Builder builderForValue) {
        if (cityChestBuilder_ == null) {
          ensureCityChestIsMutable();
          cityChest_.add(builderForValue.build());
          onChanged();
        } else {
          cityChestBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .Proto.Common.CityChestDto cityChest = 3;</code>
       */
      public Builder addCityChest(
          int index, com.dxx.game.dto.CommonProto.CityChestDto.Builder builderForValue) {
        if (cityChestBuilder_ == null) {
          ensureCityChestIsMutable();
          cityChest_.add(index, builderForValue.build());
          onChanged();
        } else {
          cityChestBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .Proto.Common.CityChestDto cityChest = 3;</code>
       */
      public Builder addAllCityChest(
          java.lang.Iterable<? extends com.dxx.game.dto.CommonProto.CityChestDto> values) {
        if (cityChestBuilder_ == null) {
          ensureCityChestIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, cityChest_);
          onChanged();
        } else {
          cityChestBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .Proto.Common.CityChestDto cityChest = 3;</code>
       */
      public Builder clearCityChest() {
        if (cityChestBuilder_ == null) {
          cityChest_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000004);
          onChanged();
        } else {
          cityChestBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .Proto.Common.CityChestDto cityChest = 3;</code>
       */
      public Builder removeCityChest(int index) {
        if (cityChestBuilder_ == null) {
          ensureCityChestIsMutable();
          cityChest_.remove(index);
          onChanged();
        } else {
          cityChestBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .Proto.Common.CityChestDto cityChest = 3;</code>
       */
      public com.dxx.game.dto.CommonProto.CityChestDto.Builder getCityChestBuilder(
          int index) {
        return getCityChestFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .Proto.Common.CityChestDto cityChest = 3;</code>
       */
      public com.dxx.game.dto.CommonProto.CityChestDtoOrBuilder getCityChestOrBuilder(
          int index) {
        if (cityChestBuilder_ == null) {
          return cityChest_.get(index);  } else {
          return cityChestBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .Proto.Common.CityChestDto cityChest = 3;</code>
       */
      public java.util.List<? extends com.dxx.game.dto.CommonProto.CityChestDtoOrBuilder> 
           getCityChestOrBuilderList() {
        if (cityChestBuilder_ != null) {
          return cityChestBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(cityChest_);
        }
      }
      /**
       * <code>repeated .Proto.Common.CityChestDto cityChest = 3;</code>
       */
      public com.dxx.game.dto.CommonProto.CityChestDto.Builder addCityChestBuilder() {
        return getCityChestFieldBuilder().addBuilder(
            com.dxx.game.dto.CommonProto.CityChestDto.getDefaultInstance());
      }
      /**
       * <code>repeated .Proto.Common.CityChestDto cityChest = 3;</code>
       */
      public com.dxx.game.dto.CommonProto.CityChestDto.Builder addCityChestBuilder(
          int index) {
        return getCityChestFieldBuilder().addBuilder(
            index, com.dxx.game.dto.CommonProto.CityChestDto.getDefaultInstance());
      }
      /**
       * <code>repeated .Proto.Common.CityChestDto cityChest = 3;</code>
       */
      public java.util.List<com.dxx.game.dto.CommonProto.CityChestDto.Builder> 
           getCityChestBuilderList() {
        return getCityChestFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          com.dxx.game.dto.CommonProto.CityChestDto, com.dxx.game.dto.CommonProto.CityChestDto.Builder, com.dxx.game.dto.CommonProto.CityChestDtoOrBuilder> 
          getCityChestFieldBuilder() {
        if (cityChestBuilder_ == null) {
          cityChestBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              com.dxx.game.dto.CommonProto.CityChestDto, com.dxx.game.dto.CommonProto.CityChestDto.Builder, com.dxx.game.dto.CommonProto.CityChestDtoOrBuilder>(
                  cityChest_,
                  ((bitField0_ & 0x00000004) != 0),
                  getParentForChildren(),
                  isClean());
          cityChest_ = null;
        }
        return cityChestBuilder_;
      }

      private long startTime_ ;
      /**
       * <code>uint64 startTime = 4;</code>
       * @return The startTime.
       */
      @java.lang.Override
      public long getStartTime() {
        return startTime_;
      }
      /**
       * <code>uint64 startTime = 4;</code>
       * @param value The startTime to set.
       * @return This builder for chaining.
       */
      public Builder setStartTime(long value) {

        startTime_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 startTime = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearStartTime() {
        bitField0_ = (bitField0_ & ~0x00000008);
        startTime_ = 0L;
        onChanged();
        return this;
      }

      private long refreshTime_ ;
      /**
       * <code>uint64 refreshTime = 5;</code>
       * @return The refreshTime.
       */
      @java.lang.Override
      public long getRefreshTime() {
        return refreshTime_;
      }
      /**
       * <code>uint64 refreshTime = 5;</code>
       * @param value The refreshTime to set.
       * @return This builder for chaining.
       */
      public Builder setRefreshTime(long value) {

        refreshTime_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 refreshTime = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearRefreshTime() {
        bitField0_ = (bitField0_ & ~0x00000010);
        refreshTime_ = 0L;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Actor.CityGetChestInfoResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.Actor.CityGetChestInfoResponse)
    private static final com.dxx.game.dto.CityProto.CityGetChestInfoResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.CityProto.CityGetChestInfoResponse();
    }

    public static com.dxx.game.dto.CityProto.CityGetChestInfoResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<CityGetChestInfoResponse>
        PARSER = new com.google.protobuf.AbstractParser<CityGetChestInfoResponse>() {
      @java.lang.Override
      public CityGetChestInfoResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<CityGetChestInfoResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CityGetChestInfoResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.CityProto.CityGetChestInfoResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface CityOpenChestRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Actor.CityOpenChestRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();

    /**
     * <code>repeated uint64 rowId = 2;</code>
     * @return A list containing the rowId.
     */
    java.util.List<java.lang.Long> getRowIdList();
    /**
     * <code>repeated uint64 rowId = 2;</code>
     * @return The count of rowId.
     */
    int getRowIdCount();
    /**
     * <code>repeated uint64 rowId = 2;</code>
     * @param index The index of the element to return.
     * @return The rowId at the given index.
     */
    long getRowId(int index);
  }
  /**
   * <pre>
   * CMD PackageId=12607 主城-开启宝箱
   * </pre>
   *
   * Protobuf type {@code Proto.Actor.CityOpenChestRequest}
   */
  public static final class CityOpenChestRequest extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Actor.CityOpenChestRequest)
      CityOpenChestRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        CityOpenChestRequest.class.getName());
    }
    // Use CityOpenChestRequest.newBuilder() to construct.
    private CityOpenChestRequest(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private CityOpenChestRequest() {
      rowId_ = emptyLongList();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityOpenChestRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityOpenChestRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.CityProto.CityOpenChestRequest.class, com.dxx.game.dto.CityProto.CityOpenChestRequest.Builder.class);
    }

    private int bitField0_;
    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }

    public static final int ROWID_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.LongList rowId_ =
        emptyLongList();
    /**
     * <code>repeated uint64 rowId = 2;</code>
     * @return A list containing the rowId.
     */
    @java.lang.Override
    public java.util.List<java.lang.Long>
        getRowIdList() {
      return rowId_;
    }
    /**
     * <code>repeated uint64 rowId = 2;</code>
     * @return The count of rowId.
     */
    public int getRowIdCount() {
      return rowId_.size();
    }
    /**
     * <code>repeated uint64 rowId = 2;</code>
     * @param index The index of the element to return.
     * @return The rowId at the given index.
     */
    public long getRowId(int index) {
      return rowId_.getLong(index);
    }
    private int rowIdMemoizedSerializedSize = -1;

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getCommonParams());
      }
      if (getRowIdList().size() > 0) {
        output.writeUInt32NoTag(18);
        output.writeUInt32NoTag(rowIdMemoizedSerializedSize);
      }
      for (int i = 0; i < rowId_.size(); i++) {
        output.writeUInt64NoTag(rowId_.getLong(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      {
        int dataSize = 0;
        for (int i = 0; i < rowId_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeUInt64SizeNoTag(rowId_.getLong(i));
        }
        size += dataSize;
        if (!getRowIdList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        rowIdMemoizedSerializedSize = dataSize;
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.CityProto.CityOpenChestRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.CityProto.CityOpenChestRequest other = (com.dxx.game.dto.CityProto.CityOpenChestRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (!getRowIdList()
          .equals(other.getRowIdList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      if (getRowIdCount() > 0) {
        hash = (37 * hash) + ROWID_FIELD_NUMBER;
        hash = (53 * hash) + getRowIdList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.CityProto.CityOpenChestRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.CityProto.CityOpenChestRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.CityProto.CityOpenChestRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.CityProto.CityOpenChestRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.CityProto.CityOpenChestRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.CityProto.CityOpenChestRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.CityProto.CityOpenChestRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.CityProto.CityOpenChestRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.CityProto.CityOpenChestRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.CityProto.CityOpenChestRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.CityProto.CityOpenChestRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.CityProto.CityOpenChestRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.CityProto.CityOpenChestRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=12607 主城-开启宝箱
     * </pre>
     *
     * Protobuf type {@code Proto.Actor.CityOpenChestRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Actor.CityOpenChestRequest)
        com.dxx.game.dto.CityProto.CityOpenChestRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityOpenChestRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityOpenChestRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.CityProto.CityOpenChestRequest.class, com.dxx.game.dto.CityProto.CityOpenChestRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.CityProto.CityOpenChestRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getCommonParamsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        rowId_ = emptyLongList();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityOpenChestRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.CityProto.CityOpenChestRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.CityProto.CityOpenChestRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.CityProto.CityOpenChestRequest build() {
        com.dxx.game.dto.CityProto.CityOpenChestRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.CityProto.CityOpenChestRequest buildPartial() {
        com.dxx.game.dto.CityProto.CityOpenChestRequest result = new com.dxx.game.dto.CityProto.CityOpenChestRequest(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.CityProto.CityOpenChestRequest result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.commonParams_ = commonParamsBuilder_ == null
              ? commonParams_
              : commonParamsBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          rowId_.makeImmutable();
          result.rowId_ = rowId_;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.CityProto.CityOpenChestRequest) {
          return mergeFrom((com.dxx.game.dto.CityProto.CityOpenChestRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.CityProto.CityOpenChestRequest other) {
        if (other == com.dxx.game.dto.CityProto.CityOpenChestRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        if (!other.rowId_.isEmpty()) {
          if (rowId_.isEmpty()) {
            rowId_ = other.rowId_;
            rowId_.makeImmutable();
            bitField0_ |= 0x00000002;
          } else {
            ensureRowIdIsMutable();
            rowId_.addAll(other.rowId_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getCommonParamsFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 16: {
                long v = input.readUInt64();
                ensureRowIdIsMutable();
                rowId_.addLong(v);
                break;
              } // case 16
              case 18: {
                int length = input.readRawVarint32();
                int limit = input.pushLimit(length);
                ensureRowIdIsMutable();
                while (input.getBytesUntilLimit() > 0) {
                  rowId_.addLong(input.readUInt64());
                }
                input.popLimit(limit);
                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
        } else {
          commonParamsBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
            commonParams_ != null &&
            commonParams_ != com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance()) {
            getCommonParamsBuilder().mergeFrom(value);
          } else {
            commonParams_ = value;
          }
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }
        if (commonParams_ != null) {
          bitField0_ |= 0x00000001;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        bitField0_ = (bitField0_ & ~0x00000001);
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }

      private com.google.protobuf.Internal.LongList rowId_ = emptyLongList();
      private void ensureRowIdIsMutable() {
        if (!rowId_.isModifiable()) {
          rowId_ = makeMutableCopy(rowId_);
        }
        bitField0_ |= 0x00000002;
      }
      /**
       * <code>repeated uint64 rowId = 2;</code>
       * @return A list containing the rowId.
       */
      public java.util.List<java.lang.Long>
          getRowIdList() {
        rowId_.makeImmutable();
        return rowId_;
      }
      /**
       * <code>repeated uint64 rowId = 2;</code>
       * @return The count of rowId.
       */
      public int getRowIdCount() {
        return rowId_.size();
      }
      /**
       * <code>repeated uint64 rowId = 2;</code>
       * @param index The index of the element to return.
       * @return The rowId at the given index.
       */
      public long getRowId(int index) {
        return rowId_.getLong(index);
      }
      /**
       * <code>repeated uint64 rowId = 2;</code>
       * @param index The index to set the value at.
       * @param value The rowId to set.
       * @return This builder for chaining.
       */
      public Builder setRowId(
          int index, long value) {

        ensureRowIdIsMutable();
        rowId_.setLong(index, value);
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint64 rowId = 2;</code>
       * @param value The rowId to add.
       * @return This builder for chaining.
       */
      public Builder addRowId(long value) {

        ensureRowIdIsMutable();
        rowId_.addLong(value);
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint64 rowId = 2;</code>
       * @param values The rowId to add.
       * @return This builder for chaining.
       */
      public Builder addAllRowId(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensureRowIdIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, rowId_);
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint64 rowId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearRowId() {
        rowId_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Actor.CityOpenChestRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.Actor.CityOpenChestRequest)
    private static final com.dxx.game.dto.CityProto.CityOpenChestRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.CityProto.CityOpenChestRequest();
    }

    public static com.dxx.game.dto.CityProto.CityOpenChestRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<CityOpenChestRequest>
        PARSER = new com.google.protobuf.AbstractParser<CityOpenChestRequest>() {
      @java.lang.Override
      public CityOpenChestRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<CityOpenChestRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CityOpenChestRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.CityProto.CityOpenChestRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface CityOpenChestResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Actor.CityOpenChestResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    boolean hasCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    com.dxx.game.dto.CommonProto.CommonData getCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder();

    /**
     * <code>repeated uint64 rowId = 3;</code>
     * @return A list containing the rowId.
     */
    java.util.List<java.lang.Long> getRowIdList();
    /**
     * <code>repeated uint64 rowId = 3;</code>
     * @return The count of rowId.
     */
    int getRowIdCount();
    /**
     * <code>repeated uint64 rowId = 3;</code>
     * @param index The index of the element to return.
     * @return The rowId at the given index.
     */
    long getRowId(int index);

    /**
     * <code>uint64 refreshTime = 4;</code>
     * @return The refreshTime.
     */
    long getRefreshTime();

    /**
     * <code>int32 score = 5;</code>
     * @return The score.
     */
    int getScore();
  }
  /**
   * <pre>
   * CMD PackageId=12608
   * </pre>
   *
   * Protobuf type {@code Proto.Actor.CityOpenChestResponse}
   */
  public static final class CityOpenChestResponse extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Actor.CityOpenChestResponse)
      CityOpenChestResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        CityOpenChestResponse.class.getName());
    }
    // Use CityOpenChestResponse.newBuilder() to construct.
    private CityOpenChestResponse(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private CityOpenChestResponse() {
      rowId_ = emptyLongList();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityOpenChestResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityOpenChestResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.CityProto.CityOpenChestResponse.class, com.dxx.game.dto.CityProto.CityOpenChestResponse.Builder.class);
    }

    private int bitField0_;
    public static final int CODE_FIELD_NUMBER = 1;
    private int code_ = 0;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int COMMONDATA_FIELD_NUMBER = 2;
    private com.dxx.game.dto.CommonProto.CommonData commonData_;
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    @java.lang.Override
    public boolean hasCommonData() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }

    public static final int ROWID_FIELD_NUMBER = 3;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.LongList rowId_ =
        emptyLongList();
    /**
     * <code>repeated uint64 rowId = 3;</code>
     * @return A list containing the rowId.
     */
    @java.lang.Override
    public java.util.List<java.lang.Long>
        getRowIdList() {
      return rowId_;
    }
    /**
     * <code>repeated uint64 rowId = 3;</code>
     * @return The count of rowId.
     */
    public int getRowIdCount() {
      return rowId_.size();
    }
    /**
     * <code>repeated uint64 rowId = 3;</code>
     * @param index The index of the element to return.
     * @return The rowId at the given index.
     */
    public long getRowId(int index) {
      return rowId_.getLong(index);
    }
    private int rowIdMemoizedSerializedSize = -1;

    public static final int REFRESHTIME_FIELD_NUMBER = 4;
    private long refreshTime_ = 0L;
    /**
     * <code>uint64 refreshTime = 4;</code>
     * @return The refreshTime.
     */
    @java.lang.Override
    public long getRefreshTime() {
      return refreshTime_;
    }

    public static final int SCORE_FIELD_NUMBER = 5;
    private int score_ = 0;
    /**
     * <code>int32 score = 5;</code>
     * @return The score.
     */
    @java.lang.Override
    public int getScore() {
      return score_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(2, getCommonData());
      }
      if (getRowIdList().size() > 0) {
        output.writeUInt32NoTag(26);
        output.writeUInt32NoTag(rowIdMemoizedSerializedSize);
      }
      for (int i = 0; i < rowId_.size(); i++) {
        output.writeUInt64NoTag(rowId_.getLong(i));
      }
      if (refreshTime_ != 0L) {
        output.writeUInt64(4, refreshTime_);
      }
      if (score_ != 0) {
        output.writeInt32(5, score_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getCommonData());
      }
      {
        int dataSize = 0;
        for (int i = 0; i < rowId_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeUInt64SizeNoTag(rowId_.getLong(i));
        }
        size += dataSize;
        if (!getRowIdList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        rowIdMemoizedSerializedSize = dataSize;
      }
      if (refreshTime_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(4, refreshTime_);
      }
      if (score_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, score_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.CityProto.CityOpenChestResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.CityProto.CityOpenChestResponse other = (com.dxx.game.dto.CityProto.CityOpenChestResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (hasCommonData() != other.hasCommonData()) return false;
      if (hasCommonData()) {
        if (!getCommonData()
            .equals(other.getCommonData())) return false;
      }
      if (!getRowIdList()
          .equals(other.getRowIdList())) return false;
      if (getRefreshTime()
          != other.getRefreshTime()) return false;
      if (getScore()
          != other.getScore()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      if (hasCommonData()) {
        hash = (37 * hash) + COMMONDATA_FIELD_NUMBER;
        hash = (53 * hash) + getCommonData().hashCode();
      }
      if (getRowIdCount() > 0) {
        hash = (37 * hash) + ROWID_FIELD_NUMBER;
        hash = (53 * hash) + getRowIdList().hashCode();
      }
      hash = (37 * hash) + REFRESHTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getRefreshTime());
      hash = (37 * hash) + SCORE_FIELD_NUMBER;
      hash = (53 * hash) + getScore();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.CityProto.CityOpenChestResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.CityProto.CityOpenChestResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.CityProto.CityOpenChestResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.CityProto.CityOpenChestResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.CityProto.CityOpenChestResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.CityProto.CityOpenChestResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.CityProto.CityOpenChestResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.CityProto.CityOpenChestResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.CityProto.CityOpenChestResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.CityProto.CityOpenChestResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.CityProto.CityOpenChestResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.CityProto.CityOpenChestResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.CityProto.CityOpenChestResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=12608
     * </pre>
     *
     * Protobuf type {@code Proto.Actor.CityOpenChestResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Actor.CityOpenChestResponse)
        com.dxx.game.dto.CityProto.CityOpenChestResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityOpenChestResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityOpenChestResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.CityProto.CityOpenChestResponse.class, com.dxx.game.dto.CityProto.CityOpenChestResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.CityProto.CityOpenChestResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getCommonDataFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        code_ = 0;
        commonData_ = null;
        if (commonDataBuilder_ != null) {
          commonDataBuilder_.dispose();
          commonDataBuilder_ = null;
        }
        rowId_ = emptyLongList();
        refreshTime_ = 0L;
        score_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityOpenChestResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.CityProto.CityOpenChestResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.CityProto.CityOpenChestResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.CityProto.CityOpenChestResponse build() {
        com.dxx.game.dto.CityProto.CityOpenChestResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.CityProto.CityOpenChestResponse buildPartial() {
        com.dxx.game.dto.CityProto.CityOpenChestResponse result = new com.dxx.game.dto.CityProto.CityOpenChestResponse(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.CityProto.CityOpenChestResponse result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.code_ = code_;
        }
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.commonData_ = commonDataBuilder_ == null
              ? commonData_
              : commonDataBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          rowId_.makeImmutable();
          result.rowId_ = rowId_;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.refreshTime_ = refreshTime_;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.score_ = score_;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.CityProto.CityOpenChestResponse) {
          return mergeFrom((com.dxx.game.dto.CityProto.CityOpenChestResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.CityProto.CityOpenChestResponse other) {
        if (other == com.dxx.game.dto.CityProto.CityOpenChestResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.hasCommonData()) {
          mergeCommonData(other.getCommonData());
        }
        if (!other.rowId_.isEmpty()) {
          if (rowId_.isEmpty()) {
            rowId_ = other.rowId_;
            rowId_.makeImmutable();
            bitField0_ |= 0x00000004;
          } else {
            ensureRowIdIsMutable();
            rowId_.addAll(other.rowId_);
          }
          onChanged();
        }
        if (other.getRefreshTime() != 0L) {
          setRefreshTime(other.getRefreshTime());
        }
        if (other.getScore() != 0) {
          setScore(other.getScore());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                code_ = input.readInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 18: {
                input.readMessage(
                    getCommonDataFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              case 24: {
                long v = input.readUInt64();
                ensureRowIdIsMutable();
                rowId_.addLong(v);
                break;
              } // case 24
              case 26: {
                int length = input.readRawVarint32();
                int limit = input.pushLimit(length);
                ensureRowIdIsMutable();
                while (input.getBytesUntilLimit() > 0) {
                  rowId_.addLong(input.readUInt64());
                }
                input.popLimit(limit);
                break;
              } // case 26
              case 32: {
                refreshTime_ = input.readUInt64();
                bitField0_ |= 0x00000008;
                break;
              } // case 32
              case 40: {
                score_ = input.readInt32();
                bitField0_ |= 0x00000010;
                break;
              } // case 40
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {

        code_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        bitField0_ = (bitField0_ & ~0x00000001);
        code_ = 0;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonData commonData_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> commonDataBuilder_;
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return Whether the commonData field is set.
       */
      public boolean hasCommonData() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return The commonData.
       */
      public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
        if (commonDataBuilder_ == null) {
          return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        } else {
          return commonDataBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonData_ = value;
        } else {
          commonDataBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(
          com.dxx.game.dto.CommonProto.CommonData.Builder builderForValue) {
        if (commonDataBuilder_ == null) {
          commonData_ = builderForValue.build();
        } else {
          commonDataBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder mergeCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
            commonData_ != null &&
            commonData_ != com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance()) {
            getCommonDataBuilder().mergeFrom(value);
          } else {
            commonData_ = value;
          }
        } else {
          commonDataBuilder_.mergeFrom(value);
        }
        if (commonData_ != null) {
          bitField0_ |= 0x00000002;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder clearCommonData() {
        bitField0_ = (bitField0_ & ~0x00000002);
        commonData_ = null;
        if (commonDataBuilder_ != null) {
          commonDataBuilder_.dispose();
          commonDataBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonData.Builder getCommonDataBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getCommonDataFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
        if (commonDataBuilder_ != null) {
          return commonDataBuilder_.getMessageOrBuilder();
        } else {
          return commonData_ == null ?
              com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> 
          getCommonDataFieldBuilder() {
        if (commonDataBuilder_ == null) {
          commonDataBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder>(
                  getCommonData(),
                  getParentForChildren(),
                  isClean());
          commonData_ = null;
        }
        return commonDataBuilder_;
      }

      private com.google.protobuf.Internal.LongList rowId_ = emptyLongList();
      private void ensureRowIdIsMutable() {
        if (!rowId_.isModifiable()) {
          rowId_ = makeMutableCopy(rowId_);
        }
        bitField0_ |= 0x00000004;
      }
      /**
       * <code>repeated uint64 rowId = 3;</code>
       * @return A list containing the rowId.
       */
      public java.util.List<java.lang.Long>
          getRowIdList() {
        rowId_.makeImmutable();
        return rowId_;
      }
      /**
       * <code>repeated uint64 rowId = 3;</code>
       * @return The count of rowId.
       */
      public int getRowIdCount() {
        return rowId_.size();
      }
      /**
       * <code>repeated uint64 rowId = 3;</code>
       * @param index The index of the element to return.
       * @return The rowId at the given index.
       */
      public long getRowId(int index) {
        return rowId_.getLong(index);
      }
      /**
       * <code>repeated uint64 rowId = 3;</code>
       * @param index The index to set the value at.
       * @param value The rowId to set.
       * @return This builder for chaining.
       */
      public Builder setRowId(
          int index, long value) {

        ensureRowIdIsMutable();
        rowId_.setLong(index, value);
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint64 rowId = 3;</code>
       * @param value The rowId to add.
       * @return This builder for chaining.
       */
      public Builder addRowId(long value) {

        ensureRowIdIsMutable();
        rowId_.addLong(value);
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint64 rowId = 3;</code>
       * @param values The rowId to add.
       * @return This builder for chaining.
       */
      public Builder addAllRowId(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensureRowIdIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, rowId_);
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint64 rowId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearRowId() {
        rowId_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }

      private long refreshTime_ ;
      /**
       * <code>uint64 refreshTime = 4;</code>
       * @return The refreshTime.
       */
      @java.lang.Override
      public long getRefreshTime() {
        return refreshTime_;
      }
      /**
       * <code>uint64 refreshTime = 4;</code>
       * @param value The refreshTime to set.
       * @return This builder for chaining.
       */
      public Builder setRefreshTime(long value) {

        refreshTime_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 refreshTime = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearRefreshTime() {
        bitField0_ = (bitField0_ & ~0x00000008);
        refreshTime_ = 0L;
        onChanged();
        return this;
      }

      private int score_ ;
      /**
       * <code>int32 score = 5;</code>
       * @return The score.
       */
      @java.lang.Override
      public int getScore() {
        return score_;
      }
      /**
       * <code>int32 score = 5;</code>
       * @param value The score to set.
       * @return This builder for chaining.
       */
      public Builder setScore(int value) {

        score_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>int32 score = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearScore() {
        bitField0_ = (bitField0_ & ~0x00000010);
        score_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Actor.CityOpenChestResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.Actor.CityOpenChestResponse)
    private static final com.dxx.game.dto.CityProto.CityOpenChestResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.CityProto.CityOpenChestResponse();
    }

    public static com.dxx.game.dto.CityProto.CityOpenChestResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<CityOpenChestResponse>
        PARSER = new com.google.protobuf.AbstractParser<CityOpenChestResponse>() {
      @java.lang.Override
      public CityOpenChestResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<CityOpenChestResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CityOpenChestResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.CityProto.CityOpenChestResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface CityTakeScoreRewardRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Actor.CityTakeScoreRewardRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();
  }
  /**
   * <pre>
   * CMD PackageId=12609 主城-领取积分奖励
   * </pre>
   *
   * Protobuf type {@code Proto.Actor.CityTakeScoreRewardRequest}
   */
  public static final class CityTakeScoreRewardRequest extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Actor.CityTakeScoreRewardRequest)
      CityTakeScoreRewardRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        CityTakeScoreRewardRequest.class.getName());
    }
    // Use CityTakeScoreRewardRequest.newBuilder() to construct.
    private CityTakeScoreRewardRequest(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private CityTakeScoreRewardRequest() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityTakeScoreRewardRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityTakeScoreRewardRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.CityProto.CityTakeScoreRewardRequest.class, com.dxx.game.dto.CityProto.CityTakeScoreRewardRequest.Builder.class);
    }

    private int bitField0_;
    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getCommonParams());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.CityProto.CityTakeScoreRewardRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.CityProto.CityTakeScoreRewardRequest other = (com.dxx.game.dto.CityProto.CityTakeScoreRewardRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.CityProto.CityTakeScoreRewardRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.CityProto.CityTakeScoreRewardRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.CityProto.CityTakeScoreRewardRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.CityProto.CityTakeScoreRewardRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.CityProto.CityTakeScoreRewardRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.CityProto.CityTakeScoreRewardRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.CityProto.CityTakeScoreRewardRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.CityProto.CityTakeScoreRewardRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.CityProto.CityTakeScoreRewardRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.CityProto.CityTakeScoreRewardRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.CityProto.CityTakeScoreRewardRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.CityProto.CityTakeScoreRewardRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.CityProto.CityTakeScoreRewardRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=12609 主城-领取积分奖励
     * </pre>
     *
     * Protobuf type {@code Proto.Actor.CityTakeScoreRewardRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Actor.CityTakeScoreRewardRequest)
        com.dxx.game.dto.CityProto.CityTakeScoreRewardRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityTakeScoreRewardRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityTakeScoreRewardRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.CityProto.CityTakeScoreRewardRequest.class, com.dxx.game.dto.CityProto.CityTakeScoreRewardRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.CityProto.CityTakeScoreRewardRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getCommonParamsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityTakeScoreRewardRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.CityProto.CityTakeScoreRewardRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.CityProto.CityTakeScoreRewardRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.CityProto.CityTakeScoreRewardRequest build() {
        com.dxx.game.dto.CityProto.CityTakeScoreRewardRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.CityProto.CityTakeScoreRewardRequest buildPartial() {
        com.dxx.game.dto.CityProto.CityTakeScoreRewardRequest result = new com.dxx.game.dto.CityProto.CityTakeScoreRewardRequest(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.CityProto.CityTakeScoreRewardRequest result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.commonParams_ = commonParamsBuilder_ == null
              ? commonParams_
              : commonParamsBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.CityProto.CityTakeScoreRewardRequest) {
          return mergeFrom((com.dxx.game.dto.CityProto.CityTakeScoreRewardRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.CityProto.CityTakeScoreRewardRequest other) {
        if (other == com.dxx.game.dto.CityProto.CityTakeScoreRewardRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getCommonParamsFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
        } else {
          commonParamsBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
            commonParams_ != null &&
            commonParams_ != com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance()) {
            getCommonParamsBuilder().mergeFrom(value);
          } else {
            commonParams_ = value;
          }
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }
        if (commonParams_ != null) {
          bitField0_ |= 0x00000001;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        bitField0_ = (bitField0_ & ~0x00000001);
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Actor.CityTakeScoreRewardRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.Actor.CityTakeScoreRewardRequest)
    private static final com.dxx.game.dto.CityProto.CityTakeScoreRewardRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.CityProto.CityTakeScoreRewardRequest();
    }

    public static com.dxx.game.dto.CityProto.CityTakeScoreRewardRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<CityTakeScoreRewardRequest>
        PARSER = new com.google.protobuf.AbstractParser<CityTakeScoreRewardRequest>() {
      @java.lang.Override
      public CityTakeScoreRewardRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<CityTakeScoreRewardRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CityTakeScoreRewardRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.CityProto.CityTakeScoreRewardRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface CityTakeScoreRewardResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Actor.CityTakeScoreRewardResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    boolean hasCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    com.dxx.game.dto.CommonProto.CommonData getCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder();

    /**
     * <code>uint64 refreshTime = 3;</code>
     * @return The refreshTime.
     */
    long getRefreshTime();

    /**
     * <code>int32 score = 4;</code>
     * @return The score.
     */
    int getScore();
  }
  /**
   * <pre>
   * CMD PackageId=12610
   * </pre>
   *
   * Protobuf type {@code Proto.Actor.CityTakeScoreRewardResponse}
   */
  public static final class CityTakeScoreRewardResponse extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Actor.CityTakeScoreRewardResponse)
      CityTakeScoreRewardResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        CityTakeScoreRewardResponse.class.getName());
    }
    // Use CityTakeScoreRewardResponse.newBuilder() to construct.
    private CityTakeScoreRewardResponse(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private CityTakeScoreRewardResponse() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityTakeScoreRewardResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityTakeScoreRewardResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.CityProto.CityTakeScoreRewardResponse.class, com.dxx.game.dto.CityProto.CityTakeScoreRewardResponse.Builder.class);
    }

    private int bitField0_;
    public static final int CODE_FIELD_NUMBER = 1;
    private int code_ = 0;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int COMMONDATA_FIELD_NUMBER = 2;
    private com.dxx.game.dto.CommonProto.CommonData commonData_;
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    @java.lang.Override
    public boolean hasCommonData() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }

    public static final int REFRESHTIME_FIELD_NUMBER = 3;
    private long refreshTime_ = 0L;
    /**
     * <code>uint64 refreshTime = 3;</code>
     * @return The refreshTime.
     */
    @java.lang.Override
    public long getRefreshTime() {
      return refreshTime_;
    }

    public static final int SCORE_FIELD_NUMBER = 4;
    private int score_ = 0;
    /**
     * <code>int32 score = 4;</code>
     * @return The score.
     */
    @java.lang.Override
    public int getScore() {
      return score_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(2, getCommonData());
      }
      if (refreshTime_ != 0L) {
        output.writeUInt64(3, refreshTime_);
      }
      if (score_ != 0) {
        output.writeInt32(4, score_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getCommonData());
      }
      if (refreshTime_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(3, refreshTime_);
      }
      if (score_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, score_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.CityProto.CityTakeScoreRewardResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.CityProto.CityTakeScoreRewardResponse other = (com.dxx.game.dto.CityProto.CityTakeScoreRewardResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (hasCommonData() != other.hasCommonData()) return false;
      if (hasCommonData()) {
        if (!getCommonData()
            .equals(other.getCommonData())) return false;
      }
      if (getRefreshTime()
          != other.getRefreshTime()) return false;
      if (getScore()
          != other.getScore()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      if (hasCommonData()) {
        hash = (37 * hash) + COMMONDATA_FIELD_NUMBER;
        hash = (53 * hash) + getCommonData().hashCode();
      }
      hash = (37 * hash) + REFRESHTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getRefreshTime());
      hash = (37 * hash) + SCORE_FIELD_NUMBER;
      hash = (53 * hash) + getScore();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.CityProto.CityTakeScoreRewardResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.CityProto.CityTakeScoreRewardResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.CityProto.CityTakeScoreRewardResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.CityProto.CityTakeScoreRewardResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.CityProto.CityTakeScoreRewardResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.CityProto.CityTakeScoreRewardResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.CityProto.CityTakeScoreRewardResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.CityProto.CityTakeScoreRewardResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.CityProto.CityTakeScoreRewardResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.CityProto.CityTakeScoreRewardResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.CityProto.CityTakeScoreRewardResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.CityProto.CityTakeScoreRewardResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.CityProto.CityTakeScoreRewardResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=12610
     * </pre>
     *
     * Protobuf type {@code Proto.Actor.CityTakeScoreRewardResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Actor.CityTakeScoreRewardResponse)
        com.dxx.game.dto.CityProto.CityTakeScoreRewardResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityTakeScoreRewardResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityTakeScoreRewardResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.CityProto.CityTakeScoreRewardResponse.class, com.dxx.game.dto.CityProto.CityTakeScoreRewardResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.CityProto.CityTakeScoreRewardResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getCommonDataFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        code_ = 0;
        commonData_ = null;
        if (commonDataBuilder_ != null) {
          commonDataBuilder_.dispose();
          commonDataBuilder_ = null;
        }
        refreshTime_ = 0L;
        score_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityTakeScoreRewardResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.CityProto.CityTakeScoreRewardResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.CityProto.CityTakeScoreRewardResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.CityProto.CityTakeScoreRewardResponse build() {
        com.dxx.game.dto.CityProto.CityTakeScoreRewardResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.CityProto.CityTakeScoreRewardResponse buildPartial() {
        com.dxx.game.dto.CityProto.CityTakeScoreRewardResponse result = new com.dxx.game.dto.CityProto.CityTakeScoreRewardResponse(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.CityProto.CityTakeScoreRewardResponse result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.code_ = code_;
        }
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.commonData_ = commonDataBuilder_ == null
              ? commonData_
              : commonDataBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.refreshTime_ = refreshTime_;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.score_ = score_;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.CityProto.CityTakeScoreRewardResponse) {
          return mergeFrom((com.dxx.game.dto.CityProto.CityTakeScoreRewardResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.CityProto.CityTakeScoreRewardResponse other) {
        if (other == com.dxx.game.dto.CityProto.CityTakeScoreRewardResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.hasCommonData()) {
          mergeCommonData(other.getCommonData());
        }
        if (other.getRefreshTime() != 0L) {
          setRefreshTime(other.getRefreshTime());
        }
        if (other.getScore() != 0) {
          setScore(other.getScore());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                code_ = input.readInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 18: {
                input.readMessage(
                    getCommonDataFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              case 24: {
                refreshTime_ = input.readUInt64();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              case 32: {
                score_ = input.readInt32();
                bitField0_ |= 0x00000008;
                break;
              } // case 32
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {

        code_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        bitField0_ = (bitField0_ & ~0x00000001);
        code_ = 0;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonData commonData_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> commonDataBuilder_;
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return Whether the commonData field is set.
       */
      public boolean hasCommonData() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return The commonData.
       */
      public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
        if (commonDataBuilder_ == null) {
          return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        } else {
          return commonDataBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonData_ = value;
        } else {
          commonDataBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(
          com.dxx.game.dto.CommonProto.CommonData.Builder builderForValue) {
        if (commonDataBuilder_ == null) {
          commonData_ = builderForValue.build();
        } else {
          commonDataBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder mergeCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
            commonData_ != null &&
            commonData_ != com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance()) {
            getCommonDataBuilder().mergeFrom(value);
          } else {
            commonData_ = value;
          }
        } else {
          commonDataBuilder_.mergeFrom(value);
        }
        if (commonData_ != null) {
          bitField0_ |= 0x00000002;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder clearCommonData() {
        bitField0_ = (bitField0_ & ~0x00000002);
        commonData_ = null;
        if (commonDataBuilder_ != null) {
          commonDataBuilder_.dispose();
          commonDataBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonData.Builder getCommonDataBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getCommonDataFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
        if (commonDataBuilder_ != null) {
          return commonDataBuilder_.getMessageOrBuilder();
        } else {
          return commonData_ == null ?
              com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> 
          getCommonDataFieldBuilder() {
        if (commonDataBuilder_ == null) {
          commonDataBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder>(
                  getCommonData(),
                  getParentForChildren(),
                  isClean());
          commonData_ = null;
        }
        return commonDataBuilder_;
      }

      private long refreshTime_ ;
      /**
       * <code>uint64 refreshTime = 3;</code>
       * @return The refreshTime.
       */
      @java.lang.Override
      public long getRefreshTime() {
        return refreshTime_;
      }
      /**
       * <code>uint64 refreshTime = 3;</code>
       * @param value The refreshTime to set.
       * @return This builder for chaining.
       */
      public Builder setRefreshTime(long value) {

        refreshTime_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 refreshTime = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearRefreshTime() {
        bitField0_ = (bitField0_ & ~0x00000004);
        refreshTime_ = 0L;
        onChanged();
        return this;
      }

      private int score_ ;
      /**
       * <code>int32 score = 4;</code>
       * @return The score.
       */
      @java.lang.Override
      public int getScore() {
        return score_;
      }
      /**
       * <code>int32 score = 4;</code>
       * @param value The score to set.
       * @return This builder for chaining.
       */
      public Builder setScore(int value) {

        score_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>int32 score = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearScore() {
        bitField0_ = (bitField0_ & ~0x00000008);
        score_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Actor.CityTakeScoreRewardResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.Actor.CityTakeScoreRewardResponse)
    private static final com.dxx.game.dto.CityProto.CityTakeScoreRewardResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.CityProto.CityTakeScoreRewardResponse();
    }

    public static com.dxx.game.dto.CityProto.CityTakeScoreRewardResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<CityTakeScoreRewardResponse>
        PARSER = new com.google.protobuf.AbstractParser<CityTakeScoreRewardResponse>() {
      @java.lang.Override
      public CityTakeScoreRewardResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<CityTakeScoreRewardResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CityTakeScoreRewardResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.CityProto.CityTakeScoreRewardResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface CitySyncPowerRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Actor.CitySyncPowerRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();

    /**
     * <code>int32 power = 2;</code>
     * @return The power.
     */
    int getPower();

    /**
     * <pre>
     * 版本号
     * </pre>
     *
     * <code>string clientVersion = 3;</code>
     * @return The clientVersion.
     */
    java.lang.String getClientVersion();
    /**
     * <pre>
     * 版本号
     * </pre>
     *
     * <code>string clientVersion = 3;</code>
     * @return The bytes for clientVersion.
     */
    com.google.protobuf.ByteString
        getClientVersionBytes();
  }
  /**
   * <pre>
   * CMD PackageId=12611 主城-临时同步社交展示战力
   * </pre>
   *
   * Protobuf type {@code Proto.Actor.CitySyncPowerRequest}
   */
  public static final class CitySyncPowerRequest extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Actor.CitySyncPowerRequest)
      CitySyncPowerRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        CitySyncPowerRequest.class.getName());
    }
    // Use CitySyncPowerRequest.newBuilder() to construct.
    private CitySyncPowerRequest(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private CitySyncPowerRequest() {
      clientVersion_ = "";
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CitySyncPowerRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CitySyncPowerRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.CityProto.CitySyncPowerRequest.class, com.dxx.game.dto.CityProto.CitySyncPowerRequest.Builder.class);
    }

    private int bitField0_;
    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }

    public static final int POWER_FIELD_NUMBER = 2;
    private int power_ = 0;
    /**
     * <code>int32 power = 2;</code>
     * @return The power.
     */
    @java.lang.Override
    public int getPower() {
      return power_;
    }

    public static final int CLIENTVERSION_FIELD_NUMBER = 3;
    @SuppressWarnings("serial")
    private volatile java.lang.Object clientVersion_ = "";
    /**
     * <pre>
     * 版本号
     * </pre>
     *
     * <code>string clientVersion = 3;</code>
     * @return The clientVersion.
     */
    @java.lang.Override
    public java.lang.String getClientVersion() {
      java.lang.Object ref = clientVersion_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        clientVersion_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 版本号
     * </pre>
     *
     * <code>string clientVersion = 3;</code>
     * @return The bytes for clientVersion.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getClientVersionBytes() {
      java.lang.Object ref = clientVersion_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        clientVersion_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getCommonParams());
      }
      if (power_ != 0) {
        output.writeInt32(2, power_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(clientVersion_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 3, clientVersion_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      if (power_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, power_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(clientVersion_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(3, clientVersion_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.CityProto.CitySyncPowerRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.CityProto.CitySyncPowerRequest other = (com.dxx.game.dto.CityProto.CitySyncPowerRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (getPower()
          != other.getPower()) return false;
      if (!getClientVersion()
          .equals(other.getClientVersion())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (37 * hash) + POWER_FIELD_NUMBER;
      hash = (53 * hash) + getPower();
      hash = (37 * hash) + CLIENTVERSION_FIELD_NUMBER;
      hash = (53 * hash) + getClientVersion().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.CityProto.CitySyncPowerRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.CityProto.CitySyncPowerRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.CityProto.CitySyncPowerRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.CityProto.CitySyncPowerRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.CityProto.CitySyncPowerRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.CityProto.CitySyncPowerRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.CityProto.CitySyncPowerRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.CityProto.CitySyncPowerRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.CityProto.CitySyncPowerRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.CityProto.CitySyncPowerRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.CityProto.CitySyncPowerRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.CityProto.CitySyncPowerRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.CityProto.CitySyncPowerRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=12611 主城-临时同步社交展示战力
     * </pre>
     *
     * Protobuf type {@code Proto.Actor.CitySyncPowerRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Actor.CitySyncPowerRequest)
        com.dxx.game.dto.CityProto.CitySyncPowerRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CitySyncPowerRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CitySyncPowerRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.CityProto.CitySyncPowerRequest.class, com.dxx.game.dto.CityProto.CitySyncPowerRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.CityProto.CitySyncPowerRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getCommonParamsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        power_ = 0;
        clientVersion_ = "";
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CitySyncPowerRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.CityProto.CitySyncPowerRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.CityProto.CitySyncPowerRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.CityProto.CitySyncPowerRequest build() {
        com.dxx.game.dto.CityProto.CitySyncPowerRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.CityProto.CitySyncPowerRequest buildPartial() {
        com.dxx.game.dto.CityProto.CitySyncPowerRequest result = new com.dxx.game.dto.CityProto.CitySyncPowerRequest(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.CityProto.CitySyncPowerRequest result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.commonParams_ = commonParamsBuilder_ == null
              ? commonParams_
              : commonParamsBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.power_ = power_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.clientVersion_ = clientVersion_;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.CityProto.CitySyncPowerRequest) {
          return mergeFrom((com.dxx.game.dto.CityProto.CitySyncPowerRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.CityProto.CitySyncPowerRequest other) {
        if (other == com.dxx.game.dto.CityProto.CitySyncPowerRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        if (other.getPower() != 0) {
          setPower(other.getPower());
        }
        if (!other.getClientVersion().isEmpty()) {
          clientVersion_ = other.clientVersion_;
          bitField0_ |= 0x00000004;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getCommonParamsFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 16: {
                power_ = input.readInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 26: {
                clientVersion_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000004;
                break;
              } // case 26
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
        } else {
          commonParamsBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
            commonParams_ != null &&
            commonParams_ != com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance()) {
            getCommonParamsBuilder().mergeFrom(value);
          } else {
            commonParams_ = value;
          }
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }
        if (commonParams_ != null) {
          bitField0_ |= 0x00000001;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        bitField0_ = (bitField0_ & ~0x00000001);
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }

      private int power_ ;
      /**
       * <code>int32 power = 2;</code>
       * @return The power.
       */
      @java.lang.Override
      public int getPower() {
        return power_;
      }
      /**
       * <code>int32 power = 2;</code>
       * @param value The power to set.
       * @return This builder for chaining.
       */
      public Builder setPower(int value) {

        power_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>int32 power = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearPower() {
        bitField0_ = (bitField0_ & ~0x00000002);
        power_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object clientVersion_ = "";
      /**
       * <pre>
       * 版本号
       * </pre>
       *
       * <code>string clientVersion = 3;</code>
       * @return The clientVersion.
       */
      public java.lang.String getClientVersion() {
        java.lang.Object ref = clientVersion_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          clientVersion_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 版本号
       * </pre>
       *
       * <code>string clientVersion = 3;</code>
       * @return The bytes for clientVersion.
       */
      public com.google.protobuf.ByteString
          getClientVersionBytes() {
        java.lang.Object ref = clientVersion_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          clientVersion_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 版本号
       * </pre>
       *
       * <code>string clientVersion = 3;</code>
       * @param value The clientVersion to set.
       * @return This builder for chaining.
       */
      public Builder setClientVersion(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        clientVersion_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 版本号
       * </pre>
       *
       * <code>string clientVersion = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearClientVersion() {
        clientVersion_ = getDefaultInstance().getClientVersion();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 版本号
       * </pre>
       *
       * <code>string clientVersion = 3;</code>
       * @param value The bytes for clientVersion to set.
       * @return This builder for chaining.
       */
      public Builder setClientVersionBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        clientVersion_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Actor.CitySyncPowerRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.Actor.CitySyncPowerRequest)
    private static final com.dxx.game.dto.CityProto.CitySyncPowerRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.CityProto.CitySyncPowerRequest();
    }

    public static com.dxx.game.dto.CityProto.CitySyncPowerRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<CitySyncPowerRequest>
        PARSER = new com.google.protobuf.AbstractParser<CitySyncPowerRequest>() {
      @java.lang.Override
      public CitySyncPowerRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<CitySyncPowerRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CitySyncPowerRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.CityProto.CitySyncPowerRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface CitySyncPowerResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Actor.CitySyncPowerResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    boolean hasCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    com.dxx.game.dto.CommonProto.CommonData getCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder();
  }
  /**
   * <pre>
   * CMD PackageId=12612
   * </pre>
   *
   * Protobuf type {@code Proto.Actor.CitySyncPowerResponse}
   */
  public static final class CitySyncPowerResponse extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Actor.CitySyncPowerResponse)
      CitySyncPowerResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        CitySyncPowerResponse.class.getName());
    }
    // Use CitySyncPowerResponse.newBuilder() to construct.
    private CitySyncPowerResponse(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private CitySyncPowerResponse() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CitySyncPowerResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CitySyncPowerResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.CityProto.CitySyncPowerResponse.class, com.dxx.game.dto.CityProto.CitySyncPowerResponse.Builder.class);
    }

    private int bitField0_;
    public static final int CODE_FIELD_NUMBER = 1;
    private int code_ = 0;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int COMMONDATA_FIELD_NUMBER = 2;
    private com.dxx.game.dto.CommonProto.CommonData commonData_;
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    @java.lang.Override
    public boolean hasCommonData() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(2, getCommonData());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getCommonData());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.CityProto.CitySyncPowerResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.CityProto.CitySyncPowerResponse other = (com.dxx.game.dto.CityProto.CitySyncPowerResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (hasCommonData() != other.hasCommonData()) return false;
      if (hasCommonData()) {
        if (!getCommonData()
            .equals(other.getCommonData())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      if (hasCommonData()) {
        hash = (37 * hash) + COMMONDATA_FIELD_NUMBER;
        hash = (53 * hash) + getCommonData().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.CityProto.CitySyncPowerResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.CityProto.CitySyncPowerResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.CityProto.CitySyncPowerResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.CityProto.CitySyncPowerResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.CityProto.CitySyncPowerResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.CityProto.CitySyncPowerResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.CityProto.CitySyncPowerResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.CityProto.CitySyncPowerResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.CityProto.CitySyncPowerResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.CityProto.CitySyncPowerResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.CityProto.CitySyncPowerResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.CityProto.CitySyncPowerResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.CityProto.CitySyncPowerResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=12612
     * </pre>
     *
     * Protobuf type {@code Proto.Actor.CitySyncPowerResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Actor.CitySyncPowerResponse)
        com.dxx.game.dto.CityProto.CitySyncPowerResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CitySyncPowerResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CitySyncPowerResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.CityProto.CitySyncPowerResponse.class, com.dxx.game.dto.CityProto.CitySyncPowerResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.CityProto.CitySyncPowerResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getCommonDataFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        code_ = 0;
        commonData_ = null;
        if (commonDataBuilder_ != null) {
          commonDataBuilder_.dispose();
          commonDataBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CitySyncPowerResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.CityProto.CitySyncPowerResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.CityProto.CitySyncPowerResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.CityProto.CitySyncPowerResponse build() {
        com.dxx.game.dto.CityProto.CitySyncPowerResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.CityProto.CitySyncPowerResponse buildPartial() {
        com.dxx.game.dto.CityProto.CitySyncPowerResponse result = new com.dxx.game.dto.CityProto.CitySyncPowerResponse(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.CityProto.CitySyncPowerResponse result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.code_ = code_;
        }
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.commonData_ = commonDataBuilder_ == null
              ? commonData_
              : commonDataBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.CityProto.CitySyncPowerResponse) {
          return mergeFrom((com.dxx.game.dto.CityProto.CitySyncPowerResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.CityProto.CitySyncPowerResponse other) {
        if (other == com.dxx.game.dto.CityProto.CitySyncPowerResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.hasCommonData()) {
          mergeCommonData(other.getCommonData());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                code_ = input.readInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 18: {
                input.readMessage(
                    getCommonDataFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {

        code_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        bitField0_ = (bitField0_ & ~0x00000001);
        code_ = 0;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonData commonData_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> commonDataBuilder_;
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return Whether the commonData field is set.
       */
      public boolean hasCommonData() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return The commonData.
       */
      public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
        if (commonDataBuilder_ == null) {
          return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        } else {
          return commonDataBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonData_ = value;
        } else {
          commonDataBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(
          com.dxx.game.dto.CommonProto.CommonData.Builder builderForValue) {
        if (commonDataBuilder_ == null) {
          commonData_ = builderForValue.build();
        } else {
          commonDataBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder mergeCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
            commonData_ != null &&
            commonData_ != com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance()) {
            getCommonDataBuilder().mergeFrom(value);
          } else {
            commonData_ = value;
          }
        } else {
          commonDataBuilder_.mergeFrom(value);
        }
        if (commonData_ != null) {
          bitField0_ |= 0x00000002;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder clearCommonData() {
        bitField0_ = (bitField0_ & ~0x00000002);
        commonData_ = null;
        if (commonDataBuilder_ != null) {
          commonDataBuilder_.dispose();
          commonDataBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonData.Builder getCommonDataBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getCommonDataFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
        if (commonDataBuilder_ != null) {
          return commonDataBuilder_.getMessageOrBuilder();
        } else {
          return commonData_ == null ?
              com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> 
          getCommonDataFieldBuilder() {
        if (commonDataBuilder_ == null) {
          commonDataBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder>(
                  getCommonData(),
                  getParentForChildren(),
                  isClean());
          commonData_ = null;
        }
        return commonDataBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Actor.CitySyncPowerResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.Actor.CitySyncPowerResponse)
    private static final com.dxx.game.dto.CityProto.CitySyncPowerResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.CityProto.CitySyncPowerResponse();
    }

    public static com.dxx.game.dto.CityProto.CitySyncPowerResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<CitySyncPowerResponse>
        PARSER = new com.google.protobuf.AbstractParser<CitySyncPowerResponse>() {
      @java.lang.Override
      public CitySyncPowerResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<CitySyncPowerResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CitySyncPowerResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.CityProto.CitySyncPowerResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface CityGetInfoRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Actor.CityGetInfoRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();
  }
  /**
   * <pre>
   * CMD PackageId=12613 主城-获取信息
   * </pre>
   *
   * Protobuf type {@code Proto.Actor.CityGetInfoRequest}
   */
  public static final class CityGetInfoRequest extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Actor.CityGetInfoRequest)
      CityGetInfoRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        CityGetInfoRequest.class.getName());
    }
    // Use CityGetInfoRequest.newBuilder() to construct.
    private CityGetInfoRequest(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private CityGetInfoRequest() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityGetInfoRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityGetInfoRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.CityProto.CityGetInfoRequest.class, com.dxx.game.dto.CityProto.CityGetInfoRequest.Builder.class);
    }

    private int bitField0_;
    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getCommonParams());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.CityProto.CityGetInfoRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.CityProto.CityGetInfoRequest other = (com.dxx.game.dto.CityProto.CityGetInfoRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.CityProto.CityGetInfoRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.CityProto.CityGetInfoRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.CityProto.CityGetInfoRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.CityProto.CityGetInfoRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.CityProto.CityGetInfoRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.CityProto.CityGetInfoRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.CityProto.CityGetInfoRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.CityProto.CityGetInfoRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.CityProto.CityGetInfoRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.CityProto.CityGetInfoRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.CityProto.CityGetInfoRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.CityProto.CityGetInfoRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.CityProto.CityGetInfoRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=12613 主城-获取信息
     * </pre>
     *
     * Protobuf type {@code Proto.Actor.CityGetInfoRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Actor.CityGetInfoRequest)
        com.dxx.game.dto.CityProto.CityGetInfoRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityGetInfoRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityGetInfoRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.CityProto.CityGetInfoRequest.class, com.dxx.game.dto.CityProto.CityGetInfoRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.CityProto.CityGetInfoRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getCommonParamsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityGetInfoRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.CityProto.CityGetInfoRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.CityProto.CityGetInfoRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.CityProto.CityGetInfoRequest build() {
        com.dxx.game.dto.CityProto.CityGetInfoRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.CityProto.CityGetInfoRequest buildPartial() {
        com.dxx.game.dto.CityProto.CityGetInfoRequest result = new com.dxx.game.dto.CityProto.CityGetInfoRequest(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.CityProto.CityGetInfoRequest result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.commonParams_ = commonParamsBuilder_ == null
              ? commonParams_
              : commonParamsBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.CityProto.CityGetInfoRequest) {
          return mergeFrom((com.dxx.game.dto.CityProto.CityGetInfoRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.CityProto.CityGetInfoRequest other) {
        if (other == com.dxx.game.dto.CityProto.CityGetInfoRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getCommonParamsFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
        } else {
          commonParamsBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
            commonParams_ != null &&
            commonParams_ != com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance()) {
            getCommonParamsBuilder().mergeFrom(value);
          } else {
            commonParams_ = value;
          }
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }
        if (commonParams_ != null) {
          bitField0_ |= 0x00000001;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        bitField0_ = (bitField0_ & ~0x00000001);
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Actor.CityGetInfoRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.Actor.CityGetInfoRequest)
    private static final com.dxx.game.dto.CityProto.CityGetInfoRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.CityProto.CityGetInfoRequest();
    }

    public static com.dxx.game.dto.CityProto.CityGetInfoRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<CityGetInfoRequest>
        PARSER = new com.google.protobuf.AbstractParser<CityGetInfoRequest>() {
      @java.lang.Override
      public CityGetInfoRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<CityGetInfoRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CityGetInfoRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.CityProto.CityGetInfoRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface CityGetInfoResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Actor.CityGetInfoResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    boolean hasCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    com.dxx.game.dto.CommonProto.CommonData getCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder();

    /**
     * <pre>
     * 领主数据
     * </pre>
     *
     * <code>.Proto.Common.LordDto extra = 3;</code>
     * @return Whether the extra field is set.
     */
    boolean hasExtra();
    /**
     * <pre>
     * 领主数据
     * </pre>
     *
     * <code>.Proto.Common.LordDto extra = 3;</code>
     * @return The extra.
     */
    com.dxx.game.dto.CommonProto.LordDto getExtra();
    /**
     * <pre>
     * 领主数据
     * </pre>
     *
     * <code>.Proto.Common.LordDto extra = 3;</code>
     */
    com.dxx.game.dto.CommonProto.LordDtoOrBuilder getExtraOrBuilder();

    /**
     * <pre>
     * 奴隶个数
     * </pre>
     *
     * <code>uint32 slaveCount = 4;</code>
     * @return The slaveCount.
     */
    int getSlaveCount();
  }
  /**
   * <pre>
   * CMD PackageId=12614
   * </pre>
   *
   * Protobuf type {@code Proto.Actor.CityGetInfoResponse}
   */
  public static final class CityGetInfoResponse extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Actor.CityGetInfoResponse)
      CityGetInfoResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        CityGetInfoResponse.class.getName());
    }
    // Use CityGetInfoResponse.newBuilder() to construct.
    private CityGetInfoResponse(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private CityGetInfoResponse() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityGetInfoResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityGetInfoResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.CityProto.CityGetInfoResponse.class, com.dxx.game.dto.CityProto.CityGetInfoResponse.Builder.class);
    }

    private int bitField0_;
    public static final int CODE_FIELD_NUMBER = 1;
    private int code_ = 0;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int COMMONDATA_FIELD_NUMBER = 2;
    private com.dxx.game.dto.CommonProto.CommonData commonData_;
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    @java.lang.Override
    public boolean hasCommonData() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }

    public static final int EXTRA_FIELD_NUMBER = 3;
    private com.dxx.game.dto.CommonProto.LordDto extra_;
    /**
     * <pre>
     * 领主数据
     * </pre>
     *
     * <code>.Proto.Common.LordDto extra = 3;</code>
     * @return Whether the extra field is set.
     */
    @java.lang.Override
    public boolean hasExtra() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 领主数据
     * </pre>
     *
     * <code>.Proto.Common.LordDto extra = 3;</code>
     * @return The extra.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.LordDto getExtra() {
      return extra_ == null ? com.dxx.game.dto.CommonProto.LordDto.getDefaultInstance() : extra_;
    }
    /**
     * <pre>
     * 领主数据
     * </pre>
     *
     * <code>.Proto.Common.LordDto extra = 3;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.LordDtoOrBuilder getExtraOrBuilder() {
      return extra_ == null ? com.dxx.game.dto.CommonProto.LordDto.getDefaultInstance() : extra_;
    }

    public static final int SLAVECOUNT_FIELD_NUMBER = 4;
    private int slaveCount_ = 0;
    /**
     * <pre>
     * 奴隶个数
     * </pre>
     *
     * <code>uint32 slaveCount = 4;</code>
     * @return The slaveCount.
     */
    @java.lang.Override
    public int getSlaveCount() {
      return slaveCount_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(2, getCommonData());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(3, getExtra());
      }
      if (slaveCount_ != 0) {
        output.writeUInt32(4, slaveCount_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getCommonData());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getExtra());
      }
      if (slaveCount_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(4, slaveCount_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.CityProto.CityGetInfoResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.CityProto.CityGetInfoResponse other = (com.dxx.game.dto.CityProto.CityGetInfoResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (hasCommonData() != other.hasCommonData()) return false;
      if (hasCommonData()) {
        if (!getCommonData()
            .equals(other.getCommonData())) return false;
      }
      if (hasExtra() != other.hasExtra()) return false;
      if (hasExtra()) {
        if (!getExtra()
            .equals(other.getExtra())) return false;
      }
      if (getSlaveCount()
          != other.getSlaveCount()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      if (hasCommonData()) {
        hash = (37 * hash) + COMMONDATA_FIELD_NUMBER;
        hash = (53 * hash) + getCommonData().hashCode();
      }
      if (hasExtra()) {
        hash = (37 * hash) + EXTRA_FIELD_NUMBER;
        hash = (53 * hash) + getExtra().hashCode();
      }
      hash = (37 * hash) + SLAVECOUNT_FIELD_NUMBER;
      hash = (53 * hash) + getSlaveCount();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.CityProto.CityGetInfoResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.CityProto.CityGetInfoResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.CityProto.CityGetInfoResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.CityProto.CityGetInfoResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.CityProto.CityGetInfoResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.CityProto.CityGetInfoResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.CityProto.CityGetInfoResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.CityProto.CityGetInfoResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.CityProto.CityGetInfoResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.CityProto.CityGetInfoResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.CityProto.CityGetInfoResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.CityProto.CityGetInfoResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.CityProto.CityGetInfoResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=12614
     * </pre>
     *
     * Protobuf type {@code Proto.Actor.CityGetInfoResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Actor.CityGetInfoResponse)
        com.dxx.game.dto.CityProto.CityGetInfoResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityGetInfoResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityGetInfoResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.CityProto.CityGetInfoResponse.class, com.dxx.game.dto.CityProto.CityGetInfoResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.CityProto.CityGetInfoResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getCommonDataFieldBuilder();
          getExtraFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        code_ = 0;
        commonData_ = null;
        if (commonDataBuilder_ != null) {
          commonDataBuilder_.dispose();
          commonDataBuilder_ = null;
        }
        extra_ = null;
        if (extraBuilder_ != null) {
          extraBuilder_.dispose();
          extraBuilder_ = null;
        }
        slaveCount_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.CityProto.internal_static_Proto_Actor_CityGetInfoResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.CityProto.CityGetInfoResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.CityProto.CityGetInfoResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.CityProto.CityGetInfoResponse build() {
        com.dxx.game.dto.CityProto.CityGetInfoResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.CityProto.CityGetInfoResponse buildPartial() {
        com.dxx.game.dto.CityProto.CityGetInfoResponse result = new com.dxx.game.dto.CityProto.CityGetInfoResponse(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.CityProto.CityGetInfoResponse result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.code_ = code_;
        }
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.commonData_ = commonDataBuilder_ == null
              ? commonData_
              : commonDataBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.extra_ = extraBuilder_ == null
              ? extra_
              : extraBuilder_.build();
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.slaveCount_ = slaveCount_;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.CityProto.CityGetInfoResponse) {
          return mergeFrom((com.dxx.game.dto.CityProto.CityGetInfoResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.CityProto.CityGetInfoResponse other) {
        if (other == com.dxx.game.dto.CityProto.CityGetInfoResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.hasCommonData()) {
          mergeCommonData(other.getCommonData());
        }
        if (other.hasExtra()) {
          mergeExtra(other.getExtra());
        }
        if (other.getSlaveCount() != 0) {
          setSlaveCount(other.getSlaveCount());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                code_ = input.readInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 18: {
                input.readMessage(
                    getCommonDataFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              case 26: {
                input.readMessage(
                    getExtraFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000004;
                break;
              } // case 26
              case 32: {
                slaveCount_ = input.readUInt32();
                bitField0_ |= 0x00000008;
                break;
              } // case 32
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {

        code_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        bitField0_ = (bitField0_ & ~0x00000001);
        code_ = 0;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonData commonData_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> commonDataBuilder_;
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return Whether the commonData field is set.
       */
      public boolean hasCommonData() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return The commonData.
       */
      public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
        if (commonDataBuilder_ == null) {
          return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        } else {
          return commonDataBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonData_ = value;
        } else {
          commonDataBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(
          com.dxx.game.dto.CommonProto.CommonData.Builder builderForValue) {
        if (commonDataBuilder_ == null) {
          commonData_ = builderForValue.build();
        } else {
          commonDataBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder mergeCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
            commonData_ != null &&
            commonData_ != com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance()) {
            getCommonDataBuilder().mergeFrom(value);
          } else {
            commonData_ = value;
          }
        } else {
          commonDataBuilder_.mergeFrom(value);
        }
        if (commonData_ != null) {
          bitField0_ |= 0x00000002;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder clearCommonData() {
        bitField0_ = (bitField0_ & ~0x00000002);
        commonData_ = null;
        if (commonDataBuilder_ != null) {
          commonDataBuilder_.dispose();
          commonDataBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonData.Builder getCommonDataBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getCommonDataFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
        if (commonDataBuilder_ != null) {
          return commonDataBuilder_.getMessageOrBuilder();
        } else {
          return commonData_ == null ?
              com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> 
          getCommonDataFieldBuilder() {
        if (commonDataBuilder_ == null) {
          commonDataBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder>(
                  getCommonData(),
                  getParentForChildren(),
                  isClean());
          commonData_ = null;
        }
        return commonDataBuilder_;
      }

      private com.dxx.game.dto.CommonProto.LordDto extra_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.LordDto, com.dxx.game.dto.CommonProto.LordDto.Builder, com.dxx.game.dto.CommonProto.LordDtoOrBuilder> extraBuilder_;
      /**
       * <pre>
       * 领主数据
       * </pre>
       *
       * <code>.Proto.Common.LordDto extra = 3;</code>
       * @return Whether the extra field is set.
       */
      public boolean hasExtra() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 领主数据
       * </pre>
       *
       * <code>.Proto.Common.LordDto extra = 3;</code>
       * @return The extra.
       */
      public com.dxx.game.dto.CommonProto.LordDto getExtra() {
        if (extraBuilder_ == null) {
          return extra_ == null ? com.dxx.game.dto.CommonProto.LordDto.getDefaultInstance() : extra_;
        } else {
          return extraBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 领主数据
       * </pre>
       *
       * <code>.Proto.Common.LordDto extra = 3;</code>
       */
      public Builder setExtra(com.dxx.game.dto.CommonProto.LordDto value) {
        if (extraBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          extra_ = value;
        } else {
          extraBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 领主数据
       * </pre>
       *
       * <code>.Proto.Common.LordDto extra = 3;</code>
       */
      public Builder setExtra(
          com.dxx.game.dto.CommonProto.LordDto.Builder builderForValue) {
        if (extraBuilder_ == null) {
          extra_ = builderForValue.build();
        } else {
          extraBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 领主数据
       * </pre>
       *
       * <code>.Proto.Common.LordDto extra = 3;</code>
       */
      public Builder mergeExtra(com.dxx.game.dto.CommonProto.LordDto value) {
        if (extraBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0) &&
            extra_ != null &&
            extra_ != com.dxx.game.dto.CommonProto.LordDto.getDefaultInstance()) {
            getExtraBuilder().mergeFrom(value);
          } else {
            extra_ = value;
          }
        } else {
          extraBuilder_.mergeFrom(value);
        }
        if (extra_ != null) {
          bitField0_ |= 0x00000004;
          onChanged();
        }
        return this;
      }
      /**
       * <pre>
       * 领主数据
       * </pre>
       *
       * <code>.Proto.Common.LordDto extra = 3;</code>
       */
      public Builder clearExtra() {
        bitField0_ = (bitField0_ & ~0x00000004);
        extra_ = null;
        if (extraBuilder_ != null) {
          extraBuilder_.dispose();
          extraBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 领主数据
       * </pre>
       *
       * <code>.Proto.Common.LordDto extra = 3;</code>
       */
      public com.dxx.game.dto.CommonProto.LordDto.Builder getExtraBuilder() {
        bitField0_ |= 0x00000004;
        onChanged();
        return getExtraFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 领主数据
       * </pre>
       *
       * <code>.Proto.Common.LordDto extra = 3;</code>
       */
      public com.dxx.game.dto.CommonProto.LordDtoOrBuilder getExtraOrBuilder() {
        if (extraBuilder_ != null) {
          return extraBuilder_.getMessageOrBuilder();
        } else {
          return extra_ == null ?
              com.dxx.game.dto.CommonProto.LordDto.getDefaultInstance() : extra_;
        }
      }
      /**
       * <pre>
       * 领主数据
       * </pre>
       *
       * <code>.Proto.Common.LordDto extra = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.LordDto, com.dxx.game.dto.CommonProto.LordDto.Builder, com.dxx.game.dto.CommonProto.LordDtoOrBuilder> 
          getExtraFieldBuilder() {
        if (extraBuilder_ == null) {
          extraBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.LordDto, com.dxx.game.dto.CommonProto.LordDto.Builder, com.dxx.game.dto.CommonProto.LordDtoOrBuilder>(
                  getExtra(),
                  getParentForChildren(),
                  isClean());
          extra_ = null;
        }
        return extraBuilder_;
      }

      private int slaveCount_ ;
      /**
       * <pre>
       * 奴隶个数
       * </pre>
       *
       * <code>uint32 slaveCount = 4;</code>
       * @return The slaveCount.
       */
      @java.lang.Override
      public int getSlaveCount() {
        return slaveCount_;
      }
      /**
       * <pre>
       * 奴隶个数
       * </pre>
       *
       * <code>uint32 slaveCount = 4;</code>
       * @param value The slaveCount to set.
       * @return This builder for chaining.
       */
      public Builder setSlaveCount(int value) {

        slaveCount_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 奴隶个数
       * </pre>
       *
       * <code>uint32 slaveCount = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearSlaveCount() {
        bitField0_ = (bitField0_ & ~0x00000008);
        slaveCount_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Actor.CityGetInfoResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.Actor.CityGetInfoResponse)
    private static final com.dxx.game.dto.CityProto.CityGetInfoResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.CityProto.CityGetInfoResponse();
    }

    public static com.dxx.game.dto.CityProto.CityGetInfoResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<CityGetInfoResponse>
        PARSER = new com.google.protobuf.AbstractParser<CityGetInfoResponse>() {
      @java.lang.Override
      public CityGetInfoResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<CityGetInfoResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CityGetInfoResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.CityProto.CityGetInfoResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Actor_CityGoldmineLevelUpRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Actor_CityGoldmineLevelUpRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Actor_CityGoldmineLevelUpResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Actor_CityGoldmineLevelUpResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Actor_CityGoldmineHangRewardRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Actor_CityGoldmineHangRewardRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Actor_CityGoldmineHangRewardResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Actor_CityGoldmineHangRewardResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Actor_CityGetChestInfoRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Actor_CityGetChestInfoRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Actor_CityGetChestInfoResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Actor_CityGetChestInfoResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Actor_CityOpenChestRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Actor_CityOpenChestRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Actor_CityOpenChestResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Actor_CityOpenChestResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Actor_CityTakeScoreRewardRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Actor_CityTakeScoreRewardRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Actor_CityTakeScoreRewardResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Actor_CityTakeScoreRewardResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Actor_CitySyncPowerRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Actor_CitySyncPowerRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Actor_CitySyncPowerResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Actor_CitySyncPowerResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Actor_CityGetInfoRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Actor_CityGetInfoRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Actor_CityGetInfoResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Actor_CityGetInfoResponse_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\ncity.proto\022\013Proto.Actor\032\014common.proto\"" +
      "N\n\032CityGoldmineLevelUpRequest\0220\n\014commonP" +
      "arams\030\001 \001(\0132\032.Proto.Common.CommonParams\"" +
      "h\n\033CityGoldmineLevelUpResponse\022\014\n\004code\030\001" +
      " \001(\005\022\r\n\005level\030\002 \001(\005\022,\n\ncommonData\030\003 \001(\0132" +
      "\030.Proto.Common.CommonData\"Q\n\035CityGoldmin" +
      "eHangRewardRequest\0220\n\014commonParams\030\001 \001(\013" +
      "2\032.Proto.Common.CommonParams\"|\n\036CityGold" +
      "mineHangRewardResponse\022\014\n\004code\030\001 \001(\005\022\036\n\026" +
      "lastGoldmineRewardTime\030\002 \001(\004\022,\n\ncommonDa" +
      "ta\030\003 \001(\0132\030.Proto.Common.CommonData\"K\n\027Ci" +
      "tyGetChestInfoRequest\0220\n\014commonParams\030\001 " +
      "\001(\0132\032.Proto.Common.CommonParams\"\255\001\n\030City" +
      "GetChestInfoResponse\022\014\n\004code\030\001 \001(\005\022,\n\nco" +
      "mmonData\030\002 \001(\0132\030.Proto.Common.CommonData" +
      "\022-\n\tcityChest\030\003 \003(\0132\032.Proto.Common.CityC" +
      "hestDto\022\021\n\tstartTime\030\004 \001(\004\022\023\n\013refreshTim" +
      "e\030\005 \001(\004\"W\n\024CityOpenChestRequest\0220\n\014commo" +
      "nParams\030\001 \001(\0132\032.Proto.Common.CommonParam" +
      "s\022\r\n\005rowId\030\002 \003(\004\"\206\001\n\025CityOpenChestRespon" +
      "se\022\014\n\004code\030\001 \001(\005\022,\n\ncommonData\030\002 \001(\0132\030.P" +
      "roto.Common.CommonData\022\r\n\005rowId\030\003 \003(\004\022\023\n" +
      "\013refreshTime\030\004 \001(\004\022\r\n\005score\030\005 \001(\005\"N\n\032Cit" +
      "yTakeScoreRewardRequest\0220\n\014commonParams\030" +
      "\001 \001(\0132\032.Proto.Common.CommonParams\"}\n\033Cit" +
      "yTakeScoreRewardResponse\022\014\n\004code\030\001 \001(\005\022," +
      "\n\ncommonData\030\002 \001(\0132\030.Proto.Common.Common" +
      "Data\022\023\n\013refreshTime\030\003 \001(\004\022\r\n\005score\030\004 \001(\005" +
      "\"n\n\024CitySyncPowerRequest\0220\n\014commonParams" +
      "\030\001 \001(\0132\032.Proto.Common.CommonParams\022\r\n\005po" +
      "wer\030\002 \001(\005\022\025\n\rclientVersion\030\003 \001(\t\"S\n\025City" +
      "SyncPowerResponse\022\014\n\004code\030\001 \001(\005\022,\n\ncommo" +
      "nData\030\002 \001(\0132\030.Proto.Common.CommonData\"F\n" +
      "\022CityGetInfoRequest\0220\n\014commonParams\030\001 \001(" +
      "\0132\032.Proto.Common.CommonParams\"\213\001\n\023CityGe" +
      "tInfoResponse\022\014\n\004code\030\001 \001(\005\022,\n\ncommonDat" +
      "a\030\002 \001(\0132\030.Proto.Common.CommonData\022$\n\005ext" +
      "ra\030\003 \001(\0132\025.Proto.Common.LordDto\022\022\n\nslave" +
      "Count\030\004 \001(\rB\035\n\020com.dxx.game.dtoB\tCityPro" +
      "tob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.dxx.game.dto.CommonProto.getDescriptor(),
        });
    internal_static_Proto_Actor_CityGoldmineLevelUpRequest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_Proto_Actor_CityGoldmineLevelUpRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Actor_CityGoldmineLevelUpRequest_descriptor,
        new java.lang.String[] { "CommonParams", });
    internal_static_Proto_Actor_CityGoldmineLevelUpResponse_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_Proto_Actor_CityGoldmineLevelUpResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Actor_CityGoldmineLevelUpResponse_descriptor,
        new java.lang.String[] { "Code", "Level", "CommonData", });
    internal_static_Proto_Actor_CityGoldmineHangRewardRequest_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_Proto_Actor_CityGoldmineHangRewardRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Actor_CityGoldmineHangRewardRequest_descriptor,
        new java.lang.String[] { "CommonParams", });
    internal_static_Proto_Actor_CityGoldmineHangRewardResponse_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_Proto_Actor_CityGoldmineHangRewardResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Actor_CityGoldmineHangRewardResponse_descriptor,
        new java.lang.String[] { "Code", "LastGoldmineRewardTime", "CommonData", });
    internal_static_Proto_Actor_CityGetChestInfoRequest_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_Proto_Actor_CityGetChestInfoRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Actor_CityGetChestInfoRequest_descriptor,
        new java.lang.String[] { "CommonParams", });
    internal_static_Proto_Actor_CityGetChestInfoResponse_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_Proto_Actor_CityGetChestInfoResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Actor_CityGetChestInfoResponse_descriptor,
        new java.lang.String[] { "Code", "CommonData", "CityChest", "StartTime", "RefreshTime", });
    internal_static_Proto_Actor_CityOpenChestRequest_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_Proto_Actor_CityOpenChestRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Actor_CityOpenChestRequest_descriptor,
        new java.lang.String[] { "CommonParams", "RowId", });
    internal_static_Proto_Actor_CityOpenChestResponse_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_Proto_Actor_CityOpenChestResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Actor_CityOpenChestResponse_descriptor,
        new java.lang.String[] { "Code", "CommonData", "RowId", "RefreshTime", "Score", });
    internal_static_Proto_Actor_CityTakeScoreRewardRequest_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_Proto_Actor_CityTakeScoreRewardRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Actor_CityTakeScoreRewardRequest_descriptor,
        new java.lang.String[] { "CommonParams", });
    internal_static_Proto_Actor_CityTakeScoreRewardResponse_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_Proto_Actor_CityTakeScoreRewardResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Actor_CityTakeScoreRewardResponse_descriptor,
        new java.lang.String[] { "Code", "CommonData", "RefreshTime", "Score", });
    internal_static_Proto_Actor_CitySyncPowerRequest_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_Proto_Actor_CitySyncPowerRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Actor_CitySyncPowerRequest_descriptor,
        new java.lang.String[] { "CommonParams", "Power", "ClientVersion", });
    internal_static_Proto_Actor_CitySyncPowerResponse_descriptor =
      getDescriptor().getMessageTypes().get(11);
    internal_static_Proto_Actor_CitySyncPowerResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Actor_CitySyncPowerResponse_descriptor,
        new java.lang.String[] { "Code", "CommonData", });
    internal_static_Proto_Actor_CityGetInfoRequest_descriptor =
      getDescriptor().getMessageTypes().get(12);
    internal_static_Proto_Actor_CityGetInfoRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Actor_CityGetInfoRequest_descriptor,
        new java.lang.String[] { "CommonParams", });
    internal_static_Proto_Actor_CityGetInfoResponse_descriptor =
      getDescriptor().getMessageTypes().get(13);
    internal_static_Proto_Actor_CityGetInfoResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Actor_CityGetInfoResponse_descriptor,
        new java.lang.String[] { "Code", "CommonData", "Extra", "SlaveCount", });
    descriptor.resolveAllFeaturesImmutable();
    com.dxx.game.dto.CommonProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
