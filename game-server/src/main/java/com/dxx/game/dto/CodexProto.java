// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: codex.proto
// Protobuf Java Version: 4.29.0

package com.dxx.game.dto;

public final class CodexProto {
  private CodexProto() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 29,
      /* patch= */ 0,
      /* suffix= */ "",
      CodexProto.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Codex_HeroCodexRewardRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Codex_HeroCodexRewardRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Codex_HeroCodexRewardResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Codex_HeroCodexRewardResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Codex_MonsterCodexDto_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Codex_MonsterCodexDto_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Codex_MonsterCodexRewardRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Codex_MonsterCodexRewardRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Codex_MonsterCodexRewardResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Codex_MonsterCodexRewardResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Codex_MonsterCodexAddRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Codex_MonsterCodexAddRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Codex_MonsterCodexAddResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Codex_MonsterCodexAddResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Codex_MonsterCodexAddResponse_MonsterCodexEntry_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Codex_MonsterCodexAddResponse_MonsterCodexEntry_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\013codex.proto\022\013Proto.Codex\032\014common.proto" +
      "\"Z\n\026HeroCodexRewardRequest\0220\n\014commonPara" +
      "ms\030\001 \001(\0132\032.Proto.Common.CommonParams\022\016\n\006" +
      "heroId\030\002 \001(\005\"F\n\027HeroCodexRewardResponse\022" +
      "\014\n\004code\030\001 \001(\005\022\016\n\006heroId\030\002 \001(\005\022\r\n\005codex\030\003" +
      " \001(\005\":\n\017MonsterCodexDto\022\023\n\013monsterFlag\030\001" +
      " \001(\003\022\022\n\nrewardFlag\030\002 \001(\003\"e\n\031MonsterCodex" +
      "RewardRequest\0220\n\014commonParams\030\001 \001(\0132\032.Pr" +
      "oto.Common.CommonParams\022\026\n\016monsterCodexI" +
      "d\030\002 \001(\005\"y\n\032MonsterCodexRewardResponse\022\014\n" +
      "\004code\030\001 \001(\005\022\031\n\021monsterCodexIndex\030\002 \001(\005\0222" +
      "\n\014monsterCodex\030\003 \001(\0132\034.Proto.Codex.Monst" +
      "erCodexDto\"b\n\026MonsterCodexAddRequest\0220\n\014" +
      "commonParams\030\001 \001(\0132\032.Proto.Common.Common" +
      "Params\022\026\n\016monsterCodexId\030\002 \003(\005\"\310\001\n\027Monst" +
      "erCodexAddResponse\022\014\n\004code\030\001 \001(\005\022L\n\014mons" +
      "terCodex\030\002 \003(\01326.Proto.Codex.MonsterCode" +
      "xAddResponse.MonsterCodexEntry\032Q\n\021Monste" +
      "rCodexEntry\022\013\n\003key\030\001 \001(\005\022+\n\005value\030\002 \001(\0132" +
      "\034.Proto.Codex.MonsterCodexDto:\0028\001B \n\020com" +
      ".dxx.game.dtoB\nCodexProtoP\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.dxx.game.dto.CommonProto.getDescriptor(),
        });
    internal_static_Proto_Codex_HeroCodexRewardRequest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_Proto_Codex_HeroCodexRewardRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Codex_HeroCodexRewardRequest_descriptor,
        new java.lang.String[] { "CommonParams", "HeroId", });
    internal_static_Proto_Codex_HeroCodexRewardResponse_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_Proto_Codex_HeroCodexRewardResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Codex_HeroCodexRewardResponse_descriptor,
        new java.lang.String[] { "Code", "HeroId", "Codex", });
    internal_static_Proto_Codex_MonsterCodexDto_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_Proto_Codex_MonsterCodexDto_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Codex_MonsterCodexDto_descriptor,
        new java.lang.String[] { "MonsterFlag", "RewardFlag", });
    internal_static_Proto_Codex_MonsterCodexRewardRequest_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_Proto_Codex_MonsterCodexRewardRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Codex_MonsterCodexRewardRequest_descriptor,
        new java.lang.String[] { "CommonParams", "MonsterCodexId", });
    internal_static_Proto_Codex_MonsterCodexRewardResponse_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_Proto_Codex_MonsterCodexRewardResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Codex_MonsterCodexRewardResponse_descriptor,
        new java.lang.String[] { "Code", "MonsterCodexIndex", "MonsterCodex", });
    internal_static_Proto_Codex_MonsterCodexAddRequest_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_Proto_Codex_MonsterCodexAddRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Codex_MonsterCodexAddRequest_descriptor,
        new java.lang.String[] { "CommonParams", "MonsterCodexId", });
    internal_static_Proto_Codex_MonsterCodexAddResponse_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_Proto_Codex_MonsterCodexAddResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Codex_MonsterCodexAddResponse_descriptor,
        new java.lang.String[] { "Code", "MonsterCodex", });
    internal_static_Proto_Codex_MonsterCodexAddResponse_MonsterCodexEntry_descriptor =
      internal_static_Proto_Codex_MonsterCodexAddResponse_descriptor.getNestedTypes().get(0);
    internal_static_Proto_Codex_MonsterCodexAddResponse_MonsterCodexEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Codex_MonsterCodexAddResponse_MonsterCodexEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    descriptor.resolveAllFeaturesImmutable();
    com.dxx.game.dto.CommonProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
