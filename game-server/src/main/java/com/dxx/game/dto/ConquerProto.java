// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: conquer.proto
// Protobuf Java Version: 4.29.0

package com.dxx.game.dto;

public final class ConquerProto {
  private ConquerProto() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 29,
      /* patch= */ 0,
      /* suffix= */ "",
      ConquerProto.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ConquerListRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Conquer.ConquerListRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();

    /**
     * <code>int64 userId = 2;</code>
     * @return The userId.
     */
    long getUserId();
  }
  /**
   * <pre>
   * CMD PackageId=12801 征服战-列表
   * </pre>
   *
   * Protobuf type {@code Proto.Conquer.ConquerListRequest}
   */
  public static final class ConquerListRequest extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Conquer.ConquerListRequest)
      ConquerListRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        ConquerListRequest.class.getName());
    }
    // Use ConquerListRequest.newBuilder() to construct.
    private ConquerListRequest(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private ConquerListRequest() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.ConquerProto.internal_static_Proto_Conquer_ConquerListRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.ConquerProto.internal_static_Proto_Conquer_ConquerListRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.ConquerProto.ConquerListRequest.class, com.dxx.game.dto.ConquerProto.ConquerListRequest.Builder.class);
    }

    private int bitField0_;
    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }

    public static final int USERID_FIELD_NUMBER = 2;
    private long userId_ = 0L;
    /**
     * <code>int64 userId = 2;</code>
     * @return The userId.
     */
    @java.lang.Override
    public long getUserId() {
      return userId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getCommonParams());
      }
      if (userId_ != 0L) {
        output.writeInt64(2, userId_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      if (userId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, userId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.ConquerProto.ConquerListRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.ConquerProto.ConquerListRequest other = (com.dxx.game.dto.ConquerProto.ConquerListRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (getUserId()
          != other.getUserId()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (37 * hash) + USERID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getUserId());
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.ConquerProto.ConquerListRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerListRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerListRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerListRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerListRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerListRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerListRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerListRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.ConquerProto.ConquerListRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.ConquerProto.ConquerListRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerListRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerListRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.ConquerProto.ConquerListRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=12801 征服战-列表
     * </pre>
     *
     * Protobuf type {@code Proto.Conquer.ConquerListRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Conquer.ConquerListRequest)
        com.dxx.game.dto.ConquerProto.ConquerListRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.ConquerProto.internal_static_Proto_Conquer_ConquerListRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.ConquerProto.internal_static_Proto_Conquer_ConquerListRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.ConquerProto.ConquerListRequest.class, com.dxx.game.dto.ConquerProto.ConquerListRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.ConquerProto.ConquerListRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getCommonParamsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        userId_ = 0L;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.ConquerProto.internal_static_Proto_Conquer_ConquerListRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.ConquerProto.ConquerListRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.ConquerProto.ConquerListRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.ConquerProto.ConquerListRequest build() {
        com.dxx.game.dto.ConquerProto.ConquerListRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.ConquerProto.ConquerListRequest buildPartial() {
        com.dxx.game.dto.ConquerProto.ConquerListRequest result = new com.dxx.game.dto.ConquerProto.ConquerListRequest(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.ConquerProto.ConquerListRequest result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.commonParams_ = commonParamsBuilder_ == null
              ? commonParams_
              : commonParamsBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.userId_ = userId_;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.ConquerProto.ConquerListRequest) {
          return mergeFrom((com.dxx.game.dto.ConquerProto.ConquerListRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.ConquerProto.ConquerListRequest other) {
        if (other == com.dxx.game.dto.ConquerProto.ConquerListRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        if (other.getUserId() != 0L) {
          setUserId(other.getUserId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getCommonParamsFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 16: {
                userId_ = input.readInt64();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
        } else {
          commonParamsBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
            commonParams_ != null &&
            commonParams_ != com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance()) {
            getCommonParamsBuilder().mergeFrom(value);
          } else {
            commonParams_ = value;
          }
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }
        if (commonParams_ != null) {
          bitField0_ |= 0x00000001;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        bitField0_ = (bitField0_ & ~0x00000001);
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }

      private long userId_ ;
      /**
       * <code>int64 userId = 2;</code>
       * @return The userId.
       */
      @java.lang.Override
      public long getUserId() {
        return userId_;
      }
      /**
       * <code>int64 userId = 2;</code>
       * @param value The userId to set.
       * @return This builder for chaining.
       */
      public Builder setUserId(long value) {

        userId_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>int64 userId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearUserId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        userId_ = 0L;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Conquer.ConquerListRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.Conquer.ConquerListRequest)
    private static final com.dxx.game.dto.ConquerProto.ConquerListRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.ConquerProto.ConquerListRequest();
    }

    public static com.dxx.game.dto.ConquerProto.ConquerListRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ConquerListRequest>
        PARSER = new com.google.protobuf.AbstractParser<ConquerListRequest>() {
      @java.lang.Override
      public ConquerListRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<ConquerListRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ConquerListRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.ConquerProto.ConquerListRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ConquerListResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Conquer.ConquerListResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    boolean hasCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    com.dxx.game.dto.CommonProto.CommonData getCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder();

    /**
     * <code>int64 userId = 3;</code>
     * @return The userId.
     */
    long getUserId();

    /**
     * <pre>
     * 个人数据
     * </pre>
     *
     * <code>.Proto.Conquer.ConquerUserDto owner = 4;</code>
     * @return Whether the owner field is set.
     */
    boolean hasOwner();
    /**
     * <pre>
     * 个人数据
     * </pre>
     *
     * <code>.Proto.Conquer.ConquerUserDto owner = 4;</code>
     * @return The owner.
     */
    com.dxx.game.dto.ConquerProto.ConquerUserDto getOwner();
    /**
     * <pre>
     * 个人数据
     * </pre>
     *
     * <code>.Proto.Conquer.ConquerUserDto owner = 4;</code>
     */
    com.dxx.game.dto.ConquerProto.ConquerUserDtoOrBuilder getOwnerOrBuilder();

    /**
     * <pre>
     * 领主数据
     * </pre>
     *
     * <code>.Proto.Conquer.ConquerUserDto lord = 5;</code>
     * @return Whether the lord field is set.
     */
    boolean hasLord();
    /**
     * <pre>
     * 领主数据
     * </pre>
     *
     * <code>.Proto.Conquer.ConquerUserDto lord = 5;</code>
     * @return The lord.
     */
    com.dxx.game.dto.ConquerProto.ConquerUserDto getLord();
    /**
     * <pre>
     * 领主数据
     * </pre>
     *
     * <code>.Proto.Conquer.ConquerUserDto lord = 5;</code>
     */
    com.dxx.game.dto.ConquerProto.ConquerUserDtoOrBuilder getLordOrBuilder();

    /**
     * <pre>
     * 奴隶列表
     * </pre>
     *
     * <code>repeated .Proto.Conquer.ConquerUserDto slaves = 6;</code>
     */
    java.util.List<com.dxx.game.dto.ConquerProto.ConquerUserDto> 
        getSlavesList();
    /**
     * <pre>
     * 奴隶列表
     * </pre>
     *
     * <code>repeated .Proto.Conquer.ConquerUserDto slaves = 6;</code>
     */
    com.dxx.game.dto.ConquerProto.ConquerUserDto getSlaves(int index);
    /**
     * <pre>
     * 奴隶列表
     * </pre>
     *
     * <code>repeated .Proto.Conquer.ConquerUserDto slaves = 6;</code>
     */
    int getSlavesCount();
    /**
     * <pre>
     * 奴隶列表
     * </pre>
     *
     * <code>repeated .Proto.Conquer.ConquerUserDto slaves = 6;</code>
     */
    java.util.List<? extends com.dxx.game.dto.ConquerProto.ConquerUserDtoOrBuilder> 
        getSlavesOrBuilderList();
    /**
     * <pre>
     * 奴隶列表
     * </pre>
     *
     * <code>repeated .Proto.Conquer.ConquerUserDto slaves = 6;</code>
     */
    com.dxx.game.dto.ConquerProto.ConquerUserDtoOrBuilder getSlavesOrBuilder(
        int index);
  }
  /**
   * <pre>
   * CMD PackageId=12802
   * </pre>
   *
   * Protobuf type {@code Proto.Conquer.ConquerListResponse}
   */
  public static final class ConquerListResponse extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Conquer.ConquerListResponse)
      ConquerListResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        ConquerListResponse.class.getName());
    }
    // Use ConquerListResponse.newBuilder() to construct.
    private ConquerListResponse(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private ConquerListResponse() {
      slaves_ = java.util.Collections.emptyList();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.ConquerProto.internal_static_Proto_Conquer_ConquerListResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.ConquerProto.internal_static_Proto_Conquer_ConquerListResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.ConquerProto.ConquerListResponse.class, com.dxx.game.dto.ConquerProto.ConquerListResponse.Builder.class);
    }

    private int bitField0_;
    public static final int CODE_FIELD_NUMBER = 1;
    private int code_ = 0;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int COMMONDATA_FIELD_NUMBER = 2;
    private com.dxx.game.dto.CommonProto.CommonData commonData_;
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    @java.lang.Override
    public boolean hasCommonData() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }

    public static final int USERID_FIELD_NUMBER = 3;
    private long userId_ = 0L;
    /**
     * <code>int64 userId = 3;</code>
     * @return The userId.
     */
    @java.lang.Override
    public long getUserId() {
      return userId_;
    }

    public static final int OWNER_FIELD_NUMBER = 4;
    private com.dxx.game.dto.ConquerProto.ConquerUserDto owner_;
    /**
     * <pre>
     * 个人数据
     * </pre>
     *
     * <code>.Proto.Conquer.ConquerUserDto owner = 4;</code>
     * @return Whether the owner field is set.
     */
    @java.lang.Override
    public boolean hasOwner() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 个人数据
     * </pre>
     *
     * <code>.Proto.Conquer.ConquerUserDto owner = 4;</code>
     * @return The owner.
     */
    @java.lang.Override
    public com.dxx.game.dto.ConquerProto.ConquerUserDto getOwner() {
      return owner_ == null ? com.dxx.game.dto.ConquerProto.ConquerUserDto.getDefaultInstance() : owner_;
    }
    /**
     * <pre>
     * 个人数据
     * </pre>
     *
     * <code>.Proto.Conquer.ConquerUserDto owner = 4;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.ConquerProto.ConquerUserDtoOrBuilder getOwnerOrBuilder() {
      return owner_ == null ? com.dxx.game.dto.ConquerProto.ConquerUserDto.getDefaultInstance() : owner_;
    }

    public static final int LORD_FIELD_NUMBER = 5;
    private com.dxx.game.dto.ConquerProto.ConquerUserDto lord_;
    /**
     * <pre>
     * 领主数据
     * </pre>
     *
     * <code>.Proto.Conquer.ConquerUserDto lord = 5;</code>
     * @return Whether the lord field is set.
     */
    @java.lang.Override
    public boolean hasLord() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 领主数据
     * </pre>
     *
     * <code>.Proto.Conquer.ConquerUserDto lord = 5;</code>
     * @return The lord.
     */
    @java.lang.Override
    public com.dxx.game.dto.ConquerProto.ConquerUserDto getLord() {
      return lord_ == null ? com.dxx.game.dto.ConquerProto.ConquerUserDto.getDefaultInstance() : lord_;
    }
    /**
     * <pre>
     * 领主数据
     * </pre>
     *
     * <code>.Proto.Conquer.ConquerUserDto lord = 5;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.ConquerProto.ConquerUserDtoOrBuilder getLordOrBuilder() {
      return lord_ == null ? com.dxx.game.dto.ConquerProto.ConquerUserDto.getDefaultInstance() : lord_;
    }

    public static final int SLAVES_FIELD_NUMBER = 6;
    @SuppressWarnings("serial")
    private java.util.List<com.dxx.game.dto.ConquerProto.ConquerUserDto> slaves_;
    /**
     * <pre>
     * 奴隶列表
     * </pre>
     *
     * <code>repeated .Proto.Conquer.ConquerUserDto slaves = 6;</code>
     */
    @java.lang.Override
    public java.util.List<com.dxx.game.dto.ConquerProto.ConquerUserDto> getSlavesList() {
      return slaves_;
    }
    /**
     * <pre>
     * 奴隶列表
     * </pre>
     *
     * <code>repeated .Proto.Conquer.ConquerUserDto slaves = 6;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.dxx.game.dto.ConquerProto.ConquerUserDtoOrBuilder> 
        getSlavesOrBuilderList() {
      return slaves_;
    }
    /**
     * <pre>
     * 奴隶列表
     * </pre>
     *
     * <code>repeated .Proto.Conquer.ConquerUserDto slaves = 6;</code>
     */
    @java.lang.Override
    public int getSlavesCount() {
      return slaves_.size();
    }
    /**
     * <pre>
     * 奴隶列表
     * </pre>
     *
     * <code>repeated .Proto.Conquer.ConquerUserDto slaves = 6;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.ConquerProto.ConquerUserDto getSlaves(int index) {
      return slaves_.get(index);
    }
    /**
     * <pre>
     * 奴隶列表
     * </pre>
     *
     * <code>repeated .Proto.Conquer.ConquerUserDto slaves = 6;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.ConquerProto.ConquerUserDtoOrBuilder getSlavesOrBuilder(
        int index) {
      return slaves_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(2, getCommonData());
      }
      if (userId_ != 0L) {
        output.writeInt64(3, userId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(4, getOwner());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeMessage(5, getLord());
      }
      for (int i = 0; i < slaves_.size(); i++) {
        output.writeMessage(6, slaves_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getCommonData());
      }
      if (userId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, userId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, getOwner());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, getLord());
      }
      for (int i = 0; i < slaves_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(6, slaves_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.ConquerProto.ConquerListResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.ConquerProto.ConquerListResponse other = (com.dxx.game.dto.ConquerProto.ConquerListResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (hasCommonData() != other.hasCommonData()) return false;
      if (hasCommonData()) {
        if (!getCommonData()
            .equals(other.getCommonData())) return false;
      }
      if (getUserId()
          != other.getUserId()) return false;
      if (hasOwner() != other.hasOwner()) return false;
      if (hasOwner()) {
        if (!getOwner()
            .equals(other.getOwner())) return false;
      }
      if (hasLord() != other.hasLord()) return false;
      if (hasLord()) {
        if (!getLord()
            .equals(other.getLord())) return false;
      }
      if (!getSlavesList()
          .equals(other.getSlavesList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      if (hasCommonData()) {
        hash = (37 * hash) + COMMONDATA_FIELD_NUMBER;
        hash = (53 * hash) + getCommonData().hashCode();
      }
      hash = (37 * hash) + USERID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getUserId());
      if (hasOwner()) {
        hash = (37 * hash) + OWNER_FIELD_NUMBER;
        hash = (53 * hash) + getOwner().hashCode();
      }
      if (hasLord()) {
        hash = (37 * hash) + LORD_FIELD_NUMBER;
        hash = (53 * hash) + getLord().hashCode();
      }
      if (getSlavesCount() > 0) {
        hash = (37 * hash) + SLAVES_FIELD_NUMBER;
        hash = (53 * hash) + getSlavesList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.ConquerProto.ConquerListResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerListResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerListResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerListResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerListResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerListResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerListResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerListResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.ConquerProto.ConquerListResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.ConquerProto.ConquerListResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerListResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerListResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.ConquerProto.ConquerListResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=12802
     * </pre>
     *
     * Protobuf type {@code Proto.Conquer.ConquerListResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Conquer.ConquerListResponse)
        com.dxx.game.dto.ConquerProto.ConquerListResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.ConquerProto.internal_static_Proto_Conquer_ConquerListResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.ConquerProto.internal_static_Proto_Conquer_ConquerListResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.ConquerProto.ConquerListResponse.class, com.dxx.game.dto.ConquerProto.ConquerListResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.ConquerProto.ConquerListResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getCommonDataFieldBuilder();
          getOwnerFieldBuilder();
          getLordFieldBuilder();
          getSlavesFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        code_ = 0;
        commonData_ = null;
        if (commonDataBuilder_ != null) {
          commonDataBuilder_.dispose();
          commonDataBuilder_ = null;
        }
        userId_ = 0L;
        owner_ = null;
        if (ownerBuilder_ != null) {
          ownerBuilder_.dispose();
          ownerBuilder_ = null;
        }
        lord_ = null;
        if (lordBuilder_ != null) {
          lordBuilder_.dispose();
          lordBuilder_ = null;
        }
        if (slavesBuilder_ == null) {
          slaves_ = java.util.Collections.emptyList();
        } else {
          slaves_ = null;
          slavesBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000020);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.ConquerProto.internal_static_Proto_Conquer_ConquerListResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.ConquerProto.ConquerListResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.ConquerProto.ConquerListResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.ConquerProto.ConquerListResponse build() {
        com.dxx.game.dto.ConquerProto.ConquerListResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.ConquerProto.ConquerListResponse buildPartial() {
        com.dxx.game.dto.ConquerProto.ConquerListResponse result = new com.dxx.game.dto.ConquerProto.ConquerListResponse(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(com.dxx.game.dto.ConquerProto.ConquerListResponse result) {
        if (slavesBuilder_ == null) {
          if (((bitField0_ & 0x00000020) != 0)) {
            slaves_ = java.util.Collections.unmodifiableList(slaves_);
            bitField0_ = (bitField0_ & ~0x00000020);
          }
          result.slaves_ = slaves_;
        } else {
          result.slaves_ = slavesBuilder_.build();
        }
      }

      private void buildPartial0(com.dxx.game.dto.ConquerProto.ConquerListResponse result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.code_ = code_;
        }
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.commonData_ = commonDataBuilder_ == null
              ? commonData_
              : commonDataBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.userId_ = userId_;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.owner_ = ownerBuilder_ == null
              ? owner_
              : ownerBuilder_.build();
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.lord_ = lordBuilder_ == null
              ? lord_
              : lordBuilder_.build();
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.ConquerProto.ConquerListResponse) {
          return mergeFrom((com.dxx.game.dto.ConquerProto.ConquerListResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.ConquerProto.ConquerListResponse other) {
        if (other == com.dxx.game.dto.ConquerProto.ConquerListResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.hasCommonData()) {
          mergeCommonData(other.getCommonData());
        }
        if (other.getUserId() != 0L) {
          setUserId(other.getUserId());
        }
        if (other.hasOwner()) {
          mergeOwner(other.getOwner());
        }
        if (other.hasLord()) {
          mergeLord(other.getLord());
        }
        if (slavesBuilder_ == null) {
          if (!other.slaves_.isEmpty()) {
            if (slaves_.isEmpty()) {
              slaves_ = other.slaves_;
              bitField0_ = (bitField0_ & ~0x00000020);
            } else {
              ensureSlavesIsMutable();
              slaves_.addAll(other.slaves_);
            }
            onChanged();
          }
        } else {
          if (!other.slaves_.isEmpty()) {
            if (slavesBuilder_.isEmpty()) {
              slavesBuilder_.dispose();
              slavesBuilder_ = null;
              slaves_ = other.slaves_;
              bitField0_ = (bitField0_ & ~0x00000020);
              slavesBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getSlavesFieldBuilder() : null;
            } else {
              slavesBuilder_.addAllMessages(other.slaves_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                code_ = input.readInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 18: {
                input.readMessage(
                    getCommonDataFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              case 24: {
                userId_ = input.readInt64();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              case 34: {
                input.readMessage(
                    getOwnerFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000008;
                break;
              } // case 34
              case 42: {
                input.readMessage(
                    getLordFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000010;
                break;
              } // case 42
              case 50: {
                com.dxx.game.dto.ConquerProto.ConquerUserDto m =
                    input.readMessage(
                        com.dxx.game.dto.ConquerProto.ConquerUserDto.parser(),
                        extensionRegistry);
                if (slavesBuilder_ == null) {
                  ensureSlavesIsMutable();
                  slaves_.add(m);
                } else {
                  slavesBuilder_.addMessage(m);
                }
                break;
              } // case 50
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {

        code_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        bitField0_ = (bitField0_ & ~0x00000001);
        code_ = 0;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonData commonData_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> commonDataBuilder_;
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return Whether the commonData field is set.
       */
      public boolean hasCommonData() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return The commonData.
       */
      public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
        if (commonDataBuilder_ == null) {
          return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        } else {
          return commonDataBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonData_ = value;
        } else {
          commonDataBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(
          com.dxx.game.dto.CommonProto.CommonData.Builder builderForValue) {
        if (commonDataBuilder_ == null) {
          commonData_ = builderForValue.build();
        } else {
          commonDataBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder mergeCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
            commonData_ != null &&
            commonData_ != com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance()) {
            getCommonDataBuilder().mergeFrom(value);
          } else {
            commonData_ = value;
          }
        } else {
          commonDataBuilder_.mergeFrom(value);
        }
        if (commonData_ != null) {
          bitField0_ |= 0x00000002;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder clearCommonData() {
        bitField0_ = (bitField0_ & ~0x00000002);
        commonData_ = null;
        if (commonDataBuilder_ != null) {
          commonDataBuilder_.dispose();
          commonDataBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonData.Builder getCommonDataBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getCommonDataFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
        if (commonDataBuilder_ != null) {
          return commonDataBuilder_.getMessageOrBuilder();
        } else {
          return commonData_ == null ?
              com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> 
          getCommonDataFieldBuilder() {
        if (commonDataBuilder_ == null) {
          commonDataBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder>(
                  getCommonData(),
                  getParentForChildren(),
                  isClean());
          commonData_ = null;
        }
        return commonDataBuilder_;
      }

      private long userId_ ;
      /**
       * <code>int64 userId = 3;</code>
       * @return The userId.
       */
      @java.lang.Override
      public long getUserId() {
        return userId_;
      }
      /**
       * <code>int64 userId = 3;</code>
       * @param value The userId to set.
       * @return This builder for chaining.
       */
      public Builder setUserId(long value) {

        userId_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>int64 userId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearUserId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        userId_ = 0L;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.ConquerProto.ConquerUserDto owner_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.ConquerProto.ConquerUserDto, com.dxx.game.dto.ConquerProto.ConquerUserDto.Builder, com.dxx.game.dto.ConquerProto.ConquerUserDtoOrBuilder> ownerBuilder_;
      /**
       * <pre>
       * 个人数据
       * </pre>
       *
       * <code>.Proto.Conquer.ConquerUserDto owner = 4;</code>
       * @return Whether the owner field is set.
       */
      public boolean hasOwner() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 个人数据
       * </pre>
       *
       * <code>.Proto.Conquer.ConquerUserDto owner = 4;</code>
       * @return The owner.
       */
      public com.dxx.game.dto.ConquerProto.ConquerUserDto getOwner() {
        if (ownerBuilder_ == null) {
          return owner_ == null ? com.dxx.game.dto.ConquerProto.ConquerUserDto.getDefaultInstance() : owner_;
        } else {
          return ownerBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 个人数据
       * </pre>
       *
       * <code>.Proto.Conquer.ConquerUserDto owner = 4;</code>
       */
      public Builder setOwner(com.dxx.game.dto.ConquerProto.ConquerUserDto value) {
        if (ownerBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          owner_ = value;
        } else {
          ownerBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 个人数据
       * </pre>
       *
       * <code>.Proto.Conquer.ConquerUserDto owner = 4;</code>
       */
      public Builder setOwner(
          com.dxx.game.dto.ConquerProto.ConquerUserDto.Builder builderForValue) {
        if (ownerBuilder_ == null) {
          owner_ = builderForValue.build();
        } else {
          ownerBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 个人数据
       * </pre>
       *
       * <code>.Proto.Conquer.ConquerUserDto owner = 4;</code>
       */
      public Builder mergeOwner(com.dxx.game.dto.ConquerProto.ConquerUserDto value) {
        if (ownerBuilder_ == null) {
          if (((bitField0_ & 0x00000008) != 0) &&
            owner_ != null &&
            owner_ != com.dxx.game.dto.ConquerProto.ConquerUserDto.getDefaultInstance()) {
            getOwnerBuilder().mergeFrom(value);
          } else {
            owner_ = value;
          }
        } else {
          ownerBuilder_.mergeFrom(value);
        }
        if (owner_ != null) {
          bitField0_ |= 0x00000008;
          onChanged();
        }
        return this;
      }
      /**
       * <pre>
       * 个人数据
       * </pre>
       *
       * <code>.Proto.Conquer.ConquerUserDto owner = 4;</code>
       */
      public Builder clearOwner() {
        bitField0_ = (bitField0_ & ~0x00000008);
        owner_ = null;
        if (ownerBuilder_ != null) {
          ownerBuilder_.dispose();
          ownerBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 个人数据
       * </pre>
       *
       * <code>.Proto.Conquer.ConquerUserDto owner = 4;</code>
       */
      public com.dxx.game.dto.ConquerProto.ConquerUserDto.Builder getOwnerBuilder() {
        bitField0_ |= 0x00000008;
        onChanged();
        return getOwnerFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 个人数据
       * </pre>
       *
       * <code>.Proto.Conquer.ConquerUserDto owner = 4;</code>
       */
      public com.dxx.game.dto.ConquerProto.ConquerUserDtoOrBuilder getOwnerOrBuilder() {
        if (ownerBuilder_ != null) {
          return ownerBuilder_.getMessageOrBuilder();
        } else {
          return owner_ == null ?
              com.dxx.game.dto.ConquerProto.ConquerUserDto.getDefaultInstance() : owner_;
        }
      }
      /**
       * <pre>
       * 个人数据
       * </pre>
       *
       * <code>.Proto.Conquer.ConquerUserDto owner = 4;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.ConquerProto.ConquerUserDto, com.dxx.game.dto.ConquerProto.ConquerUserDto.Builder, com.dxx.game.dto.ConquerProto.ConquerUserDtoOrBuilder> 
          getOwnerFieldBuilder() {
        if (ownerBuilder_ == null) {
          ownerBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.ConquerProto.ConquerUserDto, com.dxx.game.dto.ConquerProto.ConquerUserDto.Builder, com.dxx.game.dto.ConquerProto.ConquerUserDtoOrBuilder>(
                  getOwner(),
                  getParentForChildren(),
                  isClean());
          owner_ = null;
        }
        return ownerBuilder_;
      }

      private com.dxx.game.dto.ConquerProto.ConquerUserDto lord_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.ConquerProto.ConquerUserDto, com.dxx.game.dto.ConquerProto.ConquerUserDto.Builder, com.dxx.game.dto.ConquerProto.ConquerUserDtoOrBuilder> lordBuilder_;
      /**
       * <pre>
       * 领主数据
       * </pre>
       *
       * <code>.Proto.Conquer.ConquerUserDto lord = 5;</code>
       * @return Whether the lord field is set.
       */
      public boolean hasLord() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <pre>
       * 领主数据
       * </pre>
       *
       * <code>.Proto.Conquer.ConquerUserDto lord = 5;</code>
       * @return The lord.
       */
      public com.dxx.game.dto.ConquerProto.ConquerUserDto getLord() {
        if (lordBuilder_ == null) {
          return lord_ == null ? com.dxx.game.dto.ConquerProto.ConquerUserDto.getDefaultInstance() : lord_;
        } else {
          return lordBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 领主数据
       * </pre>
       *
       * <code>.Proto.Conquer.ConquerUserDto lord = 5;</code>
       */
      public Builder setLord(com.dxx.game.dto.ConquerProto.ConquerUserDto value) {
        if (lordBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          lord_ = value;
        } else {
          lordBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 领主数据
       * </pre>
       *
       * <code>.Proto.Conquer.ConquerUserDto lord = 5;</code>
       */
      public Builder setLord(
          com.dxx.game.dto.ConquerProto.ConquerUserDto.Builder builderForValue) {
        if (lordBuilder_ == null) {
          lord_ = builderForValue.build();
        } else {
          lordBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 领主数据
       * </pre>
       *
       * <code>.Proto.Conquer.ConquerUserDto lord = 5;</code>
       */
      public Builder mergeLord(com.dxx.game.dto.ConquerProto.ConquerUserDto value) {
        if (lordBuilder_ == null) {
          if (((bitField0_ & 0x00000010) != 0) &&
            lord_ != null &&
            lord_ != com.dxx.game.dto.ConquerProto.ConquerUserDto.getDefaultInstance()) {
            getLordBuilder().mergeFrom(value);
          } else {
            lord_ = value;
          }
        } else {
          lordBuilder_.mergeFrom(value);
        }
        if (lord_ != null) {
          bitField0_ |= 0x00000010;
          onChanged();
        }
        return this;
      }
      /**
       * <pre>
       * 领主数据
       * </pre>
       *
       * <code>.Proto.Conquer.ConquerUserDto lord = 5;</code>
       */
      public Builder clearLord() {
        bitField0_ = (bitField0_ & ~0x00000010);
        lord_ = null;
        if (lordBuilder_ != null) {
          lordBuilder_.dispose();
          lordBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 领主数据
       * </pre>
       *
       * <code>.Proto.Conquer.ConquerUserDto lord = 5;</code>
       */
      public com.dxx.game.dto.ConquerProto.ConquerUserDto.Builder getLordBuilder() {
        bitField0_ |= 0x00000010;
        onChanged();
        return getLordFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 领主数据
       * </pre>
       *
       * <code>.Proto.Conquer.ConquerUserDto lord = 5;</code>
       */
      public com.dxx.game.dto.ConquerProto.ConquerUserDtoOrBuilder getLordOrBuilder() {
        if (lordBuilder_ != null) {
          return lordBuilder_.getMessageOrBuilder();
        } else {
          return lord_ == null ?
              com.dxx.game.dto.ConquerProto.ConquerUserDto.getDefaultInstance() : lord_;
        }
      }
      /**
       * <pre>
       * 领主数据
       * </pre>
       *
       * <code>.Proto.Conquer.ConquerUserDto lord = 5;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.ConquerProto.ConquerUserDto, com.dxx.game.dto.ConquerProto.ConquerUserDto.Builder, com.dxx.game.dto.ConquerProto.ConquerUserDtoOrBuilder> 
          getLordFieldBuilder() {
        if (lordBuilder_ == null) {
          lordBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.ConquerProto.ConquerUserDto, com.dxx.game.dto.ConquerProto.ConquerUserDto.Builder, com.dxx.game.dto.ConquerProto.ConquerUserDtoOrBuilder>(
                  getLord(),
                  getParentForChildren(),
                  isClean());
          lord_ = null;
        }
        return lordBuilder_;
      }

      private java.util.List<com.dxx.game.dto.ConquerProto.ConquerUserDto> slaves_ =
        java.util.Collections.emptyList();
      private void ensureSlavesIsMutable() {
        if (!((bitField0_ & 0x00000020) != 0)) {
          slaves_ = new java.util.ArrayList<com.dxx.game.dto.ConquerProto.ConquerUserDto>(slaves_);
          bitField0_ |= 0x00000020;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          com.dxx.game.dto.ConquerProto.ConquerUserDto, com.dxx.game.dto.ConquerProto.ConquerUserDto.Builder, com.dxx.game.dto.ConquerProto.ConquerUserDtoOrBuilder> slavesBuilder_;

      /**
       * <pre>
       * 奴隶列表
       * </pre>
       *
       * <code>repeated .Proto.Conquer.ConquerUserDto slaves = 6;</code>
       */
      public java.util.List<com.dxx.game.dto.ConquerProto.ConquerUserDto> getSlavesList() {
        if (slavesBuilder_ == null) {
          return java.util.Collections.unmodifiableList(slaves_);
        } else {
          return slavesBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 奴隶列表
       * </pre>
       *
       * <code>repeated .Proto.Conquer.ConquerUserDto slaves = 6;</code>
       */
      public int getSlavesCount() {
        if (slavesBuilder_ == null) {
          return slaves_.size();
        } else {
          return slavesBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 奴隶列表
       * </pre>
       *
       * <code>repeated .Proto.Conquer.ConquerUserDto slaves = 6;</code>
       */
      public com.dxx.game.dto.ConquerProto.ConquerUserDto getSlaves(int index) {
        if (slavesBuilder_ == null) {
          return slaves_.get(index);
        } else {
          return slavesBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 奴隶列表
       * </pre>
       *
       * <code>repeated .Proto.Conquer.ConquerUserDto slaves = 6;</code>
       */
      public Builder setSlaves(
          int index, com.dxx.game.dto.ConquerProto.ConquerUserDto value) {
        if (slavesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSlavesIsMutable();
          slaves_.set(index, value);
          onChanged();
        } else {
          slavesBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 奴隶列表
       * </pre>
       *
       * <code>repeated .Proto.Conquer.ConquerUserDto slaves = 6;</code>
       */
      public Builder setSlaves(
          int index, com.dxx.game.dto.ConquerProto.ConquerUserDto.Builder builderForValue) {
        if (slavesBuilder_ == null) {
          ensureSlavesIsMutable();
          slaves_.set(index, builderForValue.build());
          onChanged();
        } else {
          slavesBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 奴隶列表
       * </pre>
       *
       * <code>repeated .Proto.Conquer.ConquerUserDto slaves = 6;</code>
       */
      public Builder addSlaves(com.dxx.game.dto.ConquerProto.ConquerUserDto value) {
        if (slavesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSlavesIsMutable();
          slaves_.add(value);
          onChanged();
        } else {
          slavesBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 奴隶列表
       * </pre>
       *
       * <code>repeated .Proto.Conquer.ConquerUserDto slaves = 6;</code>
       */
      public Builder addSlaves(
          int index, com.dxx.game.dto.ConquerProto.ConquerUserDto value) {
        if (slavesBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureSlavesIsMutable();
          slaves_.add(index, value);
          onChanged();
        } else {
          slavesBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 奴隶列表
       * </pre>
       *
       * <code>repeated .Proto.Conquer.ConquerUserDto slaves = 6;</code>
       */
      public Builder addSlaves(
          com.dxx.game.dto.ConquerProto.ConquerUserDto.Builder builderForValue) {
        if (slavesBuilder_ == null) {
          ensureSlavesIsMutable();
          slaves_.add(builderForValue.build());
          onChanged();
        } else {
          slavesBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 奴隶列表
       * </pre>
       *
       * <code>repeated .Proto.Conquer.ConquerUserDto slaves = 6;</code>
       */
      public Builder addSlaves(
          int index, com.dxx.game.dto.ConquerProto.ConquerUserDto.Builder builderForValue) {
        if (slavesBuilder_ == null) {
          ensureSlavesIsMutable();
          slaves_.add(index, builderForValue.build());
          onChanged();
        } else {
          slavesBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 奴隶列表
       * </pre>
       *
       * <code>repeated .Proto.Conquer.ConquerUserDto slaves = 6;</code>
       */
      public Builder addAllSlaves(
          java.lang.Iterable<? extends com.dxx.game.dto.ConquerProto.ConquerUserDto> values) {
        if (slavesBuilder_ == null) {
          ensureSlavesIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, slaves_);
          onChanged();
        } else {
          slavesBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 奴隶列表
       * </pre>
       *
       * <code>repeated .Proto.Conquer.ConquerUserDto slaves = 6;</code>
       */
      public Builder clearSlaves() {
        if (slavesBuilder_ == null) {
          slaves_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000020);
          onChanged();
        } else {
          slavesBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 奴隶列表
       * </pre>
       *
       * <code>repeated .Proto.Conquer.ConquerUserDto slaves = 6;</code>
       */
      public Builder removeSlaves(int index) {
        if (slavesBuilder_ == null) {
          ensureSlavesIsMutable();
          slaves_.remove(index);
          onChanged();
        } else {
          slavesBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 奴隶列表
       * </pre>
       *
       * <code>repeated .Proto.Conquer.ConquerUserDto slaves = 6;</code>
       */
      public com.dxx.game.dto.ConquerProto.ConquerUserDto.Builder getSlavesBuilder(
          int index) {
        return getSlavesFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 奴隶列表
       * </pre>
       *
       * <code>repeated .Proto.Conquer.ConquerUserDto slaves = 6;</code>
       */
      public com.dxx.game.dto.ConquerProto.ConquerUserDtoOrBuilder getSlavesOrBuilder(
          int index) {
        if (slavesBuilder_ == null) {
          return slaves_.get(index);  } else {
          return slavesBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 奴隶列表
       * </pre>
       *
       * <code>repeated .Proto.Conquer.ConquerUserDto slaves = 6;</code>
       */
      public java.util.List<? extends com.dxx.game.dto.ConquerProto.ConquerUserDtoOrBuilder> 
           getSlavesOrBuilderList() {
        if (slavesBuilder_ != null) {
          return slavesBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(slaves_);
        }
      }
      /**
       * <pre>
       * 奴隶列表
       * </pre>
       *
       * <code>repeated .Proto.Conquer.ConquerUserDto slaves = 6;</code>
       */
      public com.dxx.game.dto.ConquerProto.ConquerUserDto.Builder addSlavesBuilder() {
        return getSlavesFieldBuilder().addBuilder(
            com.dxx.game.dto.ConquerProto.ConquerUserDto.getDefaultInstance());
      }
      /**
       * <pre>
       * 奴隶列表
       * </pre>
       *
       * <code>repeated .Proto.Conquer.ConquerUserDto slaves = 6;</code>
       */
      public com.dxx.game.dto.ConquerProto.ConquerUserDto.Builder addSlavesBuilder(
          int index) {
        return getSlavesFieldBuilder().addBuilder(
            index, com.dxx.game.dto.ConquerProto.ConquerUserDto.getDefaultInstance());
      }
      /**
       * <pre>
       * 奴隶列表
       * </pre>
       *
       * <code>repeated .Proto.Conquer.ConquerUserDto slaves = 6;</code>
       */
      public java.util.List<com.dxx.game.dto.ConquerProto.ConquerUserDto.Builder> 
           getSlavesBuilderList() {
        return getSlavesFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          com.dxx.game.dto.ConquerProto.ConquerUserDto, com.dxx.game.dto.ConquerProto.ConquerUserDto.Builder, com.dxx.game.dto.ConquerProto.ConquerUserDtoOrBuilder> 
          getSlavesFieldBuilder() {
        if (slavesBuilder_ == null) {
          slavesBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              com.dxx.game.dto.ConquerProto.ConquerUserDto, com.dxx.game.dto.ConquerProto.ConquerUserDto.Builder, com.dxx.game.dto.ConquerProto.ConquerUserDtoOrBuilder>(
                  slaves_,
                  ((bitField0_ & 0x00000020) != 0),
                  getParentForChildren(),
                  isClean());
          slaves_ = null;
        }
        return slavesBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Conquer.ConquerListResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.Conquer.ConquerListResponse)
    private static final com.dxx.game.dto.ConquerProto.ConquerListResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.ConquerProto.ConquerListResponse();
    }

    public static com.dxx.game.dto.ConquerProto.ConquerListResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ConquerListResponse>
        PARSER = new com.google.protobuf.AbstractParser<ConquerListResponse>() {
      @java.lang.Override
      public ConquerListResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<ConquerListResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ConquerListResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.ConquerProto.ConquerListResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ConquerBattleRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Conquer.ConquerBattleRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();

    /**
     * <code>int64 userId = 2;</code>
     * @return The userId.
     */
    long getUserId();

    /**
     * <code>string clientVersion = 3;</code>
     * @return The clientVersion.
     */
    java.lang.String getClientVersion();
    /**
     * <code>string clientVersion = 3;</code>
     * @return The bytes for clientVersion.
     */
    com.google.protobuf.ByteString
        getClientVersionBytes();
  }
  /**
   * <pre>
   * CMD PackageId=12803 征服战-征服
   * </pre>
   *
   * Protobuf type {@code Proto.Conquer.ConquerBattleRequest}
   */
  public static final class ConquerBattleRequest extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Conquer.ConquerBattleRequest)
      ConquerBattleRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        ConquerBattleRequest.class.getName());
    }
    // Use ConquerBattleRequest.newBuilder() to construct.
    private ConquerBattleRequest(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private ConquerBattleRequest() {
      clientVersion_ = "";
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.ConquerProto.internal_static_Proto_Conquer_ConquerBattleRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.ConquerProto.internal_static_Proto_Conquer_ConquerBattleRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.ConquerProto.ConquerBattleRequest.class, com.dxx.game.dto.ConquerProto.ConquerBattleRequest.Builder.class);
    }

    private int bitField0_;
    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }

    public static final int USERID_FIELD_NUMBER = 2;
    private long userId_ = 0L;
    /**
     * <code>int64 userId = 2;</code>
     * @return The userId.
     */
    @java.lang.Override
    public long getUserId() {
      return userId_;
    }

    public static final int CLIENTVERSION_FIELD_NUMBER = 3;
    @SuppressWarnings("serial")
    private volatile java.lang.Object clientVersion_ = "";
    /**
     * <code>string clientVersion = 3;</code>
     * @return The clientVersion.
     */
    @java.lang.Override
    public java.lang.String getClientVersion() {
      java.lang.Object ref = clientVersion_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        clientVersion_ = s;
        return s;
      }
    }
    /**
     * <code>string clientVersion = 3;</code>
     * @return The bytes for clientVersion.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getClientVersionBytes() {
      java.lang.Object ref = clientVersion_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        clientVersion_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getCommonParams());
      }
      if (userId_ != 0L) {
        output.writeInt64(2, userId_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(clientVersion_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 3, clientVersion_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      if (userId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, userId_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(clientVersion_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(3, clientVersion_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.ConquerProto.ConquerBattleRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.ConquerProto.ConquerBattleRequest other = (com.dxx.game.dto.ConquerProto.ConquerBattleRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (getUserId()
          != other.getUserId()) return false;
      if (!getClientVersion()
          .equals(other.getClientVersion())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (37 * hash) + USERID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getUserId());
      hash = (37 * hash) + CLIENTVERSION_FIELD_NUMBER;
      hash = (53 * hash) + getClientVersion().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.ConquerProto.ConquerBattleRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerBattleRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerBattleRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerBattleRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerBattleRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerBattleRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerBattleRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerBattleRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.ConquerProto.ConquerBattleRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.ConquerProto.ConquerBattleRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerBattleRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerBattleRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.ConquerProto.ConquerBattleRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=12803 征服战-征服
     * </pre>
     *
     * Protobuf type {@code Proto.Conquer.ConquerBattleRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Conquer.ConquerBattleRequest)
        com.dxx.game.dto.ConquerProto.ConquerBattleRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.ConquerProto.internal_static_Proto_Conquer_ConquerBattleRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.ConquerProto.internal_static_Proto_Conquer_ConquerBattleRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.ConquerProto.ConquerBattleRequest.class, com.dxx.game.dto.ConquerProto.ConquerBattleRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.ConquerProto.ConquerBattleRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getCommonParamsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        userId_ = 0L;
        clientVersion_ = "";
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.ConquerProto.internal_static_Proto_Conquer_ConquerBattleRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.ConquerProto.ConquerBattleRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.ConquerProto.ConquerBattleRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.ConquerProto.ConquerBattleRequest build() {
        com.dxx.game.dto.ConquerProto.ConquerBattleRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.ConquerProto.ConquerBattleRequest buildPartial() {
        com.dxx.game.dto.ConquerProto.ConquerBattleRequest result = new com.dxx.game.dto.ConquerProto.ConquerBattleRequest(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.ConquerProto.ConquerBattleRequest result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.commonParams_ = commonParamsBuilder_ == null
              ? commonParams_
              : commonParamsBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.userId_ = userId_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.clientVersion_ = clientVersion_;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.ConquerProto.ConquerBattleRequest) {
          return mergeFrom((com.dxx.game.dto.ConquerProto.ConquerBattleRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.ConquerProto.ConquerBattleRequest other) {
        if (other == com.dxx.game.dto.ConquerProto.ConquerBattleRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        if (other.getUserId() != 0L) {
          setUserId(other.getUserId());
        }
        if (!other.getClientVersion().isEmpty()) {
          clientVersion_ = other.clientVersion_;
          bitField0_ |= 0x00000004;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getCommonParamsFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 16: {
                userId_ = input.readInt64();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 26: {
                clientVersion_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000004;
                break;
              } // case 26
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
        } else {
          commonParamsBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
            commonParams_ != null &&
            commonParams_ != com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance()) {
            getCommonParamsBuilder().mergeFrom(value);
          } else {
            commonParams_ = value;
          }
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }
        if (commonParams_ != null) {
          bitField0_ |= 0x00000001;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        bitField0_ = (bitField0_ & ~0x00000001);
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }

      private long userId_ ;
      /**
       * <code>int64 userId = 2;</code>
       * @return The userId.
       */
      @java.lang.Override
      public long getUserId() {
        return userId_;
      }
      /**
       * <code>int64 userId = 2;</code>
       * @param value The userId to set.
       * @return This builder for chaining.
       */
      public Builder setUserId(long value) {

        userId_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>int64 userId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearUserId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        userId_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object clientVersion_ = "";
      /**
       * <code>string clientVersion = 3;</code>
       * @return The clientVersion.
       */
      public java.lang.String getClientVersion() {
        java.lang.Object ref = clientVersion_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          clientVersion_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string clientVersion = 3;</code>
       * @return The bytes for clientVersion.
       */
      public com.google.protobuf.ByteString
          getClientVersionBytes() {
        java.lang.Object ref = clientVersion_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          clientVersion_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string clientVersion = 3;</code>
       * @param value The clientVersion to set.
       * @return This builder for chaining.
       */
      public Builder setClientVersion(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        clientVersion_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>string clientVersion = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearClientVersion() {
        clientVersion_ = getDefaultInstance().getClientVersion();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }
      /**
       * <code>string clientVersion = 3;</code>
       * @param value The bytes for clientVersion to set.
       * @return This builder for chaining.
       */
      public Builder setClientVersionBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        clientVersion_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Conquer.ConquerBattleRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.Conquer.ConquerBattleRequest)
    private static final com.dxx.game.dto.ConquerProto.ConquerBattleRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.ConquerProto.ConquerBattleRequest();
    }

    public static com.dxx.game.dto.ConquerProto.ConquerBattleRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ConquerBattleRequest>
        PARSER = new com.google.protobuf.AbstractParser<ConquerBattleRequest>() {
      @java.lang.Override
      public ConquerBattleRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<ConquerBattleRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ConquerBattleRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.ConquerProto.ConquerBattleRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ConquerBattleResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Conquer.ConquerBattleResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    boolean hasCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    com.dxx.game.dto.CommonProto.CommonData getCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder();

    /**
     * <code>int64 userId = 3;</code>
     * @return The userId.
     */
    long getUserId();

    /**
     * <pre>
     * 领主信息
     * </pre>
     *
     * <code>.Proto.Common.LordDto lord = 4;</code>
     * @return Whether the lord field is set.
     */
    boolean hasLord();
    /**
     * <pre>
     * 领主信息
     * </pre>
     *
     * <code>.Proto.Common.LordDto lord = 4;</code>
     * @return The lord.
     */
    com.dxx.game.dto.CommonProto.LordDto getLord();
    /**
     * <pre>
     * 领主信息
     * </pre>
     *
     * <code>.Proto.Common.LordDto lord = 4;</code>
     */
    com.dxx.game.dto.CommonProto.LordDtoOrBuilder getLordOrBuilder();

    /**
     * <pre>
     * 奴隶个数
     * </pre>
     *
     * <code>uint32 slaveCount = 5;</code>
     * @return The slaveCount.
     */
    int getSlaveCount();

    /**
     * <code>.Proto.Common.PVPRecordDto record = 6;</code>
     * @return Whether the record field is set.
     */
    boolean hasRecord();
    /**
     * <code>.Proto.Common.PVPRecordDto record = 6;</code>
     * @return The record.
     */
    com.dxx.game.dto.CommonProto.PVPRecordDto getRecord();
    /**
     * <code>.Proto.Common.PVPRecordDto record = 6;</code>
     */
    com.dxx.game.dto.CommonProto.PVPRecordDtoOrBuilder getRecordOrBuilder();
  }
  /**
   * <pre>
   * CMD PackageId=12804
   * </pre>
   *
   * Protobuf type {@code Proto.Conquer.ConquerBattleResponse}
   */
  public static final class ConquerBattleResponse extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Conquer.ConquerBattleResponse)
      ConquerBattleResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        ConquerBattleResponse.class.getName());
    }
    // Use ConquerBattleResponse.newBuilder() to construct.
    private ConquerBattleResponse(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private ConquerBattleResponse() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.ConquerProto.internal_static_Proto_Conquer_ConquerBattleResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.ConquerProto.internal_static_Proto_Conquer_ConquerBattleResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.ConquerProto.ConquerBattleResponse.class, com.dxx.game.dto.ConquerProto.ConquerBattleResponse.Builder.class);
    }

    private int bitField0_;
    public static final int CODE_FIELD_NUMBER = 1;
    private int code_ = 0;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int COMMONDATA_FIELD_NUMBER = 2;
    private com.dxx.game.dto.CommonProto.CommonData commonData_;
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    @java.lang.Override
    public boolean hasCommonData() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }

    public static final int USERID_FIELD_NUMBER = 3;
    private long userId_ = 0L;
    /**
     * <code>int64 userId = 3;</code>
     * @return The userId.
     */
    @java.lang.Override
    public long getUserId() {
      return userId_;
    }

    public static final int LORD_FIELD_NUMBER = 4;
    private com.dxx.game.dto.CommonProto.LordDto lord_;
    /**
     * <pre>
     * 领主信息
     * </pre>
     *
     * <code>.Proto.Common.LordDto lord = 4;</code>
     * @return Whether the lord field is set.
     */
    @java.lang.Override
    public boolean hasLord() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 领主信息
     * </pre>
     *
     * <code>.Proto.Common.LordDto lord = 4;</code>
     * @return The lord.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.LordDto getLord() {
      return lord_ == null ? com.dxx.game.dto.CommonProto.LordDto.getDefaultInstance() : lord_;
    }
    /**
     * <pre>
     * 领主信息
     * </pre>
     *
     * <code>.Proto.Common.LordDto lord = 4;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.LordDtoOrBuilder getLordOrBuilder() {
      return lord_ == null ? com.dxx.game.dto.CommonProto.LordDto.getDefaultInstance() : lord_;
    }

    public static final int SLAVECOUNT_FIELD_NUMBER = 5;
    private int slaveCount_ = 0;
    /**
     * <pre>
     * 奴隶个数
     * </pre>
     *
     * <code>uint32 slaveCount = 5;</code>
     * @return The slaveCount.
     */
    @java.lang.Override
    public int getSlaveCount() {
      return slaveCount_;
    }

    public static final int RECORD_FIELD_NUMBER = 6;
    private com.dxx.game.dto.CommonProto.PVPRecordDto record_;
    /**
     * <code>.Proto.Common.PVPRecordDto record = 6;</code>
     * @return Whether the record field is set.
     */
    @java.lang.Override
    public boolean hasRecord() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>.Proto.Common.PVPRecordDto record = 6;</code>
     * @return The record.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.PVPRecordDto getRecord() {
      return record_ == null ? com.dxx.game.dto.CommonProto.PVPRecordDto.getDefaultInstance() : record_;
    }
    /**
     * <code>.Proto.Common.PVPRecordDto record = 6;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.PVPRecordDtoOrBuilder getRecordOrBuilder() {
      return record_ == null ? com.dxx.game.dto.CommonProto.PVPRecordDto.getDefaultInstance() : record_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(2, getCommonData());
      }
      if (userId_ != 0L) {
        output.writeInt64(3, userId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(4, getLord());
      }
      if (slaveCount_ != 0) {
        output.writeUInt32(5, slaveCount_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeMessage(6, getRecord());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getCommonData());
      }
      if (userId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, userId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, getLord());
      }
      if (slaveCount_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(5, slaveCount_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(6, getRecord());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.ConquerProto.ConquerBattleResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.ConquerProto.ConquerBattleResponse other = (com.dxx.game.dto.ConquerProto.ConquerBattleResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (hasCommonData() != other.hasCommonData()) return false;
      if (hasCommonData()) {
        if (!getCommonData()
            .equals(other.getCommonData())) return false;
      }
      if (getUserId()
          != other.getUserId()) return false;
      if (hasLord() != other.hasLord()) return false;
      if (hasLord()) {
        if (!getLord()
            .equals(other.getLord())) return false;
      }
      if (getSlaveCount()
          != other.getSlaveCount()) return false;
      if (hasRecord() != other.hasRecord()) return false;
      if (hasRecord()) {
        if (!getRecord()
            .equals(other.getRecord())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      if (hasCommonData()) {
        hash = (37 * hash) + COMMONDATA_FIELD_NUMBER;
        hash = (53 * hash) + getCommonData().hashCode();
      }
      hash = (37 * hash) + USERID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getUserId());
      if (hasLord()) {
        hash = (37 * hash) + LORD_FIELD_NUMBER;
        hash = (53 * hash) + getLord().hashCode();
      }
      hash = (37 * hash) + SLAVECOUNT_FIELD_NUMBER;
      hash = (53 * hash) + getSlaveCount();
      if (hasRecord()) {
        hash = (37 * hash) + RECORD_FIELD_NUMBER;
        hash = (53 * hash) + getRecord().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.ConquerProto.ConquerBattleResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerBattleResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerBattleResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerBattleResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerBattleResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerBattleResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerBattleResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerBattleResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.ConquerProto.ConquerBattleResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.ConquerProto.ConquerBattleResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerBattleResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerBattleResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.ConquerProto.ConquerBattleResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=12804
     * </pre>
     *
     * Protobuf type {@code Proto.Conquer.ConquerBattleResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Conquer.ConquerBattleResponse)
        com.dxx.game.dto.ConquerProto.ConquerBattleResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.ConquerProto.internal_static_Proto_Conquer_ConquerBattleResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.ConquerProto.internal_static_Proto_Conquer_ConquerBattleResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.ConquerProto.ConquerBattleResponse.class, com.dxx.game.dto.ConquerProto.ConquerBattleResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.ConquerProto.ConquerBattleResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getCommonDataFieldBuilder();
          getLordFieldBuilder();
          getRecordFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        code_ = 0;
        commonData_ = null;
        if (commonDataBuilder_ != null) {
          commonDataBuilder_.dispose();
          commonDataBuilder_ = null;
        }
        userId_ = 0L;
        lord_ = null;
        if (lordBuilder_ != null) {
          lordBuilder_.dispose();
          lordBuilder_ = null;
        }
        slaveCount_ = 0;
        record_ = null;
        if (recordBuilder_ != null) {
          recordBuilder_.dispose();
          recordBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.ConquerProto.internal_static_Proto_Conquer_ConquerBattleResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.ConquerProto.ConquerBattleResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.ConquerProto.ConquerBattleResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.ConquerProto.ConquerBattleResponse build() {
        com.dxx.game.dto.ConquerProto.ConquerBattleResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.ConquerProto.ConquerBattleResponse buildPartial() {
        com.dxx.game.dto.ConquerProto.ConquerBattleResponse result = new com.dxx.game.dto.ConquerProto.ConquerBattleResponse(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.ConquerProto.ConquerBattleResponse result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.code_ = code_;
        }
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.commonData_ = commonDataBuilder_ == null
              ? commonData_
              : commonDataBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.userId_ = userId_;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.lord_ = lordBuilder_ == null
              ? lord_
              : lordBuilder_.build();
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.slaveCount_ = slaveCount_;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.record_ = recordBuilder_ == null
              ? record_
              : recordBuilder_.build();
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.ConquerProto.ConquerBattleResponse) {
          return mergeFrom((com.dxx.game.dto.ConquerProto.ConquerBattleResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.ConquerProto.ConquerBattleResponse other) {
        if (other == com.dxx.game.dto.ConquerProto.ConquerBattleResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.hasCommonData()) {
          mergeCommonData(other.getCommonData());
        }
        if (other.getUserId() != 0L) {
          setUserId(other.getUserId());
        }
        if (other.hasLord()) {
          mergeLord(other.getLord());
        }
        if (other.getSlaveCount() != 0) {
          setSlaveCount(other.getSlaveCount());
        }
        if (other.hasRecord()) {
          mergeRecord(other.getRecord());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                code_ = input.readInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 18: {
                input.readMessage(
                    getCommonDataFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              case 24: {
                userId_ = input.readInt64();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              case 34: {
                input.readMessage(
                    getLordFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000008;
                break;
              } // case 34
              case 40: {
                slaveCount_ = input.readUInt32();
                bitField0_ |= 0x00000010;
                break;
              } // case 40
              case 50: {
                input.readMessage(
                    getRecordFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000020;
                break;
              } // case 50
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {

        code_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        bitField0_ = (bitField0_ & ~0x00000001);
        code_ = 0;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonData commonData_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> commonDataBuilder_;
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return Whether the commonData field is set.
       */
      public boolean hasCommonData() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return The commonData.
       */
      public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
        if (commonDataBuilder_ == null) {
          return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        } else {
          return commonDataBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonData_ = value;
        } else {
          commonDataBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(
          com.dxx.game.dto.CommonProto.CommonData.Builder builderForValue) {
        if (commonDataBuilder_ == null) {
          commonData_ = builderForValue.build();
        } else {
          commonDataBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder mergeCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
            commonData_ != null &&
            commonData_ != com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance()) {
            getCommonDataBuilder().mergeFrom(value);
          } else {
            commonData_ = value;
          }
        } else {
          commonDataBuilder_.mergeFrom(value);
        }
        if (commonData_ != null) {
          bitField0_ |= 0x00000002;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder clearCommonData() {
        bitField0_ = (bitField0_ & ~0x00000002);
        commonData_ = null;
        if (commonDataBuilder_ != null) {
          commonDataBuilder_.dispose();
          commonDataBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonData.Builder getCommonDataBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getCommonDataFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
        if (commonDataBuilder_ != null) {
          return commonDataBuilder_.getMessageOrBuilder();
        } else {
          return commonData_ == null ?
              com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> 
          getCommonDataFieldBuilder() {
        if (commonDataBuilder_ == null) {
          commonDataBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder>(
                  getCommonData(),
                  getParentForChildren(),
                  isClean());
          commonData_ = null;
        }
        return commonDataBuilder_;
      }

      private long userId_ ;
      /**
       * <code>int64 userId = 3;</code>
       * @return The userId.
       */
      @java.lang.Override
      public long getUserId() {
        return userId_;
      }
      /**
       * <code>int64 userId = 3;</code>
       * @param value The userId to set.
       * @return This builder for chaining.
       */
      public Builder setUserId(long value) {

        userId_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>int64 userId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearUserId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        userId_ = 0L;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.CommonProto.LordDto lord_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.LordDto, com.dxx.game.dto.CommonProto.LordDto.Builder, com.dxx.game.dto.CommonProto.LordDtoOrBuilder> lordBuilder_;
      /**
       * <pre>
       * 领主信息
       * </pre>
       *
       * <code>.Proto.Common.LordDto lord = 4;</code>
       * @return Whether the lord field is set.
       */
      public boolean hasLord() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 领主信息
       * </pre>
       *
       * <code>.Proto.Common.LordDto lord = 4;</code>
       * @return The lord.
       */
      public com.dxx.game.dto.CommonProto.LordDto getLord() {
        if (lordBuilder_ == null) {
          return lord_ == null ? com.dxx.game.dto.CommonProto.LordDto.getDefaultInstance() : lord_;
        } else {
          return lordBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 领主信息
       * </pre>
       *
       * <code>.Proto.Common.LordDto lord = 4;</code>
       */
      public Builder setLord(com.dxx.game.dto.CommonProto.LordDto value) {
        if (lordBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          lord_ = value;
        } else {
          lordBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 领主信息
       * </pre>
       *
       * <code>.Proto.Common.LordDto lord = 4;</code>
       */
      public Builder setLord(
          com.dxx.game.dto.CommonProto.LordDto.Builder builderForValue) {
        if (lordBuilder_ == null) {
          lord_ = builderForValue.build();
        } else {
          lordBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 领主信息
       * </pre>
       *
       * <code>.Proto.Common.LordDto lord = 4;</code>
       */
      public Builder mergeLord(com.dxx.game.dto.CommonProto.LordDto value) {
        if (lordBuilder_ == null) {
          if (((bitField0_ & 0x00000008) != 0) &&
            lord_ != null &&
            lord_ != com.dxx.game.dto.CommonProto.LordDto.getDefaultInstance()) {
            getLordBuilder().mergeFrom(value);
          } else {
            lord_ = value;
          }
        } else {
          lordBuilder_.mergeFrom(value);
        }
        if (lord_ != null) {
          bitField0_ |= 0x00000008;
          onChanged();
        }
        return this;
      }
      /**
       * <pre>
       * 领主信息
       * </pre>
       *
       * <code>.Proto.Common.LordDto lord = 4;</code>
       */
      public Builder clearLord() {
        bitField0_ = (bitField0_ & ~0x00000008);
        lord_ = null;
        if (lordBuilder_ != null) {
          lordBuilder_.dispose();
          lordBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 领主信息
       * </pre>
       *
       * <code>.Proto.Common.LordDto lord = 4;</code>
       */
      public com.dxx.game.dto.CommonProto.LordDto.Builder getLordBuilder() {
        bitField0_ |= 0x00000008;
        onChanged();
        return getLordFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 领主信息
       * </pre>
       *
       * <code>.Proto.Common.LordDto lord = 4;</code>
       */
      public com.dxx.game.dto.CommonProto.LordDtoOrBuilder getLordOrBuilder() {
        if (lordBuilder_ != null) {
          return lordBuilder_.getMessageOrBuilder();
        } else {
          return lord_ == null ?
              com.dxx.game.dto.CommonProto.LordDto.getDefaultInstance() : lord_;
        }
      }
      /**
       * <pre>
       * 领主信息
       * </pre>
       *
       * <code>.Proto.Common.LordDto lord = 4;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.LordDto, com.dxx.game.dto.CommonProto.LordDto.Builder, com.dxx.game.dto.CommonProto.LordDtoOrBuilder> 
          getLordFieldBuilder() {
        if (lordBuilder_ == null) {
          lordBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.LordDto, com.dxx.game.dto.CommonProto.LordDto.Builder, com.dxx.game.dto.CommonProto.LordDtoOrBuilder>(
                  getLord(),
                  getParentForChildren(),
                  isClean());
          lord_ = null;
        }
        return lordBuilder_;
      }

      private int slaveCount_ ;
      /**
       * <pre>
       * 奴隶个数
       * </pre>
       *
       * <code>uint32 slaveCount = 5;</code>
       * @return The slaveCount.
       */
      @java.lang.Override
      public int getSlaveCount() {
        return slaveCount_;
      }
      /**
       * <pre>
       * 奴隶个数
       * </pre>
       *
       * <code>uint32 slaveCount = 5;</code>
       * @param value The slaveCount to set.
       * @return This builder for chaining.
       */
      public Builder setSlaveCount(int value) {

        slaveCount_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 奴隶个数
       * </pre>
       *
       * <code>uint32 slaveCount = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearSlaveCount() {
        bitField0_ = (bitField0_ & ~0x00000010);
        slaveCount_ = 0;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.CommonProto.PVPRecordDto record_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.PVPRecordDto, com.dxx.game.dto.CommonProto.PVPRecordDto.Builder, com.dxx.game.dto.CommonProto.PVPRecordDtoOrBuilder> recordBuilder_;
      /**
       * <code>.Proto.Common.PVPRecordDto record = 6;</code>
       * @return Whether the record field is set.
       */
      public boolean hasRecord() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <code>.Proto.Common.PVPRecordDto record = 6;</code>
       * @return The record.
       */
      public com.dxx.game.dto.CommonProto.PVPRecordDto getRecord() {
        if (recordBuilder_ == null) {
          return record_ == null ? com.dxx.game.dto.CommonProto.PVPRecordDto.getDefaultInstance() : record_;
        } else {
          return recordBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.PVPRecordDto record = 6;</code>
       */
      public Builder setRecord(com.dxx.game.dto.CommonProto.PVPRecordDto value) {
        if (recordBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          record_ = value;
        } else {
          recordBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.PVPRecordDto record = 6;</code>
       */
      public Builder setRecord(
          com.dxx.game.dto.CommonProto.PVPRecordDto.Builder builderForValue) {
        if (recordBuilder_ == null) {
          record_ = builderForValue.build();
        } else {
          recordBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.PVPRecordDto record = 6;</code>
       */
      public Builder mergeRecord(com.dxx.game.dto.CommonProto.PVPRecordDto value) {
        if (recordBuilder_ == null) {
          if (((bitField0_ & 0x00000020) != 0) &&
            record_ != null &&
            record_ != com.dxx.game.dto.CommonProto.PVPRecordDto.getDefaultInstance()) {
            getRecordBuilder().mergeFrom(value);
          } else {
            record_ = value;
          }
        } else {
          recordBuilder_.mergeFrom(value);
        }
        if (record_ != null) {
          bitField0_ |= 0x00000020;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Proto.Common.PVPRecordDto record = 6;</code>
       */
      public Builder clearRecord() {
        bitField0_ = (bitField0_ & ~0x00000020);
        record_ = null;
        if (recordBuilder_ != null) {
          recordBuilder_.dispose();
          recordBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.PVPRecordDto record = 6;</code>
       */
      public com.dxx.game.dto.CommonProto.PVPRecordDto.Builder getRecordBuilder() {
        bitField0_ |= 0x00000020;
        onChanged();
        return getRecordFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.PVPRecordDto record = 6;</code>
       */
      public com.dxx.game.dto.CommonProto.PVPRecordDtoOrBuilder getRecordOrBuilder() {
        if (recordBuilder_ != null) {
          return recordBuilder_.getMessageOrBuilder();
        } else {
          return record_ == null ?
              com.dxx.game.dto.CommonProto.PVPRecordDto.getDefaultInstance() : record_;
        }
      }
      /**
       * <code>.Proto.Common.PVPRecordDto record = 6;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.PVPRecordDto, com.dxx.game.dto.CommonProto.PVPRecordDto.Builder, com.dxx.game.dto.CommonProto.PVPRecordDtoOrBuilder> 
          getRecordFieldBuilder() {
        if (recordBuilder_ == null) {
          recordBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.PVPRecordDto, com.dxx.game.dto.CommonProto.PVPRecordDto.Builder, com.dxx.game.dto.CommonProto.PVPRecordDtoOrBuilder>(
                  getRecord(),
                  getParentForChildren(),
                  isClean());
          record_ = null;
        }
        return recordBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Conquer.ConquerBattleResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.Conquer.ConquerBattleResponse)
    private static final com.dxx.game.dto.ConquerProto.ConquerBattleResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.ConquerProto.ConquerBattleResponse();
    }

    public static com.dxx.game.dto.ConquerProto.ConquerBattleResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ConquerBattleResponse>
        PARSER = new com.google.protobuf.AbstractParser<ConquerBattleResponse>() {
      @java.lang.Override
      public ConquerBattleResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<ConquerBattleResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ConquerBattleResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.ConquerProto.ConquerBattleResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ConquerRevoltRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Conquer.ConquerRevoltRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();

    /**
     * <code>int64 userId = 2;</code>
     * @return The userId.
     */
    long getUserId();

    /**
     * <code>string clientVersion = 3;</code>
     * @return The clientVersion.
     */
    java.lang.String getClientVersion();
    /**
     * <code>string clientVersion = 3;</code>
     * @return The bytes for clientVersion.
     */
    com.google.protobuf.ByteString
        getClientVersionBytes();
  }
  /**
   * <pre>
   * CMD PackageId=12805 征服战-反叛
   * </pre>
   *
   * Protobuf type {@code Proto.Conquer.ConquerRevoltRequest}
   */
  public static final class ConquerRevoltRequest extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Conquer.ConquerRevoltRequest)
      ConquerRevoltRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        ConquerRevoltRequest.class.getName());
    }
    // Use ConquerRevoltRequest.newBuilder() to construct.
    private ConquerRevoltRequest(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private ConquerRevoltRequest() {
      clientVersion_ = "";
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.ConquerProto.internal_static_Proto_Conquer_ConquerRevoltRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.ConquerProto.internal_static_Proto_Conquer_ConquerRevoltRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.ConquerProto.ConquerRevoltRequest.class, com.dxx.game.dto.ConquerProto.ConquerRevoltRequest.Builder.class);
    }

    private int bitField0_;
    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }

    public static final int USERID_FIELD_NUMBER = 2;
    private long userId_ = 0L;
    /**
     * <code>int64 userId = 2;</code>
     * @return The userId.
     */
    @java.lang.Override
    public long getUserId() {
      return userId_;
    }

    public static final int CLIENTVERSION_FIELD_NUMBER = 3;
    @SuppressWarnings("serial")
    private volatile java.lang.Object clientVersion_ = "";
    /**
     * <code>string clientVersion = 3;</code>
     * @return The clientVersion.
     */
    @java.lang.Override
    public java.lang.String getClientVersion() {
      java.lang.Object ref = clientVersion_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        clientVersion_ = s;
        return s;
      }
    }
    /**
     * <code>string clientVersion = 3;</code>
     * @return The bytes for clientVersion.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getClientVersionBytes() {
      java.lang.Object ref = clientVersion_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        clientVersion_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getCommonParams());
      }
      if (userId_ != 0L) {
        output.writeInt64(2, userId_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(clientVersion_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 3, clientVersion_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      if (userId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, userId_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(clientVersion_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(3, clientVersion_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.ConquerProto.ConquerRevoltRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.ConquerProto.ConquerRevoltRequest other = (com.dxx.game.dto.ConquerProto.ConquerRevoltRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (getUserId()
          != other.getUserId()) return false;
      if (!getClientVersion()
          .equals(other.getClientVersion())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (37 * hash) + USERID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getUserId());
      hash = (37 * hash) + CLIENTVERSION_FIELD_NUMBER;
      hash = (53 * hash) + getClientVersion().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.ConquerProto.ConquerRevoltRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerRevoltRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerRevoltRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerRevoltRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerRevoltRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerRevoltRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerRevoltRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerRevoltRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.ConquerProto.ConquerRevoltRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.ConquerProto.ConquerRevoltRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerRevoltRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerRevoltRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.ConquerProto.ConquerRevoltRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=12805 征服战-反叛
     * </pre>
     *
     * Protobuf type {@code Proto.Conquer.ConquerRevoltRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Conquer.ConquerRevoltRequest)
        com.dxx.game.dto.ConquerProto.ConquerRevoltRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.ConquerProto.internal_static_Proto_Conquer_ConquerRevoltRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.ConquerProto.internal_static_Proto_Conquer_ConquerRevoltRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.ConquerProto.ConquerRevoltRequest.class, com.dxx.game.dto.ConquerProto.ConquerRevoltRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.ConquerProto.ConquerRevoltRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getCommonParamsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        userId_ = 0L;
        clientVersion_ = "";
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.ConquerProto.internal_static_Proto_Conquer_ConquerRevoltRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.ConquerProto.ConquerRevoltRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.ConquerProto.ConquerRevoltRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.ConquerProto.ConquerRevoltRequest build() {
        com.dxx.game.dto.ConquerProto.ConquerRevoltRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.ConquerProto.ConquerRevoltRequest buildPartial() {
        com.dxx.game.dto.ConquerProto.ConquerRevoltRequest result = new com.dxx.game.dto.ConquerProto.ConquerRevoltRequest(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.ConquerProto.ConquerRevoltRequest result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.commonParams_ = commonParamsBuilder_ == null
              ? commonParams_
              : commonParamsBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.userId_ = userId_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.clientVersion_ = clientVersion_;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.ConquerProto.ConquerRevoltRequest) {
          return mergeFrom((com.dxx.game.dto.ConquerProto.ConquerRevoltRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.ConquerProto.ConquerRevoltRequest other) {
        if (other == com.dxx.game.dto.ConquerProto.ConquerRevoltRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        if (other.getUserId() != 0L) {
          setUserId(other.getUserId());
        }
        if (!other.getClientVersion().isEmpty()) {
          clientVersion_ = other.clientVersion_;
          bitField0_ |= 0x00000004;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getCommonParamsFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 16: {
                userId_ = input.readInt64();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 26: {
                clientVersion_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000004;
                break;
              } // case 26
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
        } else {
          commonParamsBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
            commonParams_ != null &&
            commonParams_ != com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance()) {
            getCommonParamsBuilder().mergeFrom(value);
          } else {
            commonParams_ = value;
          }
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }
        if (commonParams_ != null) {
          bitField0_ |= 0x00000001;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        bitField0_ = (bitField0_ & ~0x00000001);
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }

      private long userId_ ;
      /**
       * <code>int64 userId = 2;</code>
       * @return The userId.
       */
      @java.lang.Override
      public long getUserId() {
        return userId_;
      }
      /**
       * <code>int64 userId = 2;</code>
       * @param value The userId to set.
       * @return This builder for chaining.
       */
      public Builder setUserId(long value) {

        userId_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>int64 userId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearUserId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        userId_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object clientVersion_ = "";
      /**
       * <code>string clientVersion = 3;</code>
       * @return The clientVersion.
       */
      public java.lang.String getClientVersion() {
        java.lang.Object ref = clientVersion_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          clientVersion_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string clientVersion = 3;</code>
       * @return The bytes for clientVersion.
       */
      public com.google.protobuf.ByteString
          getClientVersionBytes() {
        java.lang.Object ref = clientVersion_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          clientVersion_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string clientVersion = 3;</code>
       * @param value The clientVersion to set.
       * @return This builder for chaining.
       */
      public Builder setClientVersion(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        clientVersion_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>string clientVersion = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearClientVersion() {
        clientVersion_ = getDefaultInstance().getClientVersion();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }
      /**
       * <code>string clientVersion = 3;</code>
       * @param value The bytes for clientVersion to set.
       * @return This builder for chaining.
       */
      public Builder setClientVersionBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        clientVersion_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Conquer.ConquerRevoltRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.Conquer.ConquerRevoltRequest)
    private static final com.dxx.game.dto.ConquerProto.ConquerRevoltRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.ConquerProto.ConquerRevoltRequest();
    }

    public static com.dxx.game.dto.ConquerProto.ConquerRevoltRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ConquerRevoltRequest>
        PARSER = new com.google.protobuf.AbstractParser<ConquerRevoltRequest>() {
      @java.lang.Override
      public ConquerRevoltRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<ConquerRevoltRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ConquerRevoltRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.ConquerProto.ConquerRevoltRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ConquerRevoltResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Conquer.ConquerRevoltResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    boolean hasCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    com.dxx.game.dto.CommonProto.CommonData getCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder();

    /**
     * <code>int64 userId = 3;</code>
     * @return The userId.
     */
    long getUserId();

    /**
     * <pre>
     * 领主信息
     * </pre>
     *
     * <code>.Proto.Common.LordDto lord = 4;</code>
     * @return Whether the lord field is set.
     */
    boolean hasLord();
    /**
     * <pre>
     * 领主信息
     * </pre>
     *
     * <code>.Proto.Common.LordDto lord = 4;</code>
     * @return The lord.
     */
    com.dxx.game.dto.CommonProto.LordDto getLord();
    /**
     * <pre>
     * 领主信息
     * </pre>
     *
     * <code>.Proto.Common.LordDto lord = 4;</code>
     */
    com.dxx.game.dto.CommonProto.LordDtoOrBuilder getLordOrBuilder();

    /**
     * <pre>
     * 奴隶个数
     * </pre>
     *
     * <code>uint32 slaveCount = 5;</code>
     * @return The slaveCount.
     */
    int getSlaveCount();

    /**
     * <code>.Proto.Common.PVPRecordDto record = 6;</code>
     * @return Whether the record field is set.
     */
    boolean hasRecord();
    /**
     * <code>.Proto.Common.PVPRecordDto record = 6;</code>
     * @return The record.
     */
    com.dxx.game.dto.CommonProto.PVPRecordDto getRecord();
    /**
     * <code>.Proto.Common.PVPRecordDto record = 6;</code>
     */
    com.dxx.game.dto.CommonProto.PVPRecordDtoOrBuilder getRecordOrBuilder();
  }
  /**
   * <pre>
   * CMD PackageId=12806
   * </pre>
   *
   * Protobuf type {@code Proto.Conquer.ConquerRevoltResponse}
   */
  public static final class ConquerRevoltResponse extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Conquer.ConquerRevoltResponse)
      ConquerRevoltResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        ConquerRevoltResponse.class.getName());
    }
    // Use ConquerRevoltResponse.newBuilder() to construct.
    private ConquerRevoltResponse(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private ConquerRevoltResponse() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.ConquerProto.internal_static_Proto_Conquer_ConquerRevoltResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.ConquerProto.internal_static_Proto_Conquer_ConquerRevoltResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.ConquerProto.ConquerRevoltResponse.class, com.dxx.game.dto.ConquerProto.ConquerRevoltResponse.Builder.class);
    }

    private int bitField0_;
    public static final int CODE_FIELD_NUMBER = 1;
    private int code_ = 0;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int COMMONDATA_FIELD_NUMBER = 2;
    private com.dxx.game.dto.CommonProto.CommonData commonData_;
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    @java.lang.Override
    public boolean hasCommonData() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }

    public static final int USERID_FIELD_NUMBER = 3;
    private long userId_ = 0L;
    /**
     * <code>int64 userId = 3;</code>
     * @return The userId.
     */
    @java.lang.Override
    public long getUserId() {
      return userId_;
    }

    public static final int LORD_FIELD_NUMBER = 4;
    private com.dxx.game.dto.CommonProto.LordDto lord_;
    /**
     * <pre>
     * 领主信息
     * </pre>
     *
     * <code>.Proto.Common.LordDto lord = 4;</code>
     * @return Whether the lord field is set.
     */
    @java.lang.Override
    public boolean hasLord() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 领主信息
     * </pre>
     *
     * <code>.Proto.Common.LordDto lord = 4;</code>
     * @return The lord.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.LordDto getLord() {
      return lord_ == null ? com.dxx.game.dto.CommonProto.LordDto.getDefaultInstance() : lord_;
    }
    /**
     * <pre>
     * 领主信息
     * </pre>
     *
     * <code>.Proto.Common.LordDto lord = 4;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.LordDtoOrBuilder getLordOrBuilder() {
      return lord_ == null ? com.dxx.game.dto.CommonProto.LordDto.getDefaultInstance() : lord_;
    }

    public static final int SLAVECOUNT_FIELD_NUMBER = 5;
    private int slaveCount_ = 0;
    /**
     * <pre>
     * 奴隶个数
     * </pre>
     *
     * <code>uint32 slaveCount = 5;</code>
     * @return The slaveCount.
     */
    @java.lang.Override
    public int getSlaveCount() {
      return slaveCount_;
    }

    public static final int RECORD_FIELD_NUMBER = 6;
    private com.dxx.game.dto.CommonProto.PVPRecordDto record_;
    /**
     * <code>.Proto.Common.PVPRecordDto record = 6;</code>
     * @return Whether the record field is set.
     */
    @java.lang.Override
    public boolean hasRecord() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>.Proto.Common.PVPRecordDto record = 6;</code>
     * @return The record.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.PVPRecordDto getRecord() {
      return record_ == null ? com.dxx.game.dto.CommonProto.PVPRecordDto.getDefaultInstance() : record_;
    }
    /**
     * <code>.Proto.Common.PVPRecordDto record = 6;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.PVPRecordDtoOrBuilder getRecordOrBuilder() {
      return record_ == null ? com.dxx.game.dto.CommonProto.PVPRecordDto.getDefaultInstance() : record_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(2, getCommonData());
      }
      if (userId_ != 0L) {
        output.writeInt64(3, userId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(4, getLord());
      }
      if (slaveCount_ != 0) {
        output.writeUInt32(5, slaveCount_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeMessage(6, getRecord());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getCommonData());
      }
      if (userId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, userId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, getLord());
      }
      if (slaveCount_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(5, slaveCount_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(6, getRecord());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.ConquerProto.ConquerRevoltResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.ConquerProto.ConquerRevoltResponse other = (com.dxx.game.dto.ConquerProto.ConquerRevoltResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (hasCommonData() != other.hasCommonData()) return false;
      if (hasCommonData()) {
        if (!getCommonData()
            .equals(other.getCommonData())) return false;
      }
      if (getUserId()
          != other.getUserId()) return false;
      if (hasLord() != other.hasLord()) return false;
      if (hasLord()) {
        if (!getLord()
            .equals(other.getLord())) return false;
      }
      if (getSlaveCount()
          != other.getSlaveCount()) return false;
      if (hasRecord() != other.hasRecord()) return false;
      if (hasRecord()) {
        if (!getRecord()
            .equals(other.getRecord())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      if (hasCommonData()) {
        hash = (37 * hash) + COMMONDATA_FIELD_NUMBER;
        hash = (53 * hash) + getCommonData().hashCode();
      }
      hash = (37 * hash) + USERID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getUserId());
      if (hasLord()) {
        hash = (37 * hash) + LORD_FIELD_NUMBER;
        hash = (53 * hash) + getLord().hashCode();
      }
      hash = (37 * hash) + SLAVECOUNT_FIELD_NUMBER;
      hash = (53 * hash) + getSlaveCount();
      if (hasRecord()) {
        hash = (37 * hash) + RECORD_FIELD_NUMBER;
        hash = (53 * hash) + getRecord().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.ConquerProto.ConquerRevoltResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerRevoltResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerRevoltResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerRevoltResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerRevoltResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerRevoltResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerRevoltResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerRevoltResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.ConquerProto.ConquerRevoltResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.ConquerProto.ConquerRevoltResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerRevoltResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerRevoltResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.ConquerProto.ConquerRevoltResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=12806
     * </pre>
     *
     * Protobuf type {@code Proto.Conquer.ConquerRevoltResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Conquer.ConquerRevoltResponse)
        com.dxx.game.dto.ConquerProto.ConquerRevoltResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.ConquerProto.internal_static_Proto_Conquer_ConquerRevoltResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.ConquerProto.internal_static_Proto_Conquer_ConquerRevoltResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.ConquerProto.ConquerRevoltResponse.class, com.dxx.game.dto.ConquerProto.ConquerRevoltResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.ConquerProto.ConquerRevoltResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getCommonDataFieldBuilder();
          getLordFieldBuilder();
          getRecordFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        code_ = 0;
        commonData_ = null;
        if (commonDataBuilder_ != null) {
          commonDataBuilder_.dispose();
          commonDataBuilder_ = null;
        }
        userId_ = 0L;
        lord_ = null;
        if (lordBuilder_ != null) {
          lordBuilder_.dispose();
          lordBuilder_ = null;
        }
        slaveCount_ = 0;
        record_ = null;
        if (recordBuilder_ != null) {
          recordBuilder_.dispose();
          recordBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.ConquerProto.internal_static_Proto_Conquer_ConquerRevoltResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.ConquerProto.ConquerRevoltResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.ConquerProto.ConquerRevoltResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.ConquerProto.ConquerRevoltResponse build() {
        com.dxx.game.dto.ConquerProto.ConquerRevoltResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.ConquerProto.ConquerRevoltResponse buildPartial() {
        com.dxx.game.dto.ConquerProto.ConquerRevoltResponse result = new com.dxx.game.dto.ConquerProto.ConquerRevoltResponse(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.ConquerProto.ConquerRevoltResponse result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.code_ = code_;
        }
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.commonData_ = commonDataBuilder_ == null
              ? commonData_
              : commonDataBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.userId_ = userId_;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.lord_ = lordBuilder_ == null
              ? lord_
              : lordBuilder_.build();
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.slaveCount_ = slaveCount_;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.record_ = recordBuilder_ == null
              ? record_
              : recordBuilder_.build();
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.ConquerProto.ConquerRevoltResponse) {
          return mergeFrom((com.dxx.game.dto.ConquerProto.ConquerRevoltResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.ConquerProto.ConquerRevoltResponse other) {
        if (other == com.dxx.game.dto.ConquerProto.ConquerRevoltResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.hasCommonData()) {
          mergeCommonData(other.getCommonData());
        }
        if (other.getUserId() != 0L) {
          setUserId(other.getUserId());
        }
        if (other.hasLord()) {
          mergeLord(other.getLord());
        }
        if (other.getSlaveCount() != 0) {
          setSlaveCount(other.getSlaveCount());
        }
        if (other.hasRecord()) {
          mergeRecord(other.getRecord());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                code_ = input.readInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 18: {
                input.readMessage(
                    getCommonDataFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              case 24: {
                userId_ = input.readInt64();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              case 34: {
                input.readMessage(
                    getLordFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000008;
                break;
              } // case 34
              case 40: {
                slaveCount_ = input.readUInt32();
                bitField0_ |= 0x00000010;
                break;
              } // case 40
              case 50: {
                input.readMessage(
                    getRecordFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000020;
                break;
              } // case 50
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {

        code_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        bitField0_ = (bitField0_ & ~0x00000001);
        code_ = 0;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonData commonData_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> commonDataBuilder_;
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return Whether the commonData field is set.
       */
      public boolean hasCommonData() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return The commonData.
       */
      public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
        if (commonDataBuilder_ == null) {
          return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        } else {
          return commonDataBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonData_ = value;
        } else {
          commonDataBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(
          com.dxx.game.dto.CommonProto.CommonData.Builder builderForValue) {
        if (commonDataBuilder_ == null) {
          commonData_ = builderForValue.build();
        } else {
          commonDataBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder mergeCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
            commonData_ != null &&
            commonData_ != com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance()) {
            getCommonDataBuilder().mergeFrom(value);
          } else {
            commonData_ = value;
          }
        } else {
          commonDataBuilder_.mergeFrom(value);
        }
        if (commonData_ != null) {
          bitField0_ |= 0x00000002;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder clearCommonData() {
        bitField0_ = (bitField0_ & ~0x00000002);
        commonData_ = null;
        if (commonDataBuilder_ != null) {
          commonDataBuilder_.dispose();
          commonDataBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonData.Builder getCommonDataBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getCommonDataFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
        if (commonDataBuilder_ != null) {
          return commonDataBuilder_.getMessageOrBuilder();
        } else {
          return commonData_ == null ?
              com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> 
          getCommonDataFieldBuilder() {
        if (commonDataBuilder_ == null) {
          commonDataBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder>(
                  getCommonData(),
                  getParentForChildren(),
                  isClean());
          commonData_ = null;
        }
        return commonDataBuilder_;
      }

      private long userId_ ;
      /**
       * <code>int64 userId = 3;</code>
       * @return The userId.
       */
      @java.lang.Override
      public long getUserId() {
        return userId_;
      }
      /**
       * <code>int64 userId = 3;</code>
       * @param value The userId to set.
       * @return This builder for chaining.
       */
      public Builder setUserId(long value) {

        userId_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>int64 userId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearUserId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        userId_ = 0L;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.CommonProto.LordDto lord_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.LordDto, com.dxx.game.dto.CommonProto.LordDto.Builder, com.dxx.game.dto.CommonProto.LordDtoOrBuilder> lordBuilder_;
      /**
       * <pre>
       * 领主信息
       * </pre>
       *
       * <code>.Proto.Common.LordDto lord = 4;</code>
       * @return Whether the lord field is set.
       */
      public boolean hasLord() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 领主信息
       * </pre>
       *
       * <code>.Proto.Common.LordDto lord = 4;</code>
       * @return The lord.
       */
      public com.dxx.game.dto.CommonProto.LordDto getLord() {
        if (lordBuilder_ == null) {
          return lord_ == null ? com.dxx.game.dto.CommonProto.LordDto.getDefaultInstance() : lord_;
        } else {
          return lordBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 领主信息
       * </pre>
       *
       * <code>.Proto.Common.LordDto lord = 4;</code>
       */
      public Builder setLord(com.dxx.game.dto.CommonProto.LordDto value) {
        if (lordBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          lord_ = value;
        } else {
          lordBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 领主信息
       * </pre>
       *
       * <code>.Proto.Common.LordDto lord = 4;</code>
       */
      public Builder setLord(
          com.dxx.game.dto.CommonProto.LordDto.Builder builderForValue) {
        if (lordBuilder_ == null) {
          lord_ = builderForValue.build();
        } else {
          lordBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 领主信息
       * </pre>
       *
       * <code>.Proto.Common.LordDto lord = 4;</code>
       */
      public Builder mergeLord(com.dxx.game.dto.CommonProto.LordDto value) {
        if (lordBuilder_ == null) {
          if (((bitField0_ & 0x00000008) != 0) &&
            lord_ != null &&
            lord_ != com.dxx.game.dto.CommonProto.LordDto.getDefaultInstance()) {
            getLordBuilder().mergeFrom(value);
          } else {
            lord_ = value;
          }
        } else {
          lordBuilder_.mergeFrom(value);
        }
        if (lord_ != null) {
          bitField0_ |= 0x00000008;
          onChanged();
        }
        return this;
      }
      /**
       * <pre>
       * 领主信息
       * </pre>
       *
       * <code>.Proto.Common.LordDto lord = 4;</code>
       */
      public Builder clearLord() {
        bitField0_ = (bitField0_ & ~0x00000008);
        lord_ = null;
        if (lordBuilder_ != null) {
          lordBuilder_.dispose();
          lordBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 领主信息
       * </pre>
       *
       * <code>.Proto.Common.LordDto lord = 4;</code>
       */
      public com.dxx.game.dto.CommonProto.LordDto.Builder getLordBuilder() {
        bitField0_ |= 0x00000008;
        onChanged();
        return getLordFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 领主信息
       * </pre>
       *
       * <code>.Proto.Common.LordDto lord = 4;</code>
       */
      public com.dxx.game.dto.CommonProto.LordDtoOrBuilder getLordOrBuilder() {
        if (lordBuilder_ != null) {
          return lordBuilder_.getMessageOrBuilder();
        } else {
          return lord_ == null ?
              com.dxx.game.dto.CommonProto.LordDto.getDefaultInstance() : lord_;
        }
      }
      /**
       * <pre>
       * 领主信息
       * </pre>
       *
       * <code>.Proto.Common.LordDto lord = 4;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.LordDto, com.dxx.game.dto.CommonProto.LordDto.Builder, com.dxx.game.dto.CommonProto.LordDtoOrBuilder> 
          getLordFieldBuilder() {
        if (lordBuilder_ == null) {
          lordBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.LordDto, com.dxx.game.dto.CommonProto.LordDto.Builder, com.dxx.game.dto.CommonProto.LordDtoOrBuilder>(
                  getLord(),
                  getParentForChildren(),
                  isClean());
          lord_ = null;
        }
        return lordBuilder_;
      }

      private int slaveCount_ ;
      /**
       * <pre>
       * 奴隶个数
       * </pre>
       *
       * <code>uint32 slaveCount = 5;</code>
       * @return The slaveCount.
       */
      @java.lang.Override
      public int getSlaveCount() {
        return slaveCount_;
      }
      /**
       * <pre>
       * 奴隶个数
       * </pre>
       *
       * <code>uint32 slaveCount = 5;</code>
       * @param value The slaveCount to set.
       * @return This builder for chaining.
       */
      public Builder setSlaveCount(int value) {

        slaveCount_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 奴隶个数
       * </pre>
       *
       * <code>uint32 slaveCount = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearSlaveCount() {
        bitField0_ = (bitField0_ & ~0x00000010);
        slaveCount_ = 0;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.CommonProto.PVPRecordDto record_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.PVPRecordDto, com.dxx.game.dto.CommonProto.PVPRecordDto.Builder, com.dxx.game.dto.CommonProto.PVPRecordDtoOrBuilder> recordBuilder_;
      /**
       * <code>.Proto.Common.PVPRecordDto record = 6;</code>
       * @return Whether the record field is set.
       */
      public boolean hasRecord() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <code>.Proto.Common.PVPRecordDto record = 6;</code>
       * @return The record.
       */
      public com.dxx.game.dto.CommonProto.PVPRecordDto getRecord() {
        if (recordBuilder_ == null) {
          return record_ == null ? com.dxx.game.dto.CommonProto.PVPRecordDto.getDefaultInstance() : record_;
        } else {
          return recordBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.PVPRecordDto record = 6;</code>
       */
      public Builder setRecord(com.dxx.game.dto.CommonProto.PVPRecordDto value) {
        if (recordBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          record_ = value;
        } else {
          recordBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.PVPRecordDto record = 6;</code>
       */
      public Builder setRecord(
          com.dxx.game.dto.CommonProto.PVPRecordDto.Builder builderForValue) {
        if (recordBuilder_ == null) {
          record_ = builderForValue.build();
        } else {
          recordBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.PVPRecordDto record = 6;</code>
       */
      public Builder mergeRecord(com.dxx.game.dto.CommonProto.PVPRecordDto value) {
        if (recordBuilder_ == null) {
          if (((bitField0_ & 0x00000020) != 0) &&
            record_ != null &&
            record_ != com.dxx.game.dto.CommonProto.PVPRecordDto.getDefaultInstance()) {
            getRecordBuilder().mergeFrom(value);
          } else {
            record_ = value;
          }
        } else {
          recordBuilder_.mergeFrom(value);
        }
        if (record_ != null) {
          bitField0_ |= 0x00000020;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Proto.Common.PVPRecordDto record = 6;</code>
       */
      public Builder clearRecord() {
        bitField0_ = (bitField0_ & ~0x00000020);
        record_ = null;
        if (recordBuilder_ != null) {
          recordBuilder_.dispose();
          recordBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.PVPRecordDto record = 6;</code>
       */
      public com.dxx.game.dto.CommonProto.PVPRecordDto.Builder getRecordBuilder() {
        bitField0_ |= 0x00000020;
        onChanged();
        return getRecordFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.PVPRecordDto record = 6;</code>
       */
      public com.dxx.game.dto.CommonProto.PVPRecordDtoOrBuilder getRecordOrBuilder() {
        if (recordBuilder_ != null) {
          return recordBuilder_.getMessageOrBuilder();
        } else {
          return record_ == null ?
              com.dxx.game.dto.CommonProto.PVPRecordDto.getDefaultInstance() : record_;
        }
      }
      /**
       * <code>.Proto.Common.PVPRecordDto record = 6;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.PVPRecordDto, com.dxx.game.dto.CommonProto.PVPRecordDto.Builder, com.dxx.game.dto.CommonProto.PVPRecordDtoOrBuilder> 
          getRecordFieldBuilder() {
        if (recordBuilder_ == null) {
          recordBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.PVPRecordDto, com.dxx.game.dto.CommonProto.PVPRecordDto.Builder, com.dxx.game.dto.CommonProto.PVPRecordDtoOrBuilder>(
                  getRecord(),
                  getParentForChildren(),
                  isClean());
          record_ = null;
        }
        return recordBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Conquer.ConquerRevoltResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.Conquer.ConquerRevoltResponse)
    private static final com.dxx.game.dto.ConquerProto.ConquerRevoltResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.ConquerProto.ConquerRevoltResponse();
    }

    public static com.dxx.game.dto.ConquerProto.ConquerRevoltResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ConquerRevoltResponse>
        PARSER = new com.google.protobuf.AbstractParser<ConquerRevoltResponse>() {
      @java.lang.Override
      public ConquerRevoltResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<ConquerRevoltResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ConquerRevoltResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.ConquerProto.ConquerRevoltResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ConquerLootRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Conquer.ConquerLootRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();

    /**
     * <code>int64 userId = 2;</code>
     * @return The userId.
     */
    long getUserId();

    /**
     * <code>string clientVersion = 3;</code>
     * @return The clientVersion.
     */
    java.lang.String getClientVersion();
    /**
     * <code>string clientVersion = 3;</code>
     * @return The bytes for clientVersion.
     */
    com.google.protobuf.ByteString
        getClientVersionBytes();
  }
  /**
   * <pre>
   * CMD PackageId=12807 征服战-抢夺
   * </pre>
   *
   * Protobuf type {@code Proto.Conquer.ConquerLootRequest}
   */
  public static final class ConquerLootRequest extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Conquer.ConquerLootRequest)
      ConquerLootRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        ConquerLootRequest.class.getName());
    }
    // Use ConquerLootRequest.newBuilder() to construct.
    private ConquerLootRequest(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private ConquerLootRequest() {
      clientVersion_ = "";
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.ConquerProto.internal_static_Proto_Conquer_ConquerLootRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.ConquerProto.internal_static_Proto_Conquer_ConquerLootRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.ConquerProto.ConquerLootRequest.class, com.dxx.game.dto.ConquerProto.ConquerLootRequest.Builder.class);
    }

    private int bitField0_;
    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }

    public static final int USERID_FIELD_NUMBER = 2;
    private long userId_ = 0L;
    /**
     * <code>int64 userId = 2;</code>
     * @return The userId.
     */
    @java.lang.Override
    public long getUserId() {
      return userId_;
    }

    public static final int CLIENTVERSION_FIELD_NUMBER = 3;
    @SuppressWarnings("serial")
    private volatile java.lang.Object clientVersion_ = "";
    /**
     * <code>string clientVersion = 3;</code>
     * @return The clientVersion.
     */
    @java.lang.Override
    public java.lang.String getClientVersion() {
      java.lang.Object ref = clientVersion_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        clientVersion_ = s;
        return s;
      }
    }
    /**
     * <code>string clientVersion = 3;</code>
     * @return The bytes for clientVersion.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getClientVersionBytes() {
      java.lang.Object ref = clientVersion_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        clientVersion_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getCommonParams());
      }
      if (userId_ != 0L) {
        output.writeInt64(2, userId_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(clientVersion_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 3, clientVersion_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      if (userId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, userId_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(clientVersion_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(3, clientVersion_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.ConquerProto.ConquerLootRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.ConquerProto.ConquerLootRequest other = (com.dxx.game.dto.ConquerProto.ConquerLootRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (getUserId()
          != other.getUserId()) return false;
      if (!getClientVersion()
          .equals(other.getClientVersion())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (37 * hash) + USERID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getUserId());
      hash = (37 * hash) + CLIENTVERSION_FIELD_NUMBER;
      hash = (53 * hash) + getClientVersion().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.ConquerProto.ConquerLootRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerLootRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerLootRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerLootRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerLootRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerLootRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerLootRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerLootRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.ConquerProto.ConquerLootRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.ConquerProto.ConquerLootRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerLootRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerLootRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.ConquerProto.ConquerLootRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=12807 征服战-抢夺
     * </pre>
     *
     * Protobuf type {@code Proto.Conquer.ConquerLootRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Conquer.ConquerLootRequest)
        com.dxx.game.dto.ConquerProto.ConquerLootRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.ConquerProto.internal_static_Proto_Conquer_ConquerLootRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.ConquerProto.internal_static_Proto_Conquer_ConquerLootRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.ConquerProto.ConquerLootRequest.class, com.dxx.game.dto.ConquerProto.ConquerLootRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.ConquerProto.ConquerLootRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getCommonParamsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        userId_ = 0L;
        clientVersion_ = "";
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.ConquerProto.internal_static_Proto_Conquer_ConquerLootRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.ConquerProto.ConquerLootRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.ConquerProto.ConquerLootRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.ConquerProto.ConquerLootRequest build() {
        com.dxx.game.dto.ConquerProto.ConquerLootRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.ConquerProto.ConquerLootRequest buildPartial() {
        com.dxx.game.dto.ConquerProto.ConquerLootRequest result = new com.dxx.game.dto.ConquerProto.ConquerLootRequest(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.ConquerProto.ConquerLootRequest result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.commonParams_ = commonParamsBuilder_ == null
              ? commonParams_
              : commonParamsBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.userId_ = userId_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.clientVersion_ = clientVersion_;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.ConquerProto.ConquerLootRequest) {
          return mergeFrom((com.dxx.game.dto.ConquerProto.ConquerLootRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.ConquerProto.ConquerLootRequest other) {
        if (other == com.dxx.game.dto.ConquerProto.ConquerLootRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        if (other.getUserId() != 0L) {
          setUserId(other.getUserId());
        }
        if (!other.getClientVersion().isEmpty()) {
          clientVersion_ = other.clientVersion_;
          bitField0_ |= 0x00000004;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getCommonParamsFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 16: {
                userId_ = input.readInt64();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 26: {
                clientVersion_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000004;
                break;
              } // case 26
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
        } else {
          commonParamsBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
            commonParams_ != null &&
            commonParams_ != com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance()) {
            getCommonParamsBuilder().mergeFrom(value);
          } else {
            commonParams_ = value;
          }
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }
        if (commonParams_ != null) {
          bitField0_ |= 0x00000001;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        bitField0_ = (bitField0_ & ~0x00000001);
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }

      private long userId_ ;
      /**
       * <code>int64 userId = 2;</code>
       * @return The userId.
       */
      @java.lang.Override
      public long getUserId() {
        return userId_;
      }
      /**
       * <code>int64 userId = 2;</code>
       * @param value The userId to set.
       * @return This builder for chaining.
       */
      public Builder setUserId(long value) {

        userId_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>int64 userId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearUserId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        userId_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object clientVersion_ = "";
      /**
       * <code>string clientVersion = 3;</code>
       * @return The clientVersion.
       */
      public java.lang.String getClientVersion() {
        java.lang.Object ref = clientVersion_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          clientVersion_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string clientVersion = 3;</code>
       * @return The bytes for clientVersion.
       */
      public com.google.protobuf.ByteString
          getClientVersionBytes() {
        java.lang.Object ref = clientVersion_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          clientVersion_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string clientVersion = 3;</code>
       * @param value The clientVersion to set.
       * @return This builder for chaining.
       */
      public Builder setClientVersion(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        clientVersion_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>string clientVersion = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearClientVersion() {
        clientVersion_ = getDefaultInstance().getClientVersion();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }
      /**
       * <code>string clientVersion = 3;</code>
       * @param value The bytes for clientVersion to set.
       * @return This builder for chaining.
       */
      public Builder setClientVersionBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        clientVersion_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Conquer.ConquerLootRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.Conquer.ConquerLootRequest)
    private static final com.dxx.game.dto.ConquerProto.ConquerLootRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.ConquerProto.ConquerLootRequest();
    }

    public static com.dxx.game.dto.ConquerProto.ConquerLootRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ConquerLootRequest>
        PARSER = new com.google.protobuf.AbstractParser<ConquerLootRequest>() {
      @java.lang.Override
      public ConquerLootRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<ConquerLootRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ConquerLootRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.ConquerProto.ConquerLootRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ConquerLootResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Conquer.ConquerLootResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    boolean hasCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    com.dxx.game.dto.CommonProto.CommonData getCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder();

    /**
     * <code>int64 userId = 3;</code>
     * @return The userId.
     */
    long getUserId();

    /**
     * <pre>
     * 领主信息
     * </pre>
     *
     * <code>.Proto.Common.LordDto lord = 4;</code>
     * @return Whether the lord field is set.
     */
    boolean hasLord();
    /**
     * <pre>
     * 领主信息
     * </pre>
     *
     * <code>.Proto.Common.LordDto lord = 4;</code>
     * @return The lord.
     */
    com.dxx.game.dto.CommonProto.LordDto getLord();
    /**
     * <pre>
     * 领主信息
     * </pre>
     *
     * <code>.Proto.Common.LordDto lord = 4;</code>
     */
    com.dxx.game.dto.CommonProto.LordDtoOrBuilder getLordOrBuilder();

    /**
     * <pre>
     * 奴隶个数
     * </pre>
     *
     * <code>uint32 slaveCount = 5;</code>
     * @return The slaveCount.
     */
    int getSlaveCount();

    /**
     * <code>.Proto.Common.PVPRecordDto record = 6;</code>
     * @return Whether the record field is set.
     */
    boolean hasRecord();
    /**
     * <code>.Proto.Common.PVPRecordDto record = 6;</code>
     * @return The record.
     */
    com.dxx.game.dto.CommonProto.PVPRecordDto getRecord();
    /**
     * <code>.Proto.Common.PVPRecordDto record = 6;</code>
     */
    com.dxx.game.dto.CommonProto.PVPRecordDtoOrBuilder getRecordOrBuilder();
  }
  /**
   * <pre>
   * CMD PackageId=12808
   * </pre>
   *
   * Protobuf type {@code Proto.Conquer.ConquerLootResponse}
   */
  public static final class ConquerLootResponse extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Conquer.ConquerLootResponse)
      ConquerLootResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        ConquerLootResponse.class.getName());
    }
    // Use ConquerLootResponse.newBuilder() to construct.
    private ConquerLootResponse(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private ConquerLootResponse() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.ConquerProto.internal_static_Proto_Conquer_ConquerLootResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.ConquerProto.internal_static_Proto_Conquer_ConquerLootResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.ConquerProto.ConquerLootResponse.class, com.dxx.game.dto.ConquerProto.ConquerLootResponse.Builder.class);
    }

    private int bitField0_;
    public static final int CODE_FIELD_NUMBER = 1;
    private int code_ = 0;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int COMMONDATA_FIELD_NUMBER = 2;
    private com.dxx.game.dto.CommonProto.CommonData commonData_;
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    @java.lang.Override
    public boolean hasCommonData() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }

    public static final int USERID_FIELD_NUMBER = 3;
    private long userId_ = 0L;
    /**
     * <code>int64 userId = 3;</code>
     * @return The userId.
     */
    @java.lang.Override
    public long getUserId() {
      return userId_;
    }

    public static final int LORD_FIELD_NUMBER = 4;
    private com.dxx.game.dto.CommonProto.LordDto lord_;
    /**
     * <pre>
     * 领主信息
     * </pre>
     *
     * <code>.Proto.Common.LordDto lord = 4;</code>
     * @return Whether the lord field is set.
     */
    @java.lang.Override
    public boolean hasLord() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 领主信息
     * </pre>
     *
     * <code>.Proto.Common.LordDto lord = 4;</code>
     * @return The lord.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.LordDto getLord() {
      return lord_ == null ? com.dxx.game.dto.CommonProto.LordDto.getDefaultInstance() : lord_;
    }
    /**
     * <pre>
     * 领主信息
     * </pre>
     *
     * <code>.Proto.Common.LordDto lord = 4;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.LordDtoOrBuilder getLordOrBuilder() {
      return lord_ == null ? com.dxx.game.dto.CommonProto.LordDto.getDefaultInstance() : lord_;
    }

    public static final int SLAVECOUNT_FIELD_NUMBER = 5;
    private int slaveCount_ = 0;
    /**
     * <pre>
     * 奴隶个数
     * </pre>
     *
     * <code>uint32 slaveCount = 5;</code>
     * @return The slaveCount.
     */
    @java.lang.Override
    public int getSlaveCount() {
      return slaveCount_;
    }

    public static final int RECORD_FIELD_NUMBER = 6;
    private com.dxx.game.dto.CommonProto.PVPRecordDto record_;
    /**
     * <code>.Proto.Common.PVPRecordDto record = 6;</code>
     * @return Whether the record field is set.
     */
    @java.lang.Override
    public boolean hasRecord() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>.Proto.Common.PVPRecordDto record = 6;</code>
     * @return The record.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.PVPRecordDto getRecord() {
      return record_ == null ? com.dxx.game.dto.CommonProto.PVPRecordDto.getDefaultInstance() : record_;
    }
    /**
     * <code>.Proto.Common.PVPRecordDto record = 6;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.PVPRecordDtoOrBuilder getRecordOrBuilder() {
      return record_ == null ? com.dxx.game.dto.CommonProto.PVPRecordDto.getDefaultInstance() : record_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(2, getCommonData());
      }
      if (userId_ != 0L) {
        output.writeInt64(3, userId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(4, getLord());
      }
      if (slaveCount_ != 0) {
        output.writeUInt32(5, slaveCount_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeMessage(6, getRecord());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getCommonData());
      }
      if (userId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, userId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, getLord());
      }
      if (slaveCount_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(5, slaveCount_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(6, getRecord());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.ConquerProto.ConquerLootResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.ConquerProto.ConquerLootResponse other = (com.dxx.game.dto.ConquerProto.ConquerLootResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (hasCommonData() != other.hasCommonData()) return false;
      if (hasCommonData()) {
        if (!getCommonData()
            .equals(other.getCommonData())) return false;
      }
      if (getUserId()
          != other.getUserId()) return false;
      if (hasLord() != other.hasLord()) return false;
      if (hasLord()) {
        if (!getLord()
            .equals(other.getLord())) return false;
      }
      if (getSlaveCount()
          != other.getSlaveCount()) return false;
      if (hasRecord() != other.hasRecord()) return false;
      if (hasRecord()) {
        if (!getRecord()
            .equals(other.getRecord())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      if (hasCommonData()) {
        hash = (37 * hash) + COMMONDATA_FIELD_NUMBER;
        hash = (53 * hash) + getCommonData().hashCode();
      }
      hash = (37 * hash) + USERID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getUserId());
      if (hasLord()) {
        hash = (37 * hash) + LORD_FIELD_NUMBER;
        hash = (53 * hash) + getLord().hashCode();
      }
      hash = (37 * hash) + SLAVECOUNT_FIELD_NUMBER;
      hash = (53 * hash) + getSlaveCount();
      if (hasRecord()) {
        hash = (37 * hash) + RECORD_FIELD_NUMBER;
        hash = (53 * hash) + getRecord().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.ConquerProto.ConquerLootResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerLootResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerLootResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerLootResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerLootResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerLootResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerLootResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerLootResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.ConquerProto.ConquerLootResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.ConquerProto.ConquerLootResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerLootResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerLootResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.ConquerProto.ConquerLootResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=12808
     * </pre>
     *
     * Protobuf type {@code Proto.Conquer.ConquerLootResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Conquer.ConquerLootResponse)
        com.dxx.game.dto.ConquerProto.ConquerLootResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.ConquerProto.internal_static_Proto_Conquer_ConquerLootResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.ConquerProto.internal_static_Proto_Conquer_ConquerLootResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.ConquerProto.ConquerLootResponse.class, com.dxx.game.dto.ConquerProto.ConquerLootResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.ConquerProto.ConquerLootResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getCommonDataFieldBuilder();
          getLordFieldBuilder();
          getRecordFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        code_ = 0;
        commonData_ = null;
        if (commonDataBuilder_ != null) {
          commonDataBuilder_.dispose();
          commonDataBuilder_ = null;
        }
        userId_ = 0L;
        lord_ = null;
        if (lordBuilder_ != null) {
          lordBuilder_.dispose();
          lordBuilder_ = null;
        }
        slaveCount_ = 0;
        record_ = null;
        if (recordBuilder_ != null) {
          recordBuilder_.dispose();
          recordBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.ConquerProto.internal_static_Proto_Conquer_ConquerLootResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.ConquerProto.ConquerLootResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.ConquerProto.ConquerLootResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.ConquerProto.ConquerLootResponse build() {
        com.dxx.game.dto.ConquerProto.ConquerLootResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.ConquerProto.ConquerLootResponse buildPartial() {
        com.dxx.game.dto.ConquerProto.ConquerLootResponse result = new com.dxx.game.dto.ConquerProto.ConquerLootResponse(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.ConquerProto.ConquerLootResponse result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.code_ = code_;
        }
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.commonData_ = commonDataBuilder_ == null
              ? commonData_
              : commonDataBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.userId_ = userId_;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.lord_ = lordBuilder_ == null
              ? lord_
              : lordBuilder_.build();
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.slaveCount_ = slaveCount_;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.record_ = recordBuilder_ == null
              ? record_
              : recordBuilder_.build();
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.ConquerProto.ConquerLootResponse) {
          return mergeFrom((com.dxx.game.dto.ConquerProto.ConquerLootResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.ConquerProto.ConquerLootResponse other) {
        if (other == com.dxx.game.dto.ConquerProto.ConquerLootResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.hasCommonData()) {
          mergeCommonData(other.getCommonData());
        }
        if (other.getUserId() != 0L) {
          setUserId(other.getUserId());
        }
        if (other.hasLord()) {
          mergeLord(other.getLord());
        }
        if (other.getSlaveCount() != 0) {
          setSlaveCount(other.getSlaveCount());
        }
        if (other.hasRecord()) {
          mergeRecord(other.getRecord());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                code_ = input.readInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 18: {
                input.readMessage(
                    getCommonDataFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              case 24: {
                userId_ = input.readInt64();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              case 34: {
                input.readMessage(
                    getLordFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000008;
                break;
              } // case 34
              case 40: {
                slaveCount_ = input.readUInt32();
                bitField0_ |= 0x00000010;
                break;
              } // case 40
              case 50: {
                input.readMessage(
                    getRecordFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000020;
                break;
              } // case 50
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {

        code_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        bitField0_ = (bitField0_ & ~0x00000001);
        code_ = 0;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonData commonData_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> commonDataBuilder_;
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return Whether the commonData field is set.
       */
      public boolean hasCommonData() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return The commonData.
       */
      public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
        if (commonDataBuilder_ == null) {
          return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        } else {
          return commonDataBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonData_ = value;
        } else {
          commonDataBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(
          com.dxx.game.dto.CommonProto.CommonData.Builder builderForValue) {
        if (commonDataBuilder_ == null) {
          commonData_ = builderForValue.build();
        } else {
          commonDataBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder mergeCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
            commonData_ != null &&
            commonData_ != com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance()) {
            getCommonDataBuilder().mergeFrom(value);
          } else {
            commonData_ = value;
          }
        } else {
          commonDataBuilder_.mergeFrom(value);
        }
        if (commonData_ != null) {
          bitField0_ |= 0x00000002;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder clearCommonData() {
        bitField0_ = (bitField0_ & ~0x00000002);
        commonData_ = null;
        if (commonDataBuilder_ != null) {
          commonDataBuilder_.dispose();
          commonDataBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonData.Builder getCommonDataBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getCommonDataFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
        if (commonDataBuilder_ != null) {
          return commonDataBuilder_.getMessageOrBuilder();
        } else {
          return commonData_ == null ?
              com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> 
          getCommonDataFieldBuilder() {
        if (commonDataBuilder_ == null) {
          commonDataBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder>(
                  getCommonData(),
                  getParentForChildren(),
                  isClean());
          commonData_ = null;
        }
        return commonDataBuilder_;
      }

      private long userId_ ;
      /**
       * <code>int64 userId = 3;</code>
       * @return The userId.
       */
      @java.lang.Override
      public long getUserId() {
        return userId_;
      }
      /**
       * <code>int64 userId = 3;</code>
       * @param value The userId to set.
       * @return This builder for chaining.
       */
      public Builder setUserId(long value) {

        userId_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>int64 userId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearUserId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        userId_ = 0L;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.CommonProto.LordDto lord_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.LordDto, com.dxx.game.dto.CommonProto.LordDto.Builder, com.dxx.game.dto.CommonProto.LordDtoOrBuilder> lordBuilder_;
      /**
       * <pre>
       * 领主信息
       * </pre>
       *
       * <code>.Proto.Common.LordDto lord = 4;</code>
       * @return Whether the lord field is set.
       */
      public boolean hasLord() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 领主信息
       * </pre>
       *
       * <code>.Proto.Common.LordDto lord = 4;</code>
       * @return The lord.
       */
      public com.dxx.game.dto.CommonProto.LordDto getLord() {
        if (lordBuilder_ == null) {
          return lord_ == null ? com.dxx.game.dto.CommonProto.LordDto.getDefaultInstance() : lord_;
        } else {
          return lordBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 领主信息
       * </pre>
       *
       * <code>.Proto.Common.LordDto lord = 4;</code>
       */
      public Builder setLord(com.dxx.game.dto.CommonProto.LordDto value) {
        if (lordBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          lord_ = value;
        } else {
          lordBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 领主信息
       * </pre>
       *
       * <code>.Proto.Common.LordDto lord = 4;</code>
       */
      public Builder setLord(
          com.dxx.game.dto.CommonProto.LordDto.Builder builderForValue) {
        if (lordBuilder_ == null) {
          lord_ = builderForValue.build();
        } else {
          lordBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 领主信息
       * </pre>
       *
       * <code>.Proto.Common.LordDto lord = 4;</code>
       */
      public Builder mergeLord(com.dxx.game.dto.CommonProto.LordDto value) {
        if (lordBuilder_ == null) {
          if (((bitField0_ & 0x00000008) != 0) &&
            lord_ != null &&
            lord_ != com.dxx.game.dto.CommonProto.LordDto.getDefaultInstance()) {
            getLordBuilder().mergeFrom(value);
          } else {
            lord_ = value;
          }
        } else {
          lordBuilder_.mergeFrom(value);
        }
        if (lord_ != null) {
          bitField0_ |= 0x00000008;
          onChanged();
        }
        return this;
      }
      /**
       * <pre>
       * 领主信息
       * </pre>
       *
       * <code>.Proto.Common.LordDto lord = 4;</code>
       */
      public Builder clearLord() {
        bitField0_ = (bitField0_ & ~0x00000008);
        lord_ = null;
        if (lordBuilder_ != null) {
          lordBuilder_.dispose();
          lordBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 领主信息
       * </pre>
       *
       * <code>.Proto.Common.LordDto lord = 4;</code>
       */
      public com.dxx.game.dto.CommonProto.LordDto.Builder getLordBuilder() {
        bitField0_ |= 0x00000008;
        onChanged();
        return getLordFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 领主信息
       * </pre>
       *
       * <code>.Proto.Common.LordDto lord = 4;</code>
       */
      public com.dxx.game.dto.CommonProto.LordDtoOrBuilder getLordOrBuilder() {
        if (lordBuilder_ != null) {
          return lordBuilder_.getMessageOrBuilder();
        } else {
          return lord_ == null ?
              com.dxx.game.dto.CommonProto.LordDto.getDefaultInstance() : lord_;
        }
      }
      /**
       * <pre>
       * 领主信息
       * </pre>
       *
       * <code>.Proto.Common.LordDto lord = 4;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.LordDto, com.dxx.game.dto.CommonProto.LordDto.Builder, com.dxx.game.dto.CommonProto.LordDtoOrBuilder> 
          getLordFieldBuilder() {
        if (lordBuilder_ == null) {
          lordBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.LordDto, com.dxx.game.dto.CommonProto.LordDto.Builder, com.dxx.game.dto.CommonProto.LordDtoOrBuilder>(
                  getLord(),
                  getParentForChildren(),
                  isClean());
          lord_ = null;
        }
        return lordBuilder_;
      }

      private int slaveCount_ ;
      /**
       * <pre>
       * 奴隶个数
       * </pre>
       *
       * <code>uint32 slaveCount = 5;</code>
       * @return The slaveCount.
       */
      @java.lang.Override
      public int getSlaveCount() {
        return slaveCount_;
      }
      /**
       * <pre>
       * 奴隶个数
       * </pre>
       *
       * <code>uint32 slaveCount = 5;</code>
       * @param value The slaveCount to set.
       * @return This builder for chaining.
       */
      public Builder setSlaveCount(int value) {

        slaveCount_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 奴隶个数
       * </pre>
       *
       * <code>uint32 slaveCount = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearSlaveCount() {
        bitField0_ = (bitField0_ & ~0x00000010);
        slaveCount_ = 0;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.CommonProto.PVPRecordDto record_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.PVPRecordDto, com.dxx.game.dto.CommonProto.PVPRecordDto.Builder, com.dxx.game.dto.CommonProto.PVPRecordDtoOrBuilder> recordBuilder_;
      /**
       * <code>.Proto.Common.PVPRecordDto record = 6;</code>
       * @return Whether the record field is set.
       */
      public boolean hasRecord() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <code>.Proto.Common.PVPRecordDto record = 6;</code>
       * @return The record.
       */
      public com.dxx.game.dto.CommonProto.PVPRecordDto getRecord() {
        if (recordBuilder_ == null) {
          return record_ == null ? com.dxx.game.dto.CommonProto.PVPRecordDto.getDefaultInstance() : record_;
        } else {
          return recordBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.PVPRecordDto record = 6;</code>
       */
      public Builder setRecord(com.dxx.game.dto.CommonProto.PVPRecordDto value) {
        if (recordBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          record_ = value;
        } else {
          recordBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.PVPRecordDto record = 6;</code>
       */
      public Builder setRecord(
          com.dxx.game.dto.CommonProto.PVPRecordDto.Builder builderForValue) {
        if (recordBuilder_ == null) {
          record_ = builderForValue.build();
        } else {
          recordBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.PVPRecordDto record = 6;</code>
       */
      public Builder mergeRecord(com.dxx.game.dto.CommonProto.PVPRecordDto value) {
        if (recordBuilder_ == null) {
          if (((bitField0_ & 0x00000020) != 0) &&
            record_ != null &&
            record_ != com.dxx.game.dto.CommonProto.PVPRecordDto.getDefaultInstance()) {
            getRecordBuilder().mergeFrom(value);
          } else {
            record_ = value;
          }
        } else {
          recordBuilder_.mergeFrom(value);
        }
        if (record_ != null) {
          bitField0_ |= 0x00000020;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Proto.Common.PVPRecordDto record = 6;</code>
       */
      public Builder clearRecord() {
        bitField0_ = (bitField0_ & ~0x00000020);
        record_ = null;
        if (recordBuilder_ != null) {
          recordBuilder_.dispose();
          recordBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.PVPRecordDto record = 6;</code>
       */
      public com.dxx.game.dto.CommonProto.PVPRecordDto.Builder getRecordBuilder() {
        bitField0_ |= 0x00000020;
        onChanged();
        return getRecordFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.PVPRecordDto record = 6;</code>
       */
      public com.dxx.game.dto.CommonProto.PVPRecordDtoOrBuilder getRecordOrBuilder() {
        if (recordBuilder_ != null) {
          return recordBuilder_.getMessageOrBuilder();
        } else {
          return record_ == null ?
              com.dxx.game.dto.CommonProto.PVPRecordDto.getDefaultInstance() : record_;
        }
      }
      /**
       * <code>.Proto.Common.PVPRecordDto record = 6;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.PVPRecordDto, com.dxx.game.dto.CommonProto.PVPRecordDto.Builder, com.dxx.game.dto.CommonProto.PVPRecordDtoOrBuilder> 
          getRecordFieldBuilder() {
        if (recordBuilder_ == null) {
          recordBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.PVPRecordDto, com.dxx.game.dto.CommonProto.PVPRecordDto.Builder, com.dxx.game.dto.CommonProto.PVPRecordDtoOrBuilder>(
                  getRecord(),
                  getParentForChildren(),
                  isClean());
          record_ = null;
        }
        return recordBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Conquer.ConquerLootResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.Conquer.ConquerLootResponse)
    private static final com.dxx.game.dto.ConquerProto.ConquerLootResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.ConquerProto.ConquerLootResponse();
    }

    public static com.dxx.game.dto.ConquerProto.ConquerLootResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ConquerLootResponse>
        PARSER = new com.google.protobuf.AbstractParser<ConquerLootResponse>() {
      @java.lang.Override
      public ConquerLootResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<ConquerLootResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ConquerLootResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.ConquerProto.ConquerLootResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ConquerPardonRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Conquer.ConquerPardonRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();

    /**
     * <code>int64 userId = 2;</code>
     * @return The userId.
     */
    long getUserId();

    /**
     * <code>string clientVersion = 3;</code>
     * @return The clientVersion.
     */
    java.lang.String getClientVersion();
    /**
     * <code>string clientVersion = 3;</code>
     * @return The bytes for clientVersion.
     */
    com.google.protobuf.ByteString
        getClientVersionBytes();
  }
  /**
   * <pre>
   * CMD PackageId=12809 征服战-赦免
   * </pre>
   *
   * Protobuf type {@code Proto.Conquer.ConquerPardonRequest}
   */
  public static final class ConquerPardonRequest extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Conquer.ConquerPardonRequest)
      ConquerPardonRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        ConquerPardonRequest.class.getName());
    }
    // Use ConquerPardonRequest.newBuilder() to construct.
    private ConquerPardonRequest(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private ConquerPardonRequest() {
      clientVersion_ = "";
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.ConquerProto.internal_static_Proto_Conquer_ConquerPardonRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.ConquerProto.internal_static_Proto_Conquer_ConquerPardonRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.ConquerProto.ConquerPardonRequest.class, com.dxx.game.dto.ConquerProto.ConquerPardonRequest.Builder.class);
    }

    private int bitField0_;
    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }

    public static final int USERID_FIELD_NUMBER = 2;
    private long userId_ = 0L;
    /**
     * <code>int64 userId = 2;</code>
     * @return The userId.
     */
    @java.lang.Override
    public long getUserId() {
      return userId_;
    }

    public static final int CLIENTVERSION_FIELD_NUMBER = 3;
    @SuppressWarnings("serial")
    private volatile java.lang.Object clientVersion_ = "";
    /**
     * <code>string clientVersion = 3;</code>
     * @return The clientVersion.
     */
    @java.lang.Override
    public java.lang.String getClientVersion() {
      java.lang.Object ref = clientVersion_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        clientVersion_ = s;
        return s;
      }
    }
    /**
     * <code>string clientVersion = 3;</code>
     * @return The bytes for clientVersion.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getClientVersionBytes() {
      java.lang.Object ref = clientVersion_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        clientVersion_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getCommonParams());
      }
      if (userId_ != 0L) {
        output.writeInt64(2, userId_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(clientVersion_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 3, clientVersion_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      if (userId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, userId_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(clientVersion_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(3, clientVersion_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.ConquerProto.ConquerPardonRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.ConquerProto.ConquerPardonRequest other = (com.dxx.game.dto.ConquerProto.ConquerPardonRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (getUserId()
          != other.getUserId()) return false;
      if (!getClientVersion()
          .equals(other.getClientVersion())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (37 * hash) + USERID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getUserId());
      hash = (37 * hash) + CLIENTVERSION_FIELD_NUMBER;
      hash = (53 * hash) + getClientVersion().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.ConquerProto.ConquerPardonRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerPardonRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerPardonRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerPardonRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerPardonRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerPardonRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerPardonRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerPardonRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.ConquerProto.ConquerPardonRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.ConquerProto.ConquerPardonRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerPardonRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerPardonRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.ConquerProto.ConquerPardonRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=12809 征服战-赦免
     * </pre>
     *
     * Protobuf type {@code Proto.Conquer.ConquerPardonRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Conquer.ConquerPardonRequest)
        com.dxx.game.dto.ConquerProto.ConquerPardonRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.ConquerProto.internal_static_Proto_Conquer_ConquerPardonRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.ConquerProto.internal_static_Proto_Conquer_ConquerPardonRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.ConquerProto.ConquerPardonRequest.class, com.dxx.game.dto.ConquerProto.ConquerPardonRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.ConquerProto.ConquerPardonRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getCommonParamsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        userId_ = 0L;
        clientVersion_ = "";
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.ConquerProto.internal_static_Proto_Conquer_ConquerPardonRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.ConquerProto.ConquerPardonRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.ConquerProto.ConquerPardonRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.ConquerProto.ConquerPardonRequest build() {
        com.dxx.game.dto.ConquerProto.ConquerPardonRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.ConquerProto.ConquerPardonRequest buildPartial() {
        com.dxx.game.dto.ConquerProto.ConquerPardonRequest result = new com.dxx.game.dto.ConquerProto.ConquerPardonRequest(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.ConquerProto.ConquerPardonRequest result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.commonParams_ = commonParamsBuilder_ == null
              ? commonParams_
              : commonParamsBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.userId_ = userId_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.clientVersion_ = clientVersion_;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.ConquerProto.ConquerPardonRequest) {
          return mergeFrom((com.dxx.game.dto.ConquerProto.ConquerPardonRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.ConquerProto.ConquerPardonRequest other) {
        if (other == com.dxx.game.dto.ConquerProto.ConquerPardonRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        if (other.getUserId() != 0L) {
          setUserId(other.getUserId());
        }
        if (!other.getClientVersion().isEmpty()) {
          clientVersion_ = other.clientVersion_;
          bitField0_ |= 0x00000004;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getCommonParamsFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 16: {
                userId_ = input.readInt64();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 26: {
                clientVersion_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000004;
                break;
              } // case 26
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
        } else {
          commonParamsBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
            commonParams_ != null &&
            commonParams_ != com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance()) {
            getCommonParamsBuilder().mergeFrom(value);
          } else {
            commonParams_ = value;
          }
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }
        if (commonParams_ != null) {
          bitField0_ |= 0x00000001;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        bitField0_ = (bitField0_ & ~0x00000001);
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }

      private long userId_ ;
      /**
       * <code>int64 userId = 2;</code>
       * @return The userId.
       */
      @java.lang.Override
      public long getUserId() {
        return userId_;
      }
      /**
       * <code>int64 userId = 2;</code>
       * @param value The userId to set.
       * @return This builder for chaining.
       */
      public Builder setUserId(long value) {

        userId_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>int64 userId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearUserId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        userId_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object clientVersion_ = "";
      /**
       * <code>string clientVersion = 3;</code>
       * @return The clientVersion.
       */
      public java.lang.String getClientVersion() {
        java.lang.Object ref = clientVersion_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          clientVersion_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string clientVersion = 3;</code>
       * @return The bytes for clientVersion.
       */
      public com.google.protobuf.ByteString
          getClientVersionBytes() {
        java.lang.Object ref = clientVersion_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          clientVersion_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string clientVersion = 3;</code>
       * @param value The clientVersion to set.
       * @return This builder for chaining.
       */
      public Builder setClientVersion(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        clientVersion_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>string clientVersion = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearClientVersion() {
        clientVersion_ = getDefaultInstance().getClientVersion();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }
      /**
       * <code>string clientVersion = 3;</code>
       * @param value The bytes for clientVersion to set.
       * @return This builder for chaining.
       */
      public Builder setClientVersionBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        clientVersion_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Conquer.ConquerPardonRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.Conquer.ConquerPardonRequest)
    private static final com.dxx.game.dto.ConquerProto.ConquerPardonRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.ConquerProto.ConquerPardonRequest();
    }

    public static com.dxx.game.dto.ConquerProto.ConquerPardonRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ConquerPardonRequest>
        PARSER = new com.google.protobuf.AbstractParser<ConquerPardonRequest>() {
      @java.lang.Override
      public ConquerPardonRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<ConquerPardonRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ConquerPardonRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.ConquerProto.ConquerPardonRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ConquerPardonResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Conquer.ConquerPardonResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    boolean hasCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    com.dxx.game.dto.CommonProto.CommonData getCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder();

    /**
     * <code>int64 userId = 3;</code>
     * @return The userId.
     */
    long getUserId();

    /**
     * <pre>
     * 领主信息
     * </pre>
     *
     * <code>.Proto.Common.LordDto lord = 4;</code>
     * @return Whether the lord field is set.
     */
    boolean hasLord();
    /**
     * <pre>
     * 领主信息
     * </pre>
     *
     * <code>.Proto.Common.LordDto lord = 4;</code>
     * @return The lord.
     */
    com.dxx.game.dto.CommonProto.LordDto getLord();
    /**
     * <pre>
     * 领主信息
     * </pre>
     *
     * <code>.Proto.Common.LordDto lord = 4;</code>
     */
    com.dxx.game.dto.CommonProto.LordDtoOrBuilder getLordOrBuilder();

    /**
     * <pre>
     * 奴隶个数
     * </pre>
     *
     * <code>uint32 slaveCount = 5;</code>
     * @return The slaveCount.
     */
    int getSlaveCount();
  }
  /**
   * <pre>
   * CMD PackageId=12810
   * </pre>
   *
   * Protobuf type {@code Proto.Conquer.ConquerPardonResponse}
   */
  public static final class ConquerPardonResponse extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Conquer.ConquerPardonResponse)
      ConquerPardonResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        ConquerPardonResponse.class.getName());
    }
    // Use ConquerPardonResponse.newBuilder() to construct.
    private ConquerPardonResponse(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private ConquerPardonResponse() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.ConquerProto.internal_static_Proto_Conquer_ConquerPardonResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.ConquerProto.internal_static_Proto_Conquer_ConquerPardonResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.ConquerProto.ConquerPardonResponse.class, com.dxx.game.dto.ConquerProto.ConquerPardonResponse.Builder.class);
    }

    private int bitField0_;
    public static final int CODE_FIELD_NUMBER = 1;
    private int code_ = 0;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int COMMONDATA_FIELD_NUMBER = 2;
    private com.dxx.game.dto.CommonProto.CommonData commonData_;
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    @java.lang.Override
    public boolean hasCommonData() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }

    public static final int USERID_FIELD_NUMBER = 3;
    private long userId_ = 0L;
    /**
     * <code>int64 userId = 3;</code>
     * @return The userId.
     */
    @java.lang.Override
    public long getUserId() {
      return userId_;
    }

    public static final int LORD_FIELD_NUMBER = 4;
    private com.dxx.game.dto.CommonProto.LordDto lord_;
    /**
     * <pre>
     * 领主信息
     * </pre>
     *
     * <code>.Proto.Common.LordDto lord = 4;</code>
     * @return Whether the lord field is set.
     */
    @java.lang.Override
    public boolean hasLord() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 领主信息
     * </pre>
     *
     * <code>.Proto.Common.LordDto lord = 4;</code>
     * @return The lord.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.LordDto getLord() {
      return lord_ == null ? com.dxx.game.dto.CommonProto.LordDto.getDefaultInstance() : lord_;
    }
    /**
     * <pre>
     * 领主信息
     * </pre>
     *
     * <code>.Proto.Common.LordDto lord = 4;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.LordDtoOrBuilder getLordOrBuilder() {
      return lord_ == null ? com.dxx.game.dto.CommonProto.LordDto.getDefaultInstance() : lord_;
    }

    public static final int SLAVECOUNT_FIELD_NUMBER = 5;
    private int slaveCount_ = 0;
    /**
     * <pre>
     * 奴隶个数
     * </pre>
     *
     * <code>uint32 slaveCount = 5;</code>
     * @return The slaveCount.
     */
    @java.lang.Override
    public int getSlaveCount() {
      return slaveCount_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(2, getCommonData());
      }
      if (userId_ != 0L) {
        output.writeInt64(3, userId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(4, getLord());
      }
      if (slaveCount_ != 0) {
        output.writeUInt32(5, slaveCount_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getCommonData());
      }
      if (userId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, userId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, getLord());
      }
      if (slaveCount_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(5, slaveCount_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.ConquerProto.ConquerPardonResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.ConquerProto.ConquerPardonResponse other = (com.dxx.game.dto.ConquerProto.ConquerPardonResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (hasCommonData() != other.hasCommonData()) return false;
      if (hasCommonData()) {
        if (!getCommonData()
            .equals(other.getCommonData())) return false;
      }
      if (getUserId()
          != other.getUserId()) return false;
      if (hasLord() != other.hasLord()) return false;
      if (hasLord()) {
        if (!getLord()
            .equals(other.getLord())) return false;
      }
      if (getSlaveCount()
          != other.getSlaveCount()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      if (hasCommonData()) {
        hash = (37 * hash) + COMMONDATA_FIELD_NUMBER;
        hash = (53 * hash) + getCommonData().hashCode();
      }
      hash = (37 * hash) + USERID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getUserId());
      if (hasLord()) {
        hash = (37 * hash) + LORD_FIELD_NUMBER;
        hash = (53 * hash) + getLord().hashCode();
      }
      hash = (37 * hash) + SLAVECOUNT_FIELD_NUMBER;
      hash = (53 * hash) + getSlaveCount();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.ConquerProto.ConquerPardonResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerPardonResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerPardonResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerPardonResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerPardonResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerPardonResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerPardonResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerPardonResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.ConquerProto.ConquerPardonResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.ConquerProto.ConquerPardonResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerPardonResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerPardonResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.ConquerProto.ConquerPardonResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=12810
     * </pre>
     *
     * Protobuf type {@code Proto.Conquer.ConquerPardonResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Conquer.ConquerPardonResponse)
        com.dxx.game.dto.ConquerProto.ConquerPardonResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.ConquerProto.internal_static_Proto_Conquer_ConquerPardonResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.ConquerProto.internal_static_Proto_Conquer_ConquerPardonResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.ConquerProto.ConquerPardonResponse.class, com.dxx.game.dto.ConquerProto.ConquerPardonResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.ConquerProto.ConquerPardonResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getCommonDataFieldBuilder();
          getLordFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        code_ = 0;
        commonData_ = null;
        if (commonDataBuilder_ != null) {
          commonDataBuilder_.dispose();
          commonDataBuilder_ = null;
        }
        userId_ = 0L;
        lord_ = null;
        if (lordBuilder_ != null) {
          lordBuilder_.dispose();
          lordBuilder_ = null;
        }
        slaveCount_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.ConquerProto.internal_static_Proto_Conquer_ConquerPardonResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.ConquerProto.ConquerPardonResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.ConquerProto.ConquerPardonResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.ConquerProto.ConquerPardonResponse build() {
        com.dxx.game.dto.ConquerProto.ConquerPardonResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.ConquerProto.ConquerPardonResponse buildPartial() {
        com.dxx.game.dto.ConquerProto.ConquerPardonResponse result = new com.dxx.game.dto.ConquerProto.ConquerPardonResponse(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.ConquerProto.ConquerPardonResponse result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.code_ = code_;
        }
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.commonData_ = commonDataBuilder_ == null
              ? commonData_
              : commonDataBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.userId_ = userId_;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.lord_ = lordBuilder_ == null
              ? lord_
              : lordBuilder_.build();
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.slaveCount_ = slaveCount_;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.ConquerProto.ConquerPardonResponse) {
          return mergeFrom((com.dxx.game.dto.ConquerProto.ConquerPardonResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.ConquerProto.ConquerPardonResponse other) {
        if (other == com.dxx.game.dto.ConquerProto.ConquerPardonResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.hasCommonData()) {
          mergeCommonData(other.getCommonData());
        }
        if (other.getUserId() != 0L) {
          setUserId(other.getUserId());
        }
        if (other.hasLord()) {
          mergeLord(other.getLord());
        }
        if (other.getSlaveCount() != 0) {
          setSlaveCount(other.getSlaveCount());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                code_ = input.readInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 18: {
                input.readMessage(
                    getCommonDataFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              case 24: {
                userId_ = input.readInt64();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              case 34: {
                input.readMessage(
                    getLordFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000008;
                break;
              } // case 34
              case 40: {
                slaveCount_ = input.readUInt32();
                bitField0_ |= 0x00000010;
                break;
              } // case 40
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {

        code_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        bitField0_ = (bitField0_ & ~0x00000001);
        code_ = 0;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonData commonData_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> commonDataBuilder_;
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return Whether the commonData field is set.
       */
      public boolean hasCommonData() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return The commonData.
       */
      public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
        if (commonDataBuilder_ == null) {
          return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        } else {
          return commonDataBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonData_ = value;
        } else {
          commonDataBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(
          com.dxx.game.dto.CommonProto.CommonData.Builder builderForValue) {
        if (commonDataBuilder_ == null) {
          commonData_ = builderForValue.build();
        } else {
          commonDataBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder mergeCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
            commonData_ != null &&
            commonData_ != com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance()) {
            getCommonDataBuilder().mergeFrom(value);
          } else {
            commonData_ = value;
          }
        } else {
          commonDataBuilder_.mergeFrom(value);
        }
        if (commonData_ != null) {
          bitField0_ |= 0x00000002;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder clearCommonData() {
        bitField0_ = (bitField0_ & ~0x00000002);
        commonData_ = null;
        if (commonDataBuilder_ != null) {
          commonDataBuilder_.dispose();
          commonDataBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonData.Builder getCommonDataBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getCommonDataFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
        if (commonDataBuilder_ != null) {
          return commonDataBuilder_.getMessageOrBuilder();
        } else {
          return commonData_ == null ?
              com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> 
          getCommonDataFieldBuilder() {
        if (commonDataBuilder_ == null) {
          commonDataBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder>(
                  getCommonData(),
                  getParentForChildren(),
                  isClean());
          commonData_ = null;
        }
        return commonDataBuilder_;
      }

      private long userId_ ;
      /**
       * <code>int64 userId = 3;</code>
       * @return The userId.
       */
      @java.lang.Override
      public long getUserId() {
        return userId_;
      }
      /**
       * <code>int64 userId = 3;</code>
       * @param value The userId to set.
       * @return This builder for chaining.
       */
      public Builder setUserId(long value) {

        userId_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>int64 userId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearUserId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        userId_ = 0L;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.CommonProto.LordDto lord_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.LordDto, com.dxx.game.dto.CommonProto.LordDto.Builder, com.dxx.game.dto.CommonProto.LordDtoOrBuilder> lordBuilder_;
      /**
       * <pre>
       * 领主信息
       * </pre>
       *
       * <code>.Proto.Common.LordDto lord = 4;</code>
       * @return Whether the lord field is set.
       */
      public boolean hasLord() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 领主信息
       * </pre>
       *
       * <code>.Proto.Common.LordDto lord = 4;</code>
       * @return The lord.
       */
      public com.dxx.game.dto.CommonProto.LordDto getLord() {
        if (lordBuilder_ == null) {
          return lord_ == null ? com.dxx.game.dto.CommonProto.LordDto.getDefaultInstance() : lord_;
        } else {
          return lordBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 领主信息
       * </pre>
       *
       * <code>.Proto.Common.LordDto lord = 4;</code>
       */
      public Builder setLord(com.dxx.game.dto.CommonProto.LordDto value) {
        if (lordBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          lord_ = value;
        } else {
          lordBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 领主信息
       * </pre>
       *
       * <code>.Proto.Common.LordDto lord = 4;</code>
       */
      public Builder setLord(
          com.dxx.game.dto.CommonProto.LordDto.Builder builderForValue) {
        if (lordBuilder_ == null) {
          lord_ = builderForValue.build();
        } else {
          lordBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 领主信息
       * </pre>
       *
       * <code>.Proto.Common.LordDto lord = 4;</code>
       */
      public Builder mergeLord(com.dxx.game.dto.CommonProto.LordDto value) {
        if (lordBuilder_ == null) {
          if (((bitField0_ & 0x00000008) != 0) &&
            lord_ != null &&
            lord_ != com.dxx.game.dto.CommonProto.LordDto.getDefaultInstance()) {
            getLordBuilder().mergeFrom(value);
          } else {
            lord_ = value;
          }
        } else {
          lordBuilder_.mergeFrom(value);
        }
        if (lord_ != null) {
          bitField0_ |= 0x00000008;
          onChanged();
        }
        return this;
      }
      /**
       * <pre>
       * 领主信息
       * </pre>
       *
       * <code>.Proto.Common.LordDto lord = 4;</code>
       */
      public Builder clearLord() {
        bitField0_ = (bitField0_ & ~0x00000008);
        lord_ = null;
        if (lordBuilder_ != null) {
          lordBuilder_.dispose();
          lordBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 领主信息
       * </pre>
       *
       * <code>.Proto.Common.LordDto lord = 4;</code>
       */
      public com.dxx.game.dto.CommonProto.LordDto.Builder getLordBuilder() {
        bitField0_ |= 0x00000008;
        onChanged();
        return getLordFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 领主信息
       * </pre>
       *
       * <code>.Proto.Common.LordDto lord = 4;</code>
       */
      public com.dxx.game.dto.CommonProto.LordDtoOrBuilder getLordOrBuilder() {
        if (lordBuilder_ != null) {
          return lordBuilder_.getMessageOrBuilder();
        } else {
          return lord_ == null ?
              com.dxx.game.dto.CommonProto.LordDto.getDefaultInstance() : lord_;
        }
      }
      /**
       * <pre>
       * 领主信息
       * </pre>
       *
       * <code>.Proto.Common.LordDto lord = 4;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.LordDto, com.dxx.game.dto.CommonProto.LordDto.Builder, com.dxx.game.dto.CommonProto.LordDtoOrBuilder> 
          getLordFieldBuilder() {
        if (lordBuilder_ == null) {
          lordBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.LordDto, com.dxx.game.dto.CommonProto.LordDto.Builder, com.dxx.game.dto.CommonProto.LordDtoOrBuilder>(
                  getLord(),
                  getParentForChildren(),
                  isClean());
          lord_ = null;
        }
        return lordBuilder_;
      }

      private int slaveCount_ ;
      /**
       * <pre>
       * 奴隶个数
       * </pre>
       *
       * <code>uint32 slaveCount = 5;</code>
       * @return The slaveCount.
       */
      @java.lang.Override
      public int getSlaveCount() {
        return slaveCount_;
      }
      /**
       * <pre>
       * 奴隶个数
       * </pre>
       *
       * <code>uint32 slaveCount = 5;</code>
       * @param value The slaveCount to set.
       * @return This builder for chaining.
       */
      public Builder setSlaveCount(int value) {

        slaveCount_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 奴隶个数
       * </pre>
       *
       * <code>uint32 slaveCount = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearSlaveCount() {
        bitField0_ = (bitField0_ & ~0x00000010);
        slaveCount_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Conquer.ConquerPardonResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.Conquer.ConquerPardonResponse)
    private static final com.dxx.game.dto.ConquerProto.ConquerPardonResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.ConquerProto.ConquerPardonResponse();
    }

    public static com.dxx.game.dto.ConquerProto.ConquerPardonResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ConquerPardonResponse>
        PARSER = new com.google.protobuf.AbstractParser<ConquerPardonResponse>() {
      @java.lang.Override
      public ConquerPardonResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<ConquerPardonResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ConquerPardonResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.ConquerProto.ConquerPardonResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ConquerUserDtoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Conquer.ConquerUserDto)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * userId
     * </pre>
     *
     * <code>int64 userId = 1;</code>
     * @return The userId.
     */
    long getUserId();

    /**
     * <pre>
     * 头像
     * </pre>
     *
     * <code>int32 avatar = 2;</code>
     * @return The avatar.
     */
    int getAvatar();

    /**
     * <pre>
     * 头像框
     * </pre>
     *
     * <code>int32 avatarFrame = 3;</code>
     * @return The avatarFrame.
     */
    int getAvatarFrame();

    /**
     * <pre>
     * 昵称
     * </pre>
     *
     * <code>string nickName = 4;</code>
     * @return The nickName.
     */
    java.lang.String getNickName();
    /**
     * <pre>
     * 昵称
     * </pre>
     *
     * <code>string nickName = 4;</code>
     * @return The bytes for nickName.
     */
    com.google.protobuf.ByteString
        getNickNameBytes();

    /**
     * <pre>
     * 战力
     * </pre>
     *
     * <code>uint32 power = 5;</code>
     * @return The power.
     */
    int getPower();

    /**
     * <pre>
     * 税收
     * </pre>
     *
     * <code>uint32 coin = 6;</code>
     * @return The coin.
     */
    int getCoin();
  }
  /**
   * Protobuf type {@code Proto.Conquer.ConquerUserDto}
   */
  public static final class ConquerUserDto extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Conquer.ConquerUserDto)
      ConquerUserDtoOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        ConquerUserDto.class.getName());
    }
    // Use ConquerUserDto.newBuilder() to construct.
    private ConquerUserDto(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private ConquerUserDto() {
      nickName_ = "";
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.ConquerProto.internal_static_Proto_Conquer_ConquerUserDto_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.ConquerProto.internal_static_Proto_Conquer_ConquerUserDto_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.ConquerProto.ConquerUserDto.class, com.dxx.game.dto.ConquerProto.ConquerUserDto.Builder.class);
    }

    public static final int USERID_FIELD_NUMBER = 1;
    private long userId_ = 0L;
    /**
     * <pre>
     * userId
     * </pre>
     *
     * <code>int64 userId = 1;</code>
     * @return The userId.
     */
    @java.lang.Override
    public long getUserId() {
      return userId_;
    }

    public static final int AVATAR_FIELD_NUMBER = 2;
    private int avatar_ = 0;
    /**
     * <pre>
     * 头像
     * </pre>
     *
     * <code>int32 avatar = 2;</code>
     * @return The avatar.
     */
    @java.lang.Override
    public int getAvatar() {
      return avatar_;
    }

    public static final int AVATARFRAME_FIELD_NUMBER = 3;
    private int avatarFrame_ = 0;
    /**
     * <pre>
     * 头像框
     * </pre>
     *
     * <code>int32 avatarFrame = 3;</code>
     * @return The avatarFrame.
     */
    @java.lang.Override
    public int getAvatarFrame() {
      return avatarFrame_;
    }

    public static final int NICKNAME_FIELD_NUMBER = 4;
    @SuppressWarnings("serial")
    private volatile java.lang.Object nickName_ = "";
    /**
     * <pre>
     * 昵称
     * </pre>
     *
     * <code>string nickName = 4;</code>
     * @return The nickName.
     */
    @java.lang.Override
    public java.lang.String getNickName() {
      java.lang.Object ref = nickName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        nickName_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 昵称
     * </pre>
     *
     * <code>string nickName = 4;</code>
     * @return The bytes for nickName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getNickNameBytes() {
      java.lang.Object ref = nickName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        nickName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int POWER_FIELD_NUMBER = 5;
    private int power_ = 0;
    /**
     * <pre>
     * 战力
     * </pre>
     *
     * <code>uint32 power = 5;</code>
     * @return The power.
     */
    @java.lang.Override
    public int getPower() {
      return power_;
    }

    public static final int COIN_FIELD_NUMBER = 6;
    private int coin_ = 0;
    /**
     * <pre>
     * 税收
     * </pre>
     *
     * <code>uint32 coin = 6;</code>
     * @return The coin.
     */
    @java.lang.Override
    public int getCoin() {
      return coin_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (userId_ != 0L) {
        output.writeInt64(1, userId_);
      }
      if (avatar_ != 0) {
        output.writeInt32(2, avatar_);
      }
      if (avatarFrame_ != 0) {
        output.writeInt32(3, avatarFrame_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(nickName_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 4, nickName_);
      }
      if (power_ != 0) {
        output.writeUInt32(5, power_);
      }
      if (coin_ != 0) {
        output.writeUInt32(6, coin_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (userId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, userId_);
      }
      if (avatar_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, avatar_);
      }
      if (avatarFrame_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, avatarFrame_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(nickName_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(4, nickName_);
      }
      if (power_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(5, power_);
      }
      if (coin_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(6, coin_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.ConquerProto.ConquerUserDto)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.ConquerProto.ConquerUserDto other = (com.dxx.game.dto.ConquerProto.ConquerUserDto) obj;

      if (getUserId()
          != other.getUserId()) return false;
      if (getAvatar()
          != other.getAvatar()) return false;
      if (getAvatarFrame()
          != other.getAvatarFrame()) return false;
      if (!getNickName()
          .equals(other.getNickName())) return false;
      if (getPower()
          != other.getPower()) return false;
      if (getCoin()
          != other.getCoin()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + USERID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getUserId());
      hash = (37 * hash) + AVATAR_FIELD_NUMBER;
      hash = (53 * hash) + getAvatar();
      hash = (37 * hash) + AVATARFRAME_FIELD_NUMBER;
      hash = (53 * hash) + getAvatarFrame();
      hash = (37 * hash) + NICKNAME_FIELD_NUMBER;
      hash = (53 * hash) + getNickName().hashCode();
      hash = (37 * hash) + POWER_FIELD_NUMBER;
      hash = (53 * hash) + getPower();
      hash = (37 * hash) + COIN_FIELD_NUMBER;
      hash = (53 * hash) + getCoin();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.ConquerProto.ConquerUserDto parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerUserDto parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerUserDto parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerUserDto parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerUserDto parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerUserDto parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerUserDto parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerUserDto parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.ConquerProto.ConquerUserDto parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.ConquerProto.ConquerUserDto parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerUserDto parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ConquerProto.ConquerUserDto parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.ConquerProto.ConquerUserDto prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code Proto.Conquer.ConquerUserDto}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Conquer.ConquerUserDto)
        com.dxx.game.dto.ConquerProto.ConquerUserDtoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.ConquerProto.internal_static_Proto_Conquer_ConquerUserDto_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.ConquerProto.internal_static_Proto_Conquer_ConquerUserDto_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.ConquerProto.ConquerUserDto.class, com.dxx.game.dto.ConquerProto.ConquerUserDto.Builder.class);
      }

      // Construct using com.dxx.game.dto.ConquerProto.ConquerUserDto.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        userId_ = 0L;
        avatar_ = 0;
        avatarFrame_ = 0;
        nickName_ = "";
        power_ = 0;
        coin_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.ConquerProto.internal_static_Proto_Conquer_ConquerUserDto_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.ConquerProto.ConquerUserDto getDefaultInstanceForType() {
        return com.dxx.game.dto.ConquerProto.ConquerUserDto.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.ConquerProto.ConquerUserDto build() {
        com.dxx.game.dto.ConquerProto.ConquerUserDto result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.ConquerProto.ConquerUserDto buildPartial() {
        com.dxx.game.dto.ConquerProto.ConquerUserDto result = new com.dxx.game.dto.ConquerProto.ConquerUserDto(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.ConquerProto.ConquerUserDto result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.userId_ = userId_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.avatar_ = avatar_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.avatarFrame_ = avatarFrame_;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.nickName_ = nickName_;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.power_ = power_;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.coin_ = coin_;
        }
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.ConquerProto.ConquerUserDto) {
          return mergeFrom((com.dxx.game.dto.ConquerProto.ConquerUserDto)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.ConquerProto.ConquerUserDto other) {
        if (other == com.dxx.game.dto.ConquerProto.ConquerUserDto.getDefaultInstance()) return this;
        if (other.getUserId() != 0L) {
          setUserId(other.getUserId());
        }
        if (other.getAvatar() != 0) {
          setAvatar(other.getAvatar());
        }
        if (other.getAvatarFrame() != 0) {
          setAvatarFrame(other.getAvatarFrame());
        }
        if (!other.getNickName().isEmpty()) {
          nickName_ = other.nickName_;
          bitField0_ |= 0x00000008;
          onChanged();
        }
        if (other.getPower() != 0) {
          setPower(other.getPower());
        }
        if (other.getCoin() != 0) {
          setCoin(other.getCoin());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                userId_ = input.readInt64();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                avatar_ = input.readInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 24: {
                avatarFrame_ = input.readInt32();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              case 34: {
                nickName_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000008;
                break;
              } // case 34
              case 40: {
                power_ = input.readUInt32();
                bitField0_ |= 0x00000010;
                break;
              } // case 40
              case 48: {
                coin_ = input.readUInt32();
                bitField0_ |= 0x00000020;
                break;
              } // case 48
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private long userId_ ;
      /**
       * <pre>
       * userId
       * </pre>
       *
       * <code>int64 userId = 1;</code>
       * @return The userId.
       */
      @java.lang.Override
      public long getUserId() {
        return userId_;
      }
      /**
       * <pre>
       * userId
       * </pre>
       *
       * <code>int64 userId = 1;</code>
       * @param value The userId to set.
       * @return This builder for chaining.
       */
      public Builder setUserId(long value) {

        userId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * userId
       * </pre>
       *
       * <code>int64 userId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearUserId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        userId_ = 0L;
        onChanged();
        return this;
      }

      private int avatar_ ;
      /**
       * <pre>
       * 头像
       * </pre>
       *
       * <code>int32 avatar = 2;</code>
       * @return The avatar.
       */
      @java.lang.Override
      public int getAvatar() {
        return avatar_;
      }
      /**
       * <pre>
       * 头像
       * </pre>
       *
       * <code>int32 avatar = 2;</code>
       * @param value The avatar to set.
       * @return This builder for chaining.
       */
      public Builder setAvatar(int value) {

        avatar_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 头像
       * </pre>
       *
       * <code>int32 avatar = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearAvatar() {
        bitField0_ = (bitField0_ & ~0x00000002);
        avatar_ = 0;
        onChanged();
        return this;
      }

      private int avatarFrame_ ;
      /**
       * <pre>
       * 头像框
       * </pre>
       *
       * <code>int32 avatarFrame = 3;</code>
       * @return The avatarFrame.
       */
      @java.lang.Override
      public int getAvatarFrame() {
        return avatarFrame_;
      }
      /**
       * <pre>
       * 头像框
       * </pre>
       *
       * <code>int32 avatarFrame = 3;</code>
       * @param value The avatarFrame to set.
       * @return This builder for chaining.
       */
      public Builder setAvatarFrame(int value) {

        avatarFrame_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 头像框
       * </pre>
       *
       * <code>int32 avatarFrame = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearAvatarFrame() {
        bitField0_ = (bitField0_ & ~0x00000004);
        avatarFrame_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object nickName_ = "";
      /**
       * <pre>
       * 昵称
       * </pre>
       *
       * <code>string nickName = 4;</code>
       * @return The nickName.
       */
      public java.lang.String getNickName() {
        java.lang.Object ref = nickName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          nickName_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 昵称
       * </pre>
       *
       * <code>string nickName = 4;</code>
       * @return The bytes for nickName.
       */
      public com.google.protobuf.ByteString
          getNickNameBytes() {
        java.lang.Object ref = nickName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          nickName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 昵称
       * </pre>
       *
       * <code>string nickName = 4;</code>
       * @param value The nickName to set.
       * @return This builder for chaining.
       */
      public Builder setNickName(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        nickName_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 昵称
       * </pre>
       *
       * <code>string nickName = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearNickName() {
        nickName_ = getDefaultInstance().getNickName();
        bitField0_ = (bitField0_ & ~0x00000008);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 昵称
       * </pre>
       *
       * <code>string nickName = 4;</code>
       * @param value The bytes for nickName to set.
       * @return This builder for chaining.
       */
      public Builder setNickNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        nickName_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }

      private int power_ ;
      /**
       * <pre>
       * 战力
       * </pre>
       *
       * <code>uint32 power = 5;</code>
       * @return The power.
       */
      @java.lang.Override
      public int getPower() {
        return power_;
      }
      /**
       * <pre>
       * 战力
       * </pre>
       *
       * <code>uint32 power = 5;</code>
       * @param value The power to set.
       * @return This builder for chaining.
       */
      public Builder setPower(int value) {

        power_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 战力
       * </pre>
       *
       * <code>uint32 power = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearPower() {
        bitField0_ = (bitField0_ & ~0x00000010);
        power_ = 0;
        onChanged();
        return this;
      }

      private int coin_ ;
      /**
       * <pre>
       * 税收
       * </pre>
       *
       * <code>uint32 coin = 6;</code>
       * @return The coin.
       */
      @java.lang.Override
      public int getCoin() {
        return coin_;
      }
      /**
       * <pre>
       * 税收
       * </pre>
       *
       * <code>uint32 coin = 6;</code>
       * @param value The coin to set.
       * @return This builder for chaining.
       */
      public Builder setCoin(int value) {

        coin_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 税收
       * </pre>
       *
       * <code>uint32 coin = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearCoin() {
        bitField0_ = (bitField0_ & ~0x00000020);
        coin_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Conquer.ConquerUserDto)
    }

    // @@protoc_insertion_point(class_scope:Proto.Conquer.ConquerUserDto)
    private static final com.dxx.game.dto.ConquerProto.ConquerUserDto DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.ConquerProto.ConquerUserDto();
    }

    public static com.dxx.game.dto.ConquerProto.ConquerUserDto getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ConquerUserDto>
        PARSER = new com.google.protobuf.AbstractParser<ConquerUserDto>() {
      @java.lang.Override
      public ConquerUserDto parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<ConquerUserDto> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ConquerUserDto> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.ConquerProto.ConquerUserDto getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Conquer_ConquerListRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Conquer_ConquerListRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Conquer_ConquerListResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Conquer_ConquerListResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Conquer_ConquerBattleRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Conquer_ConquerBattleRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Conquer_ConquerBattleResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Conquer_ConquerBattleResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Conquer_ConquerRevoltRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Conquer_ConquerRevoltRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Conquer_ConquerRevoltResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Conquer_ConquerRevoltResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Conquer_ConquerLootRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Conquer_ConquerLootRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Conquer_ConquerLootResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Conquer_ConquerLootResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Conquer_ConquerPardonRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Conquer_ConquerPardonRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Conquer_ConquerPardonResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Conquer_ConquerPardonResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Conquer_ConquerUserDto_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Conquer_ConquerUserDto_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\rconquer.proto\022\rProto.Conquer\032\014common.p" +
      "roto\"V\n\022ConquerListRequest\0220\n\014commonPara" +
      "ms\030\001 \001(\0132\032.Proto.Common.CommonParams\022\016\n\006" +
      "userId\030\002 \001(\003\"\353\001\n\023ConquerListResponse\022\014\n\004" +
      "code\030\001 \001(\005\022,\n\ncommonData\030\002 \001(\0132\030.Proto.C" +
      "ommon.CommonData\022\016\n\006userId\030\003 \001(\003\022,\n\005owne" +
      "r\030\004 \001(\0132\035.Proto.Conquer.ConquerUserDto\022+" +
      "\n\004lord\030\005 \001(\0132\035.Proto.Conquer.ConquerUser" +
      "Dto\022-\n\006slaves\030\006 \003(\0132\035.Proto.Conquer.Conq" +
      "uerUserDto\"o\n\024ConquerBattleRequest\0220\n\014co" +
      "mmonParams\030\001 \001(\0132\032.Proto.Common.CommonPa" +
      "rams\022\016\n\006userId\030\002 \001(\003\022\025\n\rclientVersion\030\003 " +
      "\001(\t\"\310\001\n\025ConquerBattleResponse\022\014\n\004code\030\001 " +
      "\001(\005\022,\n\ncommonData\030\002 \001(\0132\030.Proto.Common.C" +
      "ommonData\022\016\n\006userId\030\003 \001(\003\022#\n\004lord\030\004 \001(\0132" +
      "\025.Proto.Common.LordDto\022\022\n\nslaveCount\030\005 \001" +
      "(\r\022*\n\006record\030\006 \001(\0132\032.Proto.Common.PVPRec" +
      "ordDto\"o\n\024ConquerRevoltRequest\0220\n\014common" +
      "Params\030\001 \001(\0132\032.Proto.Common.CommonParams" +
      "\022\016\n\006userId\030\002 \001(\003\022\025\n\rclientVersion\030\003 \001(\t\"" +
      "\310\001\n\025ConquerRevoltResponse\022\014\n\004code\030\001 \001(\005\022" +
      ",\n\ncommonData\030\002 \001(\0132\030.Proto.Common.Commo" +
      "nData\022\016\n\006userId\030\003 \001(\003\022#\n\004lord\030\004 \001(\0132\025.Pr" +
      "oto.Common.LordDto\022\022\n\nslaveCount\030\005 \001(\r\022*" +
      "\n\006record\030\006 \001(\0132\032.Proto.Common.PVPRecordD" +
      "to\"m\n\022ConquerLootRequest\0220\n\014commonParams" +
      "\030\001 \001(\0132\032.Proto.Common.CommonParams\022\016\n\006us" +
      "erId\030\002 \001(\003\022\025\n\rclientVersion\030\003 \001(\t\"\306\001\n\023Co" +
      "nquerLootResponse\022\014\n\004code\030\001 \001(\005\022,\n\ncommo" +
      "nData\030\002 \001(\0132\030.Proto.Common.CommonData\022\016\n" +
      "\006userId\030\003 \001(\003\022#\n\004lord\030\004 \001(\0132\025.Proto.Comm" +
      "on.LordDto\022\022\n\nslaveCount\030\005 \001(\r\022*\n\006record" +
      "\030\006 \001(\0132\032.Proto.Common.PVPRecordDto\"o\n\024Co" +
      "nquerPardonRequest\0220\n\014commonParams\030\001 \001(\013" +
      "2\032.Proto.Common.CommonParams\022\016\n\006userId\030\002" +
      " \001(\003\022\025\n\rclientVersion\030\003 \001(\t\"\234\001\n\025ConquerP" +
      "ardonResponse\022\014\n\004code\030\001 \001(\005\022,\n\ncommonDat" +
      "a\030\002 \001(\0132\030.Proto.Common.CommonData\022\016\n\006use" +
      "rId\030\003 \001(\003\022#\n\004lord\030\004 \001(\0132\025.Proto.Common.L" +
      "ordDto\022\022\n\nslaveCount\030\005 \001(\r\"t\n\016ConquerUse" +
      "rDto\022\016\n\006userId\030\001 \001(\003\022\016\n\006avatar\030\002 \001(\005\022\023\n\013" +
      "avatarFrame\030\003 \001(\005\022\020\n\010nickName\030\004 \001(\t\022\r\n\005p" +
      "ower\030\005 \001(\r\022\014\n\004coin\030\006 \001(\rB \n\020com.dxx.game" +
      ".dtoB\014ConquerProtob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.dxx.game.dto.CommonProto.getDescriptor(),
        });
    internal_static_Proto_Conquer_ConquerListRequest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_Proto_Conquer_ConquerListRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Conquer_ConquerListRequest_descriptor,
        new java.lang.String[] { "CommonParams", "UserId", });
    internal_static_Proto_Conquer_ConquerListResponse_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_Proto_Conquer_ConquerListResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Conquer_ConquerListResponse_descriptor,
        new java.lang.String[] { "Code", "CommonData", "UserId", "Owner", "Lord", "Slaves", });
    internal_static_Proto_Conquer_ConquerBattleRequest_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_Proto_Conquer_ConquerBattleRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Conquer_ConquerBattleRequest_descriptor,
        new java.lang.String[] { "CommonParams", "UserId", "ClientVersion", });
    internal_static_Proto_Conquer_ConquerBattleResponse_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_Proto_Conquer_ConquerBattleResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Conquer_ConquerBattleResponse_descriptor,
        new java.lang.String[] { "Code", "CommonData", "UserId", "Lord", "SlaveCount", "Record", });
    internal_static_Proto_Conquer_ConquerRevoltRequest_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_Proto_Conquer_ConquerRevoltRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Conquer_ConquerRevoltRequest_descriptor,
        new java.lang.String[] { "CommonParams", "UserId", "ClientVersion", });
    internal_static_Proto_Conquer_ConquerRevoltResponse_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_Proto_Conquer_ConquerRevoltResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Conquer_ConquerRevoltResponse_descriptor,
        new java.lang.String[] { "Code", "CommonData", "UserId", "Lord", "SlaveCount", "Record", });
    internal_static_Proto_Conquer_ConquerLootRequest_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_Proto_Conquer_ConquerLootRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Conquer_ConquerLootRequest_descriptor,
        new java.lang.String[] { "CommonParams", "UserId", "ClientVersion", });
    internal_static_Proto_Conquer_ConquerLootResponse_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_Proto_Conquer_ConquerLootResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Conquer_ConquerLootResponse_descriptor,
        new java.lang.String[] { "Code", "CommonData", "UserId", "Lord", "SlaveCount", "Record", });
    internal_static_Proto_Conquer_ConquerPardonRequest_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_Proto_Conquer_ConquerPardonRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Conquer_ConquerPardonRequest_descriptor,
        new java.lang.String[] { "CommonParams", "UserId", "ClientVersion", });
    internal_static_Proto_Conquer_ConquerPardonResponse_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_Proto_Conquer_ConquerPardonResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Conquer_ConquerPardonResponse_descriptor,
        new java.lang.String[] { "Code", "CommonData", "UserId", "Lord", "SlaveCount", });
    internal_static_Proto_Conquer_ConquerUserDto_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_Proto_Conquer_ConquerUserDto_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Conquer_ConquerUserDto_descriptor,
        new java.lang.String[] { "UserId", "Avatar", "AvatarFrame", "NickName", "Power", "Coin", });
    descriptor.resolveAllFeaturesImmutable();
    com.dxx.game.dto.CommonProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
