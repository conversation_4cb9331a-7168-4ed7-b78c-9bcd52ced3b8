// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: dungeon.proto
// Protobuf Java Version: 4.29.0

package com.dxx.game.dto;

public final class DungeonProto {
  private DungeonProto() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 29,
      /* patch= */ 0,
      /* suffix= */ "",
      DungeonProto.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Dungeon_GoldExpDungeonInfo_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Dungeon_GoldExpDungeonInfo_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Dungeon_GoldExpDungeonStartRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Dungeon_GoldExpDungeonStartRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Dungeon_GoldExpDungeonStartResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Dungeon_GoldExpDungeonStartResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Dungeon_GoldExpDungeonEndRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Dungeon_GoldExpDungeonEndRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Dungeon_GoldExpDungeonEndResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Dungeon_GoldExpDungeonEndResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Dungeon_GoldExpDungeonAutoClearRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Dungeon_GoldExpDungeonAutoClearRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Dungeon_GoldExpDungeonAutoClearResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Dungeon_GoldExpDungeonAutoClearResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Dungeon_EquipDungeonInfo_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Dungeon_EquipDungeonInfo_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Dungeon_EquipDungeonStartRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Dungeon_EquipDungeonStartRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Dungeon_EquipDungeonStartResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Dungeon_EquipDungeonStartResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Dungeon_EquipDungeonEndRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Dungeon_EquipDungeonEndRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Dungeon_EquipDungeonEndResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Dungeon_EquipDungeonEndResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Dungeon_EquipDungeonAutoClearRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Dungeon_EquipDungeonAutoClearRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Dungeon_EquipDungeonAutoClearResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Dungeon_EquipDungeonAutoClearResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Dungeon_EquipDungeonUpgradeRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Dungeon_EquipDungeonUpgradeRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Dungeon_EquipDungeonUpgradeResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Dungeon_EquipDungeonUpgradeResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Dungeon_EquipDungeonDowngradeRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Dungeon_EquipDungeonDowngradeRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Dungeon_EquipDungeonDowngradeResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Dungeon_EquipDungeonDowngradeResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Dungeon_BossDungeonStartRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Dungeon_BossDungeonStartRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Dungeon_BossDungeonStartResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Dungeon_BossDungeonStartResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Dungeon_BossDungeonEndRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Dungeon_BossDungeonEndRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Dungeon_BossDungeonEndResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Dungeon_BossDungeonEndResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Dungeon_BossDungeonGetInfoRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Dungeon_BossDungeonGetInfoRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Dungeon_BossDungeonGetInfoResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Dungeon_BossDungeonGetInfoResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Dungeon_RuneDungeonStartRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Dungeon_RuneDungeonStartRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Dungeon_RuneDungeonStartResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Dungeon_RuneDungeonStartResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Dungeon_RuneDungeonEndRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Dungeon_RuneDungeonEndRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Dungeon_RuneDungeonEndResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Dungeon_RuneDungeonEndResponse_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\rdungeon.proto\022\rProto.Dungeon\032\014battle.p" +
      "roto\032\014common.proto\"b\n\022GoldExpDungeonInfo" +
      "\022(\n\004type\030\001 \001(\0162\032.Proto.Dungeon.GoldExpTy" +
      "pe\022\021\n\tcurrentId\030\002 \001(\005\022\017\n\007maxWave\030\003 \001(\005\"x" +
      "\n\032GoldExpDungeonStartRequest\0220\n\014commonPa" +
      "rams\030\001 \001(\0132\032.Proto.Common.CommonParams\022(" +
      "\n\004type\030\002 \001(\0162\032.Proto.Dungeon.GoldExpType" +
      "\"\215\001\n\033GoldExpDungeonStartResponse\022\014\n\004code" +
      "\030\001 \001(\005\022(\n\004type\030\002 \001(\0162\032.Proto.Dungeon.Gol" +
      "dExpType\0226\n\017battleStartInfo\030\003 \001(\0132\035.Prot" +
      "o.Battle.BattleStartInfo\"\267\001\n\030GoldExpDung" +
      "eonEndRequest\0220\n\014commonParams\030\001 \001(\0132\032.Pr" +
      "oto.Common.CommonParams\022(\n\004type\030\002 \001(\0162\032." +
      "Proto.Dungeon.GoldExpType\022-\n\006result\030\003 \001(" +
      "\0132\035.Proto.Battle.BattleResultDto\022\020\n\010comm" +
      "ands\030\004 \001(\014\"w\n\031GoldExpDungeonEndResponse\022" +
      "\014\n\004code\030\001 \001(\005\022(\n\004type\030\002 \001(\0162\032.Proto.Dung" +
      "eon.GoldExpType\022\021\n\tdungeonId\030\003 \001(\005\022\017\n\007ma" +
      "xWave\030\004 \001(\005\"|\n\036GoldExpDungeonAutoClearRe" +
      "quest\0220\n\014commonParams\030\001 \001(\0132\032.Proto.Comm" +
      "on.CommonParams\022(\n\004type\030\002 \001(\0162\032.Proto.Du" +
      "ngeon.GoldExpType\"Y\n\037GoldExpDungeonAutoC" +
      "learResponse\022\014\n\004code\030\001 \001(\005\022(\n\004type\030\002 \001(\016" +
      "2\032.Proto.Dungeon.GoldExpType\"o\n\020EquipDun" +
      "geonInfo\022\021\n\tcurrentId\030\001 \001(\005\022\025\n\rmaxPointT" +
      "oday\030\002 \001(\005\022\023\n\013totalPoints\030\003 \001(\005\022\034\n\024downg" +
      "radeCdStartTime\030\004 \001(\003\"L\n\030EquipDungeonSta" +
      "rtRequest\0220\n\014commonParams\030\001 \001(\0132\032.Proto." +
      "Common.CommonParams\"a\n\031EquipDungeonStart" +
      "Response\022\014\n\004code\030\001 \001(\005\0226\n\017battleStartInf" +
      "o\030\002 \001(\0132\035.Proto.Battle.BattleStartInfo\"\213" +
      "\001\n\026EquipDungeonEndRequest\0220\n\014commonParam" +
      "s\030\001 \001(\0132\032.Proto.Common.CommonParams\022-\n\006r" +
      "esult\030\002 \001(\0132\035.Proto.Battle.BattleResultD" +
      "to\022\020\n\010commands\030\003 \001(\014\"J\n\027EquipDungeonEndR" +
      "esponse\022\014\n\004code\030\001 \001(\005\022\r\n\005point\030\002 \001(\005\022\022\n\n" +
      "totalPoint\030\003 \001(\005\"P\n\034EquipDungeonAutoClea" +
      "rRequest\0220\n\014commonParams\030\001 \001(\0132\032.Proto.C" +
      "ommon.CommonParams\"P\n\035EquipDungeonAutoCl" +
      "earResponse\022\014\n\004code\030\001 \001(\005\022\r\n\005point\030\002 \001(\005" +
      "\022\022\n\ntotalPoint\030\003 \001(\005\"N\n\032EquipDungeonUpgr" +
      "adeRequest\0220\n\014commonParams\030\001 \001(\0132\032.Proto" +
      ".Common.CommonParams\">\n\033EquipDungeonUpgr" +
      "adeResponse\022\014\n\004code\030\001 \001(\005\022\021\n\tdungeonId\030\002" +
      " \001(\005\"P\n\034EquipDungeonDowngradeRequest\0220\n\014" +
      "commonParams\030\001 \001(\0132\032.Proto.Common.Common" +
      "Params\"\\\n\035EquipDungeonDowngradeResponse\022" +
      "\014\n\004code\030\001 \001(\005\022-\n\004info\030\002 \001(\0132\037.Proto.Dung" +
      "eon.EquipDungeonInfo\"K\n\027BossDungeonStart" +
      "Request\0220\n\014commonParams\030\001 \001(\0132\032.Proto.Co" +
      "mmon.CommonParams\"`\n\030BossDungeonStartRes" +
      "ponse\022\014\n\004code\030\001 \001(\005\0226\n\017battleStartInfo\030\002" +
      " \001(\0132\035.Proto.Battle.BattleStartInfo\"\212\001\n\025" +
      "BossDungeonEndRequest\0220\n\014commonParams\030\001 " +
      "\001(\0132\032.Proto.Common.CommonParams\022-\n\006resul" +
      "t\030\002 \001(\0132\035.Proto.Battle.BattleResultDto\022\020" +
      "\n\010commands\030\003 \001(\014\"u\n\026BossDungeonEndRespon" +
      "se\022\014\n\004code\030\001 \001(\005\022\026\n\016bossTemplateId\030\002 \001(\005" +
      "\022\016\n\006damage\030\003 \001(\003\022\022\n\nbeforeRank\030\004 \001(\005\022\021\n\t" +
      "afterRank\030\005 \001(\005\"M\n\031BossDungeonGetInfoReq" +
      "uest\0220\n\014commonParams\030\001 \001(\0132\032.Proto.Commo" +
      "n.CommonParams\"U\n\032BossDungeonGetInfoResp" +
      "onse\022\026\n\016bossTemplateId\030\001 \001(\005\022\021\n\tmaxDamag" +
      "e\030\002 \001(\003\022\014\n\004rank\030\003 \001(\005\"b\n\027RuneDungeonStar" +
      "tRequest\0220\n\014commonParams\030\001 \001(\0132\032.Proto.C" +
      "ommon.CommonParams\022\025\n\rruneDungeonId\030\002 \001(" +
      "\005\"`\n\030RuneDungeonStartResponse\022\014\n\004code\030\001 " +
      "\001(\005\0226\n\017battleStartInfo\030\002 \001(\0132\035.Proto.Bat" +
      "tle.BattleStartInfo\"\212\001\n\025RuneDungeonEndRe" +
      "quest\0220\n\014commonParams\030\001 \001(\0132\032.Proto.Comm" +
      "on.CommonParams\022-\n\006result\030\002 \001(\0132\035.Proto." +
      "Battle.BattleResultDto\022\020\n\010commands\030\003 \001(\014" +
      "\":\n\026RuneDungeonEndResponse\022\014\n\004code\030\001 \001(\005" +
      "\022\022\n\npassedWave\030\002 \001(\005* \n\013GoldExpType\022\010\n\004G" +
      "OLD\020\000\022\007\n\003EXP\020\001B\"\n\020com.dxx.game.dtoB\014Dung" +
      "eonProtoP\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.dxx.game.dto.BattleProto.getDescriptor(),
          com.dxx.game.dto.CommonProto.getDescriptor(),
        });
    internal_static_Proto_Dungeon_GoldExpDungeonInfo_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_Proto_Dungeon_GoldExpDungeonInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Dungeon_GoldExpDungeonInfo_descriptor,
        new java.lang.String[] { "Type", "CurrentId", "MaxWave", });
    internal_static_Proto_Dungeon_GoldExpDungeonStartRequest_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_Proto_Dungeon_GoldExpDungeonStartRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Dungeon_GoldExpDungeonStartRequest_descriptor,
        new java.lang.String[] { "CommonParams", "Type", });
    internal_static_Proto_Dungeon_GoldExpDungeonStartResponse_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_Proto_Dungeon_GoldExpDungeonStartResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Dungeon_GoldExpDungeonStartResponse_descriptor,
        new java.lang.String[] { "Code", "Type", "BattleStartInfo", });
    internal_static_Proto_Dungeon_GoldExpDungeonEndRequest_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_Proto_Dungeon_GoldExpDungeonEndRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Dungeon_GoldExpDungeonEndRequest_descriptor,
        new java.lang.String[] { "CommonParams", "Type", "Result", "Commands", });
    internal_static_Proto_Dungeon_GoldExpDungeonEndResponse_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_Proto_Dungeon_GoldExpDungeonEndResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Dungeon_GoldExpDungeonEndResponse_descriptor,
        new java.lang.String[] { "Code", "Type", "DungeonId", "MaxWave", });
    internal_static_Proto_Dungeon_GoldExpDungeonAutoClearRequest_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_Proto_Dungeon_GoldExpDungeonAutoClearRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Dungeon_GoldExpDungeonAutoClearRequest_descriptor,
        new java.lang.String[] { "CommonParams", "Type", });
    internal_static_Proto_Dungeon_GoldExpDungeonAutoClearResponse_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_Proto_Dungeon_GoldExpDungeonAutoClearResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Dungeon_GoldExpDungeonAutoClearResponse_descriptor,
        new java.lang.String[] { "Code", "Type", });
    internal_static_Proto_Dungeon_EquipDungeonInfo_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_Proto_Dungeon_EquipDungeonInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Dungeon_EquipDungeonInfo_descriptor,
        new java.lang.String[] { "CurrentId", "MaxPointToday", "TotalPoints", "DowngradeCdStartTime", });
    internal_static_Proto_Dungeon_EquipDungeonStartRequest_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_Proto_Dungeon_EquipDungeonStartRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Dungeon_EquipDungeonStartRequest_descriptor,
        new java.lang.String[] { "CommonParams", });
    internal_static_Proto_Dungeon_EquipDungeonStartResponse_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_Proto_Dungeon_EquipDungeonStartResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Dungeon_EquipDungeonStartResponse_descriptor,
        new java.lang.String[] { "Code", "BattleStartInfo", });
    internal_static_Proto_Dungeon_EquipDungeonEndRequest_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_Proto_Dungeon_EquipDungeonEndRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Dungeon_EquipDungeonEndRequest_descriptor,
        new java.lang.String[] { "CommonParams", "Result", "Commands", });
    internal_static_Proto_Dungeon_EquipDungeonEndResponse_descriptor =
      getDescriptor().getMessageTypes().get(11);
    internal_static_Proto_Dungeon_EquipDungeonEndResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Dungeon_EquipDungeonEndResponse_descriptor,
        new java.lang.String[] { "Code", "Point", "TotalPoint", });
    internal_static_Proto_Dungeon_EquipDungeonAutoClearRequest_descriptor =
      getDescriptor().getMessageTypes().get(12);
    internal_static_Proto_Dungeon_EquipDungeonAutoClearRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Dungeon_EquipDungeonAutoClearRequest_descriptor,
        new java.lang.String[] { "CommonParams", });
    internal_static_Proto_Dungeon_EquipDungeonAutoClearResponse_descriptor =
      getDescriptor().getMessageTypes().get(13);
    internal_static_Proto_Dungeon_EquipDungeonAutoClearResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Dungeon_EquipDungeonAutoClearResponse_descriptor,
        new java.lang.String[] { "Code", "Point", "TotalPoint", });
    internal_static_Proto_Dungeon_EquipDungeonUpgradeRequest_descriptor =
      getDescriptor().getMessageTypes().get(14);
    internal_static_Proto_Dungeon_EquipDungeonUpgradeRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Dungeon_EquipDungeonUpgradeRequest_descriptor,
        new java.lang.String[] { "CommonParams", });
    internal_static_Proto_Dungeon_EquipDungeonUpgradeResponse_descriptor =
      getDescriptor().getMessageTypes().get(15);
    internal_static_Proto_Dungeon_EquipDungeonUpgradeResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Dungeon_EquipDungeonUpgradeResponse_descriptor,
        new java.lang.String[] { "Code", "DungeonId", });
    internal_static_Proto_Dungeon_EquipDungeonDowngradeRequest_descriptor =
      getDescriptor().getMessageTypes().get(16);
    internal_static_Proto_Dungeon_EquipDungeonDowngradeRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Dungeon_EquipDungeonDowngradeRequest_descriptor,
        new java.lang.String[] { "CommonParams", });
    internal_static_Proto_Dungeon_EquipDungeonDowngradeResponse_descriptor =
      getDescriptor().getMessageTypes().get(17);
    internal_static_Proto_Dungeon_EquipDungeonDowngradeResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Dungeon_EquipDungeonDowngradeResponse_descriptor,
        new java.lang.String[] { "Code", "Info", });
    internal_static_Proto_Dungeon_BossDungeonStartRequest_descriptor =
      getDescriptor().getMessageTypes().get(18);
    internal_static_Proto_Dungeon_BossDungeonStartRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Dungeon_BossDungeonStartRequest_descriptor,
        new java.lang.String[] { "CommonParams", });
    internal_static_Proto_Dungeon_BossDungeonStartResponse_descriptor =
      getDescriptor().getMessageTypes().get(19);
    internal_static_Proto_Dungeon_BossDungeonStartResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Dungeon_BossDungeonStartResponse_descriptor,
        new java.lang.String[] { "Code", "BattleStartInfo", });
    internal_static_Proto_Dungeon_BossDungeonEndRequest_descriptor =
      getDescriptor().getMessageTypes().get(20);
    internal_static_Proto_Dungeon_BossDungeonEndRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Dungeon_BossDungeonEndRequest_descriptor,
        new java.lang.String[] { "CommonParams", "Result", "Commands", });
    internal_static_Proto_Dungeon_BossDungeonEndResponse_descriptor =
      getDescriptor().getMessageTypes().get(21);
    internal_static_Proto_Dungeon_BossDungeonEndResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Dungeon_BossDungeonEndResponse_descriptor,
        new java.lang.String[] { "Code", "BossTemplateId", "Damage", "BeforeRank", "AfterRank", });
    internal_static_Proto_Dungeon_BossDungeonGetInfoRequest_descriptor =
      getDescriptor().getMessageTypes().get(22);
    internal_static_Proto_Dungeon_BossDungeonGetInfoRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Dungeon_BossDungeonGetInfoRequest_descriptor,
        new java.lang.String[] { "CommonParams", });
    internal_static_Proto_Dungeon_BossDungeonGetInfoResponse_descriptor =
      getDescriptor().getMessageTypes().get(23);
    internal_static_Proto_Dungeon_BossDungeonGetInfoResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Dungeon_BossDungeonGetInfoResponse_descriptor,
        new java.lang.String[] { "BossTemplateId", "MaxDamage", "Rank", });
    internal_static_Proto_Dungeon_RuneDungeonStartRequest_descriptor =
      getDescriptor().getMessageTypes().get(24);
    internal_static_Proto_Dungeon_RuneDungeonStartRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Dungeon_RuneDungeonStartRequest_descriptor,
        new java.lang.String[] { "CommonParams", "RuneDungeonId", });
    internal_static_Proto_Dungeon_RuneDungeonStartResponse_descriptor =
      getDescriptor().getMessageTypes().get(25);
    internal_static_Proto_Dungeon_RuneDungeonStartResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Dungeon_RuneDungeonStartResponse_descriptor,
        new java.lang.String[] { "Code", "BattleStartInfo", });
    internal_static_Proto_Dungeon_RuneDungeonEndRequest_descriptor =
      getDescriptor().getMessageTypes().get(26);
    internal_static_Proto_Dungeon_RuneDungeonEndRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Dungeon_RuneDungeonEndRequest_descriptor,
        new java.lang.String[] { "CommonParams", "Result", "Commands", });
    internal_static_Proto_Dungeon_RuneDungeonEndResponse_descriptor =
      getDescriptor().getMessageTypes().get(27);
    internal_static_Proto_Dungeon_RuneDungeonEndResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Dungeon_RuneDungeonEndResponse_descriptor,
        new java.lang.String[] { "Code", "PassedWave", });
    descriptor.resolveAllFeaturesImmutable();
    com.dxx.game.dto.BattleProto.getDescriptor();
    com.dxx.game.dto.CommonProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
