// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: fakelevel.proto
// Protobuf Java Version: 4.29.0

package com.dxx.game.dto;

public final class FakeLevelProto {
  private FakeLevelProto() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 29,
      /* patch= */ 0,
      /* suffix= */ "",
      FakeLevelProto.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_FakeLevel_FakeLevelInfo_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_FakeLevel_FakeLevelInfo_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_FakeLevel_FakeLevelGetRewardRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_FakeLevel_FakeLevelGetRewardRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_FakeLevel_FakeLevelGetRewardResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_FakeLevel_FakeLevelGetRewardResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_FakeLevel_FakeLevelGetAFKRewardRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_FakeLevel_FakeLevelGetAFKRewardRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_FakeLevel_FakeLevelGetAFKRewardResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_FakeLevel_FakeLevelGetAFKRewardResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_FakeLevel_LevelStartRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_FakeLevel_LevelStartRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_FakeLevel_LevelStartResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_FakeLevel_LevelStartResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_FakeLevel_LevelEndRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_FakeLevel_LevelEndRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_FakeLevel_LevelEndResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_FakeLevel_LevelEndResponse_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\017fakelevel.proto\022\017Proto.FakeLevel\032\014batt" +
      "le.proto\032\014common.proto\"\301\001\n\rFakeLevelInfo" +
      "\022\017\n\007levelId\030\001 \001(\005\022\034\n\024maxFinishedWaveInde" +
      "x\030\002 \001(\005\022\026\n\016pveRewardIndex\030\003 \001(\005\022\034\n\024lastG" +
      "etAfkRewardTime\030\004 \001(\003\022\"\n\032todayFastGetAfk" +
      "RewardCount\030\005 \001(\005\022\'\n\037lastResetFastAkfRew" +
      "ardCountTime\030\006 \001(\003\"_\n\031FakeLevelGetReward" +
      "Request\0220\n\014commonParams\030\001 \001(\0132\032.Proto.Co" +
      "mmon.CommonParams\022\020\n\010rewardId\030\002 \001(\005\"X\n\032F" +
      "akeLevelGetRewardResponse\022\014\n\004code\030\001 \001(\005\022" +
      ",\n\ncommonData\030\002 \001(\0132\030.Proto.Common.Commo" +
      "nData\"w\n\034FakeLevelGetAFKRewardRequest\0220\n" +
      "\014commonParams\030\001 \001(\0132\032.Proto.Common.Commo" +
      "nParams\022\016\n\006isFast\030\002 \001(\010\022\025\n\risFastWatchAd" +
      "\030\003 \001(\010\"\367\001\n\035FakeLevelGetAFKRewardResponse" +
      "\022\014\n\004code\030\001 \001(\005\022,\n\ncommonData\030\002 \001(\0132\030.Pro" +
      "to.Common.CommonData\022\034\n\024lastGetAfkReward" +
      "Time\030\003 \001(\003\022\"\n\032todayFastGetAfkRewardCount" +
      "\030\004 \001(\005\022\'\n\037lastResetFastAkfRewardCountTim" +
      "e\030\005 \001(\003\022/\n\016dropRewardList\030\006 \003(\0132\027.Proto." +
      "Common.RewardDto\"V\n\021LevelStartRequest\0220\n" +
      "\014commonParams\030\001 \001(\0132\032.Proto.Common.Commo" +
      "nParams\022\017\n\007levelId\030\002 \001(\005\"\210\001\n\022LevelStartR" +
      "esponse\022\014\n\004code\030\001 \001(\005\022,\n\ncommonData\030\002 \001(" +
      "\0132\030.Proto.Common.CommonData\0226\n\017battleSta" +
      "rtInfo\030\003 \001(\0132\035.Proto.Battle.BattleStartI" +
      "nfo\"\204\001\n\017LevelEndRequest\0220\n\014commonParams\030" +
      "\001 \001(\0132\032.Proto.Common.CommonParams\022-\n\006res" +
      "ult\030\002 \001(\0132\035.Proto.Battle.BattleResultDto" +
      "\022\020\n\010commands\030\003 \001(\014\"x\n\020LevelEndResponse\022\014" +
      "\n\004code\030\001 \001(\005\022\022\n\nmaxLevelId\030\002 \001(\005\022\024\n\014maxW" +
      "aveIndex\030\003 \001(\005\022,\n\ncommonData\030\004 \001(\0132\030.Pro" +
      "to.Common.CommonDataB$\n\020com.dxx.game.dto" +
      "B\016FakeLevelProtoP\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.dxx.game.dto.BattleProto.getDescriptor(),
          com.dxx.game.dto.CommonProto.getDescriptor(),
        });
    internal_static_Proto_FakeLevel_FakeLevelInfo_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_Proto_FakeLevel_FakeLevelInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_FakeLevel_FakeLevelInfo_descriptor,
        new java.lang.String[] { "LevelId", "MaxFinishedWaveIndex", "PveRewardIndex", "LastGetAfkRewardTime", "TodayFastGetAfkRewardCount", "LastResetFastAkfRewardCountTime", });
    internal_static_Proto_FakeLevel_FakeLevelGetRewardRequest_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_Proto_FakeLevel_FakeLevelGetRewardRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_FakeLevel_FakeLevelGetRewardRequest_descriptor,
        new java.lang.String[] { "CommonParams", "RewardId", });
    internal_static_Proto_FakeLevel_FakeLevelGetRewardResponse_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_Proto_FakeLevel_FakeLevelGetRewardResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_FakeLevel_FakeLevelGetRewardResponse_descriptor,
        new java.lang.String[] { "Code", "CommonData", });
    internal_static_Proto_FakeLevel_FakeLevelGetAFKRewardRequest_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_Proto_FakeLevel_FakeLevelGetAFKRewardRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_FakeLevel_FakeLevelGetAFKRewardRequest_descriptor,
        new java.lang.String[] { "CommonParams", "IsFast", "IsFastWatchAd", });
    internal_static_Proto_FakeLevel_FakeLevelGetAFKRewardResponse_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_Proto_FakeLevel_FakeLevelGetAFKRewardResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_FakeLevel_FakeLevelGetAFKRewardResponse_descriptor,
        new java.lang.String[] { "Code", "CommonData", "LastGetAfkRewardTime", "TodayFastGetAfkRewardCount", "LastResetFastAkfRewardCountTime", "DropRewardList", });
    internal_static_Proto_FakeLevel_LevelStartRequest_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_Proto_FakeLevel_LevelStartRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_FakeLevel_LevelStartRequest_descriptor,
        new java.lang.String[] { "CommonParams", "LevelId", });
    internal_static_Proto_FakeLevel_LevelStartResponse_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_Proto_FakeLevel_LevelStartResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_FakeLevel_LevelStartResponse_descriptor,
        new java.lang.String[] { "Code", "CommonData", "BattleStartInfo", });
    internal_static_Proto_FakeLevel_LevelEndRequest_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_Proto_FakeLevel_LevelEndRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_FakeLevel_LevelEndRequest_descriptor,
        new java.lang.String[] { "CommonParams", "Result", "Commands", });
    internal_static_Proto_FakeLevel_LevelEndResponse_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_Proto_FakeLevel_LevelEndResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_FakeLevel_LevelEndResponse_descriptor,
        new java.lang.String[] { "Code", "MaxLevelId", "MaxWaveIndex", "CommonData", });
    descriptor.resolveAllFeaturesImmutable();
    com.dxx.game.dto.BattleProto.getDescriptor();
    com.dxx.game.dto.CommonProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
