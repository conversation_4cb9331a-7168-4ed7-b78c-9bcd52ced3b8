// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: dungeon.proto
// Protobuf Java Version: 4.29.0

package com.dxx.game.dto;

/**
 * Protobuf enum {@code Proto.Dungeon.GoldExpType}
 */
public enum GoldExpType
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <code>GOLD = 0;</code>
   */
  GOLD(0),
  /**
   * <code>EXP = 1;</code>
   */
  EXP(1),
  UNRECOGNIZED(-1),
  ;

  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 29,
      /* patch= */ 0,
      /* suffix= */ "",
      GoldExpType.class.getName());
  }
  /**
   * <code>GOLD = 0;</code>
   */
  public static final int GOLD_VALUE = 0;
  /**
   * <code>EXP = 1;</code>
   */
  public static final int EXP_VALUE = 1;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static GoldExpType valueOf(int value) {
    return forNumber(value);
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   */
  public static GoldExpType forNumber(int value) {
    switch (value) {
      case 0: return GOLD;
      case 1: return EXP;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<GoldExpType>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      GoldExpType> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<GoldExpType>() {
          public GoldExpType findValueByNumber(int number) {
            return GoldExpType.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalStateException(
          "Can't get the descriptor of an unrecognized enum value.");
    }
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return com.dxx.game.dto.DungeonProto.getDescriptor().getEnumTypes().get(0);
  }

  private static final GoldExpType[] VALUES = values();

  public static GoldExpType valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private GoldExpType(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:Proto.Dungeon.GoldExpType)
}

