// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: hero.proto
// Protobuf Java Version: 4.29.0

package com.dxx.game.dto;

/**
 * Protobuf type {@code Proto.Hero.HeroSkinDto}
 */
public final class HeroSkinDto extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:Proto.Hero.HeroSkinDto)
    HeroSkinDtoOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 29,
      /* patch= */ 0,
      /* suffix= */ "",
      HeroSkinDto.class.getName());
  }
  // Use HeroSkinDto.newBuilder() to construct.
  private HeroSkinDto(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private HeroSkinDto() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.dxx.game.dto.HeroProto.internal_static_Proto_Hero_HeroSkinDto_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.dxx.game.dto.HeroProto.internal_static_Proto_Hero_HeroSkinDto_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.dxx.game.dto.HeroSkinDto.class, com.dxx.game.dto.HeroSkinDto.Builder.class);
  }

  public static final int SKINFLAG_FIELD_NUMBER = 1;
  private long skinFlag_ = 0L;
  /**
   * <code>int64 skinFlag = 1;</code>
   * @return The skinFlag.
   */
  @java.lang.Override
  public long getSkinFlag() {
    return skinFlag_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (skinFlag_ != 0L) {
      output.writeInt64(1, skinFlag_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (skinFlag_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(1, skinFlag_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.dxx.game.dto.HeroSkinDto)) {
      return super.equals(obj);
    }
    com.dxx.game.dto.HeroSkinDto other = (com.dxx.game.dto.HeroSkinDto) obj;

    if (getSkinFlag()
        != other.getSkinFlag()) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + SKINFLAG_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getSkinFlag());
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.dxx.game.dto.HeroSkinDto parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.dxx.game.dto.HeroSkinDto parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.dxx.game.dto.HeroSkinDto parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.dxx.game.dto.HeroSkinDto parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.dxx.game.dto.HeroSkinDto parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.dxx.game.dto.HeroSkinDto parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.dxx.game.dto.HeroSkinDto parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static com.dxx.game.dto.HeroSkinDto parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.dxx.game.dto.HeroSkinDto parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.dxx.game.dto.HeroSkinDto parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.dxx.game.dto.HeroSkinDto parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static com.dxx.game.dto.HeroSkinDto parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.dxx.game.dto.HeroSkinDto prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code Proto.Hero.HeroSkinDto}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:Proto.Hero.HeroSkinDto)
      com.dxx.game.dto.HeroSkinDtoOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.HeroProto.internal_static_Proto_Hero_HeroSkinDto_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.HeroProto.internal_static_Proto_Hero_HeroSkinDto_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.HeroSkinDto.class, com.dxx.game.dto.HeroSkinDto.Builder.class);
    }

    // Construct using com.dxx.game.dto.HeroSkinDto.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      skinFlag_ = 0L;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.dxx.game.dto.HeroProto.internal_static_Proto_Hero_HeroSkinDto_descriptor;
    }

    @java.lang.Override
    public com.dxx.game.dto.HeroSkinDto getDefaultInstanceForType() {
      return com.dxx.game.dto.HeroSkinDto.getDefaultInstance();
    }

    @java.lang.Override
    public com.dxx.game.dto.HeroSkinDto build() {
      com.dxx.game.dto.HeroSkinDto result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.dxx.game.dto.HeroSkinDto buildPartial() {
      com.dxx.game.dto.HeroSkinDto result = new com.dxx.game.dto.HeroSkinDto(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(com.dxx.game.dto.HeroSkinDto result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.skinFlag_ = skinFlag_;
      }
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.dxx.game.dto.HeroSkinDto) {
        return mergeFrom((com.dxx.game.dto.HeroSkinDto)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.dxx.game.dto.HeroSkinDto other) {
      if (other == com.dxx.game.dto.HeroSkinDto.getDefaultInstance()) return this;
      if (other.getSkinFlag() != 0L) {
        setSkinFlag(other.getSkinFlag());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              skinFlag_ = input.readInt64();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private long skinFlag_ ;
    /**
     * <code>int64 skinFlag = 1;</code>
     * @return The skinFlag.
     */
    @java.lang.Override
    public long getSkinFlag() {
      return skinFlag_;
    }
    /**
     * <code>int64 skinFlag = 1;</code>
     * @param value The skinFlag to set.
     * @return This builder for chaining.
     */
    public Builder setSkinFlag(long value) {

      skinFlag_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>int64 skinFlag = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearSkinFlag() {
      bitField0_ = (bitField0_ & ~0x00000001);
      skinFlag_ = 0L;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:Proto.Hero.HeroSkinDto)
  }

  // @@protoc_insertion_point(class_scope:Proto.Hero.HeroSkinDto)
  private static final com.dxx.game.dto.HeroSkinDto DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.dxx.game.dto.HeroSkinDto();
  }

  public static com.dxx.game.dto.HeroSkinDto getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<HeroSkinDto>
      PARSER = new com.google.protobuf.AbstractParser<HeroSkinDto>() {
    @java.lang.Override
    public HeroSkinDto parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<HeroSkinDto> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<HeroSkinDto> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.dxx.game.dto.HeroSkinDto getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

