// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: item.proto
// Protobuf Java Version: 4.29.0

package com.dxx.game.dto;

public final class ItemProto {
  private ItemProto() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 29,
      /* patch= */ 0,
      /* suffix= */ "",
      ItemProto.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ItemUseRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Item.ItemUseRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();

    /**
     * <pre>
     * 道具rowID
     * </pre>
     *
     * <code>int32 itemId = 2;</code>
     * @return The itemId.
     */
    int getItemId();

    /**
     * <pre>
     * 使用数量
     * </pre>
     *
     * <code>int32 count = 3;</code>
     * @return The count.
     */
    int getCount();

    /**
     * <pre>
     * key: index, value: count 道具类型为5：自选礼包时，选择道具的参数，主要是一次使用多个箱子同时选择多个种类
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; selectParams = 4;</code>
     */
    int getSelectParamsCount();
    /**
     * <pre>
     * key: index, value: count 道具类型为5：自选礼包时，选择道具的参数，主要是一次使用多个箱子同时选择多个种类
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; selectParams = 4;</code>
     */
    boolean containsSelectParams(
        int key);
    /**
     * Use {@link #getSelectParamsMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Integer, java.lang.Integer>
    getSelectParams();
    /**
     * <pre>
     * key: index, value: count 道具类型为5：自选礼包时，选择道具的参数，主要是一次使用多个箱子同时选择多个种类
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; selectParams = 4;</code>
     */
    java.util.Map<java.lang.Integer, java.lang.Integer>
    getSelectParamsMap();
    /**
     * <pre>
     * key: index, value: count 道具类型为5：自选礼包时，选择道具的参数，主要是一次使用多个箱子同时选择多个种类
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; selectParams = 4;</code>
     */
    int getSelectParamsOrDefault(
        int key,
        int defaultValue);
    /**
     * <pre>
     * key: index, value: count 道具类型为5：自选礼包时，选择道具的参数，主要是一次使用多个箱子同时选择多个种类
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; selectParams = 4;</code>
     */
    int getSelectParamsOrThrow(
        int key);
  }
  /**
   * <pre>
   * CMD PackageId=10201 道具-使用
   * </pre>
   *
   * Protobuf type {@code Proto.Item.ItemUseRequest}
   */
  public static final class ItemUseRequest extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Item.ItemUseRequest)
      ItemUseRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        ItemUseRequest.class.getName());
    }
    // Use ItemUseRequest.newBuilder() to construct.
    private ItemUseRequest(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private ItemUseRequest() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.ItemProto.internal_static_Proto_Item_ItemUseRequest_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapFieldReflectionAccessor internalGetMapFieldReflection(
        int number) {
      switch (number) {
        case 4:
          return internalGetSelectParams();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.ItemProto.internal_static_Proto_Item_ItemUseRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.ItemProto.ItemUseRequest.class, com.dxx.game.dto.ItemProto.ItemUseRequest.Builder.class);
    }

    private int bitField0_;
    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }

    public static final int ITEMID_FIELD_NUMBER = 2;
    private int itemId_ = 0;
    /**
     * <pre>
     * 道具rowID
     * </pre>
     *
     * <code>int32 itemId = 2;</code>
     * @return The itemId.
     */
    @java.lang.Override
    public int getItemId() {
      return itemId_;
    }

    public static final int COUNT_FIELD_NUMBER = 3;
    private int count_ = 0;
    /**
     * <pre>
     * 使用数量
     * </pre>
     *
     * <code>int32 count = 3;</code>
     * @return The count.
     */
    @java.lang.Override
    public int getCount() {
      return count_;
    }

    public static final int SELECTPARAMS_FIELD_NUMBER = 4;
    private static final class SelectParamsDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Integer, java.lang.Integer> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Integer, java.lang.Integer>newDefaultInstance(
                  com.dxx.game.dto.ItemProto.internal_static_Proto_Item_ItemUseRequest_SelectParamsEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0);
    }
    @SuppressWarnings("serial")
    private com.google.protobuf.MapField<
        java.lang.Integer, java.lang.Integer> selectParams_;
    private com.google.protobuf.MapField<java.lang.Integer, java.lang.Integer>
    internalGetSelectParams() {
      if (selectParams_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            SelectParamsDefaultEntryHolder.defaultEntry);
      }
      return selectParams_;
    }
    public int getSelectParamsCount() {
      return internalGetSelectParams().getMap().size();
    }
    /**
     * <pre>
     * key: index, value: count 道具类型为5：自选礼包时，选择道具的参数，主要是一次使用多个箱子同时选择多个种类
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; selectParams = 4;</code>
     */
    @java.lang.Override
    public boolean containsSelectParams(
        int key) {

      return internalGetSelectParams().getMap().containsKey(key);
    }
    /**
     * Use {@link #getSelectParamsMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.Integer, java.lang.Integer> getSelectParams() {
      return getSelectParamsMap();
    }
    /**
     * <pre>
     * key: index, value: count 道具类型为5：自选礼包时，选择道具的参数，主要是一次使用多个箱子同时选择多个种类
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; selectParams = 4;</code>
     */
    @java.lang.Override
    public java.util.Map<java.lang.Integer, java.lang.Integer> getSelectParamsMap() {
      return internalGetSelectParams().getMap();
    }
    /**
     * <pre>
     * key: index, value: count 道具类型为5：自选礼包时，选择道具的参数，主要是一次使用多个箱子同时选择多个种类
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; selectParams = 4;</code>
     */
    @java.lang.Override
    public int getSelectParamsOrDefault(
        int key,
        int defaultValue) {

      java.util.Map<java.lang.Integer, java.lang.Integer> map =
          internalGetSelectParams().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     * key: index, value: count 道具类型为5：自选礼包时，选择道具的参数，主要是一次使用多个箱子同时选择多个种类
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; selectParams = 4;</code>
     */
    @java.lang.Override
    public int getSelectParamsOrThrow(
        int key) {

      java.util.Map<java.lang.Integer, java.lang.Integer> map =
          internalGetSelectParams().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getCommonParams());
      }
      if (itemId_ != 0) {
        output.writeInt32(2, itemId_);
      }
      if (count_ != 0) {
        output.writeInt32(3, count_);
      }
      com.google.protobuf.GeneratedMessage
        .serializeIntegerMapTo(
          output,
          internalGetSelectParams(),
          SelectParamsDefaultEntryHolder.defaultEntry,
          4);
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      if (itemId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, itemId_);
      }
      if (count_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, count_);
      }
      for (java.util.Map.Entry<java.lang.Integer, java.lang.Integer> entry
           : internalGetSelectParams().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Integer, java.lang.Integer>
        selectParams__ = SelectParamsDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(4, selectParams__);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.ItemProto.ItemUseRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.ItemProto.ItemUseRequest other = (com.dxx.game.dto.ItemProto.ItemUseRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (getItemId()
          != other.getItemId()) return false;
      if (getCount()
          != other.getCount()) return false;
      if (!internalGetSelectParams().equals(
          other.internalGetSelectParams())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (37 * hash) + ITEMID_FIELD_NUMBER;
      hash = (53 * hash) + getItemId();
      hash = (37 * hash) + COUNT_FIELD_NUMBER;
      hash = (53 * hash) + getCount();
      if (!internalGetSelectParams().getMap().isEmpty()) {
        hash = (37 * hash) + SELECTPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + internalGetSelectParams().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.ItemProto.ItemUseRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ItemProto.ItemUseRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ItemProto.ItemUseRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ItemProto.ItemUseRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ItemProto.ItemUseRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ItemProto.ItemUseRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ItemProto.ItemUseRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ItemProto.ItemUseRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.ItemProto.ItemUseRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.ItemProto.ItemUseRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.ItemProto.ItemUseRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ItemProto.ItemUseRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.ItemProto.ItemUseRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=10201 道具-使用
     * </pre>
     *
     * Protobuf type {@code Proto.Item.ItemUseRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Item.ItemUseRequest)
        com.dxx.game.dto.ItemProto.ItemUseRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.ItemProto.internal_static_Proto_Item_ItemUseRequest_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapFieldReflectionAccessor internalGetMapFieldReflection(
          int number) {
        switch (number) {
          case 4:
            return internalGetSelectParams();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapFieldReflectionAccessor internalGetMutableMapFieldReflection(
          int number) {
        switch (number) {
          case 4:
            return internalGetMutableSelectParams();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.ItemProto.internal_static_Proto_Item_ItemUseRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.ItemProto.ItemUseRequest.class, com.dxx.game.dto.ItemProto.ItemUseRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.ItemProto.ItemUseRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getCommonParamsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        itemId_ = 0;
        count_ = 0;
        internalGetMutableSelectParams().clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.ItemProto.internal_static_Proto_Item_ItemUseRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.ItemProto.ItemUseRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.ItemProto.ItemUseRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.ItemProto.ItemUseRequest build() {
        com.dxx.game.dto.ItemProto.ItemUseRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.ItemProto.ItemUseRequest buildPartial() {
        com.dxx.game.dto.ItemProto.ItemUseRequest result = new com.dxx.game.dto.ItemProto.ItemUseRequest(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.ItemProto.ItemUseRequest result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.commonParams_ = commonParamsBuilder_ == null
              ? commonParams_
              : commonParamsBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.itemId_ = itemId_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.count_ = count_;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.selectParams_ = internalGetSelectParams();
          result.selectParams_.makeImmutable();
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.ItemProto.ItemUseRequest) {
          return mergeFrom((com.dxx.game.dto.ItemProto.ItemUseRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.ItemProto.ItemUseRequest other) {
        if (other == com.dxx.game.dto.ItemProto.ItemUseRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        if (other.getItemId() != 0) {
          setItemId(other.getItemId());
        }
        if (other.getCount() != 0) {
          setCount(other.getCount());
        }
        internalGetMutableSelectParams().mergeFrom(
            other.internalGetSelectParams());
        bitField0_ |= 0x00000008;
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getCommonParamsFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 16: {
                itemId_ = input.readInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 24: {
                count_ = input.readInt32();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              case 34: {
                com.google.protobuf.MapEntry<java.lang.Integer, java.lang.Integer>
                selectParams__ = input.readMessage(
                    SelectParamsDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
                internalGetMutableSelectParams().getMutableMap().put(
                    selectParams__.getKey(), selectParams__.getValue());
                bitField0_ |= 0x00000008;
                break;
              } // case 34
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
        } else {
          commonParamsBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
            commonParams_ != null &&
            commonParams_ != com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance()) {
            getCommonParamsBuilder().mergeFrom(value);
          } else {
            commonParams_ = value;
          }
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }
        if (commonParams_ != null) {
          bitField0_ |= 0x00000001;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        bitField0_ = (bitField0_ & ~0x00000001);
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }

      private int itemId_ ;
      /**
       * <pre>
       * 道具rowID
       * </pre>
       *
       * <code>int32 itemId = 2;</code>
       * @return The itemId.
       */
      @java.lang.Override
      public int getItemId() {
        return itemId_;
      }
      /**
       * <pre>
       * 道具rowID
       * </pre>
       *
       * <code>int32 itemId = 2;</code>
       * @param value The itemId to set.
       * @return This builder for chaining.
       */
      public Builder setItemId(int value) {

        itemId_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 道具rowID
       * </pre>
       *
       * <code>int32 itemId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearItemId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        itemId_ = 0;
        onChanged();
        return this;
      }

      private int count_ ;
      /**
       * <pre>
       * 使用数量
       * </pre>
       *
       * <code>int32 count = 3;</code>
       * @return The count.
       */
      @java.lang.Override
      public int getCount() {
        return count_;
      }
      /**
       * <pre>
       * 使用数量
       * </pre>
       *
       * <code>int32 count = 3;</code>
       * @param value The count to set.
       * @return This builder for chaining.
       */
      public Builder setCount(int value) {

        count_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 使用数量
       * </pre>
       *
       * <code>int32 count = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearCount() {
        bitField0_ = (bitField0_ & ~0x00000004);
        count_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.MapField<
          java.lang.Integer, java.lang.Integer> selectParams_;
      private com.google.protobuf.MapField<java.lang.Integer, java.lang.Integer>
          internalGetSelectParams() {
        if (selectParams_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              SelectParamsDefaultEntryHolder.defaultEntry);
        }
        return selectParams_;
      }
      private com.google.protobuf.MapField<java.lang.Integer, java.lang.Integer>
          internalGetMutableSelectParams() {
        if (selectParams_ == null) {
          selectParams_ = com.google.protobuf.MapField.newMapField(
              SelectParamsDefaultEntryHolder.defaultEntry);
        }
        if (!selectParams_.isMutable()) {
          selectParams_ = selectParams_.copy();
        }
        bitField0_ |= 0x00000008;
        onChanged();
        return selectParams_;
      }
      public int getSelectParamsCount() {
        return internalGetSelectParams().getMap().size();
      }
      /**
       * <pre>
       * key: index, value: count 道具类型为5：自选礼包时，选择道具的参数，主要是一次使用多个箱子同时选择多个种类
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; selectParams = 4;</code>
       */
      @java.lang.Override
      public boolean containsSelectParams(
          int key) {

        return internalGetSelectParams().getMap().containsKey(key);
      }
      /**
       * Use {@link #getSelectParamsMap()} instead.
       */
      @java.lang.Override
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, java.lang.Integer> getSelectParams() {
        return getSelectParamsMap();
      }
      /**
       * <pre>
       * key: index, value: count 道具类型为5：自选礼包时，选择道具的参数，主要是一次使用多个箱子同时选择多个种类
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; selectParams = 4;</code>
       */
      @java.lang.Override
      public java.util.Map<java.lang.Integer, java.lang.Integer> getSelectParamsMap() {
        return internalGetSelectParams().getMap();
      }
      /**
       * <pre>
       * key: index, value: count 道具类型为5：自选礼包时，选择道具的参数，主要是一次使用多个箱子同时选择多个种类
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; selectParams = 4;</code>
       */
      @java.lang.Override
      public int getSelectParamsOrDefault(
          int key,
          int defaultValue) {

        java.util.Map<java.lang.Integer, java.lang.Integer> map =
            internalGetSelectParams().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <pre>
       * key: index, value: count 道具类型为5：自选礼包时，选择道具的参数，主要是一次使用多个箱子同时选择多个种类
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; selectParams = 4;</code>
       */
      @java.lang.Override
      public int getSelectParamsOrThrow(
          int key) {

        java.util.Map<java.lang.Integer, java.lang.Integer> map =
            internalGetSelectParams().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }
      public Builder clearSelectParams() {
        bitField0_ = (bitField0_ & ~0x00000008);
        internalGetMutableSelectParams().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <pre>
       * key: index, value: count 道具类型为5：自选礼包时，选择道具的参数，主要是一次使用多个箱子同时选择多个种类
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; selectParams = 4;</code>
       */
      public Builder removeSelectParams(
          int key) {

        internalGetMutableSelectParams().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, java.lang.Integer>
          getMutableSelectParams() {
        bitField0_ |= 0x00000008;
        return internalGetMutableSelectParams().getMutableMap();
      }
      /**
       * <pre>
       * key: index, value: count 道具类型为5：自选礼包时，选择道具的参数，主要是一次使用多个箱子同时选择多个种类
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; selectParams = 4;</code>
       */
      public Builder putSelectParams(
          int key,
          int value) {


        internalGetMutableSelectParams().getMutableMap()
            .put(key, value);
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <pre>
       * key: index, value: count 道具类型为5：自选礼包时，选择道具的参数，主要是一次使用多个箱子同时选择多个种类
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; selectParams = 4;</code>
       */
      public Builder putAllSelectParams(
          java.util.Map<java.lang.Integer, java.lang.Integer> values) {
        internalGetMutableSelectParams().getMutableMap()
            .putAll(values);
        bitField0_ |= 0x00000008;
        return this;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Item.ItemUseRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.Item.ItemUseRequest)
    private static final com.dxx.game.dto.ItemProto.ItemUseRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.ItemProto.ItemUseRequest();
    }

    public static com.dxx.game.dto.ItemProto.ItemUseRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ItemUseRequest>
        PARSER = new com.google.protobuf.AbstractParser<ItemUseRequest>() {
      @java.lang.Override
      public ItemUseRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<ItemUseRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ItemUseRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.ItemProto.ItemUseRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ItemUseResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Item.ItemUseResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    boolean hasCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    com.dxx.game.dto.CommonProto.CommonData getCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder();
  }
  /**
   * <pre>
   * CMD PackageId=10202
   * </pre>
   *
   * Protobuf type {@code Proto.Item.ItemUseResponse}
   */
  public static final class ItemUseResponse extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Item.ItemUseResponse)
      ItemUseResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        ItemUseResponse.class.getName());
    }
    // Use ItemUseResponse.newBuilder() to construct.
    private ItemUseResponse(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private ItemUseResponse() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.ItemProto.internal_static_Proto_Item_ItemUseResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.ItemProto.internal_static_Proto_Item_ItemUseResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.ItemProto.ItemUseResponse.class, com.dxx.game.dto.ItemProto.ItemUseResponse.Builder.class);
    }

    private int bitField0_;
    public static final int CODE_FIELD_NUMBER = 1;
    private int code_ = 0;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int COMMONDATA_FIELD_NUMBER = 2;
    private com.dxx.game.dto.CommonProto.CommonData commonData_;
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return Whether the commonData field is set.
     */
    @java.lang.Override
    public boolean hasCommonData() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     * @return The commonData.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(2, getCommonData());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getCommonData());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.ItemProto.ItemUseResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.ItemProto.ItemUseResponse other = (com.dxx.game.dto.ItemProto.ItemUseResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (hasCommonData() != other.hasCommonData()) return false;
      if (hasCommonData()) {
        if (!getCommonData()
            .equals(other.getCommonData())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      if (hasCommonData()) {
        hash = (37 * hash) + COMMONDATA_FIELD_NUMBER;
        hash = (53 * hash) + getCommonData().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.ItemProto.ItemUseResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ItemProto.ItemUseResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ItemProto.ItemUseResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ItemProto.ItemUseResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ItemProto.ItemUseResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ItemProto.ItemUseResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ItemProto.ItemUseResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ItemProto.ItemUseResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.ItemProto.ItemUseResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.ItemProto.ItemUseResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.ItemProto.ItemUseResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ItemProto.ItemUseResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.ItemProto.ItemUseResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=10202
     * </pre>
     *
     * Protobuf type {@code Proto.Item.ItemUseResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Item.ItemUseResponse)
        com.dxx.game.dto.ItemProto.ItemUseResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.ItemProto.internal_static_Proto_Item_ItemUseResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.ItemProto.internal_static_Proto_Item_ItemUseResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.ItemProto.ItemUseResponse.class, com.dxx.game.dto.ItemProto.ItemUseResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.ItemProto.ItemUseResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getCommonDataFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        code_ = 0;
        commonData_ = null;
        if (commonDataBuilder_ != null) {
          commonDataBuilder_.dispose();
          commonDataBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.ItemProto.internal_static_Proto_Item_ItemUseResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.ItemProto.ItemUseResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.ItemProto.ItemUseResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.ItemProto.ItemUseResponse build() {
        com.dxx.game.dto.ItemProto.ItemUseResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.ItemProto.ItemUseResponse buildPartial() {
        com.dxx.game.dto.ItemProto.ItemUseResponse result = new com.dxx.game.dto.ItemProto.ItemUseResponse(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.ItemProto.ItemUseResponse result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.code_ = code_;
        }
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.commonData_ = commonDataBuilder_ == null
              ? commonData_
              : commonDataBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.ItemProto.ItemUseResponse) {
          return mergeFrom((com.dxx.game.dto.ItemProto.ItemUseResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.ItemProto.ItemUseResponse other) {
        if (other == com.dxx.game.dto.ItemProto.ItemUseResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.hasCommonData()) {
          mergeCommonData(other.getCommonData());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                code_ = input.readInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 18: {
                input.readMessage(
                    getCommonDataFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {

        code_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        bitField0_ = (bitField0_ & ~0x00000001);
        code_ = 0;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonData commonData_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> commonDataBuilder_;
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return Whether the commonData field is set.
       */
      public boolean hasCommonData() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       * @return The commonData.
       */
      public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
        if (commonDataBuilder_ == null) {
          return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        } else {
          return commonDataBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonData_ = value;
        } else {
          commonDataBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder setCommonData(
          com.dxx.game.dto.CommonProto.CommonData.Builder builderForValue) {
        if (commonDataBuilder_ == null) {
          commonData_ = builderForValue.build();
        } else {
          commonDataBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder mergeCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
            commonData_ != null &&
            commonData_ != com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance()) {
            getCommonDataBuilder().mergeFrom(value);
          } else {
            commonData_ = value;
          }
        } else {
          commonDataBuilder_.mergeFrom(value);
        }
        if (commonData_ != null) {
          bitField0_ |= 0x00000002;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public Builder clearCommonData() {
        bitField0_ = (bitField0_ & ~0x00000002);
        commonData_ = null;
        if (commonDataBuilder_ != null) {
          commonDataBuilder_.dispose();
          commonDataBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonData.Builder getCommonDataBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getCommonDataFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
        if (commonDataBuilder_ != null) {
          return commonDataBuilder_.getMessageOrBuilder();
        } else {
          return commonData_ == null ?
              com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> 
          getCommonDataFieldBuilder() {
        if (commonDataBuilder_ == null) {
          commonDataBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder>(
                  getCommonData(),
                  getParentForChildren(),
                  isClean());
          commonData_ = null;
        }
        return commonDataBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Item.ItemUseResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.Item.ItemUseResponse)
    private static final com.dxx.game.dto.ItemProto.ItemUseResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.ItemProto.ItemUseResponse();
    }

    public static com.dxx.game.dto.ItemProto.ItemUseResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ItemUseResponse>
        PARSER = new com.google.protobuf.AbstractParser<ItemUseResponse>() {
      @java.lang.Override
      public ItemUseResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<ItemUseResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ItemUseResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.ItemProto.ItemUseResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface UseBattleReviveItemRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Item.UseBattleReviveItemRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();
  }
  /**
   * <pre>
   * CMD PackageId=10203 道具-战斗使用复活道具
   * </pre>
   *
   * Protobuf type {@code Proto.Item.UseBattleReviveItemRequest}
   */
  public static final class UseBattleReviveItemRequest extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Item.UseBattleReviveItemRequest)
      UseBattleReviveItemRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        UseBattleReviveItemRequest.class.getName());
    }
    // Use UseBattleReviveItemRequest.newBuilder() to construct.
    private UseBattleReviveItemRequest(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private UseBattleReviveItemRequest() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.ItemProto.internal_static_Proto_Item_UseBattleReviveItemRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.ItemProto.internal_static_Proto_Item_UseBattleReviveItemRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.ItemProto.UseBattleReviveItemRequest.class, com.dxx.game.dto.ItemProto.UseBattleReviveItemRequest.Builder.class);
    }

    private int bitField0_;
    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getCommonParams());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.ItemProto.UseBattleReviveItemRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.ItemProto.UseBattleReviveItemRequest other = (com.dxx.game.dto.ItemProto.UseBattleReviveItemRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.ItemProto.UseBattleReviveItemRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ItemProto.UseBattleReviveItemRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ItemProto.UseBattleReviveItemRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ItemProto.UseBattleReviveItemRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ItemProto.UseBattleReviveItemRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ItemProto.UseBattleReviveItemRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ItemProto.UseBattleReviveItemRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ItemProto.UseBattleReviveItemRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.ItemProto.UseBattleReviveItemRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.ItemProto.UseBattleReviveItemRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.ItemProto.UseBattleReviveItemRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ItemProto.UseBattleReviveItemRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.ItemProto.UseBattleReviveItemRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=10203 道具-战斗使用复活道具
     * </pre>
     *
     * Protobuf type {@code Proto.Item.UseBattleReviveItemRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Item.UseBattleReviveItemRequest)
        com.dxx.game.dto.ItemProto.UseBattleReviveItemRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.ItemProto.internal_static_Proto_Item_UseBattleReviveItemRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.ItemProto.internal_static_Proto_Item_UseBattleReviveItemRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.ItemProto.UseBattleReviveItemRequest.class, com.dxx.game.dto.ItemProto.UseBattleReviveItemRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.ItemProto.UseBattleReviveItemRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getCommonParamsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.ItemProto.internal_static_Proto_Item_UseBattleReviveItemRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.ItemProto.UseBattleReviveItemRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.ItemProto.UseBattleReviveItemRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.ItemProto.UseBattleReviveItemRequest build() {
        com.dxx.game.dto.ItemProto.UseBattleReviveItemRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.ItemProto.UseBattleReviveItemRequest buildPartial() {
        com.dxx.game.dto.ItemProto.UseBattleReviveItemRequest result = new com.dxx.game.dto.ItemProto.UseBattleReviveItemRequest(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.ItemProto.UseBattleReviveItemRequest result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.commonParams_ = commonParamsBuilder_ == null
              ? commonParams_
              : commonParamsBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.ItemProto.UseBattleReviveItemRequest) {
          return mergeFrom((com.dxx.game.dto.ItemProto.UseBattleReviveItemRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.ItemProto.UseBattleReviveItemRequest other) {
        if (other == com.dxx.game.dto.ItemProto.UseBattleReviveItemRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getCommonParamsFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
        } else {
          commonParamsBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
            commonParams_ != null &&
            commonParams_ != com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance()) {
            getCommonParamsBuilder().mergeFrom(value);
          } else {
            commonParams_ = value;
          }
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }
        if (commonParams_ != null) {
          bitField0_ |= 0x00000001;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        bitField0_ = (bitField0_ & ~0x00000001);
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Item.UseBattleReviveItemRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.Item.UseBattleReviveItemRequest)
    private static final com.dxx.game.dto.ItemProto.UseBattleReviveItemRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.ItemProto.UseBattleReviveItemRequest();
    }

    public static com.dxx.game.dto.ItemProto.UseBattleReviveItemRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<UseBattleReviveItemRequest>
        PARSER = new com.google.protobuf.AbstractParser<UseBattleReviveItemRequest>() {
      @java.lang.Override
      public UseBattleReviveItemRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<UseBattleReviveItemRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<UseBattleReviveItemRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.ItemProto.UseBattleReviveItemRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface UseBattleReviveItemResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Item.UseBattleReviveItemResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();
  }
  /**
   * <pre>
   * CMD PackageId=10204 道具-战斗使用复活道具
   * </pre>
   *
   * Protobuf type {@code Proto.Item.UseBattleReviveItemResponse}
   */
  public static final class UseBattleReviveItemResponse extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Item.UseBattleReviveItemResponse)
      UseBattleReviveItemResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        UseBattleReviveItemResponse.class.getName());
    }
    // Use UseBattleReviveItemResponse.newBuilder() to construct.
    private UseBattleReviveItemResponse(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private UseBattleReviveItemResponse() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.ItemProto.internal_static_Proto_Item_UseBattleReviveItemResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.ItemProto.internal_static_Proto_Item_UseBattleReviveItemResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.ItemProto.UseBattleReviveItemResponse.class, com.dxx.game.dto.ItemProto.UseBattleReviveItemResponse.Builder.class);
    }

    public static final int CODE_FIELD_NUMBER = 1;
    private int code_ = 0;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.ItemProto.UseBattleReviveItemResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.ItemProto.UseBattleReviveItemResponse other = (com.dxx.game.dto.ItemProto.UseBattleReviveItemResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.ItemProto.UseBattleReviveItemResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ItemProto.UseBattleReviveItemResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ItemProto.UseBattleReviveItemResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ItemProto.UseBattleReviveItemResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ItemProto.UseBattleReviveItemResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.ItemProto.UseBattleReviveItemResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.ItemProto.UseBattleReviveItemResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ItemProto.UseBattleReviveItemResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.ItemProto.UseBattleReviveItemResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.ItemProto.UseBattleReviveItemResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.ItemProto.UseBattleReviveItemResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.ItemProto.UseBattleReviveItemResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.ItemProto.UseBattleReviveItemResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=10204 道具-战斗使用复活道具
     * </pre>
     *
     * Protobuf type {@code Proto.Item.UseBattleReviveItemResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Item.UseBattleReviveItemResponse)
        com.dxx.game.dto.ItemProto.UseBattleReviveItemResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.ItemProto.internal_static_Proto_Item_UseBattleReviveItemResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.ItemProto.internal_static_Proto_Item_UseBattleReviveItemResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.ItemProto.UseBattleReviveItemResponse.class, com.dxx.game.dto.ItemProto.UseBattleReviveItemResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.ItemProto.UseBattleReviveItemResponse.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        code_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.ItemProto.internal_static_Proto_Item_UseBattleReviveItemResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.ItemProto.UseBattleReviveItemResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.ItemProto.UseBattleReviveItemResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.ItemProto.UseBattleReviveItemResponse build() {
        com.dxx.game.dto.ItemProto.UseBattleReviveItemResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.ItemProto.UseBattleReviveItemResponse buildPartial() {
        com.dxx.game.dto.ItemProto.UseBattleReviveItemResponse result = new com.dxx.game.dto.ItemProto.UseBattleReviveItemResponse(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.ItemProto.UseBattleReviveItemResponse result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.code_ = code_;
        }
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.ItemProto.UseBattleReviveItemResponse) {
          return mergeFrom((com.dxx.game.dto.ItemProto.UseBattleReviveItemResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.ItemProto.UseBattleReviveItemResponse other) {
        if (other == com.dxx.game.dto.ItemProto.UseBattleReviveItemResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                code_ = input.readInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {

        code_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        bitField0_ = (bitField0_ & ~0x00000001);
        code_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Item.UseBattleReviveItemResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.Item.UseBattleReviveItemResponse)
    private static final com.dxx.game.dto.ItemProto.UseBattleReviveItemResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.ItemProto.UseBattleReviveItemResponse();
    }

    public static com.dxx.game.dto.ItemProto.UseBattleReviveItemResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<UseBattleReviveItemResponse>
        PARSER = new com.google.protobuf.AbstractParser<UseBattleReviveItemResponse>() {
      @java.lang.Override
      public UseBattleReviveItemResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<UseBattleReviveItemResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<UseBattleReviveItemResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.ItemProto.UseBattleReviveItemResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Item_ItemUseRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Item_ItemUseRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Item_ItemUseRequest_SelectParamsEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Item_ItemUseRequest_SelectParamsEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Item_ItemUseResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Item_ItemUseResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Item_UseBattleReviveItemRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Item_UseBattleReviveItemRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Item_UseBattleReviveItemResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Item_UseBattleReviveItemResponse_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\nitem.proto\022\nProto.Item\032\014common.proto\"\332" +
      "\001\n\016ItemUseRequest\0220\n\014commonParams\030\001 \001(\0132" +
      "\032.Proto.Common.CommonParams\022\016\n\006itemId\030\002 " +
      "\001(\005\022\r\n\005count\030\003 \001(\005\022B\n\014selectParams\030\004 \003(\013" +
      "2,.Proto.Item.ItemUseRequest.SelectParam" +
      "sEntry\0323\n\021SelectParamsEntry\022\013\n\003key\030\001 \001(\005" +
      "\022\r\n\005value\030\002 \001(\005:\0028\001\"M\n\017ItemUseResponse\022\014" +
      "\n\004code\030\001 \001(\005\022,\n\ncommonData\030\002 \001(\0132\030.Proto" +
      ".Common.CommonData\"N\n\032UseBattleReviveIte" +
      "mRequest\0220\n\014commonParams\030\001 \001(\0132\032.Proto.C" +
      "ommon.CommonParams\"+\n\033UseBattleReviveIte" +
      "mResponse\022\014\n\004code\030\001 \001(\005B\035\n\020com.dxx.game." +
      "dtoB\tItemProtob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.dxx.game.dto.CommonProto.getDescriptor(),
        });
    internal_static_Proto_Item_ItemUseRequest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_Proto_Item_ItemUseRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Item_ItemUseRequest_descriptor,
        new java.lang.String[] { "CommonParams", "ItemId", "Count", "SelectParams", });
    internal_static_Proto_Item_ItemUseRequest_SelectParamsEntry_descriptor =
      internal_static_Proto_Item_ItemUseRequest_descriptor.getNestedTypes().get(0);
    internal_static_Proto_Item_ItemUseRequest_SelectParamsEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Item_ItemUseRequest_SelectParamsEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_Proto_Item_ItemUseResponse_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_Proto_Item_ItemUseResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Item_ItemUseResponse_descriptor,
        new java.lang.String[] { "Code", "CommonData", });
    internal_static_Proto_Item_UseBattleReviveItemRequest_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_Proto_Item_UseBattleReviveItemRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Item_UseBattleReviveItemRequest_descriptor,
        new java.lang.String[] { "CommonParams", });
    internal_static_Proto_Item_UseBattleReviveItemResponse_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_Proto_Item_UseBattleReviveItemResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Item_UseBattleReviveItemResponse_descriptor,
        new java.lang.String[] { "Code", });
    descriptor.resolveAllFeaturesImmutable();
    com.dxx.game.dto.CommonProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
