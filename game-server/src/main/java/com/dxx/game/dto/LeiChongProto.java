// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: leichong.proto
// Protobuf Java Version: 4.29.0

package com.dxx.game.dto;

public final class LeiChongProto {
  private LeiChongProto() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 29,
      /* patch= */ 0,
      /* suffix= */ "",
      LeiChongProto.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_LeiChong_DailyLeiChongGetInfoRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_LeiChong_DailyLeiChongGetInfoRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_LeiChong_DailyLeiChongGetInfoResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_LeiChong_DailyLeiChongGetInfoResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_LeiChong_DailyLeiChongRewardRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_LeiChong_DailyLeiChongRewardRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_LeiChong_DailyLeiChongRewardResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_LeiChong_DailyLeiChongRewardResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_LeiChong_LeiChongRewardRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_LeiChong_LeiChongRewardRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_LeiChong_LeiChongRewardResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_LeiChong_LeiChongRewardResponse_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\016leichong.proto\022\016Proto.LeiChong\032\014common" +
      ".proto\"O\n\033DailyLeiChongGetInfoRequest\0220\n" +
      "\014commonParams\030\001 \001(\0132\032.Proto.Common.Commo" +
      "nParams\"Z\n\034DailyLeiChongGetInfoResponse\022" +
      "\014\n\004code\030\001 \001(\005\022,\n\004info\030\002 \001(\0132\036.Proto.Comm" +
      "on.DailyLeiChongDto\"Z\n\032DailyLeiChongRewa" +
      "rdRequest\0220\n\014commonParams\030\001 \001(\0132\032.Proto." +
      "Common.CommonParams\022\n\n\002id\030\002 \001(\005\"Y\n\033Daily" +
      "LeiChongRewardResponse\022\014\n\004code\030\001 \001(\005\022,\n\004" +
      "info\030\002 \001(\0132\036.Proto.Common.DailyLeiChongD" +
      "to\"U\n\025LeiChongRewardRequest\0220\n\014commonPar" +
      "ams\030\001 \001(\0132\032.Proto.Common.CommonParams\022\n\n" +
      "\002id\030\002 \001(\005\":\n\026LeiChongRewardResponse\022\014\n\004c" +
      "ode\030\001 \001(\005\022\022\n\nrewardFlag\030\002 \001(\003B#\n\020com.dxx" +
      ".game.dtoB\rLeiChongProtoP\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.dxx.game.dto.CommonProto.getDescriptor(),
        });
    internal_static_Proto_LeiChong_DailyLeiChongGetInfoRequest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_Proto_LeiChong_DailyLeiChongGetInfoRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_LeiChong_DailyLeiChongGetInfoRequest_descriptor,
        new java.lang.String[] { "CommonParams", });
    internal_static_Proto_LeiChong_DailyLeiChongGetInfoResponse_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_Proto_LeiChong_DailyLeiChongGetInfoResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_LeiChong_DailyLeiChongGetInfoResponse_descriptor,
        new java.lang.String[] { "Code", "Info", });
    internal_static_Proto_LeiChong_DailyLeiChongRewardRequest_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_Proto_LeiChong_DailyLeiChongRewardRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_LeiChong_DailyLeiChongRewardRequest_descriptor,
        new java.lang.String[] { "CommonParams", "Id", });
    internal_static_Proto_LeiChong_DailyLeiChongRewardResponse_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_Proto_LeiChong_DailyLeiChongRewardResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_LeiChong_DailyLeiChongRewardResponse_descriptor,
        new java.lang.String[] { "Code", "Info", });
    internal_static_Proto_LeiChong_LeiChongRewardRequest_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_Proto_LeiChong_LeiChongRewardRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_LeiChong_LeiChongRewardRequest_descriptor,
        new java.lang.String[] { "CommonParams", "Id", });
    internal_static_Proto_LeiChong_LeiChongRewardResponse_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_Proto_LeiChong_LeiChongRewardResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_LeiChong_LeiChongRewardResponse_descriptor,
        new java.lang.String[] { "Code", "RewardFlag", });
    descriptor.resolveAllFeaturesImmutable();
    com.dxx.game.dto.CommonProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
