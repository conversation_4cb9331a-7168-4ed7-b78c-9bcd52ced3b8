// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: mail.proto
// Protobuf Java Version: 4.29.0

package com.dxx.game.dto;

public final class MailProto {
  private MailProto() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 29,
      /* patch= */ 0,
      /* suffix= */ "",
      MailProto.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface MailGetListRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Mail.MailGetListRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();

    /**
     * <pre>
     * 最后一封邮件唯一ID
     * </pre>
     *
     * <code>uint64 maxRowId = 2;</code>
     * @return The maxRowId.
     */
    long getMaxRowId();

    /**
     * <pre>
     * 客户端语言
     * </pre>
     *
     * <code>string appLanguage = 3;</code>
     * @return The appLanguage.
     */
    java.lang.String getAppLanguage();
    /**
     * <pre>
     * 客户端语言
     * </pre>
     *
     * <code>string appLanguage = 3;</code>
     * @return The bytes for appLanguage.
     */
    com.google.protobuf.ByteString
        getAppLanguageBytes();
  }
  /**
   * <pre>
   * CMD PackageId=10801 邮件-获取列表
   * </pre>
   *
   * Protobuf type {@code Proto.Mail.MailGetListRequest}
   */
  public static final class MailGetListRequest extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Mail.MailGetListRequest)
      MailGetListRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        MailGetListRequest.class.getName());
    }
    // Use MailGetListRequest.newBuilder() to construct.
    private MailGetListRequest(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private MailGetListRequest() {
      appLanguage_ = "";
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.MailProto.internal_static_Proto_Mail_MailGetListRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.MailProto.internal_static_Proto_Mail_MailGetListRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.MailProto.MailGetListRequest.class, com.dxx.game.dto.MailProto.MailGetListRequest.Builder.class);
    }

    private int bitField0_;
    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }

    public static final int MAXROWID_FIELD_NUMBER = 2;
    private long maxRowId_ = 0L;
    /**
     * <pre>
     * 最后一封邮件唯一ID
     * </pre>
     *
     * <code>uint64 maxRowId = 2;</code>
     * @return The maxRowId.
     */
    @java.lang.Override
    public long getMaxRowId() {
      return maxRowId_;
    }

    public static final int APPLANGUAGE_FIELD_NUMBER = 3;
    @SuppressWarnings("serial")
    private volatile java.lang.Object appLanguage_ = "";
    /**
     * <pre>
     * 客户端语言
     * </pre>
     *
     * <code>string appLanguage = 3;</code>
     * @return The appLanguage.
     */
    @java.lang.Override
    public java.lang.String getAppLanguage() {
      java.lang.Object ref = appLanguage_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        appLanguage_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 客户端语言
     * </pre>
     *
     * <code>string appLanguage = 3;</code>
     * @return The bytes for appLanguage.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getAppLanguageBytes() {
      java.lang.Object ref = appLanguage_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        appLanguage_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getCommonParams());
      }
      if (maxRowId_ != 0L) {
        output.writeUInt64(2, maxRowId_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(appLanguage_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 3, appLanguage_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      if (maxRowId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(2, maxRowId_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(appLanguage_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(3, appLanguage_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.MailProto.MailGetListRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.MailProto.MailGetListRequest other = (com.dxx.game.dto.MailProto.MailGetListRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (getMaxRowId()
          != other.getMaxRowId()) return false;
      if (!getAppLanguage()
          .equals(other.getAppLanguage())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (37 * hash) + MAXROWID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getMaxRowId());
      hash = (37 * hash) + APPLANGUAGE_FIELD_NUMBER;
      hash = (53 * hash) + getAppLanguage().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.MailProto.MailGetListRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MailProto.MailGetListRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MailProto.MailGetListRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MailProto.MailGetListRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MailProto.MailGetListRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MailProto.MailGetListRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MailProto.MailGetListRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MailProto.MailGetListRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.MailProto.MailGetListRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.MailProto.MailGetListRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.MailProto.MailGetListRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MailProto.MailGetListRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.MailProto.MailGetListRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=10801 邮件-获取列表
     * </pre>
     *
     * Protobuf type {@code Proto.Mail.MailGetListRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Mail.MailGetListRequest)
        com.dxx.game.dto.MailProto.MailGetListRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.MailProto.internal_static_Proto_Mail_MailGetListRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.MailProto.internal_static_Proto_Mail_MailGetListRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.MailProto.MailGetListRequest.class, com.dxx.game.dto.MailProto.MailGetListRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.MailProto.MailGetListRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getCommonParamsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        maxRowId_ = 0L;
        appLanguage_ = "";
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.MailProto.internal_static_Proto_Mail_MailGetListRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.MailProto.MailGetListRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.MailProto.MailGetListRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.MailProto.MailGetListRequest build() {
        com.dxx.game.dto.MailProto.MailGetListRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.MailProto.MailGetListRequest buildPartial() {
        com.dxx.game.dto.MailProto.MailGetListRequest result = new com.dxx.game.dto.MailProto.MailGetListRequest(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.MailProto.MailGetListRequest result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.commonParams_ = commonParamsBuilder_ == null
              ? commonParams_
              : commonParamsBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.maxRowId_ = maxRowId_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.appLanguage_ = appLanguage_;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.MailProto.MailGetListRequest) {
          return mergeFrom((com.dxx.game.dto.MailProto.MailGetListRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.MailProto.MailGetListRequest other) {
        if (other == com.dxx.game.dto.MailProto.MailGetListRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        if (other.getMaxRowId() != 0L) {
          setMaxRowId(other.getMaxRowId());
        }
        if (!other.getAppLanguage().isEmpty()) {
          appLanguage_ = other.appLanguage_;
          bitField0_ |= 0x00000004;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getCommonParamsFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 16: {
                maxRowId_ = input.readUInt64();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 26: {
                appLanguage_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000004;
                break;
              } // case 26
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
        } else {
          commonParamsBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
            commonParams_ != null &&
            commonParams_ != com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance()) {
            getCommonParamsBuilder().mergeFrom(value);
          } else {
            commonParams_ = value;
          }
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }
        if (commonParams_ != null) {
          bitField0_ |= 0x00000001;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        bitField0_ = (bitField0_ & ~0x00000001);
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }

      private long maxRowId_ ;
      /**
       * <pre>
       * 最后一封邮件唯一ID
       * </pre>
       *
       * <code>uint64 maxRowId = 2;</code>
       * @return The maxRowId.
       */
      @java.lang.Override
      public long getMaxRowId() {
        return maxRowId_;
      }
      /**
       * <pre>
       * 最后一封邮件唯一ID
       * </pre>
       *
       * <code>uint64 maxRowId = 2;</code>
       * @param value The maxRowId to set.
       * @return This builder for chaining.
       */
      public Builder setMaxRowId(long value) {

        maxRowId_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 最后一封邮件唯一ID
       * </pre>
       *
       * <code>uint64 maxRowId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearMaxRowId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        maxRowId_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object appLanguage_ = "";
      /**
       * <pre>
       * 客户端语言
       * </pre>
       *
       * <code>string appLanguage = 3;</code>
       * @return The appLanguage.
       */
      public java.lang.String getAppLanguage() {
        java.lang.Object ref = appLanguage_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          appLanguage_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 客户端语言
       * </pre>
       *
       * <code>string appLanguage = 3;</code>
       * @return The bytes for appLanguage.
       */
      public com.google.protobuf.ByteString
          getAppLanguageBytes() {
        java.lang.Object ref = appLanguage_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          appLanguage_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 客户端语言
       * </pre>
       *
       * <code>string appLanguage = 3;</code>
       * @param value The appLanguage to set.
       * @return This builder for chaining.
       */
      public Builder setAppLanguage(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        appLanguage_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 客户端语言
       * </pre>
       *
       * <code>string appLanguage = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearAppLanguage() {
        appLanguage_ = getDefaultInstance().getAppLanguage();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 客户端语言
       * </pre>
       *
       * <code>string appLanguage = 3;</code>
       * @param value The bytes for appLanguage to set.
       * @return This builder for chaining.
       */
      public Builder setAppLanguageBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        appLanguage_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Mail.MailGetListRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.Mail.MailGetListRequest)
    private static final com.dxx.game.dto.MailProto.MailGetListRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.MailProto.MailGetListRequest();
    }

    public static com.dxx.game.dto.MailProto.MailGetListRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<MailGetListRequest>
        PARSER = new com.google.protobuf.AbstractParser<MailGetListRequest>() {
      @java.lang.Override
      public MailGetListRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<MailGetListRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MailGetListRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.MailProto.MailGetListRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MailGetListResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Mail.MailGetListResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <pre>
     * 邮件列表
     * </pre>
     *
     * <code>repeated .Proto.Mail.MailDto mails = 2;</code>
     */
    java.util.List<com.dxx.game.dto.MailProto.MailDto> 
        getMailsList();
    /**
     * <pre>
     * 邮件列表
     * </pre>
     *
     * <code>repeated .Proto.Mail.MailDto mails = 2;</code>
     */
    com.dxx.game.dto.MailProto.MailDto getMails(int index);
    /**
     * <pre>
     * 邮件列表
     * </pre>
     *
     * <code>repeated .Proto.Mail.MailDto mails = 2;</code>
     */
    int getMailsCount();
    /**
     * <pre>
     * 邮件列表
     * </pre>
     *
     * <code>repeated .Proto.Mail.MailDto mails = 2;</code>
     */
    java.util.List<? extends com.dxx.game.dto.MailProto.MailDtoOrBuilder> 
        getMailsOrBuilderList();
    /**
     * <pre>
     * 邮件列表
     * </pre>
     *
     * <code>repeated .Proto.Mail.MailDto mails = 2;</code>
     */
    com.dxx.game.dto.MailProto.MailDtoOrBuilder getMailsOrBuilder(
        int index);
  }
  /**
   * <pre>
   * CMD PackageId=10802 
   * </pre>
   *
   * Protobuf type {@code Proto.Mail.MailGetListResponse}
   */
  public static final class MailGetListResponse extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Mail.MailGetListResponse)
      MailGetListResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        MailGetListResponse.class.getName());
    }
    // Use MailGetListResponse.newBuilder() to construct.
    private MailGetListResponse(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private MailGetListResponse() {
      mails_ = java.util.Collections.emptyList();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.MailProto.internal_static_Proto_Mail_MailGetListResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.MailProto.internal_static_Proto_Mail_MailGetListResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.MailProto.MailGetListResponse.class, com.dxx.game.dto.MailProto.MailGetListResponse.Builder.class);
    }

    public static final int CODE_FIELD_NUMBER = 1;
    private int code_ = 0;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int MAILS_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private java.util.List<com.dxx.game.dto.MailProto.MailDto> mails_;
    /**
     * <pre>
     * 邮件列表
     * </pre>
     *
     * <code>repeated .Proto.Mail.MailDto mails = 2;</code>
     */
    @java.lang.Override
    public java.util.List<com.dxx.game.dto.MailProto.MailDto> getMailsList() {
      return mails_;
    }
    /**
     * <pre>
     * 邮件列表
     * </pre>
     *
     * <code>repeated .Proto.Mail.MailDto mails = 2;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.dxx.game.dto.MailProto.MailDtoOrBuilder> 
        getMailsOrBuilderList() {
      return mails_;
    }
    /**
     * <pre>
     * 邮件列表
     * </pre>
     *
     * <code>repeated .Proto.Mail.MailDto mails = 2;</code>
     */
    @java.lang.Override
    public int getMailsCount() {
      return mails_.size();
    }
    /**
     * <pre>
     * 邮件列表
     * </pre>
     *
     * <code>repeated .Proto.Mail.MailDto mails = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.MailProto.MailDto getMails(int index) {
      return mails_.get(index);
    }
    /**
     * <pre>
     * 邮件列表
     * </pre>
     *
     * <code>repeated .Proto.Mail.MailDto mails = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.MailProto.MailDtoOrBuilder getMailsOrBuilder(
        int index) {
      return mails_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      for (int i = 0; i < mails_.size(); i++) {
        output.writeMessage(2, mails_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      for (int i = 0; i < mails_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, mails_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.MailProto.MailGetListResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.MailProto.MailGetListResponse other = (com.dxx.game.dto.MailProto.MailGetListResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (!getMailsList()
          .equals(other.getMailsList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      if (getMailsCount() > 0) {
        hash = (37 * hash) + MAILS_FIELD_NUMBER;
        hash = (53 * hash) + getMailsList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.MailProto.MailGetListResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MailProto.MailGetListResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MailProto.MailGetListResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MailProto.MailGetListResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MailProto.MailGetListResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MailProto.MailGetListResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MailProto.MailGetListResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MailProto.MailGetListResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.MailProto.MailGetListResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.MailProto.MailGetListResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.MailProto.MailGetListResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MailProto.MailGetListResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.MailProto.MailGetListResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=10802 
     * </pre>
     *
     * Protobuf type {@code Proto.Mail.MailGetListResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Mail.MailGetListResponse)
        com.dxx.game.dto.MailProto.MailGetListResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.MailProto.internal_static_Proto_Mail_MailGetListResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.MailProto.internal_static_Proto_Mail_MailGetListResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.MailProto.MailGetListResponse.class, com.dxx.game.dto.MailProto.MailGetListResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.MailProto.MailGetListResponse.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        code_ = 0;
        if (mailsBuilder_ == null) {
          mails_ = java.util.Collections.emptyList();
        } else {
          mails_ = null;
          mailsBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.MailProto.internal_static_Proto_Mail_MailGetListResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.MailProto.MailGetListResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.MailProto.MailGetListResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.MailProto.MailGetListResponse build() {
        com.dxx.game.dto.MailProto.MailGetListResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.MailProto.MailGetListResponse buildPartial() {
        com.dxx.game.dto.MailProto.MailGetListResponse result = new com.dxx.game.dto.MailProto.MailGetListResponse(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(com.dxx.game.dto.MailProto.MailGetListResponse result) {
        if (mailsBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0)) {
            mails_ = java.util.Collections.unmodifiableList(mails_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.mails_ = mails_;
        } else {
          result.mails_ = mailsBuilder_.build();
        }
      }

      private void buildPartial0(com.dxx.game.dto.MailProto.MailGetListResponse result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.code_ = code_;
        }
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.MailProto.MailGetListResponse) {
          return mergeFrom((com.dxx.game.dto.MailProto.MailGetListResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.MailProto.MailGetListResponse other) {
        if (other == com.dxx.game.dto.MailProto.MailGetListResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (mailsBuilder_ == null) {
          if (!other.mails_.isEmpty()) {
            if (mails_.isEmpty()) {
              mails_ = other.mails_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensureMailsIsMutable();
              mails_.addAll(other.mails_);
            }
            onChanged();
          }
        } else {
          if (!other.mails_.isEmpty()) {
            if (mailsBuilder_.isEmpty()) {
              mailsBuilder_.dispose();
              mailsBuilder_ = null;
              mails_ = other.mails_;
              bitField0_ = (bitField0_ & ~0x00000002);
              mailsBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getMailsFieldBuilder() : null;
            } else {
              mailsBuilder_.addAllMessages(other.mails_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                code_ = input.readInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 18: {
                com.dxx.game.dto.MailProto.MailDto m =
                    input.readMessage(
                        com.dxx.game.dto.MailProto.MailDto.parser(),
                        extensionRegistry);
                if (mailsBuilder_ == null) {
                  ensureMailsIsMutable();
                  mails_.add(m);
                } else {
                  mailsBuilder_.addMessage(m);
                }
                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {

        code_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        bitField0_ = (bitField0_ & ~0x00000001);
        code_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.dxx.game.dto.MailProto.MailDto> mails_ =
        java.util.Collections.emptyList();
      private void ensureMailsIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          mails_ = new java.util.ArrayList<com.dxx.game.dto.MailProto.MailDto>(mails_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          com.dxx.game.dto.MailProto.MailDto, com.dxx.game.dto.MailProto.MailDto.Builder, com.dxx.game.dto.MailProto.MailDtoOrBuilder> mailsBuilder_;

      /**
       * <pre>
       * 邮件列表
       * </pre>
       *
       * <code>repeated .Proto.Mail.MailDto mails = 2;</code>
       */
      public java.util.List<com.dxx.game.dto.MailProto.MailDto> getMailsList() {
        if (mailsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(mails_);
        } else {
          return mailsBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 邮件列表
       * </pre>
       *
       * <code>repeated .Proto.Mail.MailDto mails = 2;</code>
       */
      public int getMailsCount() {
        if (mailsBuilder_ == null) {
          return mails_.size();
        } else {
          return mailsBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 邮件列表
       * </pre>
       *
       * <code>repeated .Proto.Mail.MailDto mails = 2;</code>
       */
      public com.dxx.game.dto.MailProto.MailDto getMails(int index) {
        if (mailsBuilder_ == null) {
          return mails_.get(index);
        } else {
          return mailsBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 邮件列表
       * </pre>
       *
       * <code>repeated .Proto.Mail.MailDto mails = 2;</code>
       */
      public Builder setMails(
          int index, com.dxx.game.dto.MailProto.MailDto value) {
        if (mailsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureMailsIsMutable();
          mails_.set(index, value);
          onChanged();
        } else {
          mailsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 邮件列表
       * </pre>
       *
       * <code>repeated .Proto.Mail.MailDto mails = 2;</code>
       */
      public Builder setMails(
          int index, com.dxx.game.dto.MailProto.MailDto.Builder builderForValue) {
        if (mailsBuilder_ == null) {
          ensureMailsIsMutable();
          mails_.set(index, builderForValue.build());
          onChanged();
        } else {
          mailsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 邮件列表
       * </pre>
       *
       * <code>repeated .Proto.Mail.MailDto mails = 2;</code>
       */
      public Builder addMails(com.dxx.game.dto.MailProto.MailDto value) {
        if (mailsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureMailsIsMutable();
          mails_.add(value);
          onChanged();
        } else {
          mailsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 邮件列表
       * </pre>
       *
       * <code>repeated .Proto.Mail.MailDto mails = 2;</code>
       */
      public Builder addMails(
          int index, com.dxx.game.dto.MailProto.MailDto value) {
        if (mailsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureMailsIsMutable();
          mails_.add(index, value);
          onChanged();
        } else {
          mailsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 邮件列表
       * </pre>
       *
       * <code>repeated .Proto.Mail.MailDto mails = 2;</code>
       */
      public Builder addMails(
          com.dxx.game.dto.MailProto.MailDto.Builder builderForValue) {
        if (mailsBuilder_ == null) {
          ensureMailsIsMutable();
          mails_.add(builderForValue.build());
          onChanged();
        } else {
          mailsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 邮件列表
       * </pre>
       *
       * <code>repeated .Proto.Mail.MailDto mails = 2;</code>
       */
      public Builder addMails(
          int index, com.dxx.game.dto.MailProto.MailDto.Builder builderForValue) {
        if (mailsBuilder_ == null) {
          ensureMailsIsMutable();
          mails_.add(index, builderForValue.build());
          onChanged();
        } else {
          mailsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 邮件列表
       * </pre>
       *
       * <code>repeated .Proto.Mail.MailDto mails = 2;</code>
       */
      public Builder addAllMails(
          java.lang.Iterable<? extends com.dxx.game.dto.MailProto.MailDto> values) {
        if (mailsBuilder_ == null) {
          ensureMailsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, mails_);
          onChanged();
        } else {
          mailsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 邮件列表
       * </pre>
       *
       * <code>repeated .Proto.Mail.MailDto mails = 2;</code>
       */
      public Builder clearMails() {
        if (mailsBuilder_ == null) {
          mails_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          mailsBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 邮件列表
       * </pre>
       *
       * <code>repeated .Proto.Mail.MailDto mails = 2;</code>
       */
      public Builder removeMails(int index) {
        if (mailsBuilder_ == null) {
          ensureMailsIsMutable();
          mails_.remove(index);
          onChanged();
        } else {
          mailsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 邮件列表
       * </pre>
       *
       * <code>repeated .Proto.Mail.MailDto mails = 2;</code>
       */
      public com.dxx.game.dto.MailProto.MailDto.Builder getMailsBuilder(
          int index) {
        return getMailsFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 邮件列表
       * </pre>
       *
       * <code>repeated .Proto.Mail.MailDto mails = 2;</code>
       */
      public com.dxx.game.dto.MailProto.MailDtoOrBuilder getMailsOrBuilder(
          int index) {
        if (mailsBuilder_ == null) {
          return mails_.get(index);  } else {
          return mailsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 邮件列表
       * </pre>
       *
       * <code>repeated .Proto.Mail.MailDto mails = 2;</code>
       */
      public java.util.List<? extends com.dxx.game.dto.MailProto.MailDtoOrBuilder> 
           getMailsOrBuilderList() {
        if (mailsBuilder_ != null) {
          return mailsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(mails_);
        }
      }
      /**
       * <pre>
       * 邮件列表
       * </pre>
       *
       * <code>repeated .Proto.Mail.MailDto mails = 2;</code>
       */
      public com.dxx.game.dto.MailProto.MailDto.Builder addMailsBuilder() {
        return getMailsFieldBuilder().addBuilder(
            com.dxx.game.dto.MailProto.MailDto.getDefaultInstance());
      }
      /**
       * <pre>
       * 邮件列表
       * </pre>
       *
       * <code>repeated .Proto.Mail.MailDto mails = 2;</code>
       */
      public com.dxx.game.dto.MailProto.MailDto.Builder addMailsBuilder(
          int index) {
        return getMailsFieldBuilder().addBuilder(
            index, com.dxx.game.dto.MailProto.MailDto.getDefaultInstance());
      }
      /**
       * <pre>
       * 邮件列表
       * </pre>
       *
       * <code>repeated .Proto.Mail.MailDto mails = 2;</code>
       */
      public java.util.List<com.dxx.game.dto.MailProto.MailDto.Builder> 
           getMailsBuilderList() {
        return getMailsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          com.dxx.game.dto.MailProto.MailDto, com.dxx.game.dto.MailProto.MailDto.Builder, com.dxx.game.dto.MailProto.MailDtoOrBuilder> 
          getMailsFieldBuilder() {
        if (mailsBuilder_ == null) {
          mailsBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              com.dxx.game.dto.MailProto.MailDto, com.dxx.game.dto.MailProto.MailDto.Builder, com.dxx.game.dto.MailProto.MailDtoOrBuilder>(
                  mails_,
                  ((bitField0_ & 0x00000002) != 0),
                  getParentForChildren(),
                  isClean());
          mails_ = null;
        }
        return mailsBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Mail.MailGetListResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.Mail.MailGetListResponse)
    private static final com.dxx.game.dto.MailProto.MailGetListResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.MailProto.MailGetListResponse();
    }

    public static com.dxx.game.dto.MailProto.MailGetListResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<MailGetListResponse>
        PARSER = new com.google.protobuf.AbstractParser<MailGetListResponse>() {
      @java.lang.Override
      public MailGetListResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<MailGetListResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MailGetListResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.MailProto.MailGetListResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MailReceiveAwardsRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Mail.MailReceiveAwardsRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();

    /**
     * <pre>
     * 邮件rowId列表 - 多条数据就是一键领取
     * </pre>
     *
     * <code>repeated uint64 rowIds = 2;</code>
     * @return A list containing the rowIds.
     */
    java.util.List<java.lang.Long> getRowIdsList();
    /**
     * <pre>
     * 邮件rowId列表 - 多条数据就是一键领取
     * </pre>
     *
     * <code>repeated uint64 rowIds = 2;</code>
     * @return The count of rowIds.
     */
    int getRowIdsCount();
    /**
     * <pre>
     * 邮件rowId列表 - 多条数据就是一键领取
     * </pre>
     *
     * <code>repeated uint64 rowIds = 2;</code>
     * @param index The index of the element to return.
     * @return The rowIds at the given index.
     */
    long getRowIds(int index);
  }
  /**
   * <pre>
   * CMD PackageId=10803 邮件-领取奖励
   * </pre>
   *
   * Protobuf type {@code Proto.Mail.MailReceiveAwardsRequest}
   */
  public static final class MailReceiveAwardsRequest extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Mail.MailReceiveAwardsRequest)
      MailReceiveAwardsRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        MailReceiveAwardsRequest.class.getName());
    }
    // Use MailReceiveAwardsRequest.newBuilder() to construct.
    private MailReceiveAwardsRequest(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private MailReceiveAwardsRequest() {
      rowIds_ = emptyLongList();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.MailProto.internal_static_Proto_Mail_MailReceiveAwardsRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.MailProto.internal_static_Proto_Mail_MailReceiveAwardsRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.MailProto.MailReceiveAwardsRequest.class, com.dxx.game.dto.MailProto.MailReceiveAwardsRequest.Builder.class);
    }

    private int bitField0_;
    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }

    public static final int ROWIDS_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.LongList rowIds_ =
        emptyLongList();
    /**
     * <pre>
     * 邮件rowId列表 - 多条数据就是一键领取
     * </pre>
     *
     * <code>repeated uint64 rowIds = 2;</code>
     * @return A list containing the rowIds.
     */
    @java.lang.Override
    public java.util.List<java.lang.Long>
        getRowIdsList() {
      return rowIds_;
    }
    /**
     * <pre>
     * 邮件rowId列表 - 多条数据就是一键领取
     * </pre>
     *
     * <code>repeated uint64 rowIds = 2;</code>
     * @return The count of rowIds.
     */
    public int getRowIdsCount() {
      return rowIds_.size();
    }
    /**
     * <pre>
     * 邮件rowId列表 - 多条数据就是一键领取
     * </pre>
     *
     * <code>repeated uint64 rowIds = 2;</code>
     * @param index The index of the element to return.
     * @return The rowIds at the given index.
     */
    public long getRowIds(int index) {
      return rowIds_.getLong(index);
    }
    private int rowIdsMemoizedSerializedSize = -1;

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getCommonParams());
      }
      if (getRowIdsList().size() > 0) {
        output.writeUInt32NoTag(18);
        output.writeUInt32NoTag(rowIdsMemoizedSerializedSize);
      }
      for (int i = 0; i < rowIds_.size(); i++) {
        output.writeUInt64NoTag(rowIds_.getLong(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      {
        int dataSize = 0;
        for (int i = 0; i < rowIds_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeUInt64SizeNoTag(rowIds_.getLong(i));
        }
        size += dataSize;
        if (!getRowIdsList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        rowIdsMemoizedSerializedSize = dataSize;
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.MailProto.MailReceiveAwardsRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.MailProto.MailReceiveAwardsRequest other = (com.dxx.game.dto.MailProto.MailReceiveAwardsRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (!getRowIdsList()
          .equals(other.getRowIdsList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      if (getRowIdsCount() > 0) {
        hash = (37 * hash) + ROWIDS_FIELD_NUMBER;
        hash = (53 * hash) + getRowIdsList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.MailProto.MailReceiveAwardsRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MailProto.MailReceiveAwardsRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MailProto.MailReceiveAwardsRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MailProto.MailReceiveAwardsRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MailProto.MailReceiveAwardsRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MailProto.MailReceiveAwardsRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MailProto.MailReceiveAwardsRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MailProto.MailReceiveAwardsRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.MailProto.MailReceiveAwardsRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.MailProto.MailReceiveAwardsRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.MailProto.MailReceiveAwardsRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MailProto.MailReceiveAwardsRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.MailProto.MailReceiveAwardsRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=10803 邮件-领取奖励
     * </pre>
     *
     * Protobuf type {@code Proto.Mail.MailReceiveAwardsRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Mail.MailReceiveAwardsRequest)
        com.dxx.game.dto.MailProto.MailReceiveAwardsRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.MailProto.internal_static_Proto_Mail_MailReceiveAwardsRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.MailProto.internal_static_Proto_Mail_MailReceiveAwardsRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.MailProto.MailReceiveAwardsRequest.class, com.dxx.game.dto.MailProto.MailReceiveAwardsRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.MailProto.MailReceiveAwardsRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getCommonParamsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        rowIds_ = emptyLongList();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.MailProto.internal_static_Proto_Mail_MailReceiveAwardsRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.MailProto.MailReceiveAwardsRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.MailProto.MailReceiveAwardsRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.MailProto.MailReceiveAwardsRequest build() {
        com.dxx.game.dto.MailProto.MailReceiveAwardsRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.MailProto.MailReceiveAwardsRequest buildPartial() {
        com.dxx.game.dto.MailProto.MailReceiveAwardsRequest result = new com.dxx.game.dto.MailProto.MailReceiveAwardsRequest(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.MailProto.MailReceiveAwardsRequest result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.commonParams_ = commonParamsBuilder_ == null
              ? commonParams_
              : commonParamsBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          rowIds_.makeImmutable();
          result.rowIds_ = rowIds_;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.MailProto.MailReceiveAwardsRequest) {
          return mergeFrom((com.dxx.game.dto.MailProto.MailReceiveAwardsRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.MailProto.MailReceiveAwardsRequest other) {
        if (other == com.dxx.game.dto.MailProto.MailReceiveAwardsRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        if (!other.rowIds_.isEmpty()) {
          if (rowIds_.isEmpty()) {
            rowIds_ = other.rowIds_;
            rowIds_.makeImmutable();
            bitField0_ |= 0x00000002;
          } else {
            ensureRowIdsIsMutable();
            rowIds_.addAll(other.rowIds_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getCommonParamsFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 16: {
                long v = input.readUInt64();
                ensureRowIdsIsMutable();
                rowIds_.addLong(v);
                break;
              } // case 16
              case 18: {
                int length = input.readRawVarint32();
                int limit = input.pushLimit(length);
                ensureRowIdsIsMutable();
                while (input.getBytesUntilLimit() > 0) {
                  rowIds_.addLong(input.readUInt64());
                }
                input.popLimit(limit);
                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
        } else {
          commonParamsBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
            commonParams_ != null &&
            commonParams_ != com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance()) {
            getCommonParamsBuilder().mergeFrom(value);
          } else {
            commonParams_ = value;
          }
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }
        if (commonParams_ != null) {
          bitField0_ |= 0x00000001;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        bitField0_ = (bitField0_ & ~0x00000001);
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }

      private com.google.protobuf.Internal.LongList rowIds_ = emptyLongList();
      private void ensureRowIdsIsMutable() {
        if (!rowIds_.isModifiable()) {
          rowIds_ = makeMutableCopy(rowIds_);
        }
        bitField0_ |= 0x00000002;
      }
      /**
       * <pre>
       * 邮件rowId列表 - 多条数据就是一键领取
       * </pre>
       *
       * <code>repeated uint64 rowIds = 2;</code>
       * @return A list containing the rowIds.
       */
      public java.util.List<java.lang.Long>
          getRowIdsList() {
        rowIds_.makeImmutable();
        return rowIds_;
      }
      /**
       * <pre>
       * 邮件rowId列表 - 多条数据就是一键领取
       * </pre>
       *
       * <code>repeated uint64 rowIds = 2;</code>
       * @return The count of rowIds.
       */
      public int getRowIdsCount() {
        return rowIds_.size();
      }
      /**
       * <pre>
       * 邮件rowId列表 - 多条数据就是一键领取
       * </pre>
       *
       * <code>repeated uint64 rowIds = 2;</code>
       * @param index The index of the element to return.
       * @return The rowIds at the given index.
       */
      public long getRowIds(int index) {
        return rowIds_.getLong(index);
      }
      /**
       * <pre>
       * 邮件rowId列表 - 多条数据就是一键领取
       * </pre>
       *
       * <code>repeated uint64 rowIds = 2;</code>
       * @param index The index to set the value at.
       * @param value The rowIds to set.
       * @return This builder for chaining.
       */
      public Builder setRowIds(
          int index, long value) {

        ensureRowIdsIsMutable();
        rowIds_.setLong(index, value);
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 邮件rowId列表 - 多条数据就是一键领取
       * </pre>
       *
       * <code>repeated uint64 rowIds = 2;</code>
       * @param value The rowIds to add.
       * @return This builder for chaining.
       */
      public Builder addRowIds(long value) {

        ensureRowIdsIsMutable();
        rowIds_.addLong(value);
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 邮件rowId列表 - 多条数据就是一键领取
       * </pre>
       *
       * <code>repeated uint64 rowIds = 2;</code>
       * @param values The rowIds to add.
       * @return This builder for chaining.
       */
      public Builder addAllRowIds(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensureRowIdsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, rowIds_);
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 邮件rowId列表 - 多条数据就是一键领取
       * </pre>
       *
       * <code>repeated uint64 rowIds = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearRowIds() {
        rowIds_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Mail.MailReceiveAwardsRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.Mail.MailReceiveAwardsRequest)
    private static final com.dxx.game.dto.MailProto.MailReceiveAwardsRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.MailProto.MailReceiveAwardsRequest();
    }

    public static com.dxx.game.dto.MailProto.MailReceiveAwardsRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<MailReceiveAwardsRequest>
        PARSER = new com.google.protobuf.AbstractParser<MailReceiveAwardsRequest>() {
      @java.lang.Override
      public MailReceiveAwardsRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<MailReceiveAwardsRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MailReceiveAwardsRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.MailProto.MailReceiveAwardsRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MailReceiveAwardsResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Mail.MailReceiveAwardsResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <pre>
     * 已领取邮件的rowIds - 客户端需要自己把邮件列表返回的MailDto里rewardsState状态改成true
     * </pre>
     *
     * <code>repeated uint64 rowIds = 2;</code>
     * @return A list containing the rowIds.
     */
    java.util.List<java.lang.Long> getRowIdsList();
    /**
     * <pre>
     * 已领取邮件的rowIds - 客户端需要自己把邮件列表返回的MailDto里rewardsState状态改成true
     * </pre>
     *
     * <code>repeated uint64 rowIds = 2;</code>
     * @return The count of rowIds.
     */
    int getRowIdsCount();
    /**
     * <pre>
     * 已领取邮件的rowIds - 客户端需要自己把邮件列表返回的MailDto里rewardsState状态改成true
     * </pre>
     *
     * <code>repeated uint64 rowIds = 2;</code>
     * @param index The index of the element to return.
     * @return The rowIds at the given index.
     */
    long getRowIds(int index);

    /**
     * <code>.Proto.Common.CommonData commonData = 3;</code>
     * @return Whether the commonData field is set.
     */
    boolean hasCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 3;</code>
     * @return The commonData.
     */
    com.dxx.game.dto.CommonProto.CommonData getCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 3;</code>
     */
    com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder();
  }
  /**
   * <pre>
   * CMD PackageId=10804 
   * </pre>
   *
   * Protobuf type {@code Proto.Mail.MailReceiveAwardsResponse}
   */
  public static final class MailReceiveAwardsResponse extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Mail.MailReceiveAwardsResponse)
      MailReceiveAwardsResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        MailReceiveAwardsResponse.class.getName());
    }
    // Use MailReceiveAwardsResponse.newBuilder() to construct.
    private MailReceiveAwardsResponse(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private MailReceiveAwardsResponse() {
      rowIds_ = emptyLongList();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.MailProto.internal_static_Proto_Mail_MailReceiveAwardsResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.MailProto.internal_static_Proto_Mail_MailReceiveAwardsResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.MailProto.MailReceiveAwardsResponse.class, com.dxx.game.dto.MailProto.MailReceiveAwardsResponse.Builder.class);
    }

    private int bitField0_;
    public static final int CODE_FIELD_NUMBER = 1;
    private int code_ = 0;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int ROWIDS_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.LongList rowIds_ =
        emptyLongList();
    /**
     * <pre>
     * 已领取邮件的rowIds - 客户端需要自己把邮件列表返回的MailDto里rewardsState状态改成true
     * </pre>
     *
     * <code>repeated uint64 rowIds = 2;</code>
     * @return A list containing the rowIds.
     */
    @java.lang.Override
    public java.util.List<java.lang.Long>
        getRowIdsList() {
      return rowIds_;
    }
    /**
     * <pre>
     * 已领取邮件的rowIds - 客户端需要自己把邮件列表返回的MailDto里rewardsState状态改成true
     * </pre>
     *
     * <code>repeated uint64 rowIds = 2;</code>
     * @return The count of rowIds.
     */
    public int getRowIdsCount() {
      return rowIds_.size();
    }
    /**
     * <pre>
     * 已领取邮件的rowIds - 客户端需要自己把邮件列表返回的MailDto里rewardsState状态改成true
     * </pre>
     *
     * <code>repeated uint64 rowIds = 2;</code>
     * @param index The index of the element to return.
     * @return The rowIds at the given index.
     */
    public long getRowIds(int index) {
      return rowIds_.getLong(index);
    }
    private int rowIdsMemoizedSerializedSize = -1;

    public static final int COMMONDATA_FIELD_NUMBER = 3;
    private com.dxx.game.dto.CommonProto.CommonData commonData_;
    /**
     * <code>.Proto.Common.CommonData commonData = 3;</code>
     * @return Whether the commonData field is set.
     */
    @java.lang.Override
    public boolean hasCommonData() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 3;</code>
     * @return The commonData.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 3;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (getRowIdsList().size() > 0) {
        output.writeUInt32NoTag(18);
        output.writeUInt32NoTag(rowIdsMemoizedSerializedSize);
      }
      for (int i = 0; i < rowIds_.size(); i++) {
        output.writeUInt64NoTag(rowIds_.getLong(i));
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(3, getCommonData());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < rowIds_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeUInt64SizeNoTag(rowIds_.getLong(i));
        }
        size += dataSize;
        if (!getRowIdsList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        rowIdsMemoizedSerializedSize = dataSize;
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getCommonData());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.MailProto.MailReceiveAwardsResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.MailProto.MailReceiveAwardsResponse other = (com.dxx.game.dto.MailProto.MailReceiveAwardsResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (!getRowIdsList()
          .equals(other.getRowIdsList())) return false;
      if (hasCommonData() != other.hasCommonData()) return false;
      if (hasCommonData()) {
        if (!getCommonData()
            .equals(other.getCommonData())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      if (getRowIdsCount() > 0) {
        hash = (37 * hash) + ROWIDS_FIELD_NUMBER;
        hash = (53 * hash) + getRowIdsList().hashCode();
      }
      if (hasCommonData()) {
        hash = (37 * hash) + COMMONDATA_FIELD_NUMBER;
        hash = (53 * hash) + getCommonData().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.MailProto.MailReceiveAwardsResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MailProto.MailReceiveAwardsResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MailProto.MailReceiveAwardsResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MailProto.MailReceiveAwardsResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MailProto.MailReceiveAwardsResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MailProto.MailReceiveAwardsResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MailProto.MailReceiveAwardsResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MailProto.MailReceiveAwardsResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.MailProto.MailReceiveAwardsResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.MailProto.MailReceiveAwardsResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.MailProto.MailReceiveAwardsResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MailProto.MailReceiveAwardsResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.MailProto.MailReceiveAwardsResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=10804 
     * </pre>
     *
     * Protobuf type {@code Proto.Mail.MailReceiveAwardsResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Mail.MailReceiveAwardsResponse)
        com.dxx.game.dto.MailProto.MailReceiveAwardsResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.MailProto.internal_static_Proto_Mail_MailReceiveAwardsResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.MailProto.internal_static_Proto_Mail_MailReceiveAwardsResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.MailProto.MailReceiveAwardsResponse.class, com.dxx.game.dto.MailProto.MailReceiveAwardsResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.MailProto.MailReceiveAwardsResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getCommonDataFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        code_ = 0;
        rowIds_ = emptyLongList();
        commonData_ = null;
        if (commonDataBuilder_ != null) {
          commonDataBuilder_.dispose();
          commonDataBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.MailProto.internal_static_Proto_Mail_MailReceiveAwardsResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.MailProto.MailReceiveAwardsResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.MailProto.MailReceiveAwardsResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.MailProto.MailReceiveAwardsResponse build() {
        com.dxx.game.dto.MailProto.MailReceiveAwardsResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.MailProto.MailReceiveAwardsResponse buildPartial() {
        com.dxx.game.dto.MailProto.MailReceiveAwardsResponse result = new com.dxx.game.dto.MailProto.MailReceiveAwardsResponse(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.MailProto.MailReceiveAwardsResponse result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.code_ = code_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          rowIds_.makeImmutable();
          result.rowIds_ = rowIds_;
        }
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.commonData_ = commonDataBuilder_ == null
              ? commonData_
              : commonDataBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.MailProto.MailReceiveAwardsResponse) {
          return mergeFrom((com.dxx.game.dto.MailProto.MailReceiveAwardsResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.MailProto.MailReceiveAwardsResponse other) {
        if (other == com.dxx.game.dto.MailProto.MailReceiveAwardsResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (!other.rowIds_.isEmpty()) {
          if (rowIds_.isEmpty()) {
            rowIds_ = other.rowIds_;
            rowIds_.makeImmutable();
            bitField0_ |= 0x00000002;
          } else {
            ensureRowIdsIsMutable();
            rowIds_.addAll(other.rowIds_);
          }
          onChanged();
        }
        if (other.hasCommonData()) {
          mergeCommonData(other.getCommonData());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                code_ = input.readInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                long v = input.readUInt64();
                ensureRowIdsIsMutable();
                rowIds_.addLong(v);
                break;
              } // case 16
              case 18: {
                int length = input.readRawVarint32();
                int limit = input.pushLimit(length);
                ensureRowIdsIsMutable();
                while (input.getBytesUntilLimit() > 0) {
                  rowIds_.addLong(input.readUInt64());
                }
                input.popLimit(limit);
                break;
              } // case 18
              case 26: {
                input.readMessage(
                    getCommonDataFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000004;
                break;
              } // case 26
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {

        code_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        bitField0_ = (bitField0_ & ~0x00000001);
        code_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.LongList rowIds_ = emptyLongList();
      private void ensureRowIdsIsMutable() {
        if (!rowIds_.isModifiable()) {
          rowIds_ = makeMutableCopy(rowIds_);
        }
        bitField0_ |= 0x00000002;
      }
      /**
       * <pre>
       * 已领取邮件的rowIds - 客户端需要自己把邮件列表返回的MailDto里rewardsState状态改成true
       * </pre>
       *
       * <code>repeated uint64 rowIds = 2;</code>
       * @return A list containing the rowIds.
       */
      public java.util.List<java.lang.Long>
          getRowIdsList() {
        rowIds_.makeImmutable();
        return rowIds_;
      }
      /**
       * <pre>
       * 已领取邮件的rowIds - 客户端需要自己把邮件列表返回的MailDto里rewardsState状态改成true
       * </pre>
       *
       * <code>repeated uint64 rowIds = 2;</code>
       * @return The count of rowIds.
       */
      public int getRowIdsCount() {
        return rowIds_.size();
      }
      /**
       * <pre>
       * 已领取邮件的rowIds - 客户端需要自己把邮件列表返回的MailDto里rewardsState状态改成true
       * </pre>
       *
       * <code>repeated uint64 rowIds = 2;</code>
       * @param index The index of the element to return.
       * @return The rowIds at the given index.
       */
      public long getRowIds(int index) {
        return rowIds_.getLong(index);
      }
      /**
       * <pre>
       * 已领取邮件的rowIds - 客户端需要自己把邮件列表返回的MailDto里rewardsState状态改成true
       * </pre>
       *
       * <code>repeated uint64 rowIds = 2;</code>
       * @param index The index to set the value at.
       * @param value The rowIds to set.
       * @return This builder for chaining.
       */
      public Builder setRowIds(
          int index, long value) {

        ensureRowIdsIsMutable();
        rowIds_.setLong(index, value);
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 已领取邮件的rowIds - 客户端需要自己把邮件列表返回的MailDto里rewardsState状态改成true
       * </pre>
       *
       * <code>repeated uint64 rowIds = 2;</code>
       * @param value The rowIds to add.
       * @return This builder for chaining.
       */
      public Builder addRowIds(long value) {

        ensureRowIdsIsMutable();
        rowIds_.addLong(value);
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 已领取邮件的rowIds - 客户端需要自己把邮件列表返回的MailDto里rewardsState状态改成true
       * </pre>
       *
       * <code>repeated uint64 rowIds = 2;</code>
       * @param values The rowIds to add.
       * @return This builder for chaining.
       */
      public Builder addAllRowIds(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensureRowIdsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, rowIds_);
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 已领取邮件的rowIds - 客户端需要自己把邮件列表返回的MailDto里rewardsState状态改成true
       * </pre>
       *
       * <code>repeated uint64 rowIds = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearRowIds() {
        rowIds_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }

      private com.dxx.game.dto.CommonProto.CommonData commonData_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> commonDataBuilder_;
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       * @return Whether the commonData field is set.
       */
      public boolean hasCommonData() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       * @return The commonData.
       */
      public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
        if (commonDataBuilder_ == null) {
          return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        } else {
          return commonDataBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       */
      public Builder setCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonData_ = value;
        } else {
          commonDataBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       */
      public Builder setCommonData(
          com.dxx.game.dto.CommonProto.CommonData.Builder builderForValue) {
        if (commonDataBuilder_ == null) {
          commonData_ = builderForValue.build();
        } else {
          commonDataBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       */
      public Builder mergeCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0) &&
            commonData_ != null &&
            commonData_ != com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance()) {
            getCommonDataBuilder().mergeFrom(value);
          } else {
            commonData_ = value;
          }
        } else {
          commonDataBuilder_.mergeFrom(value);
        }
        if (commonData_ != null) {
          bitField0_ |= 0x00000004;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       */
      public Builder clearCommonData() {
        bitField0_ = (bitField0_ & ~0x00000004);
        commonData_ = null;
        if (commonDataBuilder_ != null) {
          commonDataBuilder_.dispose();
          commonDataBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonData.Builder getCommonDataBuilder() {
        bitField0_ |= 0x00000004;
        onChanged();
        return getCommonDataFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
        if (commonDataBuilder_ != null) {
          return commonDataBuilder_.getMessageOrBuilder();
        } else {
          return commonData_ == null ?
              com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> 
          getCommonDataFieldBuilder() {
        if (commonDataBuilder_ == null) {
          commonDataBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder>(
                  getCommonData(),
                  getParentForChildren(),
                  isClean());
          commonData_ = null;
        }
        return commonDataBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Mail.MailReceiveAwardsResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.Mail.MailReceiveAwardsResponse)
    private static final com.dxx.game.dto.MailProto.MailReceiveAwardsResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.MailProto.MailReceiveAwardsResponse();
    }

    public static com.dxx.game.dto.MailProto.MailReceiveAwardsResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<MailReceiveAwardsResponse>
        PARSER = new com.google.protobuf.AbstractParser<MailReceiveAwardsResponse>() {
      @java.lang.Override
      public MailReceiveAwardsResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<MailReceiveAwardsResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MailReceiveAwardsResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.MailProto.MailReceiveAwardsResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MailDeleteRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Mail.MailDeleteRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();

    /**
     * <pre>
     * 邮件rowId列表 - 多条数据就是一键删除
     * </pre>
     *
     * <code>repeated uint64 rowIds = 2;</code>
     * @return A list containing the rowIds.
     */
    java.util.List<java.lang.Long> getRowIdsList();
    /**
     * <pre>
     * 邮件rowId列表 - 多条数据就是一键删除
     * </pre>
     *
     * <code>repeated uint64 rowIds = 2;</code>
     * @return The count of rowIds.
     */
    int getRowIdsCount();
    /**
     * <pre>
     * 邮件rowId列表 - 多条数据就是一键删除
     * </pre>
     *
     * <code>repeated uint64 rowIds = 2;</code>
     * @param index The index of the element to return.
     * @return The rowIds at the given index.
     */
    long getRowIds(int index);
  }
  /**
   * <pre>
   * CMD PackageId=10805 邮件-删除
   * </pre>
   *
   * Protobuf type {@code Proto.Mail.MailDeleteRequest}
   */
  public static final class MailDeleteRequest extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Mail.MailDeleteRequest)
      MailDeleteRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        MailDeleteRequest.class.getName());
    }
    // Use MailDeleteRequest.newBuilder() to construct.
    private MailDeleteRequest(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private MailDeleteRequest() {
      rowIds_ = emptyLongList();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.MailProto.internal_static_Proto_Mail_MailDeleteRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.MailProto.internal_static_Proto_Mail_MailDeleteRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.MailProto.MailDeleteRequest.class, com.dxx.game.dto.MailProto.MailDeleteRequest.Builder.class);
    }

    private int bitField0_;
    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }

    public static final int ROWIDS_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.LongList rowIds_ =
        emptyLongList();
    /**
     * <pre>
     * 邮件rowId列表 - 多条数据就是一键删除
     * </pre>
     *
     * <code>repeated uint64 rowIds = 2;</code>
     * @return A list containing the rowIds.
     */
    @java.lang.Override
    public java.util.List<java.lang.Long>
        getRowIdsList() {
      return rowIds_;
    }
    /**
     * <pre>
     * 邮件rowId列表 - 多条数据就是一键删除
     * </pre>
     *
     * <code>repeated uint64 rowIds = 2;</code>
     * @return The count of rowIds.
     */
    public int getRowIdsCount() {
      return rowIds_.size();
    }
    /**
     * <pre>
     * 邮件rowId列表 - 多条数据就是一键删除
     * </pre>
     *
     * <code>repeated uint64 rowIds = 2;</code>
     * @param index The index of the element to return.
     * @return The rowIds at the given index.
     */
    public long getRowIds(int index) {
      return rowIds_.getLong(index);
    }
    private int rowIdsMemoizedSerializedSize = -1;

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getCommonParams());
      }
      if (getRowIdsList().size() > 0) {
        output.writeUInt32NoTag(18);
        output.writeUInt32NoTag(rowIdsMemoizedSerializedSize);
      }
      for (int i = 0; i < rowIds_.size(); i++) {
        output.writeUInt64NoTag(rowIds_.getLong(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      {
        int dataSize = 0;
        for (int i = 0; i < rowIds_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeUInt64SizeNoTag(rowIds_.getLong(i));
        }
        size += dataSize;
        if (!getRowIdsList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        rowIdsMemoizedSerializedSize = dataSize;
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.MailProto.MailDeleteRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.MailProto.MailDeleteRequest other = (com.dxx.game.dto.MailProto.MailDeleteRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (!getRowIdsList()
          .equals(other.getRowIdsList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      if (getRowIdsCount() > 0) {
        hash = (37 * hash) + ROWIDS_FIELD_NUMBER;
        hash = (53 * hash) + getRowIdsList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.MailProto.MailDeleteRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MailProto.MailDeleteRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MailProto.MailDeleteRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MailProto.MailDeleteRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MailProto.MailDeleteRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MailProto.MailDeleteRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MailProto.MailDeleteRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MailProto.MailDeleteRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.MailProto.MailDeleteRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.MailProto.MailDeleteRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.MailProto.MailDeleteRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MailProto.MailDeleteRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.MailProto.MailDeleteRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=10805 邮件-删除
     * </pre>
     *
     * Protobuf type {@code Proto.Mail.MailDeleteRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Mail.MailDeleteRequest)
        com.dxx.game.dto.MailProto.MailDeleteRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.MailProto.internal_static_Proto_Mail_MailDeleteRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.MailProto.internal_static_Proto_Mail_MailDeleteRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.MailProto.MailDeleteRequest.class, com.dxx.game.dto.MailProto.MailDeleteRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.MailProto.MailDeleteRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getCommonParamsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        rowIds_ = emptyLongList();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.MailProto.internal_static_Proto_Mail_MailDeleteRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.MailProto.MailDeleteRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.MailProto.MailDeleteRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.MailProto.MailDeleteRequest build() {
        com.dxx.game.dto.MailProto.MailDeleteRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.MailProto.MailDeleteRequest buildPartial() {
        com.dxx.game.dto.MailProto.MailDeleteRequest result = new com.dxx.game.dto.MailProto.MailDeleteRequest(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.MailProto.MailDeleteRequest result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.commonParams_ = commonParamsBuilder_ == null
              ? commonParams_
              : commonParamsBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          rowIds_.makeImmutable();
          result.rowIds_ = rowIds_;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.MailProto.MailDeleteRequest) {
          return mergeFrom((com.dxx.game.dto.MailProto.MailDeleteRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.MailProto.MailDeleteRequest other) {
        if (other == com.dxx.game.dto.MailProto.MailDeleteRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        if (!other.rowIds_.isEmpty()) {
          if (rowIds_.isEmpty()) {
            rowIds_ = other.rowIds_;
            rowIds_.makeImmutable();
            bitField0_ |= 0x00000002;
          } else {
            ensureRowIdsIsMutable();
            rowIds_.addAll(other.rowIds_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getCommonParamsFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 16: {
                long v = input.readUInt64();
                ensureRowIdsIsMutable();
                rowIds_.addLong(v);
                break;
              } // case 16
              case 18: {
                int length = input.readRawVarint32();
                int limit = input.pushLimit(length);
                ensureRowIdsIsMutable();
                while (input.getBytesUntilLimit() > 0) {
                  rowIds_.addLong(input.readUInt64());
                }
                input.popLimit(limit);
                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
        } else {
          commonParamsBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
            commonParams_ != null &&
            commonParams_ != com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance()) {
            getCommonParamsBuilder().mergeFrom(value);
          } else {
            commonParams_ = value;
          }
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }
        if (commonParams_ != null) {
          bitField0_ |= 0x00000001;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        bitField0_ = (bitField0_ & ~0x00000001);
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }

      private com.google.protobuf.Internal.LongList rowIds_ = emptyLongList();
      private void ensureRowIdsIsMutable() {
        if (!rowIds_.isModifiable()) {
          rowIds_ = makeMutableCopy(rowIds_);
        }
        bitField0_ |= 0x00000002;
      }
      /**
       * <pre>
       * 邮件rowId列表 - 多条数据就是一键删除
       * </pre>
       *
       * <code>repeated uint64 rowIds = 2;</code>
       * @return A list containing the rowIds.
       */
      public java.util.List<java.lang.Long>
          getRowIdsList() {
        rowIds_.makeImmutable();
        return rowIds_;
      }
      /**
       * <pre>
       * 邮件rowId列表 - 多条数据就是一键删除
       * </pre>
       *
       * <code>repeated uint64 rowIds = 2;</code>
       * @return The count of rowIds.
       */
      public int getRowIdsCount() {
        return rowIds_.size();
      }
      /**
       * <pre>
       * 邮件rowId列表 - 多条数据就是一键删除
       * </pre>
       *
       * <code>repeated uint64 rowIds = 2;</code>
       * @param index The index of the element to return.
       * @return The rowIds at the given index.
       */
      public long getRowIds(int index) {
        return rowIds_.getLong(index);
      }
      /**
       * <pre>
       * 邮件rowId列表 - 多条数据就是一键删除
       * </pre>
       *
       * <code>repeated uint64 rowIds = 2;</code>
       * @param index The index to set the value at.
       * @param value The rowIds to set.
       * @return This builder for chaining.
       */
      public Builder setRowIds(
          int index, long value) {

        ensureRowIdsIsMutable();
        rowIds_.setLong(index, value);
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 邮件rowId列表 - 多条数据就是一键删除
       * </pre>
       *
       * <code>repeated uint64 rowIds = 2;</code>
       * @param value The rowIds to add.
       * @return This builder for chaining.
       */
      public Builder addRowIds(long value) {

        ensureRowIdsIsMutable();
        rowIds_.addLong(value);
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 邮件rowId列表 - 多条数据就是一键删除
       * </pre>
       *
       * <code>repeated uint64 rowIds = 2;</code>
       * @param values The rowIds to add.
       * @return This builder for chaining.
       */
      public Builder addAllRowIds(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensureRowIdsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, rowIds_);
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 邮件rowId列表 - 多条数据就是一键删除
       * </pre>
       *
       * <code>repeated uint64 rowIds = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearRowIds() {
        rowIds_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Mail.MailDeleteRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.Mail.MailDeleteRequest)
    private static final com.dxx.game.dto.MailProto.MailDeleteRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.MailProto.MailDeleteRequest();
    }

    public static com.dxx.game.dto.MailProto.MailDeleteRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<MailDeleteRequest>
        PARSER = new com.google.protobuf.AbstractParser<MailDeleteRequest>() {
      @java.lang.Override
      public MailDeleteRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<MailDeleteRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MailDeleteRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.MailProto.MailDeleteRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MailDeleteResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Mail.MailDeleteResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <pre>
     * 已删除邮件的rowIds
     * </pre>
     *
     * <code>repeated uint64 rowIds = 2;</code>
     * @return A list containing the rowIds.
     */
    java.util.List<java.lang.Long> getRowIdsList();
    /**
     * <pre>
     * 已删除邮件的rowIds
     * </pre>
     *
     * <code>repeated uint64 rowIds = 2;</code>
     * @return The count of rowIds.
     */
    int getRowIdsCount();
    /**
     * <pre>
     * 已删除邮件的rowIds
     * </pre>
     *
     * <code>repeated uint64 rowIds = 2;</code>
     * @param index The index of the element to return.
     * @return The rowIds at the given index.
     */
    long getRowIds(int index);
  }
  /**
   * <pre>
   * CMD PackageId=10806 
   * </pre>
   *
   * Protobuf type {@code Proto.Mail.MailDeleteResponse}
   */
  public static final class MailDeleteResponse extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Mail.MailDeleteResponse)
      MailDeleteResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        MailDeleteResponse.class.getName());
    }
    // Use MailDeleteResponse.newBuilder() to construct.
    private MailDeleteResponse(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private MailDeleteResponse() {
      rowIds_ = emptyLongList();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.MailProto.internal_static_Proto_Mail_MailDeleteResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.MailProto.internal_static_Proto_Mail_MailDeleteResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.MailProto.MailDeleteResponse.class, com.dxx.game.dto.MailProto.MailDeleteResponse.Builder.class);
    }

    public static final int CODE_FIELD_NUMBER = 1;
    private int code_ = 0;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int ROWIDS_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.LongList rowIds_ =
        emptyLongList();
    /**
     * <pre>
     * 已删除邮件的rowIds
     * </pre>
     *
     * <code>repeated uint64 rowIds = 2;</code>
     * @return A list containing the rowIds.
     */
    @java.lang.Override
    public java.util.List<java.lang.Long>
        getRowIdsList() {
      return rowIds_;
    }
    /**
     * <pre>
     * 已删除邮件的rowIds
     * </pre>
     *
     * <code>repeated uint64 rowIds = 2;</code>
     * @return The count of rowIds.
     */
    public int getRowIdsCount() {
      return rowIds_.size();
    }
    /**
     * <pre>
     * 已删除邮件的rowIds
     * </pre>
     *
     * <code>repeated uint64 rowIds = 2;</code>
     * @param index The index of the element to return.
     * @return The rowIds at the given index.
     */
    public long getRowIds(int index) {
      return rowIds_.getLong(index);
    }
    private int rowIdsMemoizedSerializedSize = -1;

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (getRowIdsList().size() > 0) {
        output.writeUInt32NoTag(18);
        output.writeUInt32NoTag(rowIdsMemoizedSerializedSize);
      }
      for (int i = 0; i < rowIds_.size(); i++) {
        output.writeUInt64NoTag(rowIds_.getLong(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < rowIds_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeUInt64SizeNoTag(rowIds_.getLong(i));
        }
        size += dataSize;
        if (!getRowIdsList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        rowIdsMemoizedSerializedSize = dataSize;
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.MailProto.MailDeleteResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.MailProto.MailDeleteResponse other = (com.dxx.game.dto.MailProto.MailDeleteResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (!getRowIdsList()
          .equals(other.getRowIdsList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      if (getRowIdsCount() > 0) {
        hash = (37 * hash) + ROWIDS_FIELD_NUMBER;
        hash = (53 * hash) + getRowIdsList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.MailProto.MailDeleteResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MailProto.MailDeleteResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MailProto.MailDeleteResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MailProto.MailDeleteResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MailProto.MailDeleteResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MailProto.MailDeleteResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MailProto.MailDeleteResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MailProto.MailDeleteResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.MailProto.MailDeleteResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.MailProto.MailDeleteResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.MailProto.MailDeleteResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MailProto.MailDeleteResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.MailProto.MailDeleteResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=10806 
     * </pre>
     *
     * Protobuf type {@code Proto.Mail.MailDeleteResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Mail.MailDeleteResponse)
        com.dxx.game.dto.MailProto.MailDeleteResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.MailProto.internal_static_Proto_Mail_MailDeleteResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.MailProto.internal_static_Proto_Mail_MailDeleteResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.MailProto.MailDeleteResponse.class, com.dxx.game.dto.MailProto.MailDeleteResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.MailProto.MailDeleteResponse.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        code_ = 0;
        rowIds_ = emptyLongList();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.MailProto.internal_static_Proto_Mail_MailDeleteResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.MailProto.MailDeleteResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.MailProto.MailDeleteResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.MailProto.MailDeleteResponse build() {
        com.dxx.game.dto.MailProto.MailDeleteResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.MailProto.MailDeleteResponse buildPartial() {
        com.dxx.game.dto.MailProto.MailDeleteResponse result = new com.dxx.game.dto.MailProto.MailDeleteResponse(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.MailProto.MailDeleteResponse result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.code_ = code_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          rowIds_.makeImmutable();
          result.rowIds_ = rowIds_;
        }
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.MailProto.MailDeleteResponse) {
          return mergeFrom((com.dxx.game.dto.MailProto.MailDeleteResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.MailProto.MailDeleteResponse other) {
        if (other == com.dxx.game.dto.MailProto.MailDeleteResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (!other.rowIds_.isEmpty()) {
          if (rowIds_.isEmpty()) {
            rowIds_ = other.rowIds_;
            rowIds_.makeImmutable();
            bitField0_ |= 0x00000002;
          } else {
            ensureRowIdsIsMutable();
            rowIds_.addAll(other.rowIds_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                code_ = input.readInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                long v = input.readUInt64();
                ensureRowIdsIsMutable();
                rowIds_.addLong(v);
                break;
              } // case 16
              case 18: {
                int length = input.readRawVarint32();
                int limit = input.pushLimit(length);
                ensureRowIdsIsMutable();
                while (input.getBytesUntilLimit() > 0) {
                  rowIds_.addLong(input.readUInt64());
                }
                input.popLimit(limit);
                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {

        code_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        bitField0_ = (bitField0_ & ~0x00000001);
        code_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.LongList rowIds_ = emptyLongList();
      private void ensureRowIdsIsMutable() {
        if (!rowIds_.isModifiable()) {
          rowIds_ = makeMutableCopy(rowIds_);
        }
        bitField0_ |= 0x00000002;
      }
      /**
       * <pre>
       * 已删除邮件的rowIds
       * </pre>
       *
       * <code>repeated uint64 rowIds = 2;</code>
       * @return A list containing the rowIds.
       */
      public java.util.List<java.lang.Long>
          getRowIdsList() {
        rowIds_.makeImmutable();
        return rowIds_;
      }
      /**
       * <pre>
       * 已删除邮件的rowIds
       * </pre>
       *
       * <code>repeated uint64 rowIds = 2;</code>
       * @return The count of rowIds.
       */
      public int getRowIdsCount() {
        return rowIds_.size();
      }
      /**
       * <pre>
       * 已删除邮件的rowIds
       * </pre>
       *
       * <code>repeated uint64 rowIds = 2;</code>
       * @param index The index of the element to return.
       * @return The rowIds at the given index.
       */
      public long getRowIds(int index) {
        return rowIds_.getLong(index);
      }
      /**
       * <pre>
       * 已删除邮件的rowIds
       * </pre>
       *
       * <code>repeated uint64 rowIds = 2;</code>
       * @param index The index to set the value at.
       * @param value The rowIds to set.
       * @return This builder for chaining.
       */
      public Builder setRowIds(
          int index, long value) {

        ensureRowIdsIsMutable();
        rowIds_.setLong(index, value);
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 已删除邮件的rowIds
       * </pre>
       *
       * <code>repeated uint64 rowIds = 2;</code>
       * @param value The rowIds to add.
       * @return This builder for chaining.
       */
      public Builder addRowIds(long value) {

        ensureRowIdsIsMutable();
        rowIds_.addLong(value);
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 已删除邮件的rowIds
       * </pre>
       *
       * <code>repeated uint64 rowIds = 2;</code>
       * @param values The rowIds to add.
       * @return This builder for chaining.
       */
      public Builder addAllRowIds(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensureRowIdsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, rowIds_);
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 已删除邮件的rowIds
       * </pre>
       *
       * <code>repeated uint64 rowIds = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearRowIds() {
        rowIds_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Mail.MailDeleteResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.Mail.MailDeleteResponse)
    private static final com.dxx.game.dto.MailProto.MailDeleteResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.MailProto.MailDeleteResponse();
    }

    public static com.dxx.game.dto.MailProto.MailDeleteResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<MailDeleteResponse>
        PARSER = new com.google.protobuf.AbstractParser<MailDeleteResponse>() {
      @java.lang.Override
      public MailDeleteResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<MailDeleteResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MailDeleteResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.MailProto.MailDeleteResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MailDtoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Mail.MailDto)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 唯一ID
     * </pre>
     *
     * <code>uint64 rowId = 1;</code>
     * @return The rowId.
     */
    long getRowId();

    /**
     * <pre>
     * 标题
     * </pre>
     *
     * <code>string title = 2;</code>
     * @return The title.
     */
    java.lang.String getTitle();
    /**
     * <pre>
     * 标题
     * </pre>
     *
     * <code>string title = 2;</code>
     * @return The bytes for title.
     */
    com.google.protobuf.ByteString
        getTitleBytes();

    /**
     * <pre>
     * 内容
     * </pre>
     *
     * <code>string content = 3;</code>
     * @return The content.
     */
    java.lang.String getContent();
    /**
     * <pre>
     * 内容
     * </pre>
     *
     * <code>string content = 3;</code>
     * @return The bytes for content.
     */
    com.google.protobuf.ByteString
        getContentBytes();

    /**
     * <pre>
     * 附件奖励
     * </pre>
     *
     * <code>repeated .Proto.Common.RewardDto rewards = 4;</code>
     */
    java.util.List<com.dxx.game.dto.CommonProto.RewardDto> 
        getRewardsList();
    /**
     * <pre>
     * 附件奖励
     * </pre>
     *
     * <code>repeated .Proto.Common.RewardDto rewards = 4;</code>
     */
    com.dxx.game.dto.CommonProto.RewardDto getRewards(int index);
    /**
     * <pre>
     * 附件奖励
     * </pre>
     *
     * <code>repeated .Proto.Common.RewardDto rewards = 4;</code>
     */
    int getRewardsCount();
    /**
     * <pre>
     * 附件奖励
     * </pre>
     *
     * <code>repeated .Proto.Common.RewardDto rewards = 4;</code>
     */
    java.util.List<? extends com.dxx.game.dto.CommonProto.RewardDtoOrBuilder> 
        getRewardsOrBuilderList();
    /**
     * <pre>
     * 附件奖励
     * </pre>
     *
     * <code>repeated .Proto.Common.RewardDto rewards = 4;</code>
     */
    com.dxx.game.dto.CommonProto.RewardDtoOrBuilder getRewardsOrBuilder(
        int index);

    /**
     * <pre>
     * 奖励领取状态true 已领取，false未领取
     * </pre>
     *
     * <code>bool rewardsState = 5;</code>
     * @return The rewardsState.
     */
    boolean getRewardsState();

    /**
     * <pre>
     * 接收到邮件的时间
     * </pre>
     *
     * <code>uint64 createTimestamp = 6;</code>
     * @return The createTimestamp.
     */
    long getCreateTimestamp();

    /**
     * <pre>
     * 1 有奖励, 2 无奖励
     * </pre>
     *
     * <code>uint32 mailType = 7;</code>
     * @return The mailType.
     */
    int getMailType();

    /**
     * <pre>
     * 标题-客户端language表ID
     * </pre>
     *
     * <code>uint32 languageTitleId = 8;</code>
     * @return The languageTitleId.
     */
    int getLanguageTitleId();

    /**
     * <pre>
     * 内容-客户端language表ID
     * </pre>
     *
     * <code>uint32 languageContentId = 9;</code>
     * @return The languageContentId.
     */
    int getLanguageContentId();

    /**
     * <pre>
     * 客户端邮件内容参数用|分割 ps:(邮件内容为- 测试参数1{0}, 测试参数2{1}. 此值为 test1|test2)
     * </pre>
     *
     * <code>string languageContentParams = 10;</code>
     * @return The languageContentParams.
     */
    java.lang.String getLanguageContentParams();
    /**
     * <pre>
     * 客户端邮件内容参数用|分割 ps:(邮件内容为- 测试参数1{0}, 测试参数2{1}. 此值为 test1|test2)
     * </pre>
     *
     * <code>string languageContentParams = 10;</code>
     * @return The bytes for languageContentParams.
     */
    com.google.protobuf.ByteString
        getLanguageContentParamsBytes();

    /**
     * <pre>
     * 邮件过期时间
     * </pre>
     *
     * <code>uint64 expiredTimestamp = 11;</code>
     * @return The expiredTimestamp.
     */
    long getExpiredTimestamp();
  }
  /**
   * Protobuf type {@code Proto.Mail.MailDto}
   */
  public static final class MailDto extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Mail.MailDto)
      MailDtoOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        MailDto.class.getName());
    }
    // Use MailDto.newBuilder() to construct.
    private MailDto(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private MailDto() {
      title_ = "";
      content_ = "";
      rewards_ = java.util.Collections.emptyList();
      languageContentParams_ = "";
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.MailProto.internal_static_Proto_Mail_MailDto_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.MailProto.internal_static_Proto_Mail_MailDto_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.MailProto.MailDto.class, com.dxx.game.dto.MailProto.MailDto.Builder.class);
    }

    public static final int ROWID_FIELD_NUMBER = 1;
    private long rowId_ = 0L;
    /**
     * <pre>
     * 唯一ID
     * </pre>
     *
     * <code>uint64 rowId = 1;</code>
     * @return The rowId.
     */
    @java.lang.Override
    public long getRowId() {
      return rowId_;
    }

    public static final int TITLE_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private volatile java.lang.Object title_ = "";
    /**
     * <pre>
     * 标题
     * </pre>
     *
     * <code>string title = 2;</code>
     * @return The title.
     */
    @java.lang.Override
    public java.lang.String getTitle() {
      java.lang.Object ref = title_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        title_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 标题
     * </pre>
     *
     * <code>string title = 2;</code>
     * @return The bytes for title.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getTitleBytes() {
      java.lang.Object ref = title_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        title_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CONTENT_FIELD_NUMBER = 3;
    @SuppressWarnings("serial")
    private volatile java.lang.Object content_ = "";
    /**
     * <pre>
     * 内容
     * </pre>
     *
     * <code>string content = 3;</code>
     * @return The content.
     */
    @java.lang.Override
    public java.lang.String getContent() {
      java.lang.Object ref = content_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        content_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 内容
     * </pre>
     *
     * <code>string content = 3;</code>
     * @return The bytes for content.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getContentBytes() {
      java.lang.Object ref = content_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        content_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int REWARDS_FIELD_NUMBER = 4;
    @SuppressWarnings("serial")
    private java.util.List<com.dxx.game.dto.CommonProto.RewardDto> rewards_;
    /**
     * <pre>
     * 附件奖励
     * </pre>
     *
     * <code>repeated .Proto.Common.RewardDto rewards = 4;</code>
     */
    @java.lang.Override
    public java.util.List<com.dxx.game.dto.CommonProto.RewardDto> getRewardsList() {
      return rewards_;
    }
    /**
     * <pre>
     * 附件奖励
     * </pre>
     *
     * <code>repeated .Proto.Common.RewardDto rewards = 4;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.dxx.game.dto.CommonProto.RewardDtoOrBuilder> 
        getRewardsOrBuilderList() {
      return rewards_;
    }
    /**
     * <pre>
     * 附件奖励
     * </pre>
     *
     * <code>repeated .Proto.Common.RewardDto rewards = 4;</code>
     */
    @java.lang.Override
    public int getRewardsCount() {
      return rewards_.size();
    }
    /**
     * <pre>
     * 附件奖励
     * </pre>
     *
     * <code>repeated .Proto.Common.RewardDto rewards = 4;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.RewardDto getRewards(int index) {
      return rewards_.get(index);
    }
    /**
     * <pre>
     * 附件奖励
     * </pre>
     *
     * <code>repeated .Proto.Common.RewardDto rewards = 4;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.RewardDtoOrBuilder getRewardsOrBuilder(
        int index) {
      return rewards_.get(index);
    }

    public static final int REWARDSSTATE_FIELD_NUMBER = 5;
    private boolean rewardsState_ = false;
    /**
     * <pre>
     * 奖励领取状态true 已领取，false未领取
     * </pre>
     *
     * <code>bool rewardsState = 5;</code>
     * @return The rewardsState.
     */
    @java.lang.Override
    public boolean getRewardsState() {
      return rewardsState_;
    }

    public static final int CREATETIMESTAMP_FIELD_NUMBER = 6;
    private long createTimestamp_ = 0L;
    /**
     * <pre>
     * 接收到邮件的时间
     * </pre>
     *
     * <code>uint64 createTimestamp = 6;</code>
     * @return The createTimestamp.
     */
    @java.lang.Override
    public long getCreateTimestamp() {
      return createTimestamp_;
    }

    public static final int MAILTYPE_FIELD_NUMBER = 7;
    private int mailType_ = 0;
    /**
     * <pre>
     * 1 有奖励, 2 无奖励
     * </pre>
     *
     * <code>uint32 mailType = 7;</code>
     * @return The mailType.
     */
    @java.lang.Override
    public int getMailType() {
      return mailType_;
    }

    public static final int LANGUAGETITLEID_FIELD_NUMBER = 8;
    private int languageTitleId_ = 0;
    /**
     * <pre>
     * 标题-客户端language表ID
     * </pre>
     *
     * <code>uint32 languageTitleId = 8;</code>
     * @return The languageTitleId.
     */
    @java.lang.Override
    public int getLanguageTitleId() {
      return languageTitleId_;
    }

    public static final int LANGUAGECONTENTID_FIELD_NUMBER = 9;
    private int languageContentId_ = 0;
    /**
     * <pre>
     * 内容-客户端language表ID
     * </pre>
     *
     * <code>uint32 languageContentId = 9;</code>
     * @return The languageContentId.
     */
    @java.lang.Override
    public int getLanguageContentId() {
      return languageContentId_;
    }

    public static final int LANGUAGECONTENTPARAMS_FIELD_NUMBER = 10;
    @SuppressWarnings("serial")
    private volatile java.lang.Object languageContentParams_ = "";
    /**
     * <pre>
     * 客户端邮件内容参数用|分割 ps:(邮件内容为- 测试参数1{0}, 测试参数2{1}. 此值为 test1|test2)
     * </pre>
     *
     * <code>string languageContentParams = 10;</code>
     * @return The languageContentParams.
     */
    @java.lang.Override
    public java.lang.String getLanguageContentParams() {
      java.lang.Object ref = languageContentParams_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        languageContentParams_ = s;
        return s;
      }
    }
    /**
     * <pre>
     * 客户端邮件内容参数用|分割 ps:(邮件内容为- 测试参数1{0}, 测试参数2{1}. 此值为 test1|test2)
     * </pre>
     *
     * <code>string languageContentParams = 10;</code>
     * @return The bytes for languageContentParams.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getLanguageContentParamsBytes() {
      java.lang.Object ref = languageContentParams_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        languageContentParams_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int EXPIREDTIMESTAMP_FIELD_NUMBER = 11;
    private long expiredTimestamp_ = 0L;
    /**
     * <pre>
     * 邮件过期时间
     * </pre>
     *
     * <code>uint64 expiredTimestamp = 11;</code>
     * @return The expiredTimestamp.
     */
    @java.lang.Override
    public long getExpiredTimestamp() {
      return expiredTimestamp_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (rowId_ != 0L) {
        output.writeUInt64(1, rowId_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(title_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 2, title_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(content_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 3, content_);
      }
      for (int i = 0; i < rewards_.size(); i++) {
        output.writeMessage(4, rewards_.get(i));
      }
      if (rewardsState_ != false) {
        output.writeBool(5, rewardsState_);
      }
      if (createTimestamp_ != 0L) {
        output.writeUInt64(6, createTimestamp_);
      }
      if (mailType_ != 0) {
        output.writeUInt32(7, mailType_);
      }
      if (languageTitleId_ != 0) {
        output.writeUInt32(8, languageTitleId_);
      }
      if (languageContentId_ != 0) {
        output.writeUInt32(9, languageContentId_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(languageContentParams_)) {
        com.google.protobuf.GeneratedMessage.writeString(output, 10, languageContentParams_);
      }
      if (expiredTimestamp_ != 0L) {
        output.writeUInt64(11, expiredTimestamp_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (rowId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(1, rowId_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(title_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(2, title_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(content_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(3, content_);
      }
      for (int i = 0; i < rewards_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, rewards_.get(i));
      }
      if (rewardsState_ != false) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(5, rewardsState_);
      }
      if (createTimestamp_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(6, createTimestamp_);
      }
      if (mailType_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(7, mailType_);
      }
      if (languageTitleId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(8, languageTitleId_);
      }
      if (languageContentId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(9, languageContentId_);
      }
      if (!com.google.protobuf.GeneratedMessage.isStringEmpty(languageContentParams_)) {
        size += com.google.protobuf.GeneratedMessage.computeStringSize(10, languageContentParams_);
      }
      if (expiredTimestamp_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(11, expiredTimestamp_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.MailProto.MailDto)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.MailProto.MailDto other = (com.dxx.game.dto.MailProto.MailDto) obj;

      if (getRowId()
          != other.getRowId()) return false;
      if (!getTitle()
          .equals(other.getTitle())) return false;
      if (!getContent()
          .equals(other.getContent())) return false;
      if (!getRewardsList()
          .equals(other.getRewardsList())) return false;
      if (getRewardsState()
          != other.getRewardsState()) return false;
      if (getCreateTimestamp()
          != other.getCreateTimestamp()) return false;
      if (getMailType()
          != other.getMailType()) return false;
      if (getLanguageTitleId()
          != other.getLanguageTitleId()) return false;
      if (getLanguageContentId()
          != other.getLanguageContentId()) return false;
      if (!getLanguageContentParams()
          .equals(other.getLanguageContentParams())) return false;
      if (getExpiredTimestamp()
          != other.getExpiredTimestamp()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ROWID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getRowId());
      hash = (37 * hash) + TITLE_FIELD_NUMBER;
      hash = (53 * hash) + getTitle().hashCode();
      hash = (37 * hash) + CONTENT_FIELD_NUMBER;
      hash = (53 * hash) + getContent().hashCode();
      if (getRewardsCount() > 0) {
        hash = (37 * hash) + REWARDS_FIELD_NUMBER;
        hash = (53 * hash) + getRewardsList().hashCode();
      }
      hash = (37 * hash) + REWARDSSTATE_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getRewardsState());
      hash = (37 * hash) + CREATETIMESTAMP_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getCreateTimestamp());
      hash = (37 * hash) + MAILTYPE_FIELD_NUMBER;
      hash = (53 * hash) + getMailType();
      hash = (37 * hash) + LANGUAGETITLEID_FIELD_NUMBER;
      hash = (53 * hash) + getLanguageTitleId();
      hash = (37 * hash) + LANGUAGECONTENTID_FIELD_NUMBER;
      hash = (53 * hash) + getLanguageContentId();
      hash = (37 * hash) + LANGUAGECONTENTPARAMS_FIELD_NUMBER;
      hash = (53 * hash) + getLanguageContentParams().hashCode();
      hash = (37 * hash) + EXPIREDTIMESTAMP_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getExpiredTimestamp());
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.MailProto.MailDto parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MailProto.MailDto parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MailProto.MailDto parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MailProto.MailDto parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MailProto.MailDto parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.MailProto.MailDto parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.MailProto.MailDto parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MailProto.MailDto parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.MailProto.MailDto parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.MailProto.MailDto parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.MailProto.MailDto parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.MailProto.MailDto parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.MailProto.MailDto prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code Proto.Mail.MailDto}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Mail.MailDto)
        com.dxx.game.dto.MailProto.MailDtoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.MailProto.internal_static_Proto_Mail_MailDto_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.MailProto.internal_static_Proto_Mail_MailDto_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.MailProto.MailDto.class, com.dxx.game.dto.MailProto.MailDto.Builder.class);
      }

      // Construct using com.dxx.game.dto.MailProto.MailDto.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        rowId_ = 0L;
        title_ = "";
        content_ = "";
        if (rewardsBuilder_ == null) {
          rewards_ = java.util.Collections.emptyList();
        } else {
          rewards_ = null;
          rewardsBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        rewardsState_ = false;
        createTimestamp_ = 0L;
        mailType_ = 0;
        languageTitleId_ = 0;
        languageContentId_ = 0;
        languageContentParams_ = "";
        expiredTimestamp_ = 0L;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.MailProto.internal_static_Proto_Mail_MailDto_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.MailProto.MailDto getDefaultInstanceForType() {
        return com.dxx.game.dto.MailProto.MailDto.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.MailProto.MailDto build() {
        com.dxx.game.dto.MailProto.MailDto result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.MailProto.MailDto buildPartial() {
        com.dxx.game.dto.MailProto.MailDto result = new com.dxx.game.dto.MailProto.MailDto(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(com.dxx.game.dto.MailProto.MailDto result) {
        if (rewardsBuilder_ == null) {
          if (((bitField0_ & 0x00000008) != 0)) {
            rewards_ = java.util.Collections.unmodifiableList(rewards_);
            bitField0_ = (bitField0_ & ~0x00000008);
          }
          result.rewards_ = rewards_;
        } else {
          result.rewards_ = rewardsBuilder_.build();
        }
      }

      private void buildPartial0(com.dxx.game.dto.MailProto.MailDto result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.rowId_ = rowId_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.title_ = title_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.content_ = content_;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.rewardsState_ = rewardsState_;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.createTimestamp_ = createTimestamp_;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.mailType_ = mailType_;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          result.languageTitleId_ = languageTitleId_;
        }
        if (((from_bitField0_ & 0x00000100) != 0)) {
          result.languageContentId_ = languageContentId_;
        }
        if (((from_bitField0_ & 0x00000200) != 0)) {
          result.languageContentParams_ = languageContentParams_;
        }
        if (((from_bitField0_ & 0x00000400) != 0)) {
          result.expiredTimestamp_ = expiredTimestamp_;
        }
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.MailProto.MailDto) {
          return mergeFrom((com.dxx.game.dto.MailProto.MailDto)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.MailProto.MailDto other) {
        if (other == com.dxx.game.dto.MailProto.MailDto.getDefaultInstance()) return this;
        if (other.getRowId() != 0L) {
          setRowId(other.getRowId());
        }
        if (!other.getTitle().isEmpty()) {
          title_ = other.title_;
          bitField0_ |= 0x00000002;
          onChanged();
        }
        if (!other.getContent().isEmpty()) {
          content_ = other.content_;
          bitField0_ |= 0x00000004;
          onChanged();
        }
        if (rewardsBuilder_ == null) {
          if (!other.rewards_.isEmpty()) {
            if (rewards_.isEmpty()) {
              rewards_ = other.rewards_;
              bitField0_ = (bitField0_ & ~0x00000008);
            } else {
              ensureRewardsIsMutable();
              rewards_.addAll(other.rewards_);
            }
            onChanged();
          }
        } else {
          if (!other.rewards_.isEmpty()) {
            if (rewardsBuilder_.isEmpty()) {
              rewardsBuilder_.dispose();
              rewardsBuilder_ = null;
              rewards_ = other.rewards_;
              bitField0_ = (bitField0_ & ~0x00000008);
              rewardsBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getRewardsFieldBuilder() : null;
            } else {
              rewardsBuilder_.addAllMessages(other.rewards_);
            }
          }
        }
        if (other.getRewardsState() != false) {
          setRewardsState(other.getRewardsState());
        }
        if (other.getCreateTimestamp() != 0L) {
          setCreateTimestamp(other.getCreateTimestamp());
        }
        if (other.getMailType() != 0) {
          setMailType(other.getMailType());
        }
        if (other.getLanguageTitleId() != 0) {
          setLanguageTitleId(other.getLanguageTitleId());
        }
        if (other.getLanguageContentId() != 0) {
          setLanguageContentId(other.getLanguageContentId());
        }
        if (!other.getLanguageContentParams().isEmpty()) {
          languageContentParams_ = other.languageContentParams_;
          bitField0_ |= 0x00000200;
          onChanged();
        }
        if (other.getExpiredTimestamp() != 0L) {
          setExpiredTimestamp(other.getExpiredTimestamp());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                rowId_ = input.readUInt64();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 18: {
                title_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              case 26: {
                content_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000004;
                break;
              } // case 26
              case 34: {
                com.dxx.game.dto.CommonProto.RewardDto m =
                    input.readMessage(
                        com.dxx.game.dto.CommonProto.RewardDto.parser(),
                        extensionRegistry);
                if (rewardsBuilder_ == null) {
                  ensureRewardsIsMutable();
                  rewards_.add(m);
                } else {
                  rewardsBuilder_.addMessage(m);
                }
                break;
              } // case 34
              case 40: {
                rewardsState_ = input.readBool();
                bitField0_ |= 0x00000010;
                break;
              } // case 40
              case 48: {
                createTimestamp_ = input.readUInt64();
                bitField0_ |= 0x00000020;
                break;
              } // case 48
              case 56: {
                mailType_ = input.readUInt32();
                bitField0_ |= 0x00000040;
                break;
              } // case 56
              case 64: {
                languageTitleId_ = input.readUInt32();
                bitField0_ |= 0x00000080;
                break;
              } // case 64
              case 72: {
                languageContentId_ = input.readUInt32();
                bitField0_ |= 0x00000100;
                break;
              } // case 72
              case 82: {
                languageContentParams_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000200;
                break;
              } // case 82
              case 88: {
                expiredTimestamp_ = input.readUInt64();
                bitField0_ |= 0x00000400;
                break;
              } // case 88
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private long rowId_ ;
      /**
       * <pre>
       * 唯一ID
       * </pre>
       *
       * <code>uint64 rowId = 1;</code>
       * @return The rowId.
       */
      @java.lang.Override
      public long getRowId() {
        return rowId_;
      }
      /**
       * <pre>
       * 唯一ID
       * </pre>
       *
       * <code>uint64 rowId = 1;</code>
       * @param value The rowId to set.
       * @return This builder for chaining.
       */
      public Builder setRowId(long value) {

        rowId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 唯一ID
       * </pre>
       *
       * <code>uint64 rowId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearRowId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        rowId_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object title_ = "";
      /**
       * <pre>
       * 标题
       * </pre>
       *
       * <code>string title = 2;</code>
       * @return The title.
       */
      public java.lang.String getTitle() {
        java.lang.Object ref = title_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          title_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 标题
       * </pre>
       *
       * <code>string title = 2;</code>
       * @return The bytes for title.
       */
      public com.google.protobuf.ByteString
          getTitleBytes() {
        java.lang.Object ref = title_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          title_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 标题
       * </pre>
       *
       * <code>string title = 2;</code>
       * @param value The title to set.
       * @return This builder for chaining.
       */
      public Builder setTitle(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        title_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 标题
       * </pre>
       *
       * <code>string title = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearTitle() {
        title_ = getDefaultInstance().getTitle();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 标题
       * </pre>
       *
       * <code>string title = 2;</code>
       * @param value The bytes for title to set.
       * @return This builder for chaining.
       */
      public Builder setTitleBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        title_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }

      private java.lang.Object content_ = "";
      /**
       * <pre>
       * 内容
       * </pre>
       *
       * <code>string content = 3;</code>
       * @return The content.
       */
      public java.lang.String getContent() {
        java.lang.Object ref = content_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          content_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 内容
       * </pre>
       *
       * <code>string content = 3;</code>
       * @return The bytes for content.
       */
      public com.google.protobuf.ByteString
          getContentBytes() {
        java.lang.Object ref = content_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          content_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 内容
       * </pre>
       *
       * <code>string content = 3;</code>
       * @param value The content to set.
       * @return This builder for chaining.
       */
      public Builder setContent(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        content_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 内容
       * </pre>
       *
       * <code>string content = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearContent() {
        content_ = getDefaultInstance().getContent();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 内容
       * </pre>
       *
       * <code>string content = 3;</code>
       * @param value The bytes for content to set.
       * @return This builder for chaining.
       */
      public Builder setContentBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        content_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }

      private java.util.List<com.dxx.game.dto.CommonProto.RewardDto> rewards_ =
        java.util.Collections.emptyList();
      private void ensureRewardsIsMutable() {
        if (!((bitField0_ & 0x00000008) != 0)) {
          rewards_ = new java.util.ArrayList<com.dxx.game.dto.CommonProto.RewardDto>(rewards_);
          bitField0_ |= 0x00000008;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          com.dxx.game.dto.CommonProto.RewardDto, com.dxx.game.dto.CommonProto.RewardDto.Builder, com.dxx.game.dto.CommonProto.RewardDtoOrBuilder> rewardsBuilder_;

      /**
       * <pre>
       * 附件奖励
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDto rewards = 4;</code>
       */
      public java.util.List<com.dxx.game.dto.CommonProto.RewardDto> getRewardsList() {
        if (rewardsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(rewards_);
        } else {
          return rewardsBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 附件奖励
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDto rewards = 4;</code>
       */
      public int getRewardsCount() {
        if (rewardsBuilder_ == null) {
          return rewards_.size();
        } else {
          return rewardsBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 附件奖励
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDto rewards = 4;</code>
       */
      public com.dxx.game.dto.CommonProto.RewardDto getRewards(int index) {
        if (rewardsBuilder_ == null) {
          return rewards_.get(index);
        } else {
          return rewardsBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 附件奖励
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDto rewards = 4;</code>
       */
      public Builder setRewards(
          int index, com.dxx.game.dto.CommonProto.RewardDto value) {
        if (rewardsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRewardsIsMutable();
          rewards_.set(index, value);
          onChanged();
        } else {
          rewardsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 附件奖励
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDto rewards = 4;</code>
       */
      public Builder setRewards(
          int index, com.dxx.game.dto.CommonProto.RewardDto.Builder builderForValue) {
        if (rewardsBuilder_ == null) {
          ensureRewardsIsMutable();
          rewards_.set(index, builderForValue.build());
          onChanged();
        } else {
          rewardsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 附件奖励
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDto rewards = 4;</code>
       */
      public Builder addRewards(com.dxx.game.dto.CommonProto.RewardDto value) {
        if (rewardsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRewardsIsMutable();
          rewards_.add(value);
          onChanged();
        } else {
          rewardsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 附件奖励
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDto rewards = 4;</code>
       */
      public Builder addRewards(
          int index, com.dxx.game.dto.CommonProto.RewardDto value) {
        if (rewardsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRewardsIsMutable();
          rewards_.add(index, value);
          onChanged();
        } else {
          rewardsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 附件奖励
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDto rewards = 4;</code>
       */
      public Builder addRewards(
          com.dxx.game.dto.CommonProto.RewardDto.Builder builderForValue) {
        if (rewardsBuilder_ == null) {
          ensureRewardsIsMutable();
          rewards_.add(builderForValue.build());
          onChanged();
        } else {
          rewardsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 附件奖励
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDto rewards = 4;</code>
       */
      public Builder addRewards(
          int index, com.dxx.game.dto.CommonProto.RewardDto.Builder builderForValue) {
        if (rewardsBuilder_ == null) {
          ensureRewardsIsMutable();
          rewards_.add(index, builderForValue.build());
          onChanged();
        } else {
          rewardsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 附件奖励
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDto rewards = 4;</code>
       */
      public Builder addAllRewards(
          java.lang.Iterable<? extends com.dxx.game.dto.CommonProto.RewardDto> values) {
        if (rewardsBuilder_ == null) {
          ensureRewardsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, rewards_);
          onChanged();
        } else {
          rewardsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 附件奖励
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDto rewards = 4;</code>
       */
      public Builder clearRewards() {
        if (rewardsBuilder_ == null) {
          rewards_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000008);
          onChanged();
        } else {
          rewardsBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 附件奖励
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDto rewards = 4;</code>
       */
      public Builder removeRewards(int index) {
        if (rewardsBuilder_ == null) {
          ensureRewardsIsMutable();
          rewards_.remove(index);
          onChanged();
        } else {
          rewardsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 附件奖励
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDto rewards = 4;</code>
       */
      public com.dxx.game.dto.CommonProto.RewardDto.Builder getRewardsBuilder(
          int index) {
        return getRewardsFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 附件奖励
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDto rewards = 4;</code>
       */
      public com.dxx.game.dto.CommonProto.RewardDtoOrBuilder getRewardsOrBuilder(
          int index) {
        if (rewardsBuilder_ == null) {
          return rewards_.get(index);  } else {
          return rewardsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 附件奖励
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDto rewards = 4;</code>
       */
      public java.util.List<? extends com.dxx.game.dto.CommonProto.RewardDtoOrBuilder> 
           getRewardsOrBuilderList() {
        if (rewardsBuilder_ != null) {
          return rewardsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(rewards_);
        }
      }
      /**
       * <pre>
       * 附件奖励
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDto rewards = 4;</code>
       */
      public com.dxx.game.dto.CommonProto.RewardDto.Builder addRewardsBuilder() {
        return getRewardsFieldBuilder().addBuilder(
            com.dxx.game.dto.CommonProto.RewardDto.getDefaultInstance());
      }
      /**
       * <pre>
       * 附件奖励
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDto rewards = 4;</code>
       */
      public com.dxx.game.dto.CommonProto.RewardDto.Builder addRewardsBuilder(
          int index) {
        return getRewardsFieldBuilder().addBuilder(
            index, com.dxx.game.dto.CommonProto.RewardDto.getDefaultInstance());
      }
      /**
       * <pre>
       * 附件奖励
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDto rewards = 4;</code>
       */
      public java.util.List<com.dxx.game.dto.CommonProto.RewardDto.Builder> 
           getRewardsBuilderList() {
        return getRewardsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          com.dxx.game.dto.CommonProto.RewardDto, com.dxx.game.dto.CommonProto.RewardDto.Builder, com.dxx.game.dto.CommonProto.RewardDtoOrBuilder> 
          getRewardsFieldBuilder() {
        if (rewardsBuilder_ == null) {
          rewardsBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              com.dxx.game.dto.CommonProto.RewardDto, com.dxx.game.dto.CommonProto.RewardDto.Builder, com.dxx.game.dto.CommonProto.RewardDtoOrBuilder>(
                  rewards_,
                  ((bitField0_ & 0x00000008) != 0),
                  getParentForChildren(),
                  isClean());
          rewards_ = null;
        }
        return rewardsBuilder_;
      }

      private boolean rewardsState_ ;
      /**
       * <pre>
       * 奖励领取状态true 已领取，false未领取
       * </pre>
       *
       * <code>bool rewardsState = 5;</code>
       * @return The rewardsState.
       */
      @java.lang.Override
      public boolean getRewardsState() {
        return rewardsState_;
      }
      /**
       * <pre>
       * 奖励领取状态true 已领取，false未领取
       * </pre>
       *
       * <code>bool rewardsState = 5;</code>
       * @param value The rewardsState to set.
       * @return This builder for chaining.
       */
      public Builder setRewardsState(boolean value) {

        rewardsState_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 奖励领取状态true 已领取，false未领取
       * </pre>
       *
       * <code>bool rewardsState = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearRewardsState() {
        bitField0_ = (bitField0_ & ~0x00000010);
        rewardsState_ = false;
        onChanged();
        return this;
      }

      private long createTimestamp_ ;
      /**
       * <pre>
       * 接收到邮件的时间
       * </pre>
       *
       * <code>uint64 createTimestamp = 6;</code>
       * @return The createTimestamp.
       */
      @java.lang.Override
      public long getCreateTimestamp() {
        return createTimestamp_;
      }
      /**
       * <pre>
       * 接收到邮件的时间
       * </pre>
       *
       * <code>uint64 createTimestamp = 6;</code>
       * @param value The createTimestamp to set.
       * @return This builder for chaining.
       */
      public Builder setCreateTimestamp(long value) {

        createTimestamp_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 接收到邮件的时间
       * </pre>
       *
       * <code>uint64 createTimestamp = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearCreateTimestamp() {
        bitField0_ = (bitField0_ & ~0x00000020);
        createTimestamp_ = 0L;
        onChanged();
        return this;
      }

      private int mailType_ ;
      /**
       * <pre>
       * 1 有奖励, 2 无奖励
       * </pre>
       *
       * <code>uint32 mailType = 7;</code>
       * @return The mailType.
       */
      @java.lang.Override
      public int getMailType() {
        return mailType_;
      }
      /**
       * <pre>
       * 1 有奖励, 2 无奖励
       * </pre>
       *
       * <code>uint32 mailType = 7;</code>
       * @param value The mailType to set.
       * @return This builder for chaining.
       */
      public Builder setMailType(int value) {

        mailType_ = value;
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 1 有奖励, 2 无奖励
       * </pre>
       *
       * <code>uint32 mailType = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearMailType() {
        bitField0_ = (bitField0_ & ~0x00000040);
        mailType_ = 0;
        onChanged();
        return this;
      }

      private int languageTitleId_ ;
      /**
       * <pre>
       * 标题-客户端language表ID
       * </pre>
       *
       * <code>uint32 languageTitleId = 8;</code>
       * @return The languageTitleId.
       */
      @java.lang.Override
      public int getLanguageTitleId() {
        return languageTitleId_;
      }
      /**
       * <pre>
       * 标题-客户端language表ID
       * </pre>
       *
       * <code>uint32 languageTitleId = 8;</code>
       * @param value The languageTitleId to set.
       * @return This builder for chaining.
       */
      public Builder setLanguageTitleId(int value) {

        languageTitleId_ = value;
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 标题-客户端language表ID
       * </pre>
       *
       * <code>uint32 languageTitleId = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearLanguageTitleId() {
        bitField0_ = (bitField0_ & ~0x00000080);
        languageTitleId_ = 0;
        onChanged();
        return this;
      }

      private int languageContentId_ ;
      /**
       * <pre>
       * 内容-客户端language表ID
       * </pre>
       *
       * <code>uint32 languageContentId = 9;</code>
       * @return The languageContentId.
       */
      @java.lang.Override
      public int getLanguageContentId() {
        return languageContentId_;
      }
      /**
       * <pre>
       * 内容-客户端language表ID
       * </pre>
       *
       * <code>uint32 languageContentId = 9;</code>
       * @param value The languageContentId to set.
       * @return This builder for chaining.
       */
      public Builder setLanguageContentId(int value) {

        languageContentId_ = value;
        bitField0_ |= 0x00000100;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 内容-客户端language表ID
       * </pre>
       *
       * <code>uint32 languageContentId = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearLanguageContentId() {
        bitField0_ = (bitField0_ & ~0x00000100);
        languageContentId_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object languageContentParams_ = "";
      /**
       * <pre>
       * 客户端邮件内容参数用|分割 ps:(邮件内容为- 测试参数1{0}, 测试参数2{1}. 此值为 test1|test2)
       * </pre>
       *
       * <code>string languageContentParams = 10;</code>
       * @return The languageContentParams.
       */
      public java.lang.String getLanguageContentParams() {
        java.lang.Object ref = languageContentParams_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          languageContentParams_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 客户端邮件内容参数用|分割 ps:(邮件内容为- 测试参数1{0}, 测试参数2{1}. 此值为 test1|test2)
       * </pre>
       *
       * <code>string languageContentParams = 10;</code>
       * @return The bytes for languageContentParams.
       */
      public com.google.protobuf.ByteString
          getLanguageContentParamsBytes() {
        java.lang.Object ref = languageContentParams_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          languageContentParams_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 客户端邮件内容参数用|分割 ps:(邮件内容为- 测试参数1{0}, 测试参数2{1}. 此值为 test1|test2)
       * </pre>
       *
       * <code>string languageContentParams = 10;</code>
       * @param value The languageContentParams to set.
       * @return This builder for chaining.
       */
      public Builder setLanguageContentParams(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        languageContentParams_ = value;
        bitField0_ |= 0x00000200;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 客户端邮件内容参数用|分割 ps:(邮件内容为- 测试参数1{0}, 测试参数2{1}. 此值为 test1|test2)
       * </pre>
       *
       * <code>string languageContentParams = 10;</code>
       * @return This builder for chaining.
       */
      public Builder clearLanguageContentParams() {
        languageContentParams_ = getDefaultInstance().getLanguageContentParams();
        bitField0_ = (bitField0_ & ~0x00000200);
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 客户端邮件内容参数用|分割 ps:(邮件内容为- 测试参数1{0}, 测试参数2{1}. 此值为 test1|test2)
       * </pre>
       *
       * <code>string languageContentParams = 10;</code>
       * @param value The bytes for languageContentParams to set.
       * @return This builder for chaining.
       */
      public Builder setLanguageContentParamsBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        languageContentParams_ = value;
        bitField0_ |= 0x00000200;
        onChanged();
        return this;
      }

      private long expiredTimestamp_ ;
      /**
       * <pre>
       * 邮件过期时间
       * </pre>
       *
       * <code>uint64 expiredTimestamp = 11;</code>
       * @return The expiredTimestamp.
       */
      @java.lang.Override
      public long getExpiredTimestamp() {
        return expiredTimestamp_;
      }
      /**
       * <pre>
       * 邮件过期时间
       * </pre>
       *
       * <code>uint64 expiredTimestamp = 11;</code>
       * @param value The expiredTimestamp to set.
       * @return This builder for chaining.
       */
      public Builder setExpiredTimestamp(long value) {

        expiredTimestamp_ = value;
        bitField0_ |= 0x00000400;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 邮件过期时间
       * </pre>
       *
       * <code>uint64 expiredTimestamp = 11;</code>
       * @return This builder for chaining.
       */
      public Builder clearExpiredTimestamp() {
        bitField0_ = (bitField0_ & ~0x00000400);
        expiredTimestamp_ = 0L;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Mail.MailDto)
    }

    // @@protoc_insertion_point(class_scope:Proto.Mail.MailDto)
    private static final com.dxx.game.dto.MailProto.MailDto DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.MailProto.MailDto();
    }

    public static com.dxx.game.dto.MailProto.MailDto getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<MailDto>
        PARSER = new com.google.protobuf.AbstractParser<MailDto>() {
      @java.lang.Override
      public MailDto parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<MailDto> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MailDto> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.MailProto.MailDto getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Mail_MailGetListRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Mail_MailGetListRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Mail_MailGetListResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Mail_MailGetListResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Mail_MailReceiveAwardsRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Mail_MailReceiveAwardsRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Mail_MailReceiveAwardsResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Mail_MailReceiveAwardsResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Mail_MailDeleteRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Mail_MailDeleteRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Mail_MailDeleteResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Mail_MailDeleteResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Mail_MailDto_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Mail_MailDto_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\nmail.proto\022\nProto.Mail\032\014common.proto\"m" +
      "\n\022MailGetListRequest\0220\n\014commonParams\030\001 \001" +
      "(\0132\032.Proto.Common.CommonParams\022\020\n\010maxRow" +
      "Id\030\002 \001(\004\022\023\n\013appLanguage\030\003 \001(\t\"G\n\023MailGet" +
      "ListResponse\022\014\n\004code\030\001 \001(\005\022\"\n\005mails\030\002 \003(" +
      "\0132\023.Proto.Mail.MailDto\"\\\n\030MailReceiveAwa" +
      "rdsRequest\0220\n\014commonParams\030\001 \001(\0132\032.Proto" +
      ".Common.CommonParams\022\016\n\006rowIds\030\002 \003(\004\"g\n\031" +
      "MailReceiveAwardsResponse\022\014\n\004code\030\001 \001(\005\022" +
      "\016\n\006rowIds\030\002 \003(\004\022,\n\ncommonData\030\003 \001(\0132\030.Pr" +
      "oto.Common.CommonData\"U\n\021MailDeleteReque" +
      "st\0220\n\014commonParams\030\001 \001(\0132\032.Proto.Common." +
      "CommonParams\022\016\n\006rowIds\030\002 \003(\004\"2\n\022MailDele" +
      "teResponse\022\014\n\004code\030\001 \001(\005\022\016\n\006rowIds\030\002 \003(\004" +
      "\"\220\002\n\007MailDto\022\r\n\005rowId\030\001 \001(\004\022\r\n\005title\030\002 \001" +
      "(\t\022\017\n\007content\030\003 \001(\t\022(\n\007rewards\030\004 \003(\0132\027.P" +
      "roto.Common.RewardDto\022\024\n\014rewardsState\030\005 " +
      "\001(\010\022\027\n\017createTimestamp\030\006 \001(\004\022\020\n\010mailType" +
      "\030\007 \001(\r\022\027\n\017languageTitleId\030\010 \001(\r\022\031\n\021langu" +
      "ageContentId\030\t \001(\r\022\035\n\025languageContentPar" +
      "ams\030\n \001(\t\022\030\n\020expiredTimestamp\030\013 \001(\004B\035\n\020c" +
      "om.dxx.game.dtoB\tMailProtob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.dxx.game.dto.CommonProto.getDescriptor(),
        });
    internal_static_Proto_Mail_MailGetListRequest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_Proto_Mail_MailGetListRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Mail_MailGetListRequest_descriptor,
        new java.lang.String[] { "CommonParams", "MaxRowId", "AppLanguage", });
    internal_static_Proto_Mail_MailGetListResponse_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_Proto_Mail_MailGetListResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Mail_MailGetListResponse_descriptor,
        new java.lang.String[] { "Code", "Mails", });
    internal_static_Proto_Mail_MailReceiveAwardsRequest_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_Proto_Mail_MailReceiveAwardsRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Mail_MailReceiveAwardsRequest_descriptor,
        new java.lang.String[] { "CommonParams", "RowIds", });
    internal_static_Proto_Mail_MailReceiveAwardsResponse_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_Proto_Mail_MailReceiveAwardsResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Mail_MailReceiveAwardsResponse_descriptor,
        new java.lang.String[] { "Code", "RowIds", "CommonData", });
    internal_static_Proto_Mail_MailDeleteRequest_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_Proto_Mail_MailDeleteRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Mail_MailDeleteRequest_descriptor,
        new java.lang.String[] { "CommonParams", "RowIds", });
    internal_static_Proto_Mail_MailDeleteResponse_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_Proto_Mail_MailDeleteResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Mail_MailDeleteResponse_descriptor,
        new java.lang.String[] { "Code", "RowIds", });
    internal_static_Proto_Mail_MailDto_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_Proto_Mail_MailDto_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Mail_MailDto_descriptor,
        new java.lang.String[] { "RowId", "Title", "Content", "Rewards", "RewardsState", "CreateTimestamp", "MailType", "LanguageTitleId", "LanguageContentId", "LanguageContentParams", "ExpiredTimestamp", });
    descriptor.resolveAllFeaturesImmutable();
    com.dxx.game.dto.CommonProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
