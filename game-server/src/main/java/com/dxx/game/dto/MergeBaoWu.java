// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: baowu.proto
// Protobuf Java Version: 4.29.0

package com.dxx.game.dto;

/**
 * Protobuf type {@code Proto.BaoWu.MergeBaoWu}
 */
public final class MergeBaoWu extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:Proto.BaoWu.MergeBaoWu)
    MergeBaoWuOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 29,
      /* patch= */ 0,
      /* suffix= */ "",
      MergeBaoWu.class.getName());
  }
  // Use MergeBaoWu.newBuilder() to construct.
  private MergeBaoWu(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private MergeBaoWu() {
    deletedRowIds_ = emptyLongList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.dxx.game.dto.BaoWuProto.internal_static_Proto_BaoWu_MergeBaoWu_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.dxx.game.dto.BaoWuProto.internal_static_Proto_BaoWu_MergeBaoWu_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.dxx.game.dto.MergeBaoWu.class, com.dxx.game.dto.MergeBaoWu.Builder.class);
  }

  private int bitField0_;
  public static final int TARGETROWID_FIELD_NUMBER = 1;
  private long targetRowId_ = 0L;
  /**
   * <pre>
   * 合并的目标宝物rowId
   * </pre>
   *
   * <code>int64 targetRowId = 1;</code>
   * @return The targetRowId.
   */
  @java.lang.Override
  public long getTargetRowId() {
    return targetRowId_;
  }

  public static final int DELETEDROWIDS_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private com.google.protobuf.Internal.LongList deletedRowIds_ =
      emptyLongList();
  /**
   * <pre>
   * 被合并的宝物rowId列表
   * </pre>
   *
   * <code>repeated int64 deletedRowIds = 2;</code>
   * @return A list containing the deletedRowIds.
   */
  @java.lang.Override
  public java.util.List<java.lang.Long>
      getDeletedRowIdsList() {
    return deletedRowIds_;
  }
  /**
   * <pre>
   * 被合并的宝物rowId列表
   * </pre>
   *
   * <code>repeated int64 deletedRowIds = 2;</code>
   * @return The count of deletedRowIds.
   */
  public int getDeletedRowIdsCount() {
    return deletedRowIds_.size();
  }
  /**
   * <pre>
   * 被合并的宝物rowId列表
   * </pre>
   *
   * <code>repeated int64 deletedRowIds = 2;</code>
   * @param index The index of the element to return.
   * @return The deletedRowIds at the given index.
   */
  public long getDeletedRowIds(int index) {
    return deletedRowIds_.getLong(index);
  }
  private int deletedRowIdsMemoizedSerializedSize = -1;

  public static final int PINGTIITEM_FIELD_NUMBER = 3;
  private com.dxx.game.dto.CommonProto.ItemDto pingTiItem_;
  /**
   * <pre>
   * 平替材料
   * </pre>
   *
   * <code>.Proto.Common.ItemDto pingTiItem = 3;</code>
   * @return Whether the pingTiItem field is set.
   */
  @java.lang.Override
  public boolean hasPingTiItem() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <pre>
   * 平替材料
   * </pre>
   *
   * <code>.Proto.Common.ItemDto pingTiItem = 3;</code>
   * @return The pingTiItem.
   */
  @java.lang.Override
  public com.dxx.game.dto.CommonProto.ItemDto getPingTiItem() {
    return pingTiItem_ == null ? com.dxx.game.dto.CommonProto.ItemDto.getDefaultInstance() : pingTiItem_;
  }
  /**
   * <pre>
   * 平替材料
   * </pre>
   *
   * <code>.Proto.Common.ItemDto pingTiItem = 3;</code>
   */
  @java.lang.Override
  public com.dxx.game.dto.CommonProto.ItemDtoOrBuilder getPingTiItemOrBuilder() {
    return pingTiItem_ == null ? com.dxx.game.dto.CommonProto.ItemDto.getDefaultInstance() : pingTiItem_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    getSerializedSize();
    if (targetRowId_ != 0L) {
      output.writeInt64(1, targetRowId_);
    }
    if (getDeletedRowIdsList().size() > 0) {
      output.writeUInt32NoTag(18);
      output.writeUInt32NoTag(deletedRowIdsMemoizedSerializedSize);
    }
    for (int i = 0; i < deletedRowIds_.size(); i++) {
      output.writeInt64NoTag(deletedRowIds_.getLong(i));
    }
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeMessage(3, getPingTiItem());
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (targetRowId_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(1, targetRowId_);
    }
    {
      int dataSize = 0;
      for (int i = 0; i < deletedRowIds_.size(); i++) {
        dataSize += com.google.protobuf.CodedOutputStream
          .computeInt64SizeNoTag(deletedRowIds_.getLong(i));
      }
      size += dataSize;
      if (!getDeletedRowIdsList().isEmpty()) {
        size += 1;
        size += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(dataSize);
      }
      deletedRowIdsMemoizedSerializedSize = dataSize;
    }
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getPingTiItem());
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.dxx.game.dto.MergeBaoWu)) {
      return super.equals(obj);
    }
    com.dxx.game.dto.MergeBaoWu other = (com.dxx.game.dto.MergeBaoWu) obj;

    if (getTargetRowId()
        != other.getTargetRowId()) return false;
    if (!getDeletedRowIdsList()
        .equals(other.getDeletedRowIdsList())) return false;
    if (hasPingTiItem() != other.hasPingTiItem()) return false;
    if (hasPingTiItem()) {
      if (!getPingTiItem()
          .equals(other.getPingTiItem())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + TARGETROWID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getTargetRowId());
    if (getDeletedRowIdsCount() > 0) {
      hash = (37 * hash) + DELETEDROWIDS_FIELD_NUMBER;
      hash = (53 * hash) + getDeletedRowIdsList().hashCode();
    }
    if (hasPingTiItem()) {
      hash = (37 * hash) + PINGTIITEM_FIELD_NUMBER;
      hash = (53 * hash) + getPingTiItem().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.dxx.game.dto.MergeBaoWu parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.dxx.game.dto.MergeBaoWu parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.dxx.game.dto.MergeBaoWu parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.dxx.game.dto.MergeBaoWu parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.dxx.game.dto.MergeBaoWu parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.dxx.game.dto.MergeBaoWu parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.dxx.game.dto.MergeBaoWu parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static com.dxx.game.dto.MergeBaoWu parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.dxx.game.dto.MergeBaoWu parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.dxx.game.dto.MergeBaoWu parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.dxx.game.dto.MergeBaoWu parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static com.dxx.game.dto.MergeBaoWu parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.dxx.game.dto.MergeBaoWu prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code Proto.BaoWu.MergeBaoWu}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:Proto.BaoWu.MergeBaoWu)
      com.dxx.game.dto.MergeBaoWuOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.BaoWuProto.internal_static_Proto_BaoWu_MergeBaoWu_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.BaoWuProto.internal_static_Proto_BaoWu_MergeBaoWu_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.MergeBaoWu.class, com.dxx.game.dto.MergeBaoWu.Builder.class);
    }

    // Construct using com.dxx.game.dto.MergeBaoWu.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        getPingTiItemFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      targetRowId_ = 0L;
      deletedRowIds_ = emptyLongList();
      pingTiItem_ = null;
      if (pingTiItemBuilder_ != null) {
        pingTiItemBuilder_.dispose();
        pingTiItemBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.dxx.game.dto.BaoWuProto.internal_static_Proto_BaoWu_MergeBaoWu_descriptor;
    }

    @java.lang.Override
    public com.dxx.game.dto.MergeBaoWu getDefaultInstanceForType() {
      return com.dxx.game.dto.MergeBaoWu.getDefaultInstance();
    }

    @java.lang.Override
    public com.dxx.game.dto.MergeBaoWu build() {
      com.dxx.game.dto.MergeBaoWu result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.dxx.game.dto.MergeBaoWu buildPartial() {
      com.dxx.game.dto.MergeBaoWu result = new com.dxx.game.dto.MergeBaoWu(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(com.dxx.game.dto.MergeBaoWu result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.targetRowId_ = targetRowId_;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        deletedRowIds_.makeImmutable();
        result.deletedRowIds_ = deletedRowIds_;
      }
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.pingTiItem_ = pingTiItemBuilder_ == null
            ? pingTiItem_
            : pingTiItemBuilder_.build();
        to_bitField0_ |= 0x00000001;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.dxx.game.dto.MergeBaoWu) {
        return mergeFrom((com.dxx.game.dto.MergeBaoWu)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.dxx.game.dto.MergeBaoWu other) {
      if (other == com.dxx.game.dto.MergeBaoWu.getDefaultInstance()) return this;
      if (other.getTargetRowId() != 0L) {
        setTargetRowId(other.getTargetRowId());
      }
      if (!other.deletedRowIds_.isEmpty()) {
        if (deletedRowIds_.isEmpty()) {
          deletedRowIds_ = other.deletedRowIds_;
          deletedRowIds_.makeImmutable();
          bitField0_ |= 0x00000002;
        } else {
          ensureDeletedRowIdsIsMutable();
          deletedRowIds_.addAll(other.deletedRowIds_);
        }
        onChanged();
      }
      if (other.hasPingTiItem()) {
        mergePingTiItem(other.getPingTiItem());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              targetRowId_ = input.readInt64();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              long v = input.readInt64();
              ensureDeletedRowIdsIsMutable();
              deletedRowIds_.addLong(v);
              break;
            } // case 16
            case 18: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              ensureDeletedRowIdsIsMutable();
              while (input.getBytesUntilLimit() > 0) {
                deletedRowIds_.addLong(input.readInt64());
              }
              input.popLimit(limit);
              break;
            } // case 18
            case 26: {
              input.readMessage(
                  getPingTiItemFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private long targetRowId_ ;
    /**
     * <pre>
     * 合并的目标宝物rowId
     * </pre>
     *
     * <code>int64 targetRowId = 1;</code>
     * @return The targetRowId.
     */
    @java.lang.Override
    public long getTargetRowId() {
      return targetRowId_;
    }
    /**
     * <pre>
     * 合并的目标宝物rowId
     * </pre>
     *
     * <code>int64 targetRowId = 1;</code>
     * @param value The targetRowId to set.
     * @return This builder for chaining.
     */
    public Builder setTargetRowId(long value) {

      targetRowId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 合并的目标宝物rowId
     * </pre>
     *
     * <code>int64 targetRowId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearTargetRowId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      targetRowId_ = 0L;
      onChanged();
      return this;
    }

    private com.google.protobuf.Internal.LongList deletedRowIds_ = emptyLongList();
    private void ensureDeletedRowIdsIsMutable() {
      if (!deletedRowIds_.isModifiable()) {
        deletedRowIds_ = makeMutableCopy(deletedRowIds_);
      }
      bitField0_ |= 0x00000002;
    }
    /**
     * <pre>
     * 被合并的宝物rowId列表
     * </pre>
     *
     * <code>repeated int64 deletedRowIds = 2;</code>
     * @return A list containing the deletedRowIds.
     */
    public java.util.List<java.lang.Long>
        getDeletedRowIdsList() {
      deletedRowIds_.makeImmutable();
      return deletedRowIds_;
    }
    /**
     * <pre>
     * 被合并的宝物rowId列表
     * </pre>
     *
     * <code>repeated int64 deletedRowIds = 2;</code>
     * @return The count of deletedRowIds.
     */
    public int getDeletedRowIdsCount() {
      return deletedRowIds_.size();
    }
    /**
     * <pre>
     * 被合并的宝物rowId列表
     * </pre>
     *
     * <code>repeated int64 deletedRowIds = 2;</code>
     * @param index The index of the element to return.
     * @return The deletedRowIds at the given index.
     */
    public long getDeletedRowIds(int index) {
      return deletedRowIds_.getLong(index);
    }
    /**
     * <pre>
     * 被合并的宝物rowId列表
     * </pre>
     *
     * <code>repeated int64 deletedRowIds = 2;</code>
     * @param index The index to set the value at.
     * @param value The deletedRowIds to set.
     * @return This builder for chaining.
     */
    public Builder setDeletedRowIds(
        int index, long value) {

      ensureDeletedRowIdsIsMutable();
      deletedRowIds_.setLong(index, value);
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 被合并的宝物rowId列表
     * </pre>
     *
     * <code>repeated int64 deletedRowIds = 2;</code>
     * @param value The deletedRowIds to add.
     * @return This builder for chaining.
     */
    public Builder addDeletedRowIds(long value) {

      ensureDeletedRowIdsIsMutable();
      deletedRowIds_.addLong(value);
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 被合并的宝物rowId列表
     * </pre>
     *
     * <code>repeated int64 deletedRowIds = 2;</code>
     * @param values The deletedRowIds to add.
     * @return This builder for chaining.
     */
    public Builder addAllDeletedRowIds(
        java.lang.Iterable<? extends java.lang.Long> values) {
      ensureDeletedRowIdsIsMutable();
      com.google.protobuf.AbstractMessageLite.Builder.addAll(
          values, deletedRowIds_);
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 被合并的宝物rowId列表
     * </pre>
     *
     * <code>repeated int64 deletedRowIds = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearDeletedRowIds() {
      deletedRowIds_ = emptyLongList();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }

    private com.dxx.game.dto.CommonProto.ItemDto pingTiItem_;
    private com.google.protobuf.SingleFieldBuilder<
        com.dxx.game.dto.CommonProto.ItemDto, com.dxx.game.dto.CommonProto.ItemDto.Builder, com.dxx.game.dto.CommonProto.ItemDtoOrBuilder> pingTiItemBuilder_;
    /**
     * <pre>
     * 平替材料
     * </pre>
     *
     * <code>.Proto.Common.ItemDto pingTiItem = 3;</code>
     * @return Whether the pingTiItem field is set.
     */
    public boolean hasPingTiItem() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 平替材料
     * </pre>
     *
     * <code>.Proto.Common.ItemDto pingTiItem = 3;</code>
     * @return The pingTiItem.
     */
    public com.dxx.game.dto.CommonProto.ItemDto getPingTiItem() {
      if (pingTiItemBuilder_ == null) {
        return pingTiItem_ == null ? com.dxx.game.dto.CommonProto.ItemDto.getDefaultInstance() : pingTiItem_;
      } else {
        return pingTiItemBuilder_.getMessage();
      }
    }
    /**
     * <pre>
     * 平替材料
     * </pre>
     *
     * <code>.Proto.Common.ItemDto pingTiItem = 3;</code>
     */
    public Builder setPingTiItem(com.dxx.game.dto.CommonProto.ItemDto value) {
      if (pingTiItemBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        pingTiItem_ = value;
      } else {
        pingTiItemBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 平替材料
     * </pre>
     *
     * <code>.Proto.Common.ItemDto pingTiItem = 3;</code>
     */
    public Builder setPingTiItem(
        com.dxx.game.dto.CommonProto.ItemDto.Builder builderForValue) {
      if (pingTiItemBuilder_ == null) {
        pingTiItem_ = builderForValue.build();
      } else {
        pingTiItemBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 平替材料
     * </pre>
     *
     * <code>.Proto.Common.ItemDto pingTiItem = 3;</code>
     */
    public Builder mergePingTiItem(com.dxx.game.dto.CommonProto.ItemDto value) {
      if (pingTiItemBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0) &&
          pingTiItem_ != null &&
          pingTiItem_ != com.dxx.game.dto.CommonProto.ItemDto.getDefaultInstance()) {
          getPingTiItemBuilder().mergeFrom(value);
        } else {
          pingTiItem_ = value;
        }
      } else {
        pingTiItemBuilder_.mergeFrom(value);
      }
      if (pingTiItem_ != null) {
        bitField0_ |= 0x00000004;
        onChanged();
      }
      return this;
    }
    /**
     * <pre>
     * 平替材料
     * </pre>
     *
     * <code>.Proto.Common.ItemDto pingTiItem = 3;</code>
     */
    public Builder clearPingTiItem() {
      bitField0_ = (bitField0_ & ~0x00000004);
      pingTiItem_ = null;
      if (pingTiItemBuilder_ != null) {
        pingTiItemBuilder_.dispose();
        pingTiItemBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 平替材料
     * </pre>
     *
     * <code>.Proto.Common.ItemDto pingTiItem = 3;</code>
     */
    public com.dxx.game.dto.CommonProto.ItemDto.Builder getPingTiItemBuilder() {
      bitField0_ |= 0x00000004;
      onChanged();
      return getPingTiItemFieldBuilder().getBuilder();
    }
    /**
     * <pre>
     * 平替材料
     * </pre>
     *
     * <code>.Proto.Common.ItemDto pingTiItem = 3;</code>
     */
    public com.dxx.game.dto.CommonProto.ItemDtoOrBuilder getPingTiItemOrBuilder() {
      if (pingTiItemBuilder_ != null) {
        return pingTiItemBuilder_.getMessageOrBuilder();
      } else {
        return pingTiItem_ == null ?
            com.dxx.game.dto.CommonProto.ItemDto.getDefaultInstance() : pingTiItem_;
      }
    }
    /**
     * <pre>
     * 平替材料
     * </pre>
     *
     * <code>.Proto.Common.ItemDto pingTiItem = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        com.dxx.game.dto.CommonProto.ItemDto, com.dxx.game.dto.CommonProto.ItemDto.Builder, com.dxx.game.dto.CommonProto.ItemDtoOrBuilder> 
        getPingTiItemFieldBuilder() {
      if (pingTiItemBuilder_ == null) {
        pingTiItemBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            com.dxx.game.dto.CommonProto.ItemDto, com.dxx.game.dto.CommonProto.ItemDto.Builder, com.dxx.game.dto.CommonProto.ItemDtoOrBuilder>(
                getPingTiItem(),
                getParentForChildren(),
                isClean());
        pingTiItem_ = null;
      }
      return pingTiItemBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:Proto.BaoWu.MergeBaoWu)
  }

  // @@protoc_insertion_point(class_scope:Proto.BaoWu.MergeBaoWu)
  private static final com.dxx.game.dto.MergeBaoWu DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.dxx.game.dto.MergeBaoWu();
  }

  public static com.dxx.game.dto.MergeBaoWu getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<MergeBaoWu>
      PARSER = new com.google.protobuf.AbstractParser<MergeBaoWu>() {
    @java.lang.Override
    public MergeBaoWu parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<MergeBaoWu> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<MergeBaoWu> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.dxx.game.dto.MergeBaoWu getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

