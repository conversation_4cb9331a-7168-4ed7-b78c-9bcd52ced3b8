// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: battle.proto
// Protobuf Java Version: 4.29.0

package com.dxx.game.dto;

/**
 * Protobuf type {@code Proto.Battle.PowerReq}
 */
public final class PowerReq extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:Proto.Battle.PowerReq)
    PowerReqOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 29,
      /* patch= */ 0,
      /* suffix= */ "",
      PowerReq.class.getName());
  }
  // Use PowerReq.newBuilder() to construct.
  private PowerReq(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private PowerReq() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.dxx.game.dto.BattleProto.internal_static_Proto_Battle_PowerReq_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.dxx.game.dto.BattleProto.internal_static_Proto_Battle_PowerReq_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.dxx.game.dto.PowerReq.class, com.dxx.game.dto.PowerReq.Builder.class);
  }

  private int bitField0_;
  public static final int USERINFO_FIELD_NUMBER = 1;
  private com.dxx.game.dto.BattleUserInfo userInfo_;
  /**
   * <code>.Proto.Battle.BattleUserInfo userInfo = 1;</code>
   * @return Whether the userInfo field is set.
   */
  @java.lang.Override
  public boolean hasUserInfo() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>.Proto.Battle.BattleUserInfo userInfo = 1;</code>
   * @return The userInfo.
   */
  @java.lang.Override
  public com.dxx.game.dto.BattleUserInfo getUserInfo() {
    return userInfo_ == null ? com.dxx.game.dto.BattleUserInfo.getDefaultInstance() : userInfo_;
  }
  /**
   * <code>.Proto.Battle.BattleUserInfo userInfo = 1;</code>
   */
  @java.lang.Override
  public com.dxx.game.dto.BattleUserInfoOrBuilder getUserInfoOrBuilder() {
    return userInfo_ == null ? com.dxx.game.dto.BattleUserInfo.getDefaultInstance() : userInfo_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeMessage(1, getUserInfo());
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, getUserInfo());
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.dxx.game.dto.PowerReq)) {
      return super.equals(obj);
    }
    com.dxx.game.dto.PowerReq other = (com.dxx.game.dto.PowerReq) obj;

    if (hasUserInfo() != other.hasUserInfo()) return false;
    if (hasUserInfo()) {
      if (!getUserInfo()
          .equals(other.getUserInfo())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasUserInfo()) {
      hash = (37 * hash) + USERINFO_FIELD_NUMBER;
      hash = (53 * hash) + getUserInfo().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.dxx.game.dto.PowerReq parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.dxx.game.dto.PowerReq parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.dxx.game.dto.PowerReq parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.dxx.game.dto.PowerReq parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.dxx.game.dto.PowerReq parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.dxx.game.dto.PowerReq parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.dxx.game.dto.PowerReq parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static com.dxx.game.dto.PowerReq parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.dxx.game.dto.PowerReq parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.dxx.game.dto.PowerReq parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.dxx.game.dto.PowerReq parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static com.dxx.game.dto.PowerReq parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.dxx.game.dto.PowerReq prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code Proto.Battle.PowerReq}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:Proto.Battle.PowerReq)
      com.dxx.game.dto.PowerReqOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.BattleProto.internal_static_Proto_Battle_PowerReq_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.BattleProto.internal_static_Proto_Battle_PowerReq_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.PowerReq.class, com.dxx.game.dto.PowerReq.Builder.class);
    }

    // Construct using com.dxx.game.dto.PowerReq.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        getUserInfoFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      userInfo_ = null;
      if (userInfoBuilder_ != null) {
        userInfoBuilder_.dispose();
        userInfoBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.dxx.game.dto.BattleProto.internal_static_Proto_Battle_PowerReq_descriptor;
    }

    @java.lang.Override
    public com.dxx.game.dto.PowerReq getDefaultInstanceForType() {
      return com.dxx.game.dto.PowerReq.getDefaultInstance();
    }

    @java.lang.Override
    public com.dxx.game.dto.PowerReq build() {
      com.dxx.game.dto.PowerReq result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.dxx.game.dto.PowerReq buildPartial() {
      com.dxx.game.dto.PowerReq result = new com.dxx.game.dto.PowerReq(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(com.dxx.game.dto.PowerReq result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.userInfo_ = userInfoBuilder_ == null
            ? userInfo_
            : userInfoBuilder_.build();
        to_bitField0_ |= 0x00000001;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.dxx.game.dto.PowerReq) {
        return mergeFrom((com.dxx.game.dto.PowerReq)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.dxx.game.dto.PowerReq other) {
      if (other == com.dxx.game.dto.PowerReq.getDefaultInstance()) return this;
      if (other.hasUserInfo()) {
        mergeUserInfo(other.getUserInfo());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              input.readMessage(
                  getUserInfoFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private com.dxx.game.dto.BattleUserInfo userInfo_;
    private com.google.protobuf.SingleFieldBuilder<
        com.dxx.game.dto.BattleUserInfo, com.dxx.game.dto.BattleUserInfo.Builder, com.dxx.game.dto.BattleUserInfoOrBuilder> userInfoBuilder_;
    /**
     * <code>.Proto.Battle.BattleUserInfo userInfo = 1;</code>
     * @return Whether the userInfo field is set.
     */
    public boolean hasUserInfo() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.Proto.Battle.BattleUserInfo userInfo = 1;</code>
     * @return The userInfo.
     */
    public com.dxx.game.dto.BattleUserInfo getUserInfo() {
      if (userInfoBuilder_ == null) {
        return userInfo_ == null ? com.dxx.game.dto.BattleUserInfo.getDefaultInstance() : userInfo_;
      } else {
        return userInfoBuilder_.getMessage();
      }
    }
    /**
     * <code>.Proto.Battle.BattleUserInfo userInfo = 1;</code>
     */
    public Builder setUserInfo(com.dxx.game.dto.BattleUserInfo value) {
      if (userInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        userInfo_ = value;
      } else {
        userInfoBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>.Proto.Battle.BattleUserInfo userInfo = 1;</code>
     */
    public Builder setUserInfo(
        com.dxx.game.dto.BattleUserInfo.Builder builderForValue) {
      if (userInfoBuilder_ == null) {
        userInfo_ = builderForValue.build();
      } else {
        userInfoBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>.Proto.Battle.BattleUserInfo userInfo = 1;</code>
     */
    public Builder mergeUserInfo(com.dxx.game.dto.BattleUserInfo value) {
      if (userInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0) &&
          userInfo_ != null &&
          userInfo_ != com.dxx.game.dto.BattleUserInfo.getDefaultInstance()) {
          getUserInfoBuilder().mergeFrom(value);
        } else {
          userInfo_ = value;
        }
      } else {
        userInfoBuilder_.mergeFrom(value);
      }
      if (userInfo_ != null) {
        bitField0_ |= 0x00000001;
        onChanged();
      }
      return this;
    }
    /**
     * <code>.Proto.Battle.BattleUserInfo userInfo = 1;</code>
     */
    public Builder clearUserInfo() {
      bitField0_ = (bitField0_ & ~0x00000001);
      userInfo_ = null;
      if (userInfoBuilder_ != null) {
        userInfoBuilder_.dispose();
        userInfoBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>.Proto.Battle.BattleUserInfo userInfo = 1;</code>
     */
    public com.dxx.game.dto.BattleUserInfo.Builder getUserInfoBuilder() {
      bitField0_ |= 0x00000001;
      onChanged();
      return getUserInfoFieldBuilder().getBuilder();
    }
    /**
     * <code>.Proto.Battle.BattleUserInfo userInfo = 1;</code>
     */
    public com.dxx.game.dto.BattleUserInfoOrBuilder getUserInfoOrBuilder() {
      if (userInfoBuilder_ != null) {
        return userInfoBuilder_.getMessageOrBuilder();
      } else {
        return userInfo_ == null ?
            com.dxx.game.dto.BattleUserInfo.getDefaultInstance() : userInfo_;
      }
    }
    /**
     * <code>.Proto.Battle.BattleUserInfo userInfo = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        com.dxx.game.dto.BattleUserInfo, com.dxx.game.dto.BattleUserInfo.Builder, com.dxx.game.dto.BattleUserInfoOrBuilder> 
        getUserInfoFieldBuilder() {
      if (userInfoBuilder_ == null) {
        userInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            com.dxx.game.dto.BattleUserInfo, com.dxx.game.dto.BattleUserInfo.Builder, com.dxx.game.dto.BattleUserInfoOrBuilder>(
                getUserInfo(),
                getParentForChildren(),
                isClean());
        userInfo_ = null;
      }
      return userInfoBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:Proto.Battle.PowerReq)
  }

  // @@protoc_insertion_point(class_scope:Proto.Battle.PowerReq)
  private static final com.dxx.game.dto.PowerReq DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.dxx.game.dto.PowerReq();
  }

  public static com.dxx.game.dto.PowerReq getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<PowerReq>
      PARSER = new com.google.protobuf.AbstractParser<PowerReq>() {
    @java.lang.Override
    public PowerReq parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<PowerReq> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<PowerReq> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.dxx.game.dto.PowerReq getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

