// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: rank.proto
// Protobuf Java Version: 4.29.0

package com.dxx.game.dto;

/**
 * Protobuf type {@code Proto.Rank.RankGuildDto}
 */
public final class RankGuildDto extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:Proto.Rank.RankGuildDto)
    RankGuildDtoOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 29,
      /* patch= */ 0,
      /* suffix= */ "",
      RankGuildDto.class.getName());
  }
  // Use RankGuildDto.newBuilder() to construct.
  private RankGuildDto(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private RankGuildDto() {
    guildName_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.dxx.game.dto.RankProto.internal_static_Proto_Rank_RankGuildDto_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.dxx.game.dto.RankProto.internal_static_Proto_Rank_RankGuildDto_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.dxx.game.dto.RankGuildDto.class, com.dxx.game.dto.RankGuildDto.Builder.class);
  }

  public static final int SERVERID_FIELD_NUMBER = 1;
  private int serverId_ = 0;
  /**
   * <code>int32 serverId = 1;</code>
   * @return The serverId.
   */
  @java.lang.Override
  public int getServerId() {
    return serverId_;
  }

  public static final int GUILDID_FIELD_NUMBER = 2;
  private long guildId_ = 0L;
  /**
   * <code>int64 guildId = 2;</code>
   * @return The guildId.
   */
  @java.lang.Override
  public long getGuildId() {
    return guildId_;
  }

  public static final int GUILDNAME_FIELD_NUMBER = 3;
  @SuppressWarnings("serial")
  private volatile java.lang.Object guildName_ = "";
  /**
   * <code>string guildName = 3;</code>
   * @return The guildName.
   */
  @java.lang.Override
  public java.lang.String getGuildName() {
    java.lang.Object ref = guildName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      guildName_ = s;
      return s;
    }
  }
  /**
   * <code>string guildName = 3;</code>
   * @return The bytes for guildName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getGuildNameBytes() {
    java.lang.Object ref = guildName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      guildName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int GUILDICON_FIELD_NUMBER = 4;
  private int guildIcon_ = 0;
  /**
   * <pre>
   * 公会图标
   * </pre>
   *
   * <code>int32 guildIcon = 4;</code>
   * @return The guildIcon.
   */
  @java.lang.Override
  public int getGuildIcon() {
    return guildIcon_;
  }

  public static final int GUILDICONBG_FIELD_NUMBER = 5;
  private int guildIconBg_ = 0;
  /**
   * <pre>
   * 公会图标背景
   * </pre>
   *
   * <code>int32 guildIconBg = 5;</code>
   * @return The guildIconBg.
   */
  @java.lang.Override
  public int getGuildIconBg() {
    return guildIconBg_;
  }

  public static final int LEVEL_FIELD_NUMBER = 6;
  private int level_ = 0;
  /**
   * <pre>
   * 公会等级
   * </pre>
   *
   * <code>int32 level = 6;</code>
   * @return The level.
   */
  @java.lang.Override
  public int getLevel() {
    return level_;
  }

  public static final int POWER_FIELD_NUMBER = 7;
  private long power_ = 0L;
  /**
   * <pre>
   * 公会总战力
   * </pre>
   *
   * <code>int64 power = 7;</code>
   * @return The power.
   */
  @java.lang.Override
  public long getPower() {
    return power_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (serverId_ != 0) {
      output.writeInt32(1, serverId_);
    }
    if (guildId_ != 0L) {
      output.writeInt64(2, guildId_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(guildName_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 3, guildName_);
    }
    if (guildIcon_ != 0) {
      output.writeInt32(4, guildIcon_);
    }
    if (guildIconBg_ != 0) {
      output.writeInt32(5, guildIconBg_);
    }
    if (level_ != 0) {
      output.writeInt32(6, level_);
    }
    if (power_ != 0L) {
      output.writeInt64(7, power_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (serverId_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, serverId_);
    }
    if (guildId_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(2, guildId_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(guildName_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(3, guildName_);
    }
    if (guildIcon_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, guildIcon_);
    }
    if (guildIconBg_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(5, guildIconBg_);
    }
    if (level_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(6, level_);
    }
    if (power_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(7, power_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.dxx.game.dto.RankGuildDto)) {
      return super.equals(obj);
    }
    com.dxx.game.dto.RankGuildDto other = (com.dxx.game.dto.RankGuildDto) obj;

    if (getServerId()
        != other.getServerId()) return false;
    if (getGuildId()
        != other.getGuildId()) return false;
    if (!getGuildName()
        .equals(other.getGuildName())) return false;
    if (getGuildIcon()
        != other.getGuildIcon()) return false;
    if (getGuildIconBg()
        != other.getGuildIconBg()) return false;
    if (getLevel()
        != other.getLevel()) return false;
    if (getPower()
        != other.getPower()) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + SERVERID_FIELD_NUMBER;
    hash = (53 * hash) + getServerId();
    hash = (37 * hash) + GUILDID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getGuildId());
    hash = (37 * hash) + GUILDNAME_FIELD_NUMBER;
    hash = (53 * hash) + getGuildName().hashCode();
    hash = (37 * hash) + GUILDICON_FIELD_NUMBER;
    hash = (53 * hash) + getGuildIcon();
    hash = (37 * hash) + GUILDICONBG_FIELD_NUMBER;
    hash = (53 * hash) + getGuildIconBg();
    hash = (37 * hash) + LEVEL_FIELD_NUMBER;
    hash = (53 * hash) + getLevel();
    hash = (37 * hash) + POWER_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getPower());
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.dxx.game.dto.RankGuildDto parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.dxx.game.dto.RankGuildDto parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.dxx.game.dto.RankGuildDto parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.dxx.game.dto.RankGuildDto parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.dxx.game.dto.RankGuildDto parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.dxx.game.dto.RankGuildDto parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.dxx.game.dto.RankGuildDto parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static com.dxx.game.dto.RankGuildDto parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.dxx.game.dto.RankGuildDto parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.dxx.game.dto.RankGuildDto parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.dxx.game.dto.RankGuildDto parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static com.dxx.game.dto.RankGuildDto parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.dxx.game.dto.RankGuildDto prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code Proto.Rank.RankGuildDto}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:Proto.Rank.RankGuildDto)
      com.dxx.game.dto.RankGuildDtoOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.RankProto.internal_static_Proto_Rank_RankGuildDto_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.RankProto.internal_static_Proto_Rank_RankGuildDto_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.RankGuildDto.class, com.dxx.game.dto.RankGuildDto.Builder.class);
    }

    // Construct using com.dxx.game.dto.RankGuildDto.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      serverId_ = 0;
      guildId_ = 0L;
      guildName_ = "";
      guildIcon_ = 0;
      guildIconBg_ = 0;
      level_ = 0;
      power_ = 0L;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.dxx.game.dto.RankProto.internal_static_Proto_Rank_RankGuildDto_descriptor;
    }

    @java.lang.Override
    public com.dxx.game.dto.RankGuildDto getDefaultInstanceForType() {
      return com.dxx.game.dto.RankGuildDto.getDefaultInstance();
    }

    @java.lang.Override
    public com.dxx.game.dto.RankGuildDto build() {
      com.dxx.game.dto.RankGuildDto result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.dxx.game.dto.RankGuildDto buildPartial() {
      com.dxx.game.dto.RankGuildDto result = new com.dxx.game.dto.RankGuildDto(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(com.dxx.game.dto.RankGuildDto result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.serverId_ = serverId_;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.guildId_ = guildId_;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.guildName_ = guildName_;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.guildIcon_ = guildIcon_;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.guildIconBg_ = guildIconBg_;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.level_ = level_;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.power_ = power_;
      }
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.dxx.game.dto.RankGuildDto) {
        return mergeFrom((com.dxx.game.dto.RankGuildDto)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.dxx.game.dto.RankGuildDto other) {
      if (other == com.dxx.game.dto.RankGuildDto.getDefaultInstance()) return this;
      if (other.getServerId() != 0) {
        setServerId(other.getServerId());
      }
      if (other.getGuildId() != 0L) {
        setGuildId(other.getGuildId());
      }
      if (!other.getGuildName().isEmpty()) {
        guildName_ = other.guildName_;
        bitField0_ |= 0x00000004;
        onChanged();
      }
      if (other.getGuildIcon() != 0) {
        setGuildIcon(other.getGuildIcon());
      }
      if (other.getGuildIconBg() != 0) {
        setGuildIconBg(other.getGuildIconBg());
      }
      if (other.getLevel() != 0) {
        setLevel(other.getLevel());
      }
      if (other.getPower() != 0L) {
        setPower(other.getPower());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              serverId_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              guildId_ = input.readInt64();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 26: {
              guildName_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 32: {
              guildIcon_ = input.readInt32();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            case 40: {
              guildIconBg_ = input.readInt32();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            case 48: {
              level_ = input.readInt32();
              bitField0_ |= 0x00000020;
              break;
            } // case 48
            case 56: {
              power_ = input.readInt64();
              bitField0_ |= 0x00000040;
              break;
            } // case 56
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int serverId_ ;
    /**
     * <code>int32 serverId = 1;</code>
     * @return The serverId.
     */
    @java.lang.Override
    public int getServerId() {
      return serverId_;
    }
    /**
     * <code>int32 serverId = 1;</code>
     * @param value The serverId to set.
     * @return This builder for chaining.
     */
    public Builder setServerId(int value) {

      serverId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>int32 serverId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearServerId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      serverId_ = 0;
      onChanged();
      return this;
    }

    private long guildId_ ;
    /**
     * <code>int64 guildId = 2;</code>
     * @return The guildId.
     */
    @java.lang.Override
    public long getGuildId() {
      return guildId_;
    }
    /**
     * <code>int64 guildId = 2;</code>
     * @param value The guildId to set.
     * @return This builder for chaining.
     */
    public Builder setGuildId(long value) {

      guildId_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>int64 guildId = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearGuildId() {
      bitField0_ = (bitField0_ & ~0x00000002);
      guildId_ = 0L;
      onChanged();
      return this;
    }

    private java.lang.Object guildName_ = "";
    /**
     * <code>string guildName = 3;</code>
     * @return The guildName.
     */
    public java.lang.String getGuildName() {
      java.lang.Object ref = guildName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        guildName_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <code>string guildName = 3;</code>
     * @return The bytes for guildName.
     */
    public com.google.protobuf.ByteString
        getGuildNameBytes() {
      java.lang.Object ref = guildName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        guildName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <code>string guildName = 3;</code>
     * @param value The guildName to set.
     * @return This builder for chaining.
     */
    public Builder setGuildName(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      guildName_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>string guildName = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearGuildName() {
      guildName_ = getDefaultInstance().getGuildName();
      bitField0_ = (bitField0_ & ~0x00000004);
      onChanged();
      return this;
    }
    /**
     * <code>string guildName = 3;</code>
     * @param value The bytes for guildName to set.
     * @return This builder for chaining.
     */
    public Builder setGuildNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      guildName_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }

    private int guildIcon_ ;
    /**
     * <pre>
     * 公会图标
     * </pre>
     *
     * <code>int32 guildIcon = 4;</code>
     * @return The guildIcon.
     */
    @java.lang.Override
    public int getGuildIcon() {
      return guildIcon_;
    }
    /**
     * <pre>
     * 公会图标
     * </pre>
     *
     * <code>int32 guildIcon = 4;</code>
     * @param value The guildIcon to set.
     * @return This builder for chaining.
     */
    public Builder setGuildIcon(int value) {

      guildIcon_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 公会图标
     * </pre>
     *
     * <code>int32 guildIcon = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearGuildIcon() {
      bitField0_ = (bitField0_ & ~0x00000008);
      guildIcon_ = 0;
      onChanged();
      return this;
    }

    private int guildIconBg_ ;
    /**
     * <pre>
     * 公会图标背景
     * </pre>
     *
     * <code>int32 guildIconBg = 5;</code>
     * @return The guildIconBg.
     */
    @java.lang.Override
    public int getGuildIconBg() {
      return guildIconBg_;
    }
    /**
     * <pre>
     * 公会图标背景
     * </pre>
     *
     * <code>int32 guildIconBg = 5;</code>
     * @param value The guildIconBg to set.
     * @return This builder for chaining.
     */
    public Builder setGuildIconBg(int value) {

      guildIconBg_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 公会图标背景
     * </pre>
     *
     * <code>int32 guildIconBg = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearGuildIconBg() {
      bitField0_ = (bitField0_ & ~0x00000010);
      guildIconBg_ = 0;
      onChanged();
      return this;
    }

    private int level_ ;
    /**
     * <pre>
     * 公会等级
     * </pre>
     *
     * <code>int32 level = 6;</code>
     * @return The level.
     */
    @java.lang.Override
    public int getLevel() {
      return level_;
    }
    /**
     * <pre>
     * 公会等级
     * </pre>
     *
     * <code>int32 level = 6;</code>
     * @param value The level to set.
     * @return This builder for chaining.
     */
    public Builder setLevel(int value) {

      level_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 公会等级
     * </pre>
     *
     * <code>int32 level = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearLevel() {
      bitField0_ = (bitField0_ & ~0x00000020);
      level_ = 0;
      onChanged();
      return this;
    }

    private long power_ ;
    /**
     * <pre>
     * 公会总战力
     * </pre>
     *
     * <code>int64 power = 7;</code>
     * @return The power.
     */
    @java.lang.Override
    public long getPower() {
      return power_;
    }
    /**
     * <pre>
     * 公会总战力
     * </pre>
     *
     * <code>int64 power = 7;</code>
     * @param value The power to set.
     * @return This builder for chaining.
     */
    public Builder setPower(long value) {

      power_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 公会总战力
     * </pre>
     *
     * <code>int64 power = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearPower() {
      bitField0_ = (bitField0_ & ~0x00000040);
      power_ = 0L;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:Proto.Rank.RankGuildDto)
  }

  // @@protoc_insertion_point(class_scope:Proto.Rank.RankGuildDto)
  private static final com.dxx.game.dto.RankGuildDto DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.dxx.game.dto.RankGuildDto();
  }

  public static com.dxx.game.dto.RankGuildDto getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<RankGuildDto>
      PARSER = new com.google.protobuf.AbstractParser<RankGuildDto>() {
    @java.lang.Override
    public RankGuildDto parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<RankGuildDto> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<RankGuildDto> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.dxx.game.dto.RankGuildDto getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

