// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: rank.proto
// Protobuf Java Version: 4.29.0

package com.dxx.game.dto;

/**
 * Protobuf enum {@code Proto.Rank.RankGuildType}
 */
public enum RankGuildType
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <pre>
   * 公会排行榜
   * </pre>
   *
   * <code>GUILD = 0;</code>
   */
  GUILD(0),
  UNRECOGNIZED(-1),
  ;

  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 29,
      /* patch= */ 0,
      /* suffix= */ "",
      RankGuildType.class.getName());
  }
  /**
   * <pre>
   * 公会排行榜
   * </pre>
   *
   * <code>GUILD = 0;</code>
   */
  public static final int GUILD_VALUE = 0;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static RankGuildType valueOf(int value) {
    return forNumber(value);
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   */
  public static RankGuildType forNumber(int value) {
    switch (value) {
      case 0: return GUILD;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<RankGuildType>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      RankGuildType> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<RankGuildType>() {
          public RankGuildType findValueByNumber(int number) {
            return RankGuildType.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalStateException(
          "Can't get the descriptor of an unrecognized enum value.");
    }
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return com.dxx.game.dto.RankProto.getDescriptor().getEnumTypes().get(1);
  }

  private static final RankGuildType[] VALUES = values();

  public static RankGuildType valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private RankGuildType(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:Proto.Rank.RankGuildType)
}

