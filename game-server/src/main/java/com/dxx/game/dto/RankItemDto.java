// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: rank.proto
// Protobuf Java Version: 4.29.0

package com.dxx.game.dto;

/**
 * Protobuf type {@code Proto.Rank.RankItemDto}
 */
public final class RankItemDto extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:Proto.Rank.RankItemDto)
    RankItemDtoOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 29,
      /* patch= */ 0,
      /* suffix= */ "",
      RankItemDto.class.getName());
  }
  // Use RankItemDto.newBuilder() to construct.
  private RankItemDto(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private RankItemDto() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.dxx.game.dto.RankProto.internal_static_Proto_Rank_RankItemDto_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.dxx.game.dto.RankProto.internal_static_Proto_Rank_RankItemDto_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.dxx.game.dto.RankItemDto.class, com.dxx.game.dto.RankItemDto.Builder.class);
  }

  private int bitField0_;
  public static final int RANK_FIELD_NUMBER = 1;
  private int rank_ = 0;
  /**
   * <code>int32 rank = 1;</code>
   * @return The rank.
   */
  @java.lang.Override
  public int getRank() {
    return rank_;
  }

  public static final int SCORE_FIELD_NUMBER = 2;
  private long score_ = 0L;
  /**
   * <code>int64 score = 2;</code>
   * @return The score.
   */
  @java.lang.Override
  public long getScore() {
    return score_;
  }

  public static final int USERINFO_FIELD_NUMBER = 3;
  private com.dxx.game.dto.RankUserDto userInfo_;
  /**
   * <code>.Proto.Rank.RankUserDto userInfo = 3;</code>
   * @return Whether the userInfo field is set.
   */
  @java.lang.Override
  public boolean hasUserInfo() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>.Proto.Rank.RankUserDto userInfo = 3;</code>
   * @return The userInfo.
   */
  @java.lang.Override
  public com.dxx.game.dto.RankUserDto getUserInfo() {
    return userInfo_ == null ? com.dxx.game.dto.RankUserDto.getDefaultInstance() : userInfo_;
  }
  /**
   * <code>.Proto.Rank.RankUserDto userInfo = 3;</code>
   */
  @java.lang.Override
  public com.dxx.game.dto.RankUserDtoOrBuilder getUserInfoOrBuilder() {
    return userInfo_ == null ? com.dxx.game.dto.RankUserDto.getDefaultInstance() : userInfo_;
  }

  public static final int GUILDINFO_FIELD_NUMBER = 4;
  private com.dxx.game.dto.RankGuildDto guildInfo_;
  /**
   * <code>.Proto.Rank.RankGuildDto guildInfo = 4;</code>
   * @return Whether the guildInfo field is set.
   */
  @java.lang.Override
  public boolean hasGuildInfo() {
    return ((bitField0_ & 0x00000002) != 0);
  }
  /**
   * <code>.Proto.Rank.RankGuildDto guildInfo = 4;</code>
   * @return The guildInfo.
   */
  @java.lang.Override
  public com.dxx.game.dto.RankGuildDto getGuildInfo() {
    return guildInfo_ == null ? com.dxx.game.dto.RankGuildDto.getDefaultInstance() : guildInfo_;
  }
  /**
   * <code>.Proto.Rank.RankGuildDto guildInfo = 4;</code>
   */
  @java.lang.Override
  public com.dxx.game.dto.RankGuildDtoOrBuilder getGuildInfoOrBuilder() {
    return guildInfo_ == null ? com.dxx.game.dto.RankGuildDto.getDefaultInstance() : guildInfo_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (rank_ != 0) {
      output.writeInt32(1, rank_);
    }
    if (score_ != 0L) {
      output.writeInt64(2, score_);
    }
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeMessage(3, getUserInfo());
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      output.writeMessage(4, getGuildInfo());
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (rank_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, rank_);
    }
    if (score_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(2, score_);
    }
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(3, getUserInfo());
    }
    if (((bitField0_ & 0x00000002) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(4, getGuildInfo());
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.dxx.game.dto.RankItemDto)) {
      return super.equals(obj);
    }
    com.dxx.game.dto.RankItemDto other = (com.dxx.game.dto.RankItemDto) obj;

    if (getRank()
        != other.getRank()) return false;
    if (getScore()
        != other.getScore()) return false;
    if (hasUserInfo() != other.hasUserInfo()) return false;
    if (hasUserInfo()) {
      if (!getUserInfo()
          .equals(other.getUserInfo())) return false;
    }
    if (hasGuildInfo() != other.hasGuildInfo()) return false;
    if (hasGuildInfo()) {
      if (!getGuildInfo()
          .equals(other.getGuildInfo())) return false;
    }
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + RANK_FIELD_NUMBER;
    hash = (53 * hash) + getRank();
    hash = (37 * hash) + SCORE_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getScore());
    if (hasUserInfo()) {
      hash = (37 * hash) + USERINFO_FIELD_NUMBER;
      hash = (53 * hash) + getUserInfo().hashCode();
    }
    if (hasGuildInfo()) {
      hash = (37 * hash) + GUILDINFO_FIELD_NUMBER;
      hash = (53 * hash) + getGuildInfo().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.dxx.game.dto.RankItemDto parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.dxx.game.dto.RankItemDto parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.dxx.game.dto.RankItemDto parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.dxx.game.dto.RankItemDto parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.dxx.game.dto.RankItemDto parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.dxx.game.dto.RankItemDto parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.dxx.game.dto.RankItemDto parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static com.dxx.game.dto.RankItemDto parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.dxx.game.dto.RankItemDto parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.dxx.game.dto.RankItemDto parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.dxx.game.dto.RankItemDto parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static com.dxx.game.dto.RankItemDto parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.dxx.game.dto.RankItemDto prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code Proto.Rank.RankItemDto}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:Proto.Rank.RankItemDto)
      com.dxx.game.dto.RankItemDtoOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.RankProto.internal_static_Proto_Rank_RankItemDto_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.RankProto.internal_static_Proto_Rank_RankItemDto_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.RankItemDto.class, com.dxx.game.dto.RankItemDto.Builder.class);
    }

    // Construct using com.dxx.game.dto.RankItemDto.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        getUserInfoFieldBuilder();
        getGuildInfoFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      rank_ = 0;
      score_ = 0L;
      userInfo_ = null;
      if (userInfoBuilder_ != null) {
        userInfoBuilder_.dispose();
        userInfoBuilder_ = null;
      }
      guildInfo_ = null;
      if (guildInfoBuilder_ != null) {
        guildInfoBuilder_.dispose();
        guildInfoBuilder_ = null;
      }
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.dxx.game.dto.RankProto.internal_static_Proto_Rank_RankItemDto_descriptor;
    }

    @java.lang.Override
    public com.dxx.game.dto.RankItemDto getDefaultInstanceForType() {
      return com.dxx.game.dto.RankItemDto.getDefaultInstance();
    }

    @java.lang.Override
    public com.dxx.game.dto.RankItemDto build() {
      com.dxx.game.dto.RankItemDto result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.dxx.game.dto.RankItemDto buildPartial() {
      com.dxx.game.dto.RankItemDto result = new com.dxx.game.dto.RankItemDto(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(com.dxx.game.dto.RankItemDto result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.rank_ = rank_;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.score_ = score_;
      }
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.userInfo_ = userInfoBuilder_ == null
            ? userInfo_
            : userInfoBuilder_.build();
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.guildInfo_ = guildInfoBuilder_ == null
            ? guildInfo_
            : guildInfoBuilder_.build();
        to_bitField0_ |= 0x00000002;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.dxx.game.dto.RankItemDto) {
        return mergeFrom((com.dxx.game.dto.RankItemDto)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.dxx.game.dto.RankItemDto other) {
      if (other == com.dxx.game.dto.RankItemDto.getDefaultInstance()) return this;
      if (other.getRank() != 0) {
        setRank(other.getRank());
      }
      if (other.getScore() != 0L) {
        setScore(other.getScore());
      }
      if (other.hasUserInfo()) {
        mergeUserInfo(other.getUserInfo());
      }
      if (other.hasGuildInfo()) {
        mergeGuildInfo(other.getGuildInfo());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              rank_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              score_ = input.readInt64();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 26: {
              input.readMessage(
                  getUserInfoFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000004;
              break;
            } // case 26
            case 34: {
              input.readMessage(
                  getGuildInfoFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000008;
              break;
            } // case 34
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int rank_ ;
    /**
     * <code>int32 rank = 1;</code>
     * @return The rank.
     */
    @java.lang.Override
    public int getRank() {
      return rank_;
    }
    /**
     * <code>int32 rank = 1;</code>
     * @param value The rank to set.
     * @return This builder for chaining.
     */
    public Builder setRank(int value) {

      rank_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>int32 rank = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearRank() {
      bitField0_ = (bitField0_ & ~0x00000001);
      rank_ = 0;
      onChanged();
      return this;
    }

    private long score_ ;
    /**
     * <code>int64 score = 2;</code>
     * @return The score.
     */
    @java.lang.Override
    public long getScore() {
      return score_;
    }
    /**
     * <code>int64 score = 2;</code>
     * @param value The score to set.
     * @return This builder for chaining.
     */
    public Builder setScore(long value) {

      score_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <code>int64 score = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearScore() {
      bitField0_ = (bitField0_ & ~0x00000002);
      score_ = 0L;
      onChanged();
      return this;
    }

    private com.dxx.game.dto.RankUserDto userInfo_;
    private com.google.protobuf.SingleFieldBuilder<
        com.dxx.game.dto.RankUserDto, com.dxx.game.dto.RankUserDto.Builder, com.dxx.game.dto.RankUserDtoOrBuilder> userInfoBuilder_;
    /**
     * <code>.Proto.Rank.RankUserDto userInfo = 3;</code>
     * @return Whether the userInfo field is set.
     */
    public boolean hasUserInfo() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>.Proto.Rank.RankUserDto userInfo = 3;</code>
     * @return The userInfo.
     */
    public com.dxx.game.dto.RankUserDto getUserInfo() {
      if (userInfoBuilder_ == null) {
        return userInfo_ == null ? com.dxx.game.dto.RankUserDto.getDefaultInstance() : userInfo_;
      } else {
        return userInfoBuilder_.getMessage();
      }
    }
    /**
     * <code>.Proto.Rank.RankUserDto userInfo = 3;</code>
     */
    public Builder setUserInfo(com.dxx.game.dto.RankUserDto value) {
      if (userInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        userInfo_ = value;
      } else {
        userInfoBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>.Proto.Rank.RankUserDto userInfo = 3;</code>
     */
    public Builder setUserInfo(
        com.dxx.game.dto.RankUserDto.Builder builderForValue) {
      if (userInfoBuilder_ == null) {
        userInfo_ = builderForValue.build();
      } else {
        userInfoBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <code>.Proto.Rank.RankUserDto userInfo = 3;</code>
     */
    public Builder mergeUserInfo(com.dxx.game.dto.RankUserDto value) {
      if (userInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000004) != 0) &&
          userInfo_ != null &&
          userInfo_ != com.dxx.game.dto.RankUserDto.getDefaultInstance()) {
          getUserInfoBuilder().mergeFrom(value);
        } else {
          userInfo_ = value;
        }
      } else {
        userInfoBuilder_.mergeFrom(value);
      }
      if (userInfo_ != null) {
        bitField0_ |= 0x00000004;
        onChanged();
      }
      return this;
    }
    /**
     * <code>.Proto.Rank.RankUserDto userInfo = 3;</code>
     */
    public Builder clearUserInfo() {
      bitField0_ = (bitField0_ & ~0x00000004);
      userInfo_ = null;
      if (userInfoBuilder_ != null) {
        userInfoBuilder_.dispose();
        userInfoBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>.Proto.Rank.RankUserDto userInfo = 3;</code>
     */
    public com.dxx.game.dto.RankUserDto.Builder getUserInfoBuilder() {
      bitField0_ |= 0x00000004;
      onChanged();
      return getUserInfoFieldBuilder().getBuilder();
    }
    /**
     * <code>.Proto.Rank.RankUserDto userInfo = 3;</code>
     */
    public com.dxx.game.dto.RankUserDtoOrBuilder getUserInfoOrBuilder() {
      if (userInfoBuilder_ != null) {
        return userInfoBuilder_.getMessageOrBuilder();
      } else {
        return userInfo_ == null ?
            com.dxx.game.dto.RankUserDto.getDefaultInstance() : userInfo_;
      }
    }
    /**
     * <code>.Proto.Rank.RankUserDto userInfo = 3;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        com.dxx.game.dto.RankUserDto, com.dxx.game.dto.RankUserDto.Builder, com.dxx.game.dto.RankUserDtoOrBuilder> 
        getUserInfoFieldBuilder() {
      if (userInfoBuilder_ == null) {
        userInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            com.dxx.game.dto.RankUserDto, com.dxx.game.dto.RankUserDto.Builder, com.dxx.game.dto.RankUserDtoOrBuilder>(
                getUserInfo(),
                getParentForChildren(),
                isClean());
        userInfo_ = null;
      }
      return userInfoBuilder_;
    }

    private com.dxx.game.dto.RankGuildDto guildInfo_;
    private com.google.protobuf.SingleFieldBuilder<
        com.dxx.game.dto.RankGuildDto, com.dxx.game.dto.RankGuildDto.Builder, com.dxx.game.dto.RankGuildDtoOrBuilder> guildInfoBuilder_;
    /**
     * <code>.Proto.Rank.RankGuildDto guildInfo = 4;</code>
     * @return Whether the guildInfo field is set.
     */
    public boolean hasGuildInfo() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>.Proto.Rank.RankGuildDto guildInfo = 4;</code>
     * @return The guildInfo.
     */
    public com.dxx.game.dto.RankGuildDto getGuildInfo() {
      if (guildInfoBuilder_ == null) {
        return guildInfo_ == null ? com.dxx.game.dto.RankGuildDto.getDefaultInstance() : guildInfo_;
      } else {
        return guildInfoBuilder_.getMessage();
      }
    }
    /**
     * <code>.Proto.Rank.RankGuildDto guildInfo = 4;</code>
     */
    public Builder setGuildInfo(com.dxx.game.dto.RankGuildDto value) {
      if (guildInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        guildInfo_ = value;
      } else {
        guildInfoBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>.Proto.Rank.RankGuildDto guildInfo = 4;</code>
     */
    public Builder setGuildInfo(
        com.dxx.game.dto.RankGuildDto.Builder builderForValue) {
      if (guildInfoBuilder_ == null) {
        guildInfo_ = builderForValue.build();
      } else {
        guildInfoBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <code>.Proto.Rank.RankGuildDto guildInfo = 4;</code>
     */
    public Builder mergeGuildInfo(com.dxx.game.dto.RankGuildDto value) {
      if (guildInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000008) != 0) &&
          guildInfo_ != null &&
          guildInfo_ != com.dxx.game.dto.RankGuildDto.getDefaultInstance()) {
          getGuildInfoBuilder().mergeFrom(value);
        } else {
          guildInfo_ = value;
        }
      } else {
        guildInfoBuilder_.mergeFrom(value);
      }
      if (guildInfo_ != null) {
        bitField0_ |= 0x00000008;
        onChanged();
      }
      return this;
    }
    /**
     * <code>.Proto.Rank.RankGuildDto guildInfo = 4;</code>
     */
    public Builder clearGuildInfo() {
      bitField0_ = (bitField0_ & ~0x00000008);
      guildInfo_ = null;
      if (guildInfoBuilder_ != null) {
        guildInfoBuilder_.dispose();
        guildInfoBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>.Proto.Rank.RankGuildDto guildInfo = 4;</code>
     */
    public com.dxx.game.dto.RankGuildDto.Builder getGuildInfoBuilder() {
      bitField0_ |= 0x00000008;
      onChanged();
      return getGuildInfoFieldBuilder().getBuilder();
    }
    /**
     * <code>.Proto.Rank.RankGuildDto guildInfo = 4;</code>
     */
    public com.dxx.game.dto.RankGuildDtoOrBuilder getGuildInfoOrBuilder() {
      if (guildInfoBuilder_ != null) {
        return guildInfoBuilder_.getMessageOrBuilder();
      } else {
        return guildInfo_ == null ?
            com.dxx.game.dto.RankGuildDto.getDefaultInstance() : guildInfo_;
      }
    }
    /**
     * <code>.Proto.Rank.RankGuildDto guildInfo = 4;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        com.dxx.game.dto.RankGuildDto, com.dxx.game.dto.RankGuildDto.Builder, com.dxx.game.dto.RankGuildDtoOrBuilder> 
        getGuildInfoFieldBuilder() {
      if (guildInfoBuilder_ == null) {
        guildInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            com.dxx.game.dto.RankGuildDto, com.dxx.game.dto.RankGuildDto.Builder, com.dxx.game.dto.RankGuildDtoOrBuilder>(
                getGuildInfo(),
                getParentForChildren(),
                isClean());
        guildInfo_ = null;
      }
      return guildInfoBuilder_;
    }

    // @@protoc_insertion_point(builder_scope:Proto.Rank.RankItemDto)
  }

  // @@protoc_insertion_point(class_scope:Proto.Rank.RankItemDto)
  private static final com.dxx.game.dto.RankItemDto DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.dxx.game.dto.RankItemDto();
  }

  public static com.dxx.game.dto.RankItemDto getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<RankItemDto>
      PARSER = new com.google.protobuf.AbstractParser<RankItemDto>() {
    @java.lang.Override
    public RankItemDto parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<RankItemDto> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<RankItemDto> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.dxx.game.dto.RankItemDto getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

