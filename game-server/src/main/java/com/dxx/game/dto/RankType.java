// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: rank.proto
// Protobuf Java Version: 4.29.0

package com.dxx.game.dto;

/**
 * Protobuf enum {@code Proto.Rank.RankType}
 */
public enum RankType
    implements com.google.protobuf.ProtocolMessageEnum {
  /**
   * <pre>
   * proto3必须让用0，但是我们代码里0不用，这里占个位
   * </pre>
   *
   * <code>ZERO_NOT_USE = 0;</code>
   */
  ZERO_NOT_USE(0),
  /**
   * <pre>
   * boss副本
   * </pre>
   *
   * <code>BOSS_DUNGEON = 1;</code>
   */
  BOSS_DUNGEON(1),
  /**
   * <pre>
   * 条件副本
   * </pre>
   *
   * <code>CONDITION_CHAPTER = 2;</code>
   */
  CONDITION_CHAPTER(2),
  /**
   * <pre>
   * 爬塔历史最大层级排行榜
   * </pre>
   *
   * <code>TOWER_MAX_LEVEL = 3;</code>
   */
  TOWER_MAX_LEVEL(3),
  /**
   * <pre>
   * 爬塔周最大层级排行榜
   * </pre>
   *
   * <code>TOWER_WEEK_MAX_LEVEL = 4;</code>
   */
  TOWER_WEEK_MAX_LEVEL(4),
  /**
   * <pre>
   * 公会战力排行
   * </pre>
   *
   * <code>GUILD_POWER = 5;</code>
   */
  GUILD_POWER(5),
  /**
   * <code>GUILD_BOSS = 6;</code>
   */
  GUILD_BOSS(6),
  /**
   * <pre>
   * 公会boss伤害个人排名
   * </pre>
   *
   * <code>GUILD_BOSS_USER = 7;</code>
   */
  GUILD_BOSS_USER(7),
  UNRECOGNIZED(-1),
  ;

  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 29,
      /* patch= */ 0,
      /* suffix= */ "",
      RankType.class.getName());
  }
  /**
   * <pre>
   * proto3必须让用0，但是我们代码里0不用，这里占个位
   * </pre>
   *
   * <code>ZERO_NOT_USE = 0;</code>
   */
  public static final int ZERO_NOT_USE_VALUE = 0;
  /**
   * <pre>
   * boss副本
   * </pre>
   *
   * <code>BOSS_DUNGEON = 1;</code>
   */
  public static final int BOSS_DUNGEON_VALUE = 1;
  /**
   * <pre>
   * 条件副本
   * </pre>
   *
   * <code>CONDITION_CHAPTER = 2;</code>
   */
  public static final int CONDITION_CHAPTER_VALUE = 2;
  /**
   * <pre>
   * 爬塔历史最大层级排行榜
   * </pre>
   *
   * <code>TOWER_MAX_LEVEL = 3;</code>
   */
  public static final int TOWER_MAX_LEVEL_VALUE = 3;
  /**
   * <pre>
   * 爬塔周最大层级排行榜
   * </pre>
   *
   * <code>TOWER_WEEK_MAX_LEVEL = 4;</code>
   */
  public static final int TOWER_WEEK_MAX_LEVEL_VALUE = 4;
  /**
   * <pre>
   * 公会战力排行
   * </pre>
   *
   * <code>GUILD_POWER = 5;</code>
   */
  public static final int GUILD_POWER_VALUE = 5;
  /**
   * <code>GUILD_BOSS = 6;</code>
   */
  public static final int GUILD_BOSS_VALUE = 6;
  /**
   * <pre>
   * 公会boss伤害个人排名
   * </pre>
   *
   * <code>GUILD_BOSS_USER = 7;</code>
   */
  public static final int GUILD_BOSS_USER_VALUE = 7;


  public final int getNumber() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalArgumentException(
          "Can't get the number of an unknown enum value.");
    }
    return value;
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   * @deprecated Use {@link #forNumber(int)} instead.
   */
  @java.lang.Deprecated
  public static RankType valueOf(int value) {
    return forNumber(value);
  }

  /**
   * @param value The numeric wire value of the corresponding enum entry.
   * @return The enum associated with the given numeric wire value.
   */
  public static RankType forNumber(int value) {
    switch (value) {
      case 0: return ZERO_NOT_USE;
      case 1: return BOSS_DUNGEON;
      case 2: return CONDITION_CHAPTER;
      case 3: return TOWER_MAX_LEVEL;
      case 4: return TOWER_WEEK_MAX_LEVEL;
      case 5: return GUILD_POWER;
      case 6: return GUILD_BOSS;
      case 7: return GUILD_BOSS_USER;
      default: return null;
    }
  }

  public static com.google.protobuf.Internal.EnumLiteMap<RankType>
      internalGetValueMap() {
    return internalValueMap;
  }
  private static final com.google.protobuf.Internal.EnumLiteMap<
      RankType> internalValueMap =
        new com.google.protobuf.Internal.EnumLiteMap<RankType>() {
          public RankType findValueByNumber(int number) {
            return RankType.forNumber(number);
          }
        };

  public final com.google.protobuf.Descriptors.EnumValueDescriptor
      getValueDescriptor() {
    if (this == UNRECOGNIZED) {
      throw new java.lang.IllegalStateException(
          "Can't get the descriptor of an unrecognized enum value.");
    }
    return getDescriptor().getValues().get(ordinal());
  }
  public final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptorForType() {
    return getDescriptor();
  }
  public static final com.google.protobuf.Descriptors.EnumDescriptor
      getDescriptor() {
    return com.dxx.game.dto.RankProto.getDescriptor().getEnumTypes().get(0);
  }

  private static final RankType[] VALUES = values();

  public static RankType valueOf(
      com.google.protobuf.Descriptors.EnumValueDescriptor desc) {
    if (desc.getType() != getDescriptor()) {
      throw new java.lang.IllegalArgumentException(
        "EnumValueDescriptor is not for this type.");
    }
    if (desc.getIndex() == -1) {
      return UNRECOGNIZED;
    }
    return VALUES[desc.getIndex()];
  }

  private final int value;

  private RankType(int value) {
    this.value = value;
  }

  // @@protoc_insertion_point(enum_scope:Proto.Rank.RankType)
}

