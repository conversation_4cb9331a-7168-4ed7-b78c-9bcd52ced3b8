// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: rank.proto
// Protobuf Java Version: 4.29.0

package com.dxx.game.dto;

/**
 * Protobuf type {@code Proto.Rank.RankUserDto}
 */
public final class RankUserDto extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:Proto.Rank.RankUserDto)
    RankUserDtoOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 29,
      /* patch= */ 0,
      /* suffix= */ "",
      RankUserDto.class.getName());
  }
  // Use RankUserDto.newBuilder() to construct.
  private RankUserDto(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private RankUserDto() {
    nickName_ = "";
    guildName_ = "";
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.dxx.game.dto.RankProto.internal_static_Proto_Rank_RankUserDto_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.dxx.game.dto.RankProto.internal_static_Proto_Rank_RankUserDto_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.dxx.game.dto.RankUserDto.class, com.dxx.game.dto.RankUserDto.Builder.class);
  }

  public static final int USERID_FIELD_NUMBER = 1;
  private long userId_ = 0L;
  /**
   * <code>int64 userId = 1;</code>
   * @return The userId.
   */
  @java.lang.Override
  public long getUserId() {
    return userId_;
  }

  public static final int NICKNAME_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private volatile java.lang.Object nickName_ = "";
  /**
   * <pre>
   * 昵称
   * </pre>
   *
   * <code>string nickName = 2;</code>
   * @return The nickName.
   */
  @java.lang.Override
  public java.lang.String getNickName() {
    java.lang.Object ref = nickName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      nickName_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 昵称
   * </pre>
   *
   * <code>string nickName = 2;</code>
   * @return The bytes for nickName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getNickNameBytes() {
    java.lang.Object ref = nickName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      nickName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  public static final int AVATAR_FIELD_NUMBER = 3;
  private int avatar_ = 0;
  /**
   * <pre>
   * 头像
   * </pre>
   *
   * <code>int32 avatar = 3;</code>
   * @return The avatar.
   */
  @java.lang.Override
  public int getAvatar() {
    return avatar_;
  }

  public static final int AVATARFRAME_FIELD_NUMBER = 4;
  private int avatarFrame_ = 0;
  /**
   * <pre>
   * 头像框
   * </pre>
   *
   * <code>int32 avatarFrame = 4;</code>
   * @return The avatarFrame.
   */
  @java.lang.Override
  public int getAvatarFrame() {
    return avatarFrame_;
  }

  public static final int LEVEL_FIELD_NUMBER = 5;
  private int level_ = 0;
  /**
   * <pre>
   * 等级
   * </pre>
   *
   * <code>int32 level = 5;</code>
   * @return The level.
   */
  @java.lang.Override
  public int getLevel() {
    return level_;
  }

  public static final int POWER_FIELD_NUMBER = 6;
  private long power_ = 0L;
  /**
   * <pre>
   * 战力
   * </pre>
   *
   * <code>int64 power = 6;</code>
   * @return The power.
   */
  @java.lang.Override
  public long getPower() {
    return power_;
  }

  public static final int SERVERID_FIELD_NUMBER = 7;
  private int serverId_ = 0;
  /**
   * <pre>
   * 服务器id
   * </pre>
   *
   * <code>int32 serverId = 7;</code>
   * @return The serverId.
   */
  @java.lang.Override
  public int getServerId() {
    return serverId_;
  }

  public static final int GUILDID_FIELD_NUMBER = 8;
  private long guildId_ = 0L;
  /**
   * <pre>
   * 公会id
   * </pre>
   *
   * <code>int64 guildId = 8;</code>
   * @return The guildId.
   */
  @java.lang.Override
  public long getGuildId() {
    return guildId_;
  }

  public static final int GUILDNAME_FIELD_NUMBER = 9;
  @SuppressWarnings("serial")
  private volatile java.lang.Object guildName_ = "";
  /**
   * <pre>
   * 公会名字
   * </pre>
   *
   * <code>string guildName = 9;</code>
   * @return The guildName.
   */
  @java.lang.Override
  public java.lang.String getGuildName() {
    java.lang.Object ref = guildName_;
    if (ref instanceof java.lang.String) {
      return (java.lang.String) ref;
    } else {
      com.google.protobuf.ByteString bs = 
          (com.google.protobuf.ByteString) ref;
      java.lang.String s = bs.toStringUtf8();
      guildName_ = s;
      return s;
    }
  }
  /**
   * <pre>
   * 公会名字
   * </pre>
   *
   * <code>string guildName = 9;</code>
   * @return The bytes for guildName.
   */
  @java.lang.Override
  public com.google.protobuf.ByteString
      getGuildNameBytes() {
    java.lang.Object ref = guildName_;
    if (ref instanceof java.lang.String) {
      com.google.protobuf.ByteString b = 
          com.google.protobuf.ByteString.copyFromUtf8(
              (java.lang.String) ref);
      guildName_ = b;
      return b;
    } else {
      return (com.google.protobuf.ByteString) ref;
    }
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (userId_ != 0L) {
      output.writeInt64(1, userId_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(nickName_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 2, nickName_);
    }
    if (avatar_ != 0) {
      output.writeInt32(3, avatar_);
    }
    if (avatarFrame_ != 0) {
      output.writeInt32(4, avatarFrame_);
    }
    if (level_ != 0) {
      output.writeInt32(5, level_);
    }
    if (power_ != 0L) {
      output.writeInt64(6, power_);
    }
    if (serverId_ != 0) {
      output.writeInt32(7, serverId_);
    }
    if (guildId_ != 0L) {
      output.writeInt64(8, guildId_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(guildName_)) {
      com.google.protobuf.GeneratedMessage.writeString(output, 9, guildName_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (userId_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(1, userId_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(nickName_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(2, nickName_);
    }
    if (avatar_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, avatar_);
    }
    if (avatarFrame_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, avatarFrame_);
    }
    if (level_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(5, level_);
    }
    if (power_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(6, power_);
    }
    if (serverId_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(7, serverId_);
    }
    if (guildId_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(8, guildId_);
    }
    if (!com.google.protobuf.GeneratedMessage.isStringEmpty(guildName_)) {
      size += com.google.protobuf.GeneratedMessage.computeStringSize(9, guildName_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.dxx.game.dto.RankUserDto)) {
      return super.equals(obj);
    }
    com.dxx.game.dto.RankUserDto other = (com.dxx.game.dto.RankUserDto) obj;

    if (getUserId()
        != other.getUserId()) return false;
    if (!getNickName()
        .equals(other.getNickName())) return false;
    if (getAvatar()
        != other.getAvatar()) return false;
    if (getAvatarFrame()
        != other.getAvatarFrame()) return false;
    if (getLevel()
        != other.getLevel()) return false;
    if (getPower()
        != other.getPower()) return false;
    if (getServerId()
        != other.getServerId()) return false;
    if (getGuildId()
        != other.getGuildId()) return false;
    if (!getGuildName()
        .equals(other.getGuildName())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + USERID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getUserId());
    hash = (37 * hash) + NICKNAME_FIELD_NUMBER;
    hash = (53 * hash) + getNickName().hashCode();
    hash = (37 * hash) + AVATAR_FIELD_NUMBER;
    hash = (53 * hash) + getAvatar();
    hash = (37 * hash) + AVATARFRAME_FIELD_NUMBER;
    hash = (53 * hash) + getAvatarFrame();
    hash = (37 * hash) + LEVEL_FIELD_NUMBER;
    hash = (53 * hash) + getLevel();
    hash = (37 * hash) + POWER_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getPower());
    hash = (37 * hash) + SERVERID_FIELD_NUMBER;
    hash = (53 * hash) + getServerId();
    hash = (37 * hash) + GUILDID_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getGuildId());
    hash = (37 * hash) + GUILDNAME_FIELD_NUMBER;
    hash = (53 * hash) + getGuildName().hashCode();
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.dxx.game.dto.RankUserDto parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.dxx.game.dto.RankUserDto parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.dxx.game.dto.RankUserDto parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.dxx.game.dto.RankUserDto parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.dxx.game.dto.RankUserDto parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.dxx.game.dto.RankUserDto parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.dxx.game.dto.RankUserDto parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static com.dxx.game.dto.RankUserDto parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.dxx.game.dto.RankUserDto parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.dxx.game.dto.RankUserDto parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.dxx.game.dto.RankUserDto parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static com.dxx.game.dto.RankUserDto parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.dxx.game.dto.RankUserDto prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code Proto.Rank.RankUserDto}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:Proto.Rank.RankUserDto)
      com.dxx.game.dto.RankUserDtoOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.RankProto.internal_static_Proto_Rank_RankUserDto_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.RankProto.internal_static_Proto_Rank_RankUserDto_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.RankUserDto.class, com.dxx.game.dto.RankUserDto.Builder.class);
    }

    // Construct using com.dxx.game.dto.RankUserDto.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      userId_ = 0L;
      nickName_ = "";
      avatar_ = 0;
      avatarFrame_ = 0;
      level_ = 0;
      power_ = 0L;
      serverId_ = 0;
      guildId_ = 0L;
      guildName_ = "";
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.dxx.game.dto.RankProto.internal_static_Proto_Rank_RankUserDto_descriptor;
    }

    @java.lang.Override
    public com.dxx.game.dto.RankUserDto getDefaultInstanceForType() {
      return com.dxx.game.dto.RankUserDto.getDefaultInstance();
    }

    @java.lang.Override
    public com.dxx.game.dto.RankUserDto build() {
      com.dxx.game.dto.RankUserDto result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.dxx.game.dto.RankUserDto buildPartial() {
      com.dxx.game.dto.RankUserDto result = new com.dxx.game.dto.RankUserDto(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(com.dxx.game.dto.RankUserDto result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.userId_ = userId_;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.nickName_ = nickName_;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.avatar_ = avatar_;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.avatarFrame_ = avatarFrame_;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.level_ = level_;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.power_ = power_;
      }
      if (((from_bitField0_ & 0x00000040) != 0)) {
        result.serverId_ = serverId_;
      }
      if (((from_bitField0_ & 0x00000080) != 0)) {
        result.guildId_ = guildId_;
      }
      if (((from_bitField0_ & 0x00000100) != 0)) {
        result.guildName_ = guildName_;
      }
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.dxx.game.dto.RankUserDto) {
        return mergeFrom((com.dxx.game.dto.RankUserDto)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.dxx.game.dto.RankUserDto other) {
      if (other == com.dxx.game.dto.RankUserDto.getDefaultInstance()) return this;
      if (other.getUserId() != 0L) {
        setUserId(other.getUserId());
      }
      if (!other.getNickName().isEmpty()) {
        nickName_ = other.nickName_;
        bitField0_ |= 0x00000002;
        onChanged();
      }
      if (other.getAvatar() != 0) {
        setAvatar(other.getAvatar());
      }
      if (other.getAvatarFrame() != 0) {
        setAvatarFrame(other.getAvatarFrame());
      }
      if (other.getLevel() != 0) {
        setLevel(other.getLevel());
      }
      if (other.getPower() != 0L) {
        setPower(other.getPower());
      }
      if (other.getServerId() != 0) {
        setServerId(other.getServerId());
      }
      if (other.getGuildId() != 0L) {
        setGuildId(other.getGuildId());
      }
      if (!other.getGuildName().isEmpty()) {
        guildName_ = other.guildName_;
        bitField0_ |= 0x00000100;
        onChanged();
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              userId_ = input.readInt64();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 18: {
              nickName_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000002;
              break;
            } // case 18
            case 24: {
              avatar_ = input.readInt32();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 32: {
              avatarFrame_ = input.readInt32();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            case 40: {
              level_ = input.readInt32();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            case 48: {
              power_ = input.readInt64();
              bitField0_ |= 0x00000020;
              break;
            } // case 48
            case 56: {
              serverId_ = input.readInt32();
              bitField0_ |= 0x00000040;
              break;
            } // case 56
            case 64: {
              guildId_ = input.readInt64();
              bitField0_ |= 0x00000080;
              break;
            } // case 64
            case 74: {
              guildName_ = input.readStringRequireUtf8();
              bitField0_ |= 0x00000100;
              break;
            } // case 74
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private long userId_ ;
    /**
     * <code>int64 userId = 1;</code>
     * @return The userId.
     */
    @java.lang.Override
    public long getUserId() {
      return userId_;
    }
    /**
     * <code>int64 userId = 1;</code>
     * @param value The userId to set.
     * @return This builder for chaining.
     */
    public Builder setUserId(long value) {

      userId_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>int64 userId = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearUserId() {
      bitField0_ = (bitField0_ & ~0x00000001);
      userId_ = 0L;
      onChanged();
      return this;
    }

    private java.lang.Object nickName_ = "";
    /**
     * <pre>
     * 昵称
     * </pre>
     *
     * <code>string nickName = 2;</code>
     * @return The nickName.
     */
    public java.lang.String getNickName() {
      java.lang.Object ref = nickName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        nickName_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 昵称
     * </pre>
     *
     * <code>string nickName = 2;</code>
     * @return The bytes for nickName.
     */
    public com.google.protobuf.ByteString
        getNickNameBytes() {
      java.lang.Object ref = nickName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        nickName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 昵称
     * </pre>
     *
     * <code>string nickName = 2;</code>
     * @param value The nickName to set.
     * @return This builder for chaining.
     */
    public Builder setNickName(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      nickName_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 昵称
     * </pre>
     *
     * <code>string nickName = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearNickName() {
      nickName_ = getDefaultInstance().getNickName();
      bitField0_ = (bitField0_ & ~0x00000002);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 昵称
     * </pre>
     *
     * <code>string nickName = 2;</code>
     * @param value The bytes for nickName to set.
     * @return This builder for chaining.
     */
    public Builder setNickNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      nickName_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }

    private int avatar_ ;
    /**
     * <pre>
     * 头像
     * </pre>
     *
     * <code>int32 avatar = 3;</code>
     * @return The avatar.
     */
    @java.lang.Override
    public int getAvatar() {
      return avatar_;
    }
    /**
     * <pre>
     * 头像
     * </pre>
     *
     * <code>int32 avatar = 3;</code>
     * @param value The avatar to set.
     * @return This builder for chaining.
     */
    public Builder setAvatar(int value) {

      avatar_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 头像
     * </pre>
     *
     * <code>int32 avatar = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearAvatar() {
      bitField0_ = (bitField0_ & ~0x00000004);
      avatar_ = 0;
      onChanged();
      return this;
    }

    private int avatarFrame_ ;
    /**
     * <pre>
     * 头像框
     * </pre>
     *
     * <code>int32 avatarFrame = 4;</code>
     * @return The avatarFrame.
     */
    @java.lang.Override
    public int getAvatarFrame() {
      return avatarFrame_;
    }
    /**
     * <pre>
     * 头像框
     * </pre>
     *
     * <code>int32 avatarFrame = 4;</code>
     * @param value The avatarFrame to set.
     * @return This builder for chaining.
     */
    public Builder setAvatarFrame(int value) {

      avatarFrame_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 头像框
     * </pre>
     *
     * <code>int32 avatarFrame = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearAvatarFrame() {
      bitField0_ = (bitField0_ & ~0x00000008);
      avatarFrame_ = 0;
      onChanged();
      return this;
    }

    private int level_ ;
    /**
     * <pre>
     * 等级
     * </pre>
     *
     * <code>int32 level = 5;</code>
     * @return The level.
     */
    @java.lang.Override
    public int getLevel() {
      return level_;
    }
    /**
     * <pre>
     * 等级
     * </pre>
     *
     * <code>int32 level = 5;</code>
     * @param value The level to set.
     * @return This builder for chaining.
     */
    public Builder setLevel(int value) {

      level_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 等级
     * </pre>
     *
     * <code>int32 level = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearLevel() {
      bitField0_ = (bitField0_ & ~0x00000010);
      level_ = 0;
      onChanged();
      return this;
    }

    private long power_ ;
    /**
     * <pre>
     * 战力
     * </pre>
     *
     * <code>int64 power = 6;</code>
     * @return The power.
     */
    @java.lang.Override
    public long getPower() {
      return power_;
    }
    /**
     * <pre>
     * 战力
     * </pre>
     *
     * <code>int64 power = 6;</code>
     * @param value The power to set.
     * @return This builder for chaining.
     */
    public Builder setPower(long value) {

      power_ = value;
      bitField0_ |= 0x00000020;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 战力
     * </pre>
     *
     * <code>int64 power = 6;</code>
     * @return This builder for chaining.
     */
    public Builder clearPower() {
      bitField0_ = (bitField0_ & ~0x00000020);
      power_ = 0L;
      onChanged();
      return this;
    }

    private int serverId_ ;
    /**
     * <pre>
     * 服务器id
     * </pre>
     *
     * <code>int32 serverId = 7;</code>
     * @return The serverId.
     */
    @java.lang.Override
    public int getServerId() {
      return serverId_;
    }
    /**
     * <pre>
     * 服务器id
     * </pre>
     *
     * <code>int32 serverId = 7;</code>
     * @param value The serverId to set.
     * @return This builder for chaining.
     */
    public Builder setServerId(int value) {

      serverId_ = value;
      bitField0_ |= 0x00000040;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 服务器id
     * </pre>
     *
     * <code>int32 serverId = 7;</code>
     * @return This builder for chaining.
     */
    public Builder clearServerId() {
      bitField0_ = (bitField0_ & ~0x00000040);
      serverId_ = 0;
      onChanged();
      return this;
    }

    private long guildId_ ;
    /**
     * <pre>
     * 公会id
     * </pre>
     *
     * <code>int64 guildId = 8;</code>
     * @return The guildId.
     */
    @java.lang.Override
    public long getGuildId() {
      return guildId_;
    }
    /**
     * <pre>
     * 公会id
     * </pre>
     *
     * <code>int64 guildId = 8;</code>
     * @param value The guildId to set.
     * @return This builder for chaining.
     */
    public Builder setGuildId(long value) {

      guildId_ = value;
      bitField0_ |= 0x00000080;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 公会id
     * </pre>
     *
     * <code>int64 guildId = 8;</code>
     * @return This builder for chaining.
     */
    public Builder clearGuildId() {
      bitField0_ = (bitField0_ & ~0x00000080);
      guildId_ = 0L;
      onChanged();
      return this;
    }

    private java.lang.Object guildName_ = "";
    /**
     * <pre>
     * 公会名字
     * </pre>
     *
     * <code>string guildName = 9;</code>
     * @return The guildName.
     */
    public java.lang.String getGuildName() {
      java.lang.Object ref = guildName_;
      if (!(ref instanceof java.lang.String)) {
        com.google.protobuf.ByteString bs =
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        guildName_ = s;
        return s;
      } else {
        return (java.lang.String) ref;
      }
    }
    /**
     * <pre>
     * 公会名字
     * </pre>
     *
     * <code>string guildName = 9;</code>
     * @return The bytes for guildName.
     */
    public com.google.protobuf.ByteString
        getGuildNameBytes() {
      java.lang.Object ref = guildName_;
      if (ref instanceof String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        guildName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }
    /**
     * <pre>
     * 公会名字
     * </pre>
     *
     * <code>string guildName = 9;</code>
     * @param value The guildName to set.
     * @return This builder for chaining.
     */
    public Builder setGuildName(
        java.lang.String value) {
      if (value == null) { throw new NullPointerException(); }
      guildName_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 公会名字
     * </pre>
     *
     * <code>string guildName = 9;</code>
     * @return This builder for chaining.
     */
    public Builder clearGuildName() {
      guildName_ = getDefaultInstance().getGuildName();
      bitField0_ = (bitField0_ & ~0x00000100);
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 公会名字
     * </pre>
     *
     * <code>string guildName = 9;</code>
     * @param value The bytes for guildName to set.
     * @return This builder for chaining.
     */
    public Builder setGuildNameBytes(
        com.google.protobuf.ByteString value) {
      if (value == null) { throw new NullPointerException(); }
      checkByteStringIsUtf8(value);
      guildName_ = value;
      bitField0_ |= 0x00000100;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:Proto.Rank.RankUserDto)
  }

  // @@protoc_insertion_point(class_scope:Proto.Rank.RankUserDto)
  private static final com.dxx.game.dto.RankUserDto DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.dxx.game.dto.RankUserDto();
  }

  public static com.dxx.game.dto.RankUserDto getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<RankUserDto>
      PARSER = new com.google.protobuf.AbstractParser<RankUserDto>() {
    @java.lang.Override
    public RankUserDto parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<RankUserDto> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<RankUserDto> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.dxx.game.dto.RankUserDto getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

