// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: recharge.proto
// Protobuf Java Version: 4.29.0

package com.dxx.game.dto;

public final class RechargeProto {
  private RechargeProto() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 29,
      /* patch= */ 0,
      /* suffix= */ "",
      RechargeProto.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Recharge_RechargePreOrderRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Recharge_RechargePreOrderRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Recharge_RechargePreOrderRequest_ExtraEntry_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Recharge_RechargePreOrderRequest_ExtraEntry_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Recharge_RechargePreOrderResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Recharge_RechargePreOrderResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Recharge_RechargePreOrderResponse_ExtraEntry_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Recharge_RechargePreOrderResponse_ExtraEntry_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Recharge_RechargeVerifyReceiptRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Recharge_RechargeVerifyReceiptRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Recharge_RechargeVerifyReceiptResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Recharge_RechargeVerifyReceiptResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Recharge_RechargeQueryResultRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Recharge_RechargeQueryResultRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Recharge_RechargeQueryResultResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Recharge_RechargeQueryResultResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Recharge_RechargeBuyFreeRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Recharge_RechargeBuyFreeRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Recharge_RechargeBuyFreeResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Recharge_RechargeBuyFreeResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Recharge_RechargeTestDeliveryRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Recharge_RechargeTestDeliveryRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Recharge_RechargeTestDeliveryResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Recharge_RechargeTestDeliveryResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Recharge_RechargeCancelPreOrderRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Recharge_RechargeCancelPreOrderRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Recharge_RechargeCancelPreOrderResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Recharge_RechargeCancelPreOrderResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Recharge_RechargeExchangeVerifyRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Recharge_RechargeExchangeVerifyRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Recharge_RechargeExchangeVerifyResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Recharge_RechargeExchangeVerifyResponse_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\016recharge.proto\022\016Proto.Recharge\032\014common" +
      ".proto\"\373\001\n\027RechargePreOrderRequest\0220\n\014co" +
      "mmonParams\030\001 \001(\0132\032.Proto.Common.CommonPa" +
      "rams\022\022\n\npurchaseId\030\002 \001(\005\022\026\n\016purchasePara" +
      "ms\030\003 \003(\005\022A\n\005extra\030\004 \003(\01322.Proto.Recharge" +
      ".RechargePreOrderRequest.ExtraEntry\022\021\n\tc" +
      "hannelId\030\005 \001(\005\032,\n\nExtraEntry\022\013\n\003key\030\001 \001(" +
      "\t\022\r\n\005value\030\002 \001(\t:\0028\001\"\256\001\n\030RechargePreOrde" +
      "rResponse\022\014\n\004code\030\001 \001(\005\022\022\n\npreOrderId\030\002 " +
      "\001(\t\022B\n\005extra\030\003 \003(\01323.Proto.Recharge.Rech" +
      "argePreOrderResponse.ExtraEntry\032,\n\nExtra" +
      "Entry\022\013\n\003key\030\001 \001(\t\022\r\n\005value\030\002 \001(\t:\0028\001\"\237\001" +
      "\n\034RechargeVerifyReceiptRequest\0220\n\014common" +
      "Params\030\001 \001(\0132\032.Proto.Common.CommonParams" +
      "\022\022\n\npreOrderId\030\002 \001(\t\022\021\n\tchannelId\030\003 \001(\005\022" +
      "\023\n\013receiptData\030\004 \001(\t\022\021\n\tproductId\030\005 \001(\t\"" +
      "\246\001\n\035RechargeVerifyReceiptResponse\022\014\n\004cod" +
      "e\030\001 \001(\005\022,\n\ncommonData\030\002 \001(\0132\030.Proto.Comm" +
      "on.CommonData\022\017\n\007transId\030\003 \001(\t\022%\n\007iapInf" +
      "o\030\004 \001(\0132\024.Proto.Common.IAPDto\022\021\n\tisSandb" +
      "ox\030\005 \001(\010\"b\n\032RechargeQueryResultRequest\0220" +
      "\n\014commonParams\030\001 \001(\0132\032.Proto.Common.Comm" +
      "onParams\022\022\n\npreOrderId\030\002 \001(\t\"\223\001\n\033Recharg" +
      "eQueryResultResponse\022\014\n\004code\030\001 \001(\005\022,\n\nco" +
      "mmonData\030\002 \001(\0132\030.Proto.Common.CommonData" +
      "\022%\n\007iapInfo\030\003 \001(\0132\024.Proto.Common.IAPDto\022" +
      "\021\n\tisSandbox\030\004 \001(\010\"v\n\026RechargeBuyFreeReq" +
      "uest\0220\n\014commonParams\030\001 \001(\0132\032.Proto.Commo" +
      "n.CommonParams\022\022\n\npurchaseId\030\002 \001(\005\022\026\n\016pu" +
      "rchaseParams\030\003 \003(\005\"|\n\027RechargeBuyFreeRes" +
      "ponse\022\014\n\004code\030\001 \001(\005\022,\n\ncommonData\030\002 \001(\0132" +
      "\030.Proto.Common.CommonData\022%\n\007iapInfo\030\003 \001" +
      "(\0132\024.Proto.Common.IAPDto\"c\n\033RechargeTest" +
      "DeliveryRequest\0220\n\014commonParams\030\001 \001(\0132\032." +
      "Proto.Common.CommonParams\022\022\n\npreOrderId\030" +
      "\002 \001(\t\"\201\001\n\034RechargeTestDeliveryResponse\022\014" +
      "\n\004code\030\001 \001(\005\022,\n\ncommonData\030\002 \001(\0132\030.Proto" +
      ".Common.CommonData\022%\n\007iapInfo\030\003 \001(\0132\024.Pr" +
      "oto.Common.IAPDto\"e\n\035RechargeCancelPreOr" +
      "derRequest\0220\n\014commonParams\030\001 \001(\0132\032.Proto" +
      ".Common.CommonParams\022\022\n\npreOrderId\030\002 \001(\t" +
      "\"B\n\036RechargeCancelPreOrderResponse\022\014\n\004co" +
      "de\030\001 \001(\005\022\022\n\npreOrderId\030\002 \001(\t\"\214\001\n\035Recharg" +
      "eExchangeVerifyRequest\0220\n\014commonParams\030\001" +
      " \001(\0132\032.Proto.Common.CommonParams\022\021\n\tchan" +
      "nelId\030\002 \001(\005\022\023\n\013receiptData\030\003 \001(\t\022\021\n\tprod" +
      "uctId\030\004 \001(\t\".\n\036RechargeExchangeVerifyRes" +
      "ponse\022\014\n\004code\030\001 \001(\005B#\n\020com.dxx.game.dtoB" +
      "\rRechargeProtoP\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.dxx.game.dto.CommonProto.getDescriptor(),
        });
    internal_static_Proto_Recharge_RechargePreOrderRequest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_Proto_Recharge_RechargePreOrderRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Recharge_RechargePreOrderRequest_descriptor,
        new java.lang.String[] { "CommonParams", "PurchaseId", "PurchaseParams", "Extra", "ChannelId", });
    internal_static_Proto_Recharge_RechargePreOrderRequest_ExtraEntry_descriptor =
      internal_static_Proto_Recharge_RechargePreOrderRequest_descriptor.getNestedTypes().get(0);
    internal_static_Proto_Recharge_RechargePreOrderRequest_ExtraEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Recharge_RechargePreOrderRequest_ExtraEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_Proto_Recharge_RechargePreOrderResponse_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_Proto_Recharge_RechargePreOrderResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Recharge_RechargePreOrderResponse_descriptor,
        new java.lang.String[] { "Code", "PreOrderId", "Extra", });
    internal_static_Proto_Recharge_RechargePreOrderResponse_ExtraEntry_descriptor =
      internal_static_Proto_Recharge_RechargePreOrderResponse_descriptor.getNestedTypes().get(0);
    internal_static_Proto_Recharge_RechargePreOrderResponse_ExtraEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Recharge_RechargePreOrderResponse_ExtraEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_Proto_Recharge_RechargeVerifyReceiptRequest_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_Proto_Recharge_RechargeVerifyReceiptRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Recharge_RechargeVerifyReceiptRequest_descriptor,
        new java.lang.String[] { "CommonParams", "PreOrderId", "ChannelId", "ReceiptData", "ProductId", });
    internal_static_Proto_Recharge_RechargeVerifyReceiptResponse_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_Proto_Recharge_RechargeVerifyReceiptResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Recharge_RechargeVerifyReceiptResponse_descriptor,
        new java.lang.String[] { "Code", "CommonData", "TransId", "IapInfo", "IsSandbox", });
    internal_static_Proto_Recharge_RechargeQueryResultRequest_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_Proto_Recharge_RechargeQueryResultRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Recharge_RechargeQueryResultRequest_descriptor,
        new java.lang.String[] { "CommonParams", "PreOrderId", });
    internal_static_Proto_Recharge_RechargeQueryResultResponse_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_Proto_Recharge_RechargeQueryResultResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Recharge_RechargeQueryResultResponse_descriptor,
        new java.lang.String[] { "Code", "CommonData", "IapInfo", "IsSandbox", });
    internal_static_Proto_Recharge_RechargeBuyFreeRequest_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_Proto_Recharge_RechargeBuyFreeRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Recharge_RechargeBuyFreeRequest_descriptor,
        new java.lang.String[] { "CommonParams", "PurchaseId", "PurchaseParams", });
    internal_static_Proto_Recharge_RechargeBuyFreeResponse_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_Proto_Recharge_RechargeBuyFreeResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Recharge_RechargeBuyFreeResponse_descriptor,
        new java.lang.String[] { "Code", "CommonData", "IapInfo", });
    internal_static_Proto_Recharge_RechargeTestDeliveryRequest_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_Proto_Recharge_RechargeTestDeliveryRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Recharge_RechargeTestDeliveryRequest_descriptor,
        new java.lang.String[] { "CommonParams", "PreOrderId", });
    internal_static_Proto_Recharge_RechargeTestDeliveryResponse_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_Proto_Recharge_RechargeTestDeliveryResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Recharge_RechargeTestDeliveryResponse_descriptor,
        new java.lang.String[] { "Code", "CommonData", "IapInfo", });
    internal_static_Proto_Recharge_RechargeCancelPreOrderRequest_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_Proto_Recharge_RechargeCancelPreOrderRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Recharge_RechargeCancelPreOrderRequest_descriptor,
        new java.lang.String[] { "CommonParams", "PreOrderId", });
    internal_static_Proto_Recharge_RechargeCancelPreOrderResponse_descriptor =
      getDescriptor().getMessageTypes().get(11);
    internal_static_Proto_Recharge_RechargeCancelPreOrderResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Recharge_RechargeCancelPreOrderResponse_descriptor,
        new java.lang.String[] { "Code", "PreOrderId", });
    internal_static_Proto_Recharge_RechargeExchangeVerifyRequest_descriptor =
      getDescriptor().getMessageTypes().get(12);
    internal_static_Proto_Recharge_RechargeExchangeVerifyRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Recharge_RechargeExchangeVerifyRequest_descriptor,
        new java.lang.String[] { "CommonParams", "ChannelId", "ReceiptData", "ProductId", });
    internal_static_Proto_Recharge_RechargeExchangeVerifyResponse_descriptor =
      getDescriptor().getMessageTypes().get(13);
    internal_static_Proto_Recharge_RechargeExchangeVerifyResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Recharge_RechargeExchangeVerifyResponse_descriptor,
        new java.lang.String[] { "Code", });
    descriptor.resolveAllFeaturesImmutable();
    com.dxx.game.dto.CommonProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
