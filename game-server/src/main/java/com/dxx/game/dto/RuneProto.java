// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: rune.proto
// Protobuf Java Version: 4.29.0

package com.dxx.game.dto;

public final class RuneProto {
  private RuneProto() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 29,
      /* patch= */ 0,
      /* suffix= */ "",
      RuneProto.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface RuneStrengthenRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Rune.RuneStrengthenRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();

    /**
     * <pre>
     * 符文rowId
     * </pre>
     *
     * <code>int64 rowId = 2;</code>
     * @return The rowId.
     */
    long getRowId();
  }
  /**
   * <pre>
   * CMD PackageId=11121 符文-强化
   * </pre>
   *
   * Protobuf type {@code Proto.Rune.RuneStrengthenRequest}
   */
  public static final class RuneStrengthenRequest extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Rune.RuneStrengthenRequest)
      RuneStrengthenRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        RuneStrengthenRequest.class.getName());
    }
    // Use RuneStrengthenRequest.newBuilder() to construct.
    private RuneStrengthenRequest(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private RuneStrengthenRequest() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.RuneProto.internal_static_Proto_Rune_RuneStrengthenRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.RuneProto.internal_static_Proto_Rune_RuneStrengthenRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.RuneProto.RuneStrengthenRequest.class, com.dxx.game.dto.RuneProto.RuneStrengthenRequest.Builder.class);
    }

    private int bitField0_;
    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }

    public static final int ROWID_FIELD_NUMBER = 2;
    private long rowId_ = 0L;
    /**
     * <pre>
     * 符文rowId
     * </pre>
     *
     * <code>int64 rowId = 2;</code>
     * @return The rowId.
     */
    @java.lang.Override
    public long getRowId() {
      return rowId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getCommonParams());
      }
      if (rowId_ != 0L) {
        output.writeInt64(2, rowId_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      if (rowId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, rowId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.RuneProto.RuneStrengthenRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.RuneProto.RuneStrengthenRequest other = (com.dxx.game.dto.RuneProto.RuneStrengthenRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (getRowId()
          != other.getRowId()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (37 * hash) + ROWID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getRowId());
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.RuneProto.RuneStrengthenRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.RuneProto.RuneStrengthenRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.RuneProto.RuneStrengthenRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.RuneProto.RuneStrengthenRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.RuneProto.RuneStrengthenRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.RuneProto.RuneStrengthenRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.RuneProto.RuneStrengthenRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.RuneProto.RuneStrengthenRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.RuneProto.RuneStrengthenRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.RuneProto.RuneStrengthenRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.RuneProto.RuneStrengthenRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.RuneProto.RuneStrengthenRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.RuneProto.RuneStrengthenRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=11121 符文-强化
     * </pre>
     *
     * Protobuf type {@code Proto.Rune.RuneStrengthenRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Rune.RuneStrengthenRequest)
        com.dxx.game.dto.RuneProto.RuneStrengthenRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.RuneProto.internal_static_Proto_Rune_RuneStrengthenRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.RuneProto.internal_static_Proto_Rune_RuneStrengthenRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.RuneProto.RuneStrengthenRequest.class, com.dxx.game.dto.RuneProto.RuneStrengthenRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.RuneProto.RuneStrengthenRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getCommonParamsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        rowId_ = 0L;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.RuneProto.internal_static_Proto_Rune_RuneStrengthenRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.RuneProto.RuneStrengthenRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.RuneProto.RuneStrengthenRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.RuneProto.RuneStrengthenRequest build() {
        com.dxx.game.dto.RuneProto.RuneStrengthenRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.RuneProto.RuneStrengthenRequest buildPartial() {
        com.dxx.game.dto.RuneProto.RuneStrengthenRequest result = new com.dxx.game.dto.RuneProto.RuneStrengthenRequest(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.RuneProto.RuneStrengthenRequest result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.commonParams_ = commonParamsBuilder_ == null
              ? commonParams_
              : commonParamsBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.rowId_ = rowId_;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.RuneProto.RuneStrengthenRequest) {
          return mergeFrom((com.dxx.game.dto.RuneProto.RuneStrengthenRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.RuneProto.RuneStrengthenRequest other) {
        if (other == com.dxx.game.dto.RuneProto.RuneStrengthenRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        if (other.getRowId() != 0L) {
          setRowId(other.getRowId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getCommonParamsFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 16: {
                rowId_ = input.readInt64();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
        } else {
          commonParamsBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
            commonParams_ != null &&
            commonParams_ != com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance()) {
            getCommonParamsBuilder().mergeFrom(value);
          } else {
            commonParams_ = value;
          }
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }
        if (commonParams_ != null) {
          bitField0_ |= 0x00000001;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        bitField0_ = (bitField0_ & ~0x00000001);
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }

      private long rowId_ ;
      /**
       * <pre>
       * 符文rowId
       * </pre>
       *
       * <code>int64 rowId = 2;</code>
       * @return The rowId.
       */
      @java.lang.Override
      public long getRowId() {
        return rowId_;
      }
      /**
       * <pre>
       * 符文rowId
       * </pre>
       *
       * <code>int64 rowId = 2;</code>
       * @param value The rowId to set.
       * @return This builder for chaining.
       */
      public Builder setRowId(long value) {

        rowId_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 符文rowId
       * </pre>
       *
       * <code>int64 rowId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearRowId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        rowId_ = 0L;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Rune.RuneStrengthenRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.Rune.RuneStrengthenRequest)
    private static final com.dxx.game.dto.RuneProto.RuneStrengthenRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.RuneProto.RuneStrengthenRequest();
    }

    public static com.dxx.game.dto.RuneProto.RuneStrengthenRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<RuneStrengthenRequest>
        PARSER = new com.google.protobuf.AbstractParser<RuneStrengthenRequest>() {
      @java.lang.Override
      public RuneStrengthenRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<RuneStrengthenRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<RuneStrengthenRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.RuneProto.RuneStrengthenRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface RuneStrengthenResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Rune.RuneStrengthenResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <pre>
     * 符文信息
     * </pre>
     *
     * <code>.Proto.Common.RuneDto rune = 2;</code>
     * @return Whether the rune field is set.
     */
    boolean hasRune();
    /**
     * <pre>
     * 符文信息
     * </pre>
     *
     * <code>.Proto.Common.RuneDto rune = 2;</code>
     * @return The rune.
     */
    com.dxx.game.dto.CommonProto.RuneDto getRune();
    /**
     * <pre>
     * 符文信息
     * </pre>
     *
     * <code>.Proto.Common.RuneDto rune = 2;</code>
     */
    com.dxx.game.dto.CommonProto.RuneDtoOrBuilder getRuneOrBuilder();
  }
  /**
   * <pre>
   * CMD PackageId=11122 符文-强化
   * </pre>
   *
   * Protobuf type {@code Proto.Rune.RuneStrengthenResponse}
   */
  public static final class RuneStrengthenResponse extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Rune.RuneStrengthenResponse)
      RuneStrengthenResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        RuneStrengthenResponse.class.getName());
    }
    // Use RuneStrengthenResponse.newBuilder() to construct.
    private RuneStrengthenResponse(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private RuneStrengthenResponse() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.RuneProto.internal_static_Proto_Rune_RuneStrengthenResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.RuneProto.internal_static_Proto_Rune_RuneStrengthenResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.RuneProto.RuneStrengthenResponse.class, com.dxx.game.dto.RuneProto.RuneStrengthenResponse.Builder.class);
    }

    private int bitField0_;
    public static final int CODE_FIELD_NUMBER = 1;
    private int code_ = 0;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int RUNE_FIELD_NUMBER = 2;
    private com.dxx.game.dto.CommonProto.RuneDto rune_;
    /**
     * <pre>
     * 符文信息
     * </pre>
     *
     * <code>.Proto.Common.RuneDto rune = 2;</code>
     * @return Whether the rune field is set.
     */
    @java.lang.Override
    public boolean hasRune() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 符文信息
     * </pre>
     *
     * <code>.Proto.Common.RuneDto rune = 2;</code>
     * @return The rune.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.RuneDto getRune() {
      return rune_ == null ? com.dxx.game.dto.CommonProto.RuneDto.getDefaultInstance() : rune_;
    }
    /**
     * <pre>
     * 符文信息
     * </pre>
     *
     * <code>.Proto.Common.RuneDto rune = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.RuneDtoOrBuilder getRuneOrBuilder() {
      return rune_ == null ? com.dxx.game.dto.CommonProto.RuneDto.getDefaultInstance() : rune_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(2, getRune());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getRune());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.RuneProto.RuneStrengthenResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.RuneProto.RuneStrengthenResponse other = (com.dxx.game.dto.RuneProto.RuneStrengthenResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (hasRune() != other.hasRune()) return false;
      if (hasRune()) {
        if (!getRune()
            .equals(other.getRune())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      if (hasRune()) {
        hash = (37 * hash) + RUNE_FIELD_NUMBER;
        hash = (53 * hash) + getRune().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.RuneProto.RuneStrengthenResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.RuneProto.RuneStrengthenResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.RuneProto.RuneStrengthenResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.RuneProto.RuneStrengthenResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.RuneProto.RuneStrengthenResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.RuneProto.RuneStrengthenResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.RuneProto.RuneStrengthenResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.RuneProto.RuneStrengthenResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.RuneProto.RuneStrengthenResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.RuneProto.RuneStrengthenResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.RuneProto.RuneStrengthenResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.RuneProto.RuneStrengthenResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.RuneProto.RuneStrengthenResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=11122 符文-强化
     * </pre>
     *
     * Protobuf type {@code Proto.Rune.RuneStrengthenResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Rune.RuneStrengthenResponse)
        com.dxx.game.dto.RuneProto.RuneStrengthenResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.RuneProto.internal_static_Proto_Rune_RuneStrengthenResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.RuneProto.internal_static_Proto_Rune_RuneStrengthenResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.RuneProto.RuneStrengthenResponse.class, com.dxx.game.dto.RuneProto.RuneStrengthenResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.RuneProto.RuneStrengthenResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getRuneFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        code_ = 0;
        rune_ = null;
        if (runeBuilder_ != null) {
          runeBuilder_.dispose();
          runeBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.RuneProto.internal_static_Proto_Rune_RuneStrengthenResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.RuneProto.RuneStrengthenResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.RuneProto.RuneStrengthenResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.RuneProto.RuneStrengthenResponse build() {
        com.dxx.game.dto.RuneProto.RuneStrengthenResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.RuneProto.RuneStrengthenResponse buildPartial() {
        com.dxx.game.dto.RuneProto.RuneStrengthenResponse result = new com.dxx.game.dto.RuneProto.RuneStrengthenResponse(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.RuneProto.RuneStrengthenResponse result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.code_ = code_;
        }
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.rune_ = runeBuilder_ == null
              ? rune_
              : runeBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.RuneProto.RuneStrengthenResponse) {
          return mergeFrom((com.dxx.game.dto.RuneProto.RuneStrengthenResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.RuneProto.RuneStrengthenResponse other) {
        if (other == com.dxx.game.dto.RuneProto.RuneStrengthenResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.hasRune()) {
          mergeRune(other.getRune());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                code_ = input.readInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 18: {
                input.readMessage(
                    getRuneFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {

        code_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        bitField0_ = (bitField0_ & ~0x00000001);
        code_ = 0;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.CommonProto.RuneDto rune_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.RuneDto, com.dxx.game.dto.CommonProto.RuneDto.Builder, com.dxx.game.dto.CommonProto.RuneDtoOrBuilder> runeBuilder_;
      /**
       * <pre>
       * 符文信息
       * </pre>
       *
       * <code>.Proto.Common.RuneDto rune = 2;</code>
       * @return Whether the rune field is set.
       */
      public boolean hasRune() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 符文信息
       * </pre>
       *
       * <code>.Proto.Common.RuneDto rune = 2;</code>
       * @return The rune.
       */
      public com.dxx.game.dto.CommonProto.RuneDto getRune() {
        if (runeBuilder_ == null) {
          return rune_ == null ? com.dxx.game.dto.CommonProto.RuneDto.getDefaultInstance() : rune_;
        } else {
          return runeBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 符文信息
       * </pre>
       *
       * <code>.Proto.Common.RuneDto rune = 2;</code>
       */
      public Builder setRune(com.dxx.game.dto.CommonProto.RuneDto value) {
        if (runeBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          rune_ = value;
        } else {
          runeBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 符文信息
       * </pre>
       *
       * <code>.Proto.Common.RuneDto rune = 2;</code>
       */
      public Builder setRune(
          com.dxx.game.dto.CommonProto.RuneDto.Builder builderForValue) {
        if (runeBuilder_ == null) {
          rune_ = builderForValue.build();
        } else {
          runeBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 符文信息
       * </pre>
       *
       * <code>.Proto.Common.RuneDto rune = 2;</code>
       */
      public Builder mergeRune(com.dxx.game.dto.CommonProto.RuneDto value) {
        if (runeBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
            rune_ != null &&
            rune_ != com.dxx.game.dto.CommonProto.RuneDto.getDefaultInstance()) {
            getRuneBuilder().mergeFrom(value);
          } else {
            rune_ = value;
          }
        } else {
          runeBuilder_.mergeFrom(value);
        }
        if (rune_ != null) {
          bitField0_ |= 0x00000002;
          onChanged();
        }
        return this;
      }
      /**
       * <pre>
       * 符文信息
       * </pre>
       *
       * <code>.Proto.Common.RuneDto rune = 2;</code>
       */
      public Builder clearRune() {
        bitField0_ = (bitField0_ & ~0x00000002);
        rune_ = null;
        if (runeBuilder_ != null) {
          runeBuilder_.dispose();
          runeBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 符文信息
       * </pre>
       *
       * <code>.Proto.Common.RuneDto rune = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.RuneDto.Builder getRuneBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getRuneFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 符文信息
       * </pre>
       *
       * <code>.Proto.Common.RuneDto rune = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.RuneDtoOrBuilder getRuneOrBuilder() {
        if (runeBuilder_ != null) {
          return runeBuilder_.getMessageOrBuilder();
        } else {
          return rune_ == null ?
              com.dxx.game.dto.CommonProto.RuneDto.getDefaultInstance() : rune_;
        }
      }
      /**
       * <pre>
       * 符文信息
       * </pre>
       *
       * <code>.Proto.Common.RuneDto rune = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.RuneDto, com.dxx.game.dto.CommonProto.RuneDto.Builder, com.dxx.game.dto.CommonProto.RuneDtoOrBuilder> 
          getRuneFieldBuilder() {
        if (runeBuilder_ == null) {
          runeBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.RuneDto, com.dxx.game.dto.CommonProto.RuneDto.Builder, com.dxx.game.dto.CommonProto.RuneDtoOrBuilder>(
                  getRune(),
                  getParentForChildren(),
                  isClean());
          rune_ = null;
        }
        return runeBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Rune.RuneStrengthenResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.Rune.RuneStrengthenResponse)
    private static final com.dxx.game.dto.RuneProto.RuneStrengthenResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.RuneProto.RuneStrengthenResponse();
    }

    public static com.dxx.game.dto.RuneProto.RuneStrengthenResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<RuneStrengthenResponse>
        PARSER = new com.google.protobuf.AbstractParser<RuneStrengthenResponse>() {
      @java.lang.Override
      public RuneStrengthenResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<RuneStrengthenResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<RuneStrengthenResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.RuneProto.RuneStrengthenResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface RuneLockOrUnLockRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Rune.RuneLockOrUnLockRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();

    /**
     * <pre>
     * 符文rowId
     * </pre>
     *
     * <code>int64 rowId = 2;</code>
     * @return The rowId.
     */
    long getRowId();

    /**
     * <pre>
     * 是否锁定 true 锁  false 解锁
     * </pre>
     *
     * <code>bool isLock = 3;</code>
     * @return The isLock.
     */
    boolean getIsLock();
  }
  /**
   * <pre>
   * CMD PackageId=11123 符文-锁定/解锁
   * </pre>
   *
   * Protobuf type {@code Proto.Rune.RuneLockOrUnLockRequest}
   */
  public static final class RuneLockOrUnLockRequest extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Rune.RuneLockOrUnLockRequest)
      RuneLockOrUnLockRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        RuneLockOrUnLockRequest.class.getName());
    }
    // Use RuneLockOrUnLockRequest.newBuilder() to construct.
    private RuneLockOrUnLockRequest(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private RuneLockOrUnLockRequest() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.RuneProto.internal_static_Proto_Rune_RuneLockOrUnLockRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.RuneProto.internal_static_Proto_Rune_RuneLockOrUnLockRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.RuneProto.RuneLockOrUnLockRequest.class, com.dxx.game.dto.RuneProto.RuneLockOrUnLockRequest.Builder.class);
    }

    private int bitField0_;
    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }

    public static final int ROWID_FIELD_NUMBER = 2;
    private long rowId_ = 0L;
    /**
     * <pre>
     * 符文rowId
     * </pre>
     *
     * <code>int64 rowId = 2;</code>
     * @return The rowId.
     */
    @java.lang.Override
    public long getRowId() {
      return rowId_;
    }

    public static final int ISLOCK_FIELD_NUMBER = 3;
    private boolean isLock_ = false;
    /**
     * <pre>
     * 是否锁定 true 锁  false 解锁
     * </pre>
     *
     * <code>bool isLock = 3;</code>
     * @return The isLock.
     */
    @java.lang.Override
    public boolean getIsLock() {
      return isLock_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getCommonParams());
      }
      if (rowId_ != 0L) {
        output.writeInt64(2, rowId_);
      }
      if (isLock_ != false) {
        output.writeBool(3, isLock_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      if (rowId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, rowId_);
      }
      if (isLock_ != false) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(3, isLock_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.RuneProto.RuneLockOrUnLockRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.RuneProto.RuneLockOrUnLockRequest other = (com.dxx.game.dto.RuneProto.RuneLockOrUnLockRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (getRowId()
          != other.getRowId()) return false;
      if (getIsLock()
          != other.getIsLock()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (37 * hash) + ROWID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getRowId());
      hash = (37 * hash) + ISLOCK_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getIsLock());
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.RuneProto.RuneLockOrUnLockRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.RuneProto.RuneLockOrUnLockRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.RuneProto.RuneLockOrUnLockRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.RuneProto.RuneLockOrUnLockRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.RuneProto.RuneLockOrUnLockRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.RuneProto.RuneLockOrUnLockRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.RuneProto.RuneLockOrUnLockRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.RuneProto.RuneLockOrUnLockRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.RuneProto.RuneLockOrUnLockRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.RuneProto.RuneLockOrUnLockRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.RuneProto.RuneLockOrUnLockRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.RuneProto.RuneLockOrUnLockRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.RuneProto.RuneLockOrUnLockRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=11123 符文-锁定/解锁
     * </pre>
     *
     * Protobuf type {@code Proto.Rune.RuneLockOrUnLockRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Rune.RuneLockOrUnLockRequest)
        com.dxx.game.dto.RuneProto.RuneLockOrUnLockRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.RuneProto.internal_static_Proto_Rune_RuneLockOrUnLockRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.RuneProto.internal_static_Proto_Rune_RuneLockOrUnLockRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.RuneProto.RuneLockOrUnLockRequest.class, com.dxx.game.dto.RuneProto.RuneLockOrUnLockRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.RuneProto.RuneLockOrUnLockRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getCommonParamsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        rowId_ = 0L;
        isLock_ = false;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.RuneProto.internal_static_Proto_Rune_RuneLockOrUnLockRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.RuneProto.RuneLockOrUnLockRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.RuneProto.RuneLockOrUnLockRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.RuneProto.RuneLockOrUnLockRequest build() {
        com.dxx.game.dto.RuneProto.RuneLockOrUnLockRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.RuneProto.RuneLockOrUnLockRequest buildPartial() {
        com.dxx.game.dto.RuneProto.RuneLockOrUnLockRequest result = new com.dxx.game.dto.RuneProto.RuneLockOrUnLockRequest(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.RuneProto.RuneLockOrUnLockRequest result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.commonParams_ = commonParamsBuilder_ == null
              ? commonParams_
              : commonParamsBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.rowId_ = rowId_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.isLock_ = isLock_;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.RuneProto.RuneLockOrUnLockRequest) {
          return mergeFrom((com.dxx.game.dto.RuneProto.RuneLockOrUnLockRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.RuneProto.RuneLockOrUnLockRequest other) {
        if (other == com.dxx.game.dto.RuneProto.RuneLockOrUnLockRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        if (other.getRowId() != 0L) {
          setRowId(other.getRowId());
        }
        if (other.getIsLock() != false) {
          setIsLock(other.getIsLock());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getCommonParamsFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 16: {
                rowId_ = input.readInt64();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 24: {
                isLock_ = input.readBool();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
        } else {
          commonParamsBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
            commonParams_ != null &&
            commonParams_ != com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance()) {
            getCommonParamsBuilder().mergeFrom(value);
          } else {
            commonParams_ = value;
          }
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }
        if (commonParams_ != null) {
          bitField0_ |= 0x00000001;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        bitField0_ = (bitField0_ & ~0x00000001);
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }

      private long rowId_ ;
      /**
       * <pre>
       * 符文rowId
       * </pre>
       *
       * <code>int64 rowId = 2;</code>
       * @return The rowId.
       */
      @java.lang.Override
      public long getRowId() {
        return rowId_;
      }
      /**
       * <pre>
       * 符文rowId
       * </pre>
       *
       * <code>int64 rowId = 2;</code>
       * @param value The rowId to set.
       * @return This builder for chaining.
       */
      public Builder setRowId(long value) {

        rowId_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 符文rowId
       * </pre>
       *
       * <code>int64 rowId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearRowId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        rowId_ = 0L;
        onChanged();
        return this;
      }

      private boolean isLock_ ;
      /**
       * <pre>
       * 是否锁定 true 锁  false 解锁
       * </pre>
       *
       * <code>bool isLock = 3;</code>
       * @return The isLock.
       */
      @java.lang.Override
      public boolean getIsLock() {
        return isLock_;
      }
      /**
       * <pre>
       * 是否锁定 true 锁  false 解锁
       * </pre>
       *
       * <code>bool isLock = 3;</code>
       * @param value The isLock to set.
       * @return This builder for chaining.
       */
      public Builder setIsLock(boolean value) {

        isLock_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 是否锁定 true 锁  false 解锁
       * </pre>
       *
       * <code>bool isLock = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsLock() {
        bitField0_ = (bitField0_ & ~0x00000004);
        isLock_ = false;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Rune.RuneLockOrUnLockRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.Rune.RuneLockOrUnLockRequest)
    private static final com.dxx.game.dto.RuneProto.RuneLockOrUnLockRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.RuneProto.RuneLockOrUnLockRequest();
    }

    public static com.dxx.game.dto.RuneProto.RuneLockOrUnLockRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<RuneLockOrUnLockRequest>
        PARSER = new com.google.protobuf.AbstractParser<RuneLockOrUnLockRequest>() {
      @java.lang.Override
      public RuneLockOrUnLockRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<RuneLockOrUnLockRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<RuneLockOrUnLockRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.RuneProto.RuneLockOrUnLockRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface RuneLockOrUnLockResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Rune.RuneLockOrUnLockResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <pre>
     * 符文rowId
     * </pre>
     *
     * <code>int64 rowId = 2;</code>
     * @return The rowId.
     */
    long getRowId();

    /**
     * <pre>
     * 是否锁定 true 锁  false 解锁
     * </pre>
     *
     * <code>bool isLock = 3;</code>
     * @return The isLock.
     */
    boolean getIsLock();
  }
  /**
   * <pre>
   * CMD PackageId=11124 符文-锁定/解锁
   * </pre>
   *
   * Protobuf type {@code Proto.Rune.RuneLockOrUnLockResponse}
   */
  public static final class RuneLockOrUnLockResponse extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Rune.RuneLockOrUnLockResponse)
      RuneLockOrUnLockResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        RuneLockOrUnLockResponse.class.getName());
    }
    // Use RuneLockOrUnLockResponse.newBuilder() to construct.
    private RuneLockOrUnLockResponse(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private RuneLockOrUnLockResponse() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.RuneProto.internal_static_Proto_Rune_RuneLockOrUnLockResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.RuneProto.internal_static_Proto_Rune_RuneLockOrUnLockResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.RuneProto.RuneLockOrUnLockResponse.class, com.dxx.game.dto.RuneProto.RuneLockOrUnLockResponse.Builder.class);
    }

    public static final int CODE_FIELD_NUMBER = 1;
    private int code_ = 0;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int ROWID_FIELD_NUMBER = 2;
    private long rowId_ = 0L;
    /**
     * <pre>
     * 符文rowId
     * </pre>
     *
     * <code>int64 rowId = 2;</code>
     * @return The rowId.
     */
    @java.lang.Override
    public long getRowId() {
      return rowId_;
    }

    public static final int ISLOCK_FIELD_NUMBER = 3;
    private boolean isLock_ = false;
    /**
     * <pre>
     * 是否锁定 true 锁  false 解锁
     * </pre>
     *
     * <code>bool isLock = 3;</code>
     * @return The isLock.
     */
    @java.lang.Override
    public boolean getIsLock() {
      return isLock_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (rowId_ != 0L) {
        output.writeInt64(2, rowId_);
      }
      if (isLock_ != false) {
        output.writeBool(3, isLock_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (rowId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, rowId_);
      }
      if (isLock_ != false) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(3, isLock_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.RuneProto.RuneLockOrUnLockResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.RuneProto.RuneLockOrUnLockResponse other = (com.dxx.game.dto.RuneProto.RuneLockOrUnLockResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (getRowId()
          != other.getRowId()) return false;
      if (getIsLock()
          != other.getIsLock()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      hash = (37 * hash) + ROWID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getRowId());
      hash = (37 * hash) + ISLOCK_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getIsLock());
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.RuneProto.RuneLockOrUnLockResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.RuneProto.RuneLockOrUnLockResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.RuneProto.RuneLockOrUnLockResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.RuneProto.RuneLockOrUnLockResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.RuneProto.RuneLockOrUnLockResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.RuneProto.RuneLockOrUnLockResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.RuneProto.RuneLockOrUnLockResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.RuneProto.RuneLockOrUnLockResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.RuneProto.RuneLockOrUnLockResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.RuneProto.RuneLockOrUnLockResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.RuneProto.RuneLockOrUnLockResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.RuneProto.RuneLockOrUnLockResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.RuneProto.RuneLockOrUnLockResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=11124 符文-锁定/解锁
     * </pre>
     *
     * Protobuf type {@code Proto.Rune.RuneLockOrUnLockResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Rune.RuneLockOrUnLockResponse)
        com.dxx.game.dto.RuneProto.RuneLockOrUnLockResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.RuneProto.internal_static_Proto_Rune_RuneLockOrUnLockResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.RuneProto.internal_static_Proto_Rune_RuneLockOrUnLockResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.RuneProto.RuneLockOrUnLockResponse.class, com.dxx.game.dto.RuneProto.RuneLockOrUnLockResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.RuneProto.RuneLockOrUnLockResponse.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        code_ = 0;
        rowId_ = 0L;
        isLock_ = false;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.RuneProto.internal_static_Proto_Rune_RuneLockOrUnLockResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.RuneProto.RuneLockOrUnLockResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.RuneProto.RuneLockOrUnLockResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.RuneProto.RuneLockOrUnLockResponse build() {
        com.dxx.game.dto.RuneProto.RuneLockOrUnLockResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.RuneProto.RuneLockOrUnLockResponse buildPartial() {
        com.dxx.game.dto.RuneProto.RuneLockOrUnLockResponse result = new com.dxx.game.dto.RuneProto.RuneLockOrUnLockResponse(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.RuneProto.RuneLockOrUnLockResponse result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.code_ = code_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.rowId_ = rowId_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.isLock_ = isLock_;
        }
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.RuneProto.RuneLockOrUnLockResponse) {
          return mergeFrom((com.dxx.game.dto.RuneProto.RuneLockOrUnLockResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.RuneProto.RuneLockOrUnLockResponse other) {
        if (other == com.dxx.game.dto.RuneProto.RuneLockOrUnLockResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.getRowId() != 0L) {
          setRowId(other.getRowId());
        }
        if (other.getIsLock() != false) {
          setIsLock(other.getIsLock());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                code_ = input.readInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                rowId_ = input.readInt64();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 24: {
                isLock_ = input.readBool();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {

        code_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        bitField0_ = (bitField0_ & ~0x00000001);
        code_ = 0;
        onChanged();
        return this;
      }

      private long rowId_ ;
      /**
       * <pre>
       * 符文rowId
       * </pre>
       *
       * <code>int64 rowId = 2;</code>
       * @return The rowId.
       */
      @java.lang.Override
      public long getRowId() {
        return rowId_;
      }
      /**
       * <pre>
       * 符文rowId
       * </pre>
       *
       * <code>int64 rowId = 2;</code>
       * @param value The rowId to set.
       * @return This builder for chaining.
       */
      public Builder setRowId(long value) {

        rowId_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 符文rowId
       * </pre>
       *
       * <code>int64 rowId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearRowId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        rowId_ = 0L;
        onChanged();
        return this;
      }

      private boolean isLock_ ;
      /**
       * <pre>
       * 是否锁定 true 锁  false 解锁
       * </pre>
       *
       * <code>bool isLock = 3;</code>
       * @return The isLock.
       */
      @java.lang.Override
      public boolean getIsLock() {
        return isLock_;
      }
      /**
       * <pre>
       * 是否锁定 true 锁  false 解锁
       * </pre>
       *
       * <code>bool isLock = 3;</code>
       * @param value The isLock to set.
       * @return This builder for chaining.
       */
      public Builder setIsLock(boolean value) {

        isLock_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 是否锁定 true 锁  false 解锁
       * </pre>
       *
       * <code>bool isLock = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsLock() {
        bitField0_ = (bitField0_ & ~0x00000004);
        isLock_ = false;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Rune.RuneLockOrUnLockResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.Rune.RuneLockOrUnLockResponse)
    private static final com.dxx.game.dto.RuneProto.RuneLockOrUnLockResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.RuneProto.RuneLockOrUnLockResponse();
    }

    public static com.dxx.game.dto.RuneProto.RuneLockOrUnLockResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<RuneLockOrUnLockResponse>
        PARSER = new com.google.protobuf.AbstractParser<RuneLockOrUnLockResponse>() {
      @java.lang.Override
      public RuneLockOrUnLockResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<RuneLockOrUnLockResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<RuneLockOrUnLockResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.RuneProto.RuneLockOrUnLockResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface RunePutOnOrTakeOffRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Rune.RunePutOnOrTakeOffRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();

    /**
     * <pre>
     * 是否是穿
     * </pre>
     *
     * <code>bool isPutOn = 2;</code>
     * @return The isPutOn.
     */
    boolean getIsPutOn();

    /**
     * <pre>
     * 符文Ids
     * </pre>
     *
     * <code>repeated int64 rowIds = 3;</code>
     * @return A list containing the rowIds.
     */
    java.util.List<java.lang.Long> getRowIdsList();
    /**
     * <pre>
     * 符文Ids
     * </pre>
     *
     * <code>repeated int64 rowIds = 3;</code>
     * @return The count of rowIds.
     */
    int getRowIdsCount();
    /**
     * <pre>
     * 符文Ids
     * </pre>
     *
     * <code>repeated int64 rowIds = 3;</code>
     * @param index The index of the element to return.
     * @return The rowIds at the given index.
     */
    long getRowIds(int index);

    /**
     * <pre>
     * 英雄id
     * </pre>
     *
     * <code>int32 heroId = 4;</code>
     * @return The heroId.
     */
    int getHeroId();
  }
  /**
   * <pre>
   * CMD PackageId=11125 符文-穿戴/卸下 (一键操作与单个穿脱)
   * </pre>
   *
   * Protobuf type {@code Proto.Rune.RunePutOnOrTakeOffRequest}
   */
  public static final class RunePutOnOrTakeOffRequest extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Rune.RunePutOnOrTakeOffRequest)
      RunePutOnOrTakeOffRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        RunePutOnOrTakeOffRequest.class.getName());
    }
    // Use RunePutOnOrTakeOffRequest.newBuilder() to construct.
    private RunePutOnOrTakeOffRequest(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private RunePutOnOrTakeOffRequest() {
      rowIds_ = emptyLongList();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.RuneProto.internal_static_Proto_Rune_RunePutOnOrTakeOffRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.RuneProto.internal_static_Proto_Rune_RunePutOnOrTakeOffRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffRequest.class, com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffRequest.Builder.class);
    }

    private int bitField0_;
    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }

    public static final int ISPUTON_FIELD_NUMBER = 2;
    private boolean isPutOn_ = false;
    /**
     * <pre>
     * 是否是穿
     * </pre>
     *
     * <code>bool isPutOn = 2;</code>
     * @return The isPutOn.
     */
    @java.lang.Override
    public boolean getIsPutOn() {
      return isPutOn_;
    }

    public static final int ROWIDS_FIELD_NUMBER = 3;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.LongList rowIds_ =
        emptyLongList();
    /**
     * <pre>
     * 符文Ids
     * </pre>
     *
     * <code>repeated int64 rowIds = 3;</code>
     * @return A list containing the rowIds.
     */
    @java.lang.Override
    public java.util.List<java.lang.Long>
        getRowIdsList() {
      return rowIds_;
    }
    /**
     * <pre>
     * 符文Ids
     * </pre>
     *
     * <code>repeated int64 rowIds = 3;</code>
     * @return The count of rowIds.
     */
    public int getRowIdsCount() {
      return rowIds_.size();
    }
    /**
     * <pre>
     * 符文Ids
     * </pre>
     *
     * <code>repeated int64 rowIds = 3;</code>
     * @param index The index of the element to return.
     * @return The rowIds at the given index.
     */
    public long getRowIds(int index) {
      return rowIds_.getLong(index);
    }
    private int rowIdsMemoizedSerializedSize = -1;

    public static final int HEROID_FIELD_NUMBER = 4;
    private int heroId_ = 0;
    /**
     * <pre>
     * 英雄id
     * </pre>
     *
     * <code>int32 heroId = 4;</code>
     * @return The heroId.
     */
    @java.lang.Override
    public int getHeroId() {
      return heroId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getCommonParams());
      }
      if (isPutOn_ != false) {
        output.writeBool(2, isPutOn_);
      }
      if (getRowIdsList().size() > 0) {
        output.writeUInt32NoTag(26);
        output.writeUInt32NoTag(rowIdsMemoizedSerializedSize);
      }
      for (int i = 0; i < rowIds_.size(); i++) {
        output.writeInt64NoTag(rowIds_.getLong(i));
      }
      if (heroId_ != 0) {
        output.writeInt32(4, heroId_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      if (isPutOn_ != false) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(2, isPutOn_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < rowIds_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt64SizeNoTag(rowIds_.getLong(i));
        }
        size += dataSize;
        if (!getRowIdsList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        rowIdsMemoizedSerializedSize = dataSize;
      }
      if (heroId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, heroId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffRequest other = (com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (getIsPutOn()
          != other.getIsPutOn()) return false;
      if (!getRowIdsList()
          .equals(other.getRowIdsList())) return false;
      if (getHeroId()
          != other.getHeroId()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (37 * hash) + ISPUTON_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getIsPutOn());
      if (getRowIdsCount() > 0) {
        hash = (37 * hash) + ROWIDS_FIELD_NUMBER;
        hash = (53 * hash) + getRowIdsList().hashCode();
      }
      hash = (37 * hash) + HEROID_FIELD_NUMBER;
      hash = (53 * hash) + getHeroId();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=11125 符文-穿戴/卸下 (一键操作与单个穿脱)
     * </pre>
     *
     * Protobuf type {@code Proto.Rune.RunePutOnOrTakeOffRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Rune.RunePutOnOrTakeOffRequest)
        com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.RuneProto.internal_static_Proto_Rune_RunePutOnOrTakeOffRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.RuneProto.internal_static_Proto_Rune_RunePutOnOrTakeOffRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffRequest.class, com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getCommonParamsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        isPutOn_ = false;
        rowIds_ = emptyLongList();
        heroId_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.RuneProto.internal_static_Proto_Rune_RunePutOnOrTakeOffRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffRequest build() {
        com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffRequest buildPartial() {
        com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffRequest result = new com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffRequest(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffRequest result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.commonParams_ = commonParamsBuilder_ == null
              ? commonParams_
              : commonParamsBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.isPutOn_ = isPutOn_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          rowIds_.makeImmutable();
          result.rowIds_ = rowIds_;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.heroId_ = heroId_;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffRequest) {
          return mergeFrom((com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffRequest other) {
        if (other == com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        if (other.getIsPutOn() != false) {
          setIsPutOn(other.getIsPutOn());
        }
        if (!other.rowIds_.isEmpty()) {
          if (rowIds_.isEmpty()) {
            rowIds_ = other.rowIds_;
            rowIds_.makeImmutable();
            bitField0_ |= 0x00000004;
          } else {
            ensureRowIdsIsMutable();
            rowIds_.addAll(other.rowIds_);
          }
          onChanged();
        }
        if (other.getHeroId() != 0) {
          setHeroId(other.getHeroId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getCommonParamsFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 16: {
                isPutOn_ = input.readBool();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 24: {
                long v = input.readInt64();
                ensureRowIdsIsMutable();
                rowIds_.addLong(v);
                break;
              } // case 24
              case 26: {
                int length = input.readRawVarint32();
                int limit = input.pushLimit(length);
                ensureRowIdsIsMutable();
                while (input.getBytesUntilLimit() > 0) {
                  rowIds_.addLong(input.readInt64());
                }
                input.popLimit(limit);
                break;
              } // case 26
              case 32: {
                heroId_ = input.readInt32();
                bitField0_ |= 0x00000008;
                break;
              } // case 32
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
        } else {
          commonParamsBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
            commonParams_ != null &&
            commonParams_ != com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance()) {
            getCommonParamsBuilder().mergeFrom(value);
          } else {
            commonParams_ = value;
          }
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }
        if (commonParams_ != null) {
          bitField0_ |= 0x00000001;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        bitField0_ = (bitField0_ & ~0x00000001);
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }

      private boolean isPutOn_ ;
      /**
       * <pre>
       * 是否是穿
       * </pre>
       *
       * <code>bool isPutOn = 2;</code>
       * @return The isPutOn.
       */
      @java.lang.Override
      public boolean getIsPutOn() {
        return isPutOn_;
      }
      /**
       * <pre>
       * 是否是穿
       * </pre>
       *
       * <code>bool isPutOn = 2;</code>
       * @param value The isPutOn to set.
       * @return This builder for chaining.
       */
      public Builder setIsPutOn(boolean value) {

        isPutOn_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 是否是穿
       * </pre>
       *
       * <code>bool isPutOn = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsPutOn() {
        bitField0_ = (bitField0_ & ~0x00000002);
        isPutOn_ = false;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.LongList rowIds_ = emptyLongList();
      private void ensureRowIdsIsMutable() {
        if (!rowIds_.isModifiable()) {
          rowIds_ = makeMutableCopy(rowIds_);
        }
        bitField0_ |= 0x00000004;
      }
      /**
       * <pre>
       * 符文Ids
       * </pre>
       *
       * <code>repeated int64 rowIds = 3;</code>
       * @return A list containing the rowIds.
       */
      public java.util.List<java.lang.Long>
          getRowIdsList() {
        rowIds_.makeImmutable();
        return rowIds_;
      }
      /**
       * <pre>
       * 符文Ids
       * </pre>
       *
       * <code>repeated int64 rowIds = 3;</code>
       * @return The count of rowIds.
       */
      public int getRowIdsCount() {
        return rowIds_.size();
      }
      /**
       * <pre>
       * 符文Ids
       * </pre>
       *
       * <code>repeated int64 rowIds = 3;</code>
       * @param index The index of the element to return.
       * @return The rowIds at the given index.
       */
      public long getRowIds(int index) {
        return rowIds_.getLong(index);
      }
      /**
       * <pre>
       * 符文Ids
       * </pre>
       *
       * <code>repeated int64 rowIds = 3;</code>
       * @param index The index to set the value at.
       * @param value The rowIds to set.
       * @return This builder for chaining.
       */
      public Builder setRowIds(
          int index, long value) {

        ensureRowIdsIsMutable();
        rowIds_.setLong(index, value);
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 符文Ids
       * </pre>
       *
       * <code>repeated int64 rowIds = 3;</code>
       * @param value The rowIds to add.
       * @return This builder for chaining.
       */
      public Builder addRowIds(long value) {

        ensureRowIdsIsMutable();
        rowIds_.addLong(value);
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 符文Ids
       * </pre>
       *
       * <code>repeated int64 rowIds = 3;</code>
       * @param values The rowIds to add.
       * @return This builder for chaining.
       */
      public Builder addAllRowIds(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensureRowIdsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, rowIds_);
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 符文Ids
       * </pre>
       *
       * <code>repeated int64 rowIds = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearRowIds() {
        rowIds_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }

      private int heroId_ ;
      /**
       * <pre>
       * 英雄id
       * </pre>
       *
       * <code>int32 heroId = 4;</code>
       * @return The heroId.
       */
      @java.lang.Override
      public int getHeroId() {
        return heroId_;
      }
      /**
       * <pre>
       * 英雄id
       * </pre>
       *
       * <code>int32 heroId = 4;</code>
       * @param value The heroId to set.
       * @return This builder for chaining.
       */
      public Builder setHeroId(int value) {

        heroId_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 英雄id
       * </pre>
       *
       * <code>int32 heroId = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearHeroId() {
        bitField0_ = (bitField0_ & ~0x00000008);
        heroId_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Rune.RunePutOnOrTakeOffRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.Rune.RunePutOnOrTakeOffRequest)
    private static final com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffRequest();
    }

    public static com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<RunePutOnOrTakeOffRequest>
        PARSER = new com.google.protobuf.AbstractParser<RunePutOnOrTakeOffRequest>() {
      @java.lang.Override
      public RunePutOnOrTakeOffRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<RunePutOnOrTakeOffRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<RunePutOnOrTakeOffRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface RunePutOnOrTakeOffResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Rune.RunePutOnOrTakeOffResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <pre>
     * 是否是穿
     * </pre>
     *
     * <code>bool isPutOn = 2;</code>
     * @return The isPutOn.
     */
    boolean getIsPutOn();

    /**
     * <pre>
     * 符文信息
     * </pre>
     *
     * <code>repeated int64 rowIds = 3;</code>
     * @return A list containing the rowIds.
     */
    java.util.List<java.lang.Long> getRowIdsList();
    /**
     * <pre>
     * 符文信息
     * </pre>
     *
     * <code>repeated int64 rowIds = 3;</code>
     * @return The count of rowIds.
     */
    int getRowIdsCount();
    /**
     * <pre>
     * 符文信息
     * </pre>
     *
     * <code>repeated int64 rowIds = 3;</code>
     * @param index The index of the element to return.
     * @return The rowIds at the given index.
     */
    long getRowIds(int index);

    /**
     * <pre>
     * 英雄id
     * </pre>
     *
     * <code>int32 heroId = 4;</code>
     * @return The heroId.
     */
    int getHeroId();
  }
  /**
   * <pre>
   * CMD PackageId=11126 符文-穿戴/卸下 (一键操作与单个穿脱) 只传要穿的要脱的
   * </pre>
   *
   * Protobuf type {@code Proto.Rune.RunePutOnOrTakeOffResponse}
   */
  public static final class RunePutOnOrTakeOffResponse extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Rune.RunePutOnOrTakeOffResponse)
      RunePutOnOrTakeOffResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        RunePutOnOrTakeOffResponse.class.getName());
    }
    // Use RunePutOnOrTakeOffResponse.newBuilder() to construct.
    private RunePutOnOrTakeOffResponse(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private RunePutOnOrTakeOffResponse() {
      rowIds_ = emptyLongList();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.RuneProto.internal_static_Proto_Rune_RunePutOnOrTakeOffResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.RuneProto.internal_static_Proto_Rune_RunePutOnOrTakeOffResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffResponse.class, com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffResponse.Builder.class);
    }

    public static final int CODE_FIELD_NUMBER = 1;
    private int code_ = 0;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int ISPUTON_FIELD_NUMBER = 2;
    private boolean isPutOn_ = false;
    /**
     * <pre>
     * 是否是穿
     * </pre>
     *
     * <code>bool isPutOn = 2;</code>
     * @return The isPutOn.
     */
    @java.lang.Override
    public boolean getIsPutOn() {
      return isPutOn_;
    }

    public static final int ROWIDS_FIELD_NUMBER = 3;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.LongList rowIds_ =
        emptyLongList();
    /**
     * <pre>
     * 符文信息
     * </pre>
     *
     * <code>repeated int64 rowIds = 3;</code>
     * @return A list containing the rowIds.
     */
    @java.lang.Override
    public java.util.List<java.lang.Long>
        getRowIdsList() {
      return rowIds_;
    }
    /**
     * <pre>
     * 符文信息
     * </pre>
     *
     * <code>repeated int64 rowIds = 3;</code>
     * @return The count of rowIds.
     */
    public int getRowIdsCount() {
      return rowIds_.size();
    }
    /**
     * <pre>
     * 符文信息
     * </pre>
     *
     * <code>repeated int64 rowIds = 3;</code>
     * @param index The index of the element to return.
     * @return The rowIds at the given index.
     */
    public long getRowIds(int index) {
      return rowIds_.getLong(index);
    }
    private int rowIdsMemoizedSerializedSize = -1;

    public static final int HEROID_FIELD_NUMBER = 4;
    private int heroId_ = 0;
    /**
     * <pre>
     * 英雄id
     * </pre>
     *
     * <code>int32 heroId = 4;</code>
     * @return The heroId.
     */
    @java.lang.Override
    public int getHeroId() {
      return heroId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (isPutOn_ != false) {
        output.writeBool(2, isPutOn_);
      }
      if (getRowIdsList().size() > 0) {
        output.writeUInt32NoTag(26);
        output.writeUInt32NoTag(rowIdsMemoizedSerializedSize);
      }
      for (int i = 0; i < rowIds_.size(); i++) {
        output.writeInt64NoTag(rowIds_.getLong(i));
      }
      if (heroId_ != 0) {
        output.writeInt32(4, heroId_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (isPutOn_ != false) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(2, isPutOn_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < rowIds_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt64SizeNoTag(rowIds_.getLong(i));
        }
        size += dataSize;
        if (!getRowIdsList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        rowIdsMemoizedSerializedSize = dataSize;
      }
      if (heroId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, heroId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffResponse other = (com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (getIsPutOn()
          != other.getIsPutOn()) return false;
      if (!getRowIdsList()
          .equals(other.getRowIdsList())) return false;
      if (getHeroId()
          != other.getHeroId()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      hash = (37 * hash) + ISPUTON_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getIsPutOn());
      if (getRowIdsCount() > 0) {
        hash = (37 * hash) + ROWIDS_FIELD_NUMBER;
        hash = (53 * hash) + getRowIdsList().hashCode();
      }
      hash = (37 * hash) + HEROID_FIELD_NUMBER;
      hash = (53 * hash) + getHeroId();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=11126 符文-穿戴/卸下 (一键操作与单个穿脱) 只传要穿的要脱的
     * </pre>
     *
     * Protobuf type {@code Proto.Rune.RunePutOnOrTakeOffResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Rune.RunePutOnOrTakeOffResponse)
        com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.RuneProto.internal_static_Proto_Rune_RunePutOnOrTakeOffResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.RuneProto.internal_static_Proto_Rune_RunePutOnOrTakeOffResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffResponse.class, com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffResponse.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        code_ = 0;
        isPutOn_ = false;
        rowIds_ = emptyLongList();
        heroId_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.RuneProto.internal_static_Proto_Rune_RunePutOnOrTakeOffResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffResponse build() {
        com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffResponse buildPartial() {
        com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffResponse result = new com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffResponse(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffResponse result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.code_ = code_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.isPutOn_ = isPutOn_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          rowIds_.makeImmutable();
          result.rowIds_ = rowIds_;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.heroId_ = heroId_;
        }
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffResponse) {
          return mergeFrom((com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffResponse other) {
        if (other == com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.getIsPutOn() != false) {
          setIsPutOn(other.getIsPutOn());
        }
        if (!other.rowIds_.isEmpty()) {
          if (rowIds_.isEmpty()) {
            rowIds_ = other.rowIds_;
            rowIds_.makeImmutable();
            bitField0_ |= 0x00000004;
          } else {
            ensureRowIdsIsMutable();
            rowIds_.addAll(other.rowIds_);
          }
          onChanged();
        }
        if (other.getHeroId() != 0) {
          setHeroId(other.getHeroId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                code_ = input.readInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                isPutOn_ = input.readBool();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 24: {
                long v = input.readInt64();
                ensureRowIdsIsMutable();
                rowIds_.addLong(v);
                break;
              } // case 24
              case 26: {
                int length = input.readRawVarint32();
                int limit = input.pushLimit(length);
                ensureRowIdsIsMutable();
                while (input.getBytesUntilLimit() > 0) {
                  rowIds_.addLong(input.readInt64());
                }
                input.popLimit(limit);
                break;
              } // case 26
              case 32: {
                heroId_ = input.readInt32();
                bitField0_ |= 0x00000008;
                break;
              } // case 32
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {

        code_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        bitField0_ = (bitField0_ & ~0x00000001);
        code_ = 0;
        onChanged();
        return this;
      }

      private boolean isPutOn_ ;
      /**
       * <pre>
       * 是否是穿
       * </pre>
       *
       * <code>bool isPutOn = 2;</code>
       * @return The isPutOn.
       */
      @java.lang.Override
      public boolean getIsPutOn() {
        return isPutOn_;
      }
      /**
       * <pre>
       * 是否是穿
       * </pre>
       *
       * <code>bool isPutOn = 2;</code>
       * @param value The isPutOn to set.
       * @return This builder for chaining.
       */
      public Builder setIsPutOn(boolean value) {

        isPutOn_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 是否是穿
       * </pre>
       *
       * <code>bool isPutOn = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsPutOn() {
        bitField0_ = (bitField0_ & ~0x00000002);
        isPutOn_ = false;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.LongList rowIds_ = emptyLongList();
      private void ensureRowIdsIsMutable() {
        if (!rowIds_.isModifiable()) {
          rowIds_ = makeMutableCopy(rowIds_);
        }
        bitField0_ |= 0x00000004;
      }
      /**
       * <pre>
       * 符文信息
       * </pre>
       *
       * <code>repeated int64 rowIds = 3;</code>
       * @return A list containing the rowIds.
       */
      public java.util.List<java.lang.Long>
          getRowIdsList() {
        rowIds_.makeImmutable();
        return rowIds_;
      }
      /**
       * <pre>
       * 符文信息
       * </pre>
       *
       * <code>repeated int64 rowIds = 3;</code>
       * @return The count of rowIds.
       */
      public int getRowIdsCount() {
        return rowIds_.size();
      }
      /**
       * <pre>
       * 符文信息
       * </pre>
       *
       * <code>repeated int64 rowIds = 3;</code>
       * @param index The index of the element to return.
       * @return The rowIds at the given index.
       */
      public long getRowIds(int index) {
        return rowIds_.getLong(index);
      }
      /**
       * <pre>
       * 符文信息
       * </pre>
       *
       * <code>repeated int64 rowIds = 3;</code>
       * @param index The index to set the value at.
       * @param value The rowIds to set.
       * @return This builder for chaining.
       */
      public Builder setRowIds(
          int index, long value) {

        ensureRowIdsIsMutable();
        rowIds_.setLong(index, value);
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 符文信息
       * </pre>
       *
       * <code>repeated int64 rowIds = 3;</code>
       * @param value The rowIds to add.
       * @return This builder for chaining.
       */
      public Builder addRowIds(long value) {

        ensureRowIdsIsMutable();
        rowIds_.addLong(value);
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 符文信息
       * </pre>
       *
       * <code>repeated int64 rowIds = 3;</code>
       * @param values The rowIds to add.
       * @return This builder for chaining.
       */
      public Builder addAllRowIds(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensureRowIdsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, rowIds_);
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 符文信息
       * </pre>
       *
       * <code>repeated int64 rowIds = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearRowIds() {
        rowIds_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }

      private int heroId_ ;
      /**
       * <pre>
       * 英雄id
       * </pre>
       *
       * <code>int32 heroId = 4;</code>
       * @return The heroId.
       */
      @java.lang.Override
      public int getHeroId() {
        return heroId_;
      }
      /**
       * <pre>
       * 英雄id
       * </pre>
       *
       * <code>int32 heroId = 4;</code>
       * @param value The heroId to set.
       * @return This builder for chaining.
       */
      public Builder setHeroId(int value) {

        heroId_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 英雄id
       * </pre>
       *
       * <code>int32 heroId = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearHeroId() {
        bitField0_ = (bitField0_ & ~0x00000008);
        heroId_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Rune.RunePutOnOrTakeOffResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.Rune.RunePutOnOrTakeOffResponse)
    private static final com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffResponse();
    }

    public static com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<RunePutOnOrTakeOffResponse>
        PARSER = new com.google.protobuf.AbstractParser<RunePutOnOrTakeOffResponse>() {
      @java.lang.Override
      public RunePutOnOrTakeOffResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<RunePutOnOrTakeOffResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<RunePutOnOrTakeOffResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface RuneSnatchRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Rune.RuneSnatchRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();

    /**
     * <pre>
     * 抢夺的符文id
     * </pre>
     *
     * <code>int64 snatchRowId = 2;</code>
     * @return The snatchRowId.
     */
    long getSnatchRowId();

    /**
     * <pre>
     * 英雄id 抢夺的英雄是谁
     * </pre>
     *
     * <code>int32 snatchHeroId = 3;</code>
     * @return The snatchHeroId.
     */
    int getSnatchHeroId();
  }
  /**
   * <pre>
   * CMD PackageId=11127 符文-抢来穿(强行穿其他英雄的符文)
   * </pre>
   *
   * Protobuf type {@code Proto.Rune.RuneSnatchRequest}
   */
  public static final class RuneSnatchRequest extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Rune.RuneSnatchRequest)
      RuneSnatchRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        RuneSnatchRequest.class.getName());
    }
    // Use RuneSnatchRequest.newBuilder() to construct.
    private RuneSnatchRequest(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private RuneSnatchRequest() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.RuneProto.internal_static_Proto_Rune_RuneSnatchRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.RuneProto.internal_static_Proto_Rune_RuneSnatchRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.RuneProto.RuneSnatchRequest.class, com.dxx.game.dto.RuneProto.RuneSnatchRequest.Builder.class);
    }

    private int bitField0_;
    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }

    public static final int SNATCHROWID_FIELD_NUMBER = 2;
    private long snatchRowId_ = 0L;
    /**
     * <pre>
     * 抢夺的符文id
     * </pre>
     *
     * <code>int64 snatchRowId = 2;</code>
     * @return The snatchRowId.
     */
    @java.lang.Override
    public long getSnatchRowId() {
      return snatchRowId_;
    }

    public static final int SNATCHHEROID_FIELD_NUMBER = 3;
    private int snatchHeroId_ = 0;
    /**
     * <pre>
     * 英雄id 抢夺的英雄是谁
     * </pre>
     *
     * <code>int32 snatchHeroId = 3;</code>
     * @return The snatchHeroId.
     */
    @java.lang.Override
    public int getSnatchHeroId() {
      return snatchHeroId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getCommonParams());
      }
      if (snatchRowId_ != 0L) {
        output.writeInt64(2, snatchRowId_);
      }
      if (snatchHeroId_ != 0) {
        output.writeInt32(3, snatchHeroId_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      if (snatchRowId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, snatchRowId_);
      }
      if (snatchHeroId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, snatchHeroId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.RuneProto.RuneSnatchRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.RuneProto.RuneSnatchRequest other = (com.dxx.game.dto.RuneProto.RuneSnatchRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (getSnatchRowId()
          != other.getSnatchRowId()) return false;
      if (getSnatchHeroId()
          != other.getSnatchHeroId()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (37 * hash) + SNATCHROWID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getSnatchRowId());
      hash = (37 * hash) + SNATCHHEROID_FIELD_NUMBER;
      hash = (53 * hash) + getSnatchHeroId();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.RuneProto.RuneSnatchRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.RuneProto.RuneSnatchRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.RuneProto.RuneSnatchRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.RuneProto.RuneSnatchRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.RuneProto.RuneSnatchRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.RuneProto.RuneSnatchRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.RuneProto.RuneSnatchRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.RuneProto.RuneSnatchRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.RuneProto.RuneSnatchRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.RuneProto.RuneSnatchRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.RuneProto.RuneSnatchRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.RuneProto.RuneSnatchRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.RuneProto.RuneSnatchRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=11127 符文-抢来穿(强行穿其他英雄的符文)
     * </pre>
     *
     * Protobuf type {@code Proto.Rune.RuneSnatchRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Rune.RuneSnatchRequest)
        com.dxx.game.dto.RuneProto.RuneSnatchRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.RuneProto.internal_static_Proto_Rune_RuneSnatchRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.RuneProto.internal_static_Proto_Rune_RuneSnatchRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.RuneProto.RuneSnatchRequest.class, com.dxx.game.dto.RuneProto.RuneSnatchRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.RuneProto.RuneSnatchRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getCommonParamsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        snatchRowId_ = 0L;
        snatchHeroId_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.RuneProto.internal_static_Proto_Rune_RuneSnatchRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.RuneProto.RuneSnatchRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.RuneProto.RuneSnatchRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.RuneProto.RuneSnatchRequest build() {
        com.dxx.game.dto.RuneProto.RuneSnatchRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.RuneProto.RuneSnatchRequest buildPartial() {
        com.dxx.game.dto.RuneProto.RuneSnatchRequest result = new com.dxx.game.dto.RuneProto.RuneSnatchRequest(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.RuneProto.RuneSnatchRequest result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.commonParams_ = commonParamsBuilder_ == null
              ? commonParams_
              : commonParamsBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.snatchRowId_ = snatchRowId_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.snatchHeroId_ = snatchHeroId_;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.RuneProto.RuneSnatchRequest) {
          return mergeFrom((com.dxx.game.dto.RuneProto.RuneSnatchRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.RuneProto.RuneSnatchRequest other) {
        if (other == com.dxx.game.dto.RuneProto.RuneSnatchRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        if (other.getSnatchRowId() != 0L) {
          setSnatchRowId(other.getSnatchRowId());
        }
        if (other.getSnatchHeroId() != 0) {
          setSnatchHeroId(other.getSnatchHeroId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getCommonParamsFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 16: {
                snatchRowId_ = input.readInt64();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 24: {
                snatchHeroId_ = input.readInt32();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
        } else {
          commonParamsBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
            commonParams_ != null &&
            commonParams_ != com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance()) {
            getCommonParamsBuilder().mergeFrom(value);
          } else {
            commonParams_ = value;
          }
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }
        if (commonParams_ != null) {
          bitField0_ |= 0x00000001;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        bitField0_ = (bitField0_ & ~0x00000001);
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }

      private long snatchRowId_ ;
      /**
       * <pre>
       * 抢夺的符文id
       * </pre>
       *
       * <code>int64 snatchRowId = 2;</code>
       * @return The snatchRowId.
       */
      @java.lang.Override
      public long getSnatchRowId() {
        return snatchRowId_;
      }
      /**
       * <pre>
       * 抢夺的符文id
       * </pre>
       *
       * <code>int64 snatchRowId = 2;</code>
       * @param value The snatchRowId to set.
       * @return This builder for chaining.
       */
      public Builder setSnatchRowId(long value) {

        snatchRowId_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 抢夺的符文id
       * </pre>
       *
       * <code>int64 snatchRowId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearSnatchRowId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        snatchRowId_ = 0L;
        onChanged();
        return this;
      }

      private int snatchHeroId_ ;
      /**
       * <pre>
       * 英雄id 抢夺的英雄是谁
       * </pre>
       *
       * <code>int32 snatchHeroId = 3;</code>
       * @return The snatchHeroId.
       */
      @java.lang.Override
      public int getSnatchHeroId() {
        return snatchHeroId_;
      }
      /**
       * <pre>
       * 英雄id 抢夺的英雄是谁
       * </pre>
       *
       * <code>int32 snatchHeroId = 3;</code>
       * @param value The snatchHeroId to set.
       * @return This builder for chaining.
       */
      public Builder setSnatchHeroId(int value) {

        snatchHeroId_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 英雄id 抢夺的英雄是谁
       * </pre>
       *
       * <code>int32 snatchHeroId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearSnatchHeroId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        snatchHeroId_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Rune.RuneSnatchRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.Rune.RuneSnatchRequest)
    private static final com.dxx.game.dto.RuneProto.RuneSnatchRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.RuneProto.RuneSnatchRequest();
    }

    public static com.dxx.game.dto.RuneProto.RuneSnatchRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<RuneSnatchRequest>
        PARSER = new com.google.protobuf.AbstractParser<RuneSnatchRequest>() {
      @java.lang.Override
      public RuneSnatchRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<RuneSnatchRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<RuneSnatchRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.RuneProto.RuneSnatchRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface RuneSnatchResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Rune.RuneSnatchResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <pre>
     * 抢夺的符文id
     * </pre>
     *
     * <code>int64 snatchRowId = 2;</code>
     * @return The snatchRowId.
     */
    long getSnatchRowId();

    /**
     * <pre>
     * 英雄id 抢夺的英雄是谁
     * </pre>
     *
     * <code>int32 snatchHeroId = 3;</code>
     * @return The snatchHeroId.
     */
    int getSnatchHeroId();
  }
  /**
   * <pre>
   * CMD PackageId=11128 符文-抢来穿(强行穿其他英雄的符文)
   * </pre>
   *
   * Protobuf type {@code Proto.Rune.RuneSnatchResponse}
   */
  public static final class RuneSnatchResponse extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Rune.RuneSnatchResponse)
      RuneSnatchResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        RuneSnatchResponse.class.getName());
    }
    // Use RuneSnatchResponse.newBuilder() to construct.
    private RuneSnatchResponse(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private RuneSnatchResponse() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.RuneProto.internal_static_Proto_Rune_RuneSnatchResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.RuneProto.internal_static_Proto_Rune_RuneSnatchResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.RuneProto.RuneSnatchResponse.class, com.dxx.game.dto.RuneProto.RuneSnatchResponse.Builder.class);
    }

    public static final int CODE_FIELD_NUMBER = 1;
    private int code_ = 0;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int SNATCHROWID_FIELD_NUMBER = 2;
    private long snatchRowId_ = 0L;
    /**
     * <pre>
     * 抢夺的符文id
     * </pre>
     *
     * <code>int64 snatchRowId = 2;</code>
     * @return The snatchRowId.
     */
    @java.lang.Override
    public long getSnatchRowId() {
      return snatchRowId_;
    }

    public static final int SNATCHHEROID_FIELD_NUMBER = 3;
    private int snatchHeroId_ = 0;
    /**
     * <pre>
     * 英雄id 抢夺的英雄是谁
     * </pre>
     *
     * <code>int32 snatchHeroId = 3;</code>
     * @return The snatchHeroId.
     */
    @java.lang.Override
    public int getSnatchHeroId() {
      return snatchHeroId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (snatchRowId_ != 0L) {
        output.writeInt64(2, snatchRowId_);
      }
      if (snatchHeroId_ != 0) {
        output.writeInt32(3, snatchHeroId_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (snatchRowId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, snatchRowId_);
      }
      if (snatchHeroId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, snatchHeroId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.RuneProto.RuneSnatchResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.RuneProto.RuneSnatchResponse other = (com.dxx.game.dto.RuneProto.RuneSnatchResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (getSnatchRowId()
          != other.getSnatchRowId()) return false;
      if (getSnatchHeroId()
          != other.getSnatchHeroId()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      hash = (37 * hash) + SNATCHROWID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getSnatchRowId());
      hash = (37 * hash) + SNATCHHEROID_FIELD_NUMBER;
      hash = (53 * hash) + getSnatchHeroId();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.RuneProto.RuneSnatchResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.RuneProto.RuneSnatchResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.RuneProto.RuneSnatchResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.RuneProto.RuneSnatchResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.RuneProto.RuneSnatchResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.RuneProto.RuneSnatchResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.RuneProto.RuneSnatchResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.RuneProto.RuneSnatchResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.RuneProto.RuneSnatchResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.RuneProto.RuneSnatchResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.RuneProto.RuneSnatchResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.RuneProto.RuneSnatchResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.RuneProto.RuneSnatchResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=11128 符文-抢来穿(强行穿其他英雄的符文)
     * </pre>
     *
     * Protobuf type {@code Proto.Rune.RuneSnatchResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Rune.RuneSnatchResponse)
        com.dxx.game.dto.RuneProto.RuneSnatchResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.RuneProto.internal_static_Proto_Rune_RuneSnatchResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.RuneProto.internal_static_Proto_Rune_RuneSnatchResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.RuneProto.RuneSnatchResponse.class, com.dxx.game.dto.RuneProto.RuneSnatchResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.RuneProto.RuneSnatchResponse.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        code_ = 0;
        snatchRowId_ = 0L;
        snatchHeroId_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.RuneProto.internal_static_Proto_Rune_RuneSnatchResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.RuneProto.RuneSnatchResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.RuneProto.RuneSnatchResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.RuneProto.RuneSnatchResponse build() {
        com.dxx.game.dto.RuneProto.RuneSnatchResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.RuneProto.RuneSnatchResponse buildPartial() {
        com.dxx.game.dto.RuneProto.RuneSnatchResponse result = new com.dxx.game.dto.RuneProto.RuneSnatchResponse(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.RuneProto.RuneSnatchResponse result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.code_ = code_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.snatchRowId_ = snatchRowId_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.snatchHeroId_ = snatchHeroId_;
        }
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.RuneProto.RuneSnatchResponse) {
          return mergeFrom((com.dxx.game.dto.RuneProto.RuneSnatchResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.RuneProto.RuneSnatchResponse other) {
        if (other == com.dxx.game.dto.RuneProto.RuneSnatchResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.getSnatchRowId() != 0L) {
          setSnatchRowId(other.getSnatchRowId());
        }
        if (other.getSnatchHeroId() != 0) {
          setSnatchHeroId(other.getSnatchHeroId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                code_ = input.readInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                snatchRowId_ = input.readInt64();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 24: {
                snatchHeroId_ = input.readInt32();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {

        code_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        bitField0_ = (bitField0_ & ~0x00000001);
        code_ = 0;
        onChanged();
        return this;
      }

      private long snatchRowId_ ;
      /**
       * <pre>
       * 抢夺的符文id
       * </pre>
       *
       * <code>int64 snatchRowId = 2;</code>
       * @return The snatchRowId.
       */
      @java.lang.Override
      public long getSnatchRowId() {
        return snatchRowId_;
      }
      /**
       * <pre>
       * 抢夺的符文id
       * </pre>
       *
       * <code>int64 snatchRowId = 2;</code>
       * @param value The snatchRowId to set.
       * @return This builder for chaining.
       */
      public Builder setSnatchRowId(long value) {

        snatchRowId_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 抢夺的符文id
       * </pre>
       *
       * <code>int64 snatchRowId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearSnatchRowId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        snatchRowId_ = 0L;
        onChanged();
        return this;
      }

      private int snatchHeroId_ ;
      /**
       * <pre>
       * 英雄id 抢夺的英雄是谁
       * </pre>
       *
       * <code>int32 snatchHeroId = 3;</code>
       * @return The snatchHeroId.
       */
      @java.lang.Override
      public int getSnatchHeroId() {
        return snatchHeroId_;
      }
      /**
       * <pre>
       * 英雄id 抢夺的英雄是谁
       * </pre>
       *
       * <code>int32 snatchHeroId = 3;</code>
       * @param value The snatchHeroId to set.
       * @return This builder for chaining.
       */
      public Builder setSnatchHeroId(int value) {

        snatchHeroId_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 英雄id 抢夺的英雄是谁
       * </pre>
       *
       * <code>int32 snatchHeroId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearSnatchHeroId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        snatchHeroId_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Rune.RuneSnatchResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.Rune.RuneSnatchResponse)
    private static final com.dxx.game.dto.RuneProto.RuneSnatchResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.RuneProto.RuneSnatchResponse();
    }

    public static com.dxx.game.dto.RuneProto.RuneSnatchResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<RuneSnatchResponse>
        PARSER = new com.google.protobuf.AbstractParser<RuneSnatchResponse>() {
      @java.lang.Override
      public RuneSnatchResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<RuneSnatchResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<RuneSnatchResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.RuneProto.RuneSnatchResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface RuneDecomposeRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Rune.RuneDecomposeRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();

    /**
     * <pre>
     * 符文Ids
     * </pre>
     *
     * <code>repeated int64 rowIds = 2;</code>
     * @return A list containing the rowIds.
     */
    java.util.List<java.lang.Long> getRowIdsList();
    /**
     * <pre>
     * 符文Ids
     * </pre>
     *
     * <code>repeated int64 rowIds = 2;</code>
     * @return The count of rowIds.
     */
    int getRowIdsCount();
    /**
     * <pre>
     * 符文Ids
     * </pre>
     *
     * <code>repeated int64 rowIds = 2;</code>
     * @param index The index of the element to return.
     * @return The rowIds at the given index.
     */
    long getRowIds(int index);
  }
  /**
   * <pre>
   * CMD PackageId=11129 符文-分解
   * </pre>
   *
   * Protobuf type {@code Proto.Rune.RuneDecomposeRequest}
   */
  public static final class RuneDecomposeRequest extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Rune.RuneDecomposeRequest)
      RuneDecomposeRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        RuneDecomposeRequest.class.getName());
    }
    // Use RuneDecomposeRequest.newBuilder() to construct.
    private RuneDecomposeRequest(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private RuneDecomposeRequest() {
      rowIds_ = emptyLongList();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.RuneProto.internal_static_Proto_Rune_RuneDecomposeRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.RuneProto.internal_static_Proto_Rune_RuneDecomposeRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.RuneProto.RuneDecomposeRequest.class, com.dxx.game.dto.RuneProto.RuneDecomposeRequest.Builder.class);
    }

    private int bitField0_;
    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }

    public static final int ROWIDS_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.LongList rowIds_ =
        emptyLongList();
    /**
     * <pre>
     * 符文Ids
     * </pre>
     *
     * <code>repeated int64 rowIds = 2;</code>
     * @return A list containing the rowIds.
     */
    @java.lang.Override
    public java.util.List<java.lang.Long>
        getRowIdsList() {
      return rowIds_;
    }
    /**
     * <pre>
     * 符文Ids
     * </pre>
     *
     * <code>repeated int64 rowIds = 2;</code>
     * @return The count of rowIds.
     */
    public int getRowIdsCount() {
      return rowIds_.size();
    }
    /**
     * <pre>
     * 符文Ids
     * </pre>
     *
     * <code>repeated int64 rowIds = 2;</code>
     * @param index The index of the element to return.
     * @return The rowIds at the given index.
     */
    public long getRowIds(int index) {
      return rowIds_.getLong(index);
    }
    private int rowIdsMemoizedSerializedSize = -1;

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getCommonParams());
      }
      if (getRowIdsList().size() > 0) {
        output.writeUInt32NoTag(18);
        output.writeUInt32NoTag(rowIdsMemoizedSerializedSize);
      }
      for (int i = 0; i < rowIds_.size(); i++) {
        output.writeInt64NoTag(rowIds_.getLong(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      {
        int dataSize = 0;
        for (int i = 0; i < rowIds_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt64SizeNoTag(rowIds_.getLong(i));
        }
        size += dataSize;
        if (!getRowIdsList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        rowIdsMemoizedSerializedSize = dataSize;
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.RuneProto.RuneDecomposeRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.RuneProto.RuneDecomposeRequest other = (com.dxx.game.dto.RuneProto.RuneDecomposeRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (!getRowIdsList()
          .equals(other.getRowIdsList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      if (getRowIdsCount() > 0) {
        hash = (37 * hash) + ROWIDS_FIELD_NUMBER;
        hash = (53 * hash) + getRowIdsList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.RuneProto.RuneDecomposeRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.RuneProto.RuneDecomposeRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.RuneProto.RuneDecomposeRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.RuneProto.RuneDecomposeRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.RuneProto.RuneDecomposeRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.RuneProto.RuneDecomposeRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.RuneProto.RuneDecomposeRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.RuneProto.RuneDecomposeRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.RuneProto.RuneDecomposeRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.RuneProto.RuneDecomposeRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.RuneProto.RuneDecomposeRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.RuneProto.RuneDecomposeRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.RuneProto.RuneDecomposeRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=11129 符文-分解
     * </pre>
     *
     * Protobuf type {@code Proto.Rune.RuneDecomposeRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Rune.RuneDecomposeRequest)
        com.dxx.game.dto.RuneProto.RuneDecomposeRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.RuneProto.internal_static_Proto_Rune_RuneDecomposeRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.RuneProto.internal_static_Proto_Rune_RuneDecomposeRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.RuneProto.RuneDecomposeRequest.class, com.dxx.game.dto.RuneProto.RuneDecomposeRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.RuneProto.RuneDecomposeRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getCommonParamsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        rowIds_ = emptyLongList();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.RuneProto.internal_static_Proto_Rune_RuneDecomposeRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.RuneProto.RuneDecomposeRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.RuneProto.RuneDecomposeRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.RuneProto.RuneDecomposeRequest build() {
        com.dxx.game.dto.RuneProto.RuneDecomposeRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.RuneProto.RuneDecomposeRequest buildPartial() {
        com.dxx.game.dto.RuneProto.RuneDecomposeRequest result = new com.dxx.game.dto.RuneProto.RuneDecomposeRequest(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.RuneProto.RuneDecomposeRequest result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.commonParams_ = commonParamsBuilder_ == null
              ? commonParams_
              : commonParamsBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          rowIds_.makeImmutable();
          result.rowIds_ = rowIds_;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.RuneProto.RuneDecomposeRequest) {
          return mergeFrom((com.dxx.game.dto.RuneProto.RuneDecomposeRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.RuneProto.RuneDecomposeRequest other) {
        if (other == com.dxx.game.dto.RuneProto.RuneDecomposeRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        if (!other.rowIds_.isEmpty()) {
          if (rowIds_.isEmpty()) {
            rowIds_ = other.rowIds_;
            rowIds_.makeImmutable();
            bitField0_ |= 0x00000002;
          } else {
            ensureRowIdsIsMutable();
            rowIds_.addAll(other.rowIds_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getCommonParamsFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 16: {
                long v = input.readInt64();
                ensureRowIdsIsMutable();
                rowIds_.addLong(v);
                break;
              } // case 16
              case 18: {
                int length = input.readRawVarint32();
                int limit = input.pushLimit(length);
                ensureRowIdsIsMutable();
                while (input.getBytesUntilLimit() > 0) {
                  rowIds_.addLong(input.readInt64());
                }
                input.popLimit(limit);
                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
        } else {
          commonParamsBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
            commonParams_ != null &&
            commonParams_ != com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance()) {
            getCommonParamsBuilder().mergeFrom(value);
          } else {
            commonParams_ = value;
          }
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }
        if (commonParams_ != null) {
          bitField0_ |= 0x00000001;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        bitField0_ = (bitField0_ & ~0x00000001);
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }

      private com.google.protobuf.Internal.LongList rowIds_ = emptyLongList();
      private void ensureRowIdsIsMutable() {
        if (!rowIds_.isModifiable()) {
          rowIds_ = makeMutableCopy(rowIds_);
        }
        bitField0_ |= 0x00000002;
      }
      /**
       * <pre>
       * 符文Ids
       * </pre>
       *
       * <code>repeated int64 rowIds = 2;</code>
       * @return A list containing the rowIds.
       */
      public java.util.List<java.lang.Long>
          getRowIdsList() {
        rowIds_.makeImmutable();
        return rowIds_;
      }
      /**
       * <pre>
       * 符文Ids
       * </pre>
       *
       * <code>repeated int64 rowIds = 2;</code>
       * @return The count of rowIds.
       */
      public int getRowIdsCount() {
        return rowIds_.size();
      }
      /**
       * <pre>
       * 符文Ids
       * </pre>
       *
       * <code>repeated int64 rowIds = 2;</code>
       * @param index The index of the element to return.
       * @return The rowIds at the given index.
       */
      public long getRowIds(int index) {
        return rowIds_.getLong(index);
      }
      /**
       * <pre>
       * 符文Ids
       * </pre>
       *
       * <code>repeated int64 rowIds = 2;</code>
       * @param index The index to set the value at.
       * @param value The rowIds to set.
       * @return This builder for chaining.
       */
      public Builder setRowIds(
          int index, long value) {

        ensureRowIdsIsMutable();
        rowIds_.setLong(index, value);
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 符文Ids
       * </pre>
       *
       * <code>repeated int64 rowIds = 2;</code>
       * @param value The rowIds to add.
       * @return This builder for chaining.
       */
      public Builder addRowIds(long value) {

        ensureRowIdsIsMutable();
        rowIds_.addLong(value);
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 符文Ids
       * </pre>
       *
       * <code>repeated int64 rowIds = 2;</code>
       * @param values The rowIds to add.
       * @return This builder for chaining.
       */
      public Builder addAllRowIds(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensureRowIdsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, rowIds_);
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 符文Ids
       * </pre>
       *
       * <code>repeated int64 rowIds = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearRowIds() {
        rowIds_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Rune.RuneDecomposeRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.Rune.RuneDecomposeRequest)
    private static final com.dxx.game.dto.RuneProto.RuneDecomposeRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.RuneProto.RuneDecomposeRequest();
    }

    public static com.dxx.game.dto.RuneProto.RuneDecomposeRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<RuneDecomposeRequest>
        PARSER = new com.google.protobuf.AbstractParser<RuneDecomposeRequest>() {
      @java.lang.Override
      public RuneDecomposeRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<RuneDecomposeRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<RuneDecomposeRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.RuneProto.RuneDecomposeRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface RuneDecomposeResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Rune.RuneDecomposeResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <pre>
     * 符文Ids
     * </pre>
     *
     * <code>repeated int64 rowIds = 2;</code>
     * @return A list containing the rowIds.
     */
    java.util.List<java.lang.Long> getRowIdsList();
    /**
     * <pre>
     * 符文Ids
     * </pre>
     *
     * <code>repeated int64 rowIds = 2;</code>
     * @return The count of rowIds.
     */
    int getRowIdsCount();
    /**
     * <pre>
     * 符文Ids
     * </pre>
     *
     * <code>repeated int64 rowIds = 2;</code>
     * @param index The index of the element to return.
     * @return The rowIds at the given index.
     */
    long getRowIds(int index);
  }
  /**
   * <pre>
   * CMD PackageId=11130 符文-分解
   * </pre>
   *
   * Protobuf type {@code Proto.Rune.RuneDecomposeResponse}
   */
  public static final class RuneDecomposeResponse extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Rune.RuneDecomposeResponse)
      RuneDecomposeResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        RuneDecomposeResponse.class.getName());
    }
    // Use RuneDecomposeResponse.newBuilder() to construct.
    private RuneDecomposeResponse(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private RuneDecomposeResponse() {
      rowIds_ = emptyLongList();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.RuneProto.internal_static_Proto_Rune_RuneDecomposeResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.RuneProto.internal_static_Proto_Rune_RuneDecomposeResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.RuneProto.RuneDecomposeResponse.class, com.dxx.game.dto.RuneProto.RuneDecomposeResponse.Builder.class);
    }

    public static final int CODE_FIELD_NUMBER = 1;
    private int code_ = 0;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int ROWIDS_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.LongList rowIds_ =
        emptyLongList();
    /**
     * <pre>
     * 符文Ids
     * </pre>
     *
     * <code>repeated int64 rowIds = 2;</code>
     * @return A list containing the rowIds.
     */
    @java.lang.Override
    public java.util.List<java.lang.Long>
        getRowIdsList() {
      return rowIds_;
    }
    /**
     * <pre>
     * 符文Ids
     * </pre>
     *
     * <code>repeated int64 rowIds = 2;</code>
     * @return The count of rowIds.
     */
    public int getRowIdsCount() {
      return rowIds_.size();
    }
    /**
     * <pre>
     * 符文Ids
     * </pre>
     *
     * <code>repeated int64 rowIds = 2;</code>
     * @param index The index of the element to return.
     * @return The rowIds at the given index.
     */
    public long getRowIds(int index) {
      return rowIds_.getLong(index);
    }
    private int rowIdsMemoizedSerializedSize = -1;

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getSerializedSize();
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (getRowIdsList().size() > 0) {
        output.writeUInt32NoTag(18);
        output.writeUInt32NoTag(rowIdsMemoizedSerializedSize);
      }
      for (int i = 0; i < rowIds_.size(); i++) {
        output.writeInt64NoTag(rowIds_.getLong(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < rowIds_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt64SizeNoTag(rowIds_.getLong(i));
        }
        size += dataSize;
        if (!getRowIdsList().isEmpty()) {
          size += 1;
          size += com.google.protobuf.CodedOutputStream
              .computeInt32SizeNoTag(dataSize);
        }
        rowIdsMemoizedSerializedSize = dataSize;
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.RuneProto.RuneDecomposeResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.RuneProto.RuneDecomposeResponse other = (com.dxx.game.dto.RuneProto.RuneDecomposeResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (!getRowIdsList()
          .equals(other.getRowIdsList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      if (getRowIdsCount() > 0) {
        hash = (37 * hash) + ROWIDS_FIELD_NUMBER;
        hash = (53 * hash) + getRowIdsList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.RuneProto.RuneDecomposeResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.RuneProto.RuneDecomposeResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.RuneProto.RuneDecomposeResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.RuneProto.RuneDecomposeResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.RuneProto.RuneDecomposeResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.RuneProto.RuneDecomposeResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.RuneProto.RuneDecomposeResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.RuneProto.RuneDecomposeResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.RuneProto.RuneDecomposeResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.RuneProto.RuneDecomposeResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.RuneProto.RuneDecomposeResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.RuneProto.RuneDecomposeResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.RuneProto.RuneDecomposeResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=11130 符文-分解
     * </pre>
     *
     * Protobuf type {@code Proto.Rune.RuneDecomposeResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Rune.RuneDecomposeResponse)
        com.dxx.game.dto.RuneProto.RuneDecomposeResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.RuneProto.internal_static_Proto_Rune_RuneDecomposeResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.RuneProto.internal_static_Proto_Rune_RuneDecomposeResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.RuneProto.RuneDecomposeResponse.class, com.dxx.game.dto.RuneProto.RuneDecomposeResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.RuneProto.RuneDecomposeResponse.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        code_ = 0;
        rowIds_ = emptyLongList();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.RuneProto.internal_static_Proto_Rune_RuneDecomposeResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.RuneProto.RuneDecomposeResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.RuneProto.RuneDecomposeResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.RuneProto.RuneDecomposeResponse build() {
        com.dxx.game.dto.RuneProto.RuneDecomposeResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.RuneProto.RuneDecomposeResponse buildPartial() {
        com.dxx.game.dto.RuneProto.RuneDecomposeResponse result = new com.dxx.game.dto.RuneProto.RuneDecomposeResponse(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.RuneProto.RuneDecomposeResponse result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.code_ = code_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          rowIds_.makeImmutable();
          result.rowIds_ = rowIds_;
        }
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.RuneProto.RuneDecomposeResponse) {
          return mergeFrom((com.dxx.game.dto.RuneProto.RuneDecomposeResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.RuneProto.RuneDecomposeResponse other) {
        if (other == com.dxx.game.dto.RuneProto.RuneDecomposeResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (!other.rowIds_.isEmpty()) {
          if (rowIds_.isEmpty()) {
            rowIds_ = other.rowIds_;
            rowIds_.makeImmutable();
            bitField0_ |= 0x00000002;
          } else {
            ensureRowIdsIsMutable();
            rowIds_.addAll(other.rowIds_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                code_ = input.readInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                long v = input.readInt64();
                ensureRowIdsIsMutable();
                rowIds_.addLong(v);
                break;
              } // case 16
              case 18: {
                int length = input.readRawVarint32();
                int limit = input.pushLimit(length);
                ensureRowIdsIsMutable();
                while (input.getBytesUntilLimit() > 0) {
                  rowIds_.addLong(input.readInt64());
                }
                input.popLimit(limit);
                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {

        code_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        bitField0_ = (bitField0_ & ~0x00000001);
        code_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.LongList rowIds_ = emptyLongList();
      private void ensureRowIdsIsMutable() {
        if (!rowIds_.isModifiable()) {
          rowIds_ = makeMutableCopy(rowIds_);
        }
        bitField0_ |= 0x00000002;
      }
      /**
       * <pre>
       * 符文Ids
       * </pre>
       *
       * <code>repeated int64 rowIds = 2;</code>
       * @return A list containing the rowIds.
       */
      public java.util.List<java.lang.Long>
          getRowIdsList() {
        rowIds_.makeImmutable();
        return rowIds_;
      }
      /**
       * <pre>
       * 符文Ids
       * </pre>
       *
       * <code>repeated int64 rowIds = 2;</code>
       * @return The count of rowIds.
       */
      public int getRowIdsCount() {
        return rowIds_.size();
      }
      /**
       * <pre>
       * 符文Ids
       * </pre>
       *
       * <code>repeated int64 rowIds = 2;</code>
       * @param index The index of the element to return.
       * @return The rowIds at the given index.
       */
      public long getRowIds(int index) {
        return rowIds_.getLong(index);
      }
      /**
       * <pre>
       * 符文Ids
       * </pre>
       *
       * <code>repeated int64 rowIds = 2;</code>
       * @param index The index to set the value at.
       * @param value The rowIds to set.
       * @return This builder for chaining.
       */
      public Builder setRowIds(
          int index, long value) {

        ensureRowIdsIsMutable();
        rowIds_.setLong(index, value);
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 符文Ids
       * </pre>
       *
       * <code>repeated int64 rowIds = 2;</code>
       * @param value The rowIds to add.
       * @return This builder for chaining.
       */
      public Builder addRowIds(long value) {

        ensureRowIdsIsMutable();
        rowIds_.addLong(value);
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 符文Ids
       * </pre>
       *
       * <code>repeated int64 rowIds = 2;</code>
       * @param values The rowIds to add.
       * @return This builder for chaining.
       */
      public Builder addAllRowIds(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensureRowIdsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, rowIds_);
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 符文Ids
       * </pre>
       *
       * <code>repeated int64 rowIds = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearRowIds() {
        rowIds_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Rune.RuneDecomposeResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.Rune.RuneDecomposeResponse)
    private static final com.dxx.game.dto.RuneProto.RuneDecomposeResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.RuneProto.RuneDecomposeResponse();
    }

    public static com.dxx.game.dto.RuneProto.RuneDecomposeResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<RuneDecomposeResponse>
        PARSER = new com.google.protobuf.AbstractParser<RuneDecomposeResponse>() {
      @java.lang.Override
      public RuneDecomposeResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<RuneDecomposeResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<RuneDecomposeResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.RuneProto.RuneDecomposeResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Rune_RuneStrengthenRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Rune_RuneStrengthenRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Rune_RuneStrengthenResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Rune_RuneStrengthenResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Rune_RuneLockOrUnLockRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Rune_RuneLockOrUnLockRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Rune_RuneLockOrUnLockResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Rune_RuneLockOrUnLockResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Rune_RunePutOnOrTakeOffRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Rune_RunePutOnOrTakeOffRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Rune_RunePutOnOrTakeOffResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Rune_RunePutOnOrTakeOffResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Rune_RuneSnatchRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Rune_RuneSnatchRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Rune_RuneSnatchResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Rune_RuneSnatchResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Rune_RuneDecomposeRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Rune_RuneDecomposeRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Rune_RuneDecomposeResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Rune_RuneDecomposeResponse_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\nrune.proto\022\nProto.Rune\032\014common.proto\"X" +
      "\n\025RuneStrengthenRequest\0220\n\014commonParams\030" +
      "\001 \001(\0132\032.Proto.Common.CommonParams\022\r\n\005row" +
      "Id\030\002 \001(\003\"K\n\026RuneStrengthenResponse\022\014\n\004co" +
      "de\030\001 \001(\005\022#\n\004rune\030\002 \001(\0132\025.Proto.Common.Ru" +
      "neDto\"j\n\027RuneLockOrUnLockRequest\0220\n\014comm" +
      "onParams\030\001 \001(\0132\032.Proto.Common.CommonPara" +
      "ms\022\r\n\005rowId\030\002 \001(\003\022\016\n\006isLock\030\003 \001(\010\"G\n\030Run" +
      "eLockOrUnLockResponse\022\014\n\004code\030\001 \001(\005\022\r\n\005r" +
      "owId\030\002 \001(\003\022\016\n\006isLock\030\003 \001(\010\"~\n\031RunePutOnO" +
      "rTakeOffRequest\0220\n\014commonParams\030\001 \001(\0132\032." +
      "Proto.Common.CommonParams\022\017\n\007isPutOn\030\002 \001" +
      "(\010\022\016\n\006rowIds\030\003 \003(\003\022\016\n\006heroId\030\004 \001(\005\"[\n\032Ru" +
      "nePutOnOrTakeOffResponse\022\014\n\004code\030\001 \001(\005\022\017" +
      "\n\007isPutOn\030\002 \001(\010\022\016\n\006rowIds\030\003 \003(\003\022\016\n\006heroI" +
      "d\030\004 \001(\005\"p\n\021RuneSnatchRequest\0220\n\014commonPa" +
      "rams\030\001 \001(\0132\032.Proto.Common.CommonParams\022\023" +
      "\n\013snatchRowId\030\002 \001(\003\022\024\n\014snatchHeroId\030\003 \001(" +
      "\005\"M\n\022RuneSnatchResponse\022\014\n\004code\030\001 \001(\005\022\023\n" +
      "\013snatchRowId\030\002 \001(\003\022\024\n\014snatchHeroId\030\003 \001(\005" +
      "\"X\n\024RuneDecomposeRequest\0220\n\014commonParams" +
      "\030\001 \001(\0132\032.Proto.Common.CommonParams\022\016\n\006ro" +
      "wIds\030\002 \003(\003\"5\n\025RuneDecomposeResponse\022\014\n\004c" +
      "ode\030\001 \001(\005\022\016\n\006rowIds\030\002 \003(\003B\035\n\020com.dxx.gam" +
      "e.dtoB\tRuneProtob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.dxx.game.dto.CommonProto.getDescriptor(),
        });
    internal_static_Proto_Rune_RuneStrengthenRequest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_Proto_Rune_RuneStrengthenRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Rune_RuneStrengthenRequest_descriptor,
        new java.lang.String[] { "CommonParams", "RowId", });
    internal_static_Proto_Rune_RuneStrengthenResponse_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_Proto_Rune_RuneStrengthenResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Rune_RuneStrengthenResponse_descriptor,
        new java.lang.String[] { "Code", "Rune", });
    internal_static_Proto_Rune_RuneLockOrUnLockRequest_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_Proto_Rune_RuneLockOrUnLockRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Rune_RuneLockOrUnLockRequest_descriptor,
        new java.lang.String[] { "CommonParams", "RowId", "IsLock", });
    internal_static_Proto_Rune_RuneLockOrUnLockResponse_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_Proto_Rune_RuneLockOrUnLockResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Rune_RuneLockOrUnLockResponse_descriptor,
        new java.lang.String[] { "Code", "RowId", "IsLock", });
    internal_static_Proto_Rune_RunePutOnOrTakeOffRequest_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_Proto_Rune_RunePutOnOrTakeOffRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Rune_RunePutOnOrTakeOffRequest_descriptor,
        new java.lang.String[] { "CommonParams", "IsPutOn", "RowIds", "HeroId", });
    internal_static_Proto_Rune_RunePutOnOrTakeOffResponse_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_Proto_Rune_RunePutOnOrTakeOffResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Rune_RunePutOnOrTakeOffResponse_descriptor,
        new java.lang.String[] { "Code", "IsPutOn", "RowIds", "HeroId", });
    internal_static_Proto_Rune_RuneSnatchRequest_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_Proto_Rune_RuneSnatchRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Rune_RuneSnatchRequest_descriptor,
        new java.lang.String[] { "CommonParams", "SnatchRowId", "SnatchHeroId", });
    internal_static_Proto_Rune_RuneSnatchResponse_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_Proto_Rune_RuneSnatchResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Rune_RuneSnatchResponse_descriptor,
        new java.lang.String[] { "Code", "SnatchRowId", "SnatchHeroId", });
    internal_static_Proto_Rune_RuneDecomposeRequest_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_Proto_Rune_RuneDecomposeRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Rune_RuneDecomposeRequest_descriptor,
        new java.lang.String[] { "CommonParams", "RowIds", });
    internal_static_Proto_Rune_RuneDecomposeResponse_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_Proto_Rune_RuneDecomposeResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Rune_RuneDecomposeResponse_descriptor,
        new java.lang.String[] { "Code", "RowIds", });
    descriptor.resolveAllFeaturesImmutable();
    com.dxx.game.dto.CommonProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
