// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: sevenday.proto
// Protobuf Java Version: 4.29.0

package com.dxx.game.dto;

/**
 * <pre>
 * 嘉年华数据
 * </pre>
 *
 * Protobuf type {@code Proto.SevenDay.SevenDayDto}
 */
public final class SevenDayDto extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:Proto.SevenDay.SevenDayDto)
    SevenDayDtoOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 29,
      /* patch= */ 0,
      /* suffix= */ "",
      SevenDayDto.class.getName());
  }
  // Use SevenDayDto.newBuilder() to construct.
  private SevenDayDto(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private SevenDayDto() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.dxx.game.dto.SevenDayProto.internal_static_Proto_SevenDay_SevenDayDto_descriptor;
  }

  @SuppressWarnings({"rawtypes"})
  @java.lang.Override
  protected com.google.protobuf.MapFieldReflectionAccessor internalGetMapFieldReflection(
      int number) {
    switch (number) {
      case 6:
        return internalGetPayBuyTimes();
      default:
        throw new RuntimeException(
            "Invalid map field number: " + number);
    }
  }
  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.dxx.game.dto.SevenDayProto.internal_static_Proto_SevenDay_SevenDayDto_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.dxx.game.dto.SevenDayDto.class, com.dxx.game.dto.SevenDayDto.Builder.class);
  }

  public static final int GROUP_FIELD_NUMBER = 1;
  private int group_ = 0;
  /**
   * <pre>
   * group
   * </pre>
   *
   * <code>int32 group = 1;</code>
   * @return The group.
   */
  @java.lang.Override
  public int getGroup() {
    return group_;
  }

  public static final int STARTTIME_FIELD_NUMBER = 2;
  private long startTime_ = 0L;
  /**
   * <pre>
   * 开始时间
   * </pre>
   *
   * <code>int64 startTime = 2;</code>
   * @return The startTime.
   */
  @java.lang.Override
  public long getStartTime() {
    return startTime_;
  }

  public static final int ENDTIME_FIELD_NUMBER = 3;
  private long endTime_ = 0L;
  /**
   * <pre>
   * 结束时间
   * </pre>
   *
   * <code>int64 endTime = 3;</code>
   * @return The endTime.
   */
  @java.lang.Override
  public long getEndTime() {
    return endTime_;
  }

  public static final int ACTIVE_FIELD_NUMBER = 4;
  private int active_ = 0;
  /**
   * <pre>
   * 当前活跃度
   * </pre>
   *
   * <code>int32 active = 4;</code>
   * @return The active.
   */
  @java.lang.Override
  public int getActive() {
    return active_;
  }

  public static final int ACTIVELOG_FIELD_NUMBER = 5;
  private long activeLog_ = 0L;
  /**
   * <pre>
   * 活跃度奖励领取记录
   * </pre>
   *
   * <code>int64 activeLog = 5;</code>
   * @return The activeLog.
   */
  @java.lang.Override
  public long getActiveLog() {
    return activeLog_;
  }

  public static final int PAYBUYTIMES_FIELD_NUMBER = 6;
  private static final class PayBuyTimesDefaultEntryHolder {
    static final com.google.protobuf.MapEntry<
        java.lang.Integer, java.lang.Integer> defaultEntry =
            com.google.protobuf.MapEntry
            .<java.lang.Integer, java.lang.Integer>newDefaultInstance(
                com.dxx.game.dto.SevenDayProto.internal_static_Proto_SevenDay_SevenDayDto_PayBuyTimesEntry_descriptor, 
                com.google.protobuf.WireFormat.FieldType.INT32,
                0,
                com.google.protobuf.WireFormat.FieldType.INT32,
                0);
  }
  @SuppressWarnings("serial")
  private com.google.protobuf.MapField<
      java.lang.Integer, java.lang.Integer> payBuyTimes_;
  private com.google.protobuf.MapField<java.lang.Integer, java.lang.Integer>
  internalGetPayBuyTimes() {
    if (payBuyTimes_ == null) {
      return com.google.protobuf.MapField.emptyMapField(
          PayBuyTimesDefaultEntryHolder.defaultEntry);
    }
    return payBuyTimes_;
  }
  public int getPayBuyTimesCount() {
    return internalGetPayBuyTimes().getMap().size();
  }
  /**
   * <pre>
   * SevenDayPay的购买记录
   * </pre>
   *
   * <code>map&lt;int32, int32&gt; payBuyTimes = 6;</code>
   */
  @java.lang.Override
  public boolean containsPayBuyTimes(
      int key) {

    return internalGetPayBuyTimes().getMap().containsKey(key);
  }
  /**
   * Use {@link #getPayBuyTimesMap()} instead.
   */
  @java.lang.Override
  @java.lang.Deprecated
  public java.util.Map<java.lang.Integer, java.lang.Integer> getPayBuyTimes() {
    return getPayBuyTimesMap();
  }
  /**
   * <pre>
   * SevenDayPay的购买记录
   * </pre>
   *
   * <code>map&lt;int32, int32&gt; payBuyTimes = 6;</code>
   */
  @java.lang.Override
  public java.util.Map<java.lang.Integer, java.lang.Integer> getPayBuyTimesMap() {
    return internalGetPayBuyTimes().getMap();
  }
  /**
   * <pre>
   * SevenDayPay的购买记录
   * </pre>
   *
   * <code>map&lt;int32, int32&gt; payBuyTimes = 6;</code>
   */
  @java.lang.Override
  public int getPayBuyTimesOrDefault(
      int key,
      int defaultValue) {

    java.util.Map<java.lang.Integer, java.lang.Integer> map =
        internalGetPayBuyTimes().getMap();
    return map.containsKey(key) ? map.get(key) : defaultValue;
  }
  /**
   * <pre>
   * SevenDayPay的购买记录
   * </pre>
   *
   * <code>map&lt;int32, int32&gt; payBuyTimes = 6;</code>
   */
  @java.lang.Override
  public int getPayBuyTimesOrThrow(
      int key) {

    java.util.Map<java.lang.Integer, java.lang.Integer> map =
        internalGetPayBuyTimes().getMap();
    if (!map.containsKey(key)) {
      throw new java.lang.IllegalArgumentException();
    }
    return map.get(key);
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (group_ != 0) {
      output.writeInt32(1, group_);
    }
    if (startTime_ != 0L) {
      output.writeInt64(2, startTime_);
    }
    if (endTime_ != 0L) {
      output.writeInt64(3, endTime_);
    }
    if (active_ != 0) {
      output.writeInt32(4, active_);
    }
    if (activeLog_ != 0L) {
      output.writeInt64(5, activeLog_);
    }
    com.google.protobuf.GeneratedMessage
      .serializeIntegerMapTo(
        output,
        internalGetPayBuyTimes(),
        PayBuyTimesDefaultEntryHolder.defaultEntry,
        6);
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (group_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, group_);
    }
    if (startTime_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(2, startTime_);
    }
    if (endTime_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(3, endTime_);
    }
    if (active_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(4, active_);
    }
    if (activeLog_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(5, activeLog_);
    }
    for (java.util.Map.Entry<java.lang.Integer, java.lang.Integer> entry
         : internalGetPayBuyTimes().getMap().entrySet()) {
      com.google.protobuf.MapEntry<java.lang.Integer, java.lang.Integer>
      payBuyTimes__ = PayBuyTimesDefaultEntryHolder.defaultEntry.newBuilderForType()
          .setKey(entry.getKey())
          .setValue(entry.getValue())
          .build();
      size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(6, payBuyTimes__);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.dxx.game.dto.SevenDayDto)) {
      return super.equals(obj);
    }
    com.dxx.game.dto.SevenDayDto other = (com.dxx.game.dto.SevenDayDto) obj;

    if (getGroup()
        != other.getGroup()) return false;
    if (getStartTime()
        != other.getStartTime()) return false;
    if (getEndTime()
        != other.getEndTime()) return false;
    if (getActive()
        != other.getActive()) return false;
    if (getActiveLog()
        != other.getActiveLog()) return false;
    if (!internalGetPayBuyTimes().equals(
        other.internalGetPayBuyTimes())) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + GROUP_FIELD_NUMBER;
    hash = (53 * hash) + getGroup();
    hash = (37 * hash) + STARTTIME_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getStartTime());
    hash = (37 * hash) + ENDTIME_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getEndTime());
    hash = (37 * hash) + ACTIVE_FIELD_NUMBER;
    hash = (53 * hash) + getActive();
    hash = (37 * hash) + ACTIVELOG_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getActiveLog());
    if (!internalGetPayBuyTimes().getMap().isEmpty()) {
      hash = (37 * hash) + PAYBUYTIMES_FIELD_NUMBER;
      hash = (53 * hash) + internalGetPayBuyTimes().hashCode();
    }
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.dxx.game.dto.SevenDayDto parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.dxx.game.dto.SevenDayDto parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.dxx.game.dto.SevenDayDto parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.dxx.game.dto.SevenDayDto parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.dxx.game.dto.SevenDayDto parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.dxx.game.dto.SevenDayDto parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.dxx.game.dto.SevenDayDto parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static com.dxx.game.dto.SevenDayDto parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.dxx.game.dto.SevenDayDto parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.dxx.game.dto.SevenDayDto parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.dxx.game.dto.SevenDayDto parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static com.dxx.game.dto.SevenDayDto parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.dxx.game.dto.SevenDayDto prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * <pre>
   * 嘉年华数据
   * </pre>
   *
   * Protobuf type {@code Proto.SevenDay.SevenDayDto}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:Proto.SevenDay.SevenDayDto)
      com.dxx.game.dto.SevenDayDtoOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.SevenDayProto.internal_static_Proto_SevenDay_SevenDayDto_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    protected com.google.protobuf.MapFieldReflectionAccessor internalGetMapFieldReflection(
        int number) {
      switch (number) {
        case 6:
          return internalGetPayBuyTimes();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @SuppressWarnings({"rawtypes"})
    protected com.google.protobuf.MapFieldReflectionAccessor internalGetMutableMapFieldReflection(
        int number) {
      switch (number) {
        case 6:
          return internalGetMutablePayBuyTimes();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.SevenDayProto.internal_static_Proto_SevenDay_SevenDayDto_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.SevenDayDto.class, com.dxx.game.dto.SevenDayDto.Builder.class);
    }

    // Construct using com.dxx.game.dto.SevenDayDto.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      group_ = 0;
      startTime_ = 0L;
      endTime_ = 0L;
      active_ = 0;
      activeLog_ = 0L;
      internalGetMutablePayBuyTimes().clear();
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.dxx.game.dto.SevenDayProto.internal_static_Proto_SevenDay_SevenDayDto_descriptor;
    }

    @java.lang.Override
    public com.dxx.game.dto.SevenDayDto getDefaultInstanceForType() {
      return com.dxx.game.dto.SevenDayDto.getDefaultInstance();
    }

    @java.lang.Override
    public com.dxx.game.dto.SevenDayDto build() {
      com.dxx.game.dto.SevenDayDto result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.dxx.game.dto.SevenDayDto buildPartial() {
      com.dxx.game.dto.SevenDayDto result = new com.dxx.game.dto.SevenDayDto(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(com.dxx.game.dto.SevenDayDto result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.group_ = group_;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.startTime_ = startTime_;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.endTime_ = endTime_;
      }
      if (((from_bitField0_ & 0x00000008) != 0)) {
        result.active_ = active_;
      }
      if (((from_bitField0_ & 0x00000010) != 0)) {
        result.activeLog_ = activeLog_;
      }
      if (((from_bitField0_ & 0x00000020) != 0)) {
        result.payBuyTimes_ = internalGetPayBuyTimes();
        result.payBuyTimes_.makeImmutable();
      }
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.dxx.game.dto.SevenDayDto) {
        return mergeFrom((com.dxx.game.dto.SevenDayDto)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.dxx.game.dto.SevenDayDto other) {
      if (other == com.dxx.game.dto.SevenDayDto.getDefaultInstance()) return this;
      if (other.getGroup() != 0) {
        setGroup(other.getGroup());
      }
      if (other.getStartTime() != 0L) {
        setStartTime(other.getStartTime());
      }
      if (other.getEndTime() != 0L) {
        setEndTime(other.getEndTime());
      }
      if (other.getActive() != 0) {
        setActive(other.getActive());
      }
      if (other.getActiveLog() != 0L) {
        setActiveLog(other.getActiveLog());
      }
      internalGetMutablePayBuyTimes().mergeFrom(
          other.internalGetPayBuyTimes());
      bitField0_ |= 0x00000020;
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              group_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              startTime_ = input.readInt64();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 24: {
              endTime_ = input.readInt64();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            case 32: {
              active_ = input.readInt32();
              bitField0_ |= 0x00000008;
              break;
            } // case 32
            case 40: {
              activeLog_ = input.readInt64();
              bitField0_ |= 0x00000010;
              break;
            } // case 40
            case 50: {
              com.google.protobuf.MapEntry<java.lang.Integer, java.lang.Integer>
              payBuyTimes__ = input.readMessage(
                  PayBuyTimesDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              internalGetMutablePayBuyTimes().getMutableMap().put(
                  payBuyTimes__.getKey(), payBuyTimes__.getValue());
              bitField0_ |= 0x00000020;
              break;
            } // case 50
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int group_ ;
    /**
     * <pre>
     * group
     * </pre>
     *
     * <code>int32 group = 1;</code>
     * @return The group.
     */
    @java.lang.Override
    public int getGroup() {
      return group_;
    }
    /**
     * <pre>
     * group
     * </pre>
     *
     * <code>int32 group = 1;</code>
     * @param value The group to set.
     * @return This builder for chaining.
     */
    public Builder setGroup(int value) {

      group_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * group
     * </pre>
     *
     * <code>int32 group = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearGroup() {
      bitField0_ = (bitField0_ & ~0x00000001);
      group_ = 0;
      onChanged();
      return this;
    }

    private long startTime_ ;
    /**
     * <pre>
     * 开始时间
     * </pre>
     *
     * <code>int64 startTime = 2;</code>
     * @return The startTime.
     */
    @java.lang.Override
    public long getStartTime() {
      return startTime_;
    }
    /**
     * <pre>
     * 开始时间
     * </pre>
     *
     * <code>int64 startTime = 2;</code>
     * @param value The startTime to set.
     * @return This builder for chaining.
     */
    public Builder setStartTime(long value) {

      startTime_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 开始时间
     * </pre>
     *
     * <code>int64 startTime = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearStartTime() {
      bitField0_ = (bitField0_ & ~0x00000002);
      startTime_ = 0L;
      onChanged();
      return this;
    }

    private long endTime_ ;
    /**
     * <pre>
     * 结束时间
     * </pre>
     *
     * <code>int64 endTime = 3;</code>
     * @return The endTime.
     */
    @java.lang.Override
    public long getEndTime() {
      return endTime_;
    }
    /**
     * <pre>
     * 结束时间
     * </pre>
     *
     * <code>int64 endTime = 3;</code>
     * @param value The endTime to set.
     * @return This builder for chaining.
     */
    public Builder setEndTime(long value) {

      endTime_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 结束时间
     * </pre>
     *
     * <code>int64 endTime = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearEndTime() {
      bitField0_ = (bitField0_ & ~0x00000004);
      endTime_ = 0L;
      onChanged();
      return this;
    }

    private int active_ ;
    /**
     * <pre>
     * 当前活跃度
     * </pre>
     *
     * <code>int32 active = 4;</code>
     * @return The active.
     */
    @java.lang.Override
    public int getActive() {
      return active_;
    }
    /**
     * <pre>
     * 当前活跃度
     * </pre>
     *
     * <code>int32 active = 4;</code>
     * @param value The active to set.
     * @return This builder for chaining.
     */
    public Builder setActive(int value) {

      active_ = value;
      bitField0_ |= 0x00000008;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 当前活跃度
     * </pre>
     *
     * <code>int32 active = 4;</code>
     * @return This builder for chaining.
     */
    public Builder clearActive() {
      bitField0_ = (bitField0_ & ~0x00000008);
      active_ = 0;
      onChanged();
      return this;
    }

    private long activeLog_ ;
    /**
     * <pre>
     * 活跃度奖励领取记录
     * </pre>
     *
     * <code>int64 activeLog = 5;</code>
     * @return The activeLog.
     */
    @java.lang.Override
    public long getActiveLog() {
      return activeLog_;
    }
    /**
     * <pre>
     * 活跃度奖励领取记录
     * </pre>
     *
     * <code>int64 activeLog = 5;</code>
     * @param value The activeLog to set.
     * @return This builder for chaining.
     */
    public Builder setActiveLog(long value) {

      activeLog_ = value;
      bitField0_ |= 0x00000010;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 活跃度奖励领取记录
     * </pre>
     *
     * <code>int64 activeLog = 5;</code>
     * @return This builder for chaining.
     */
    public Builder clearActiveLog() {
      bitField0_ = (bitField0_ & ~0x00000010);
      activeLog_ = 0L;
      onChanged();
      return this;
    }

    private com.google.protobuf.MapField<
        java.lang.Integer, java.lang.Integer> payBuyTimes_;
    private com.google.protobuf.MapField<java.lang.Integer, java.lang.Integer>
        internalGetPayBuyTimes() {
      if (payBuyTimes_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            PayBuyTimesDefaultEntryHolder.defaultEntry);
      }
      return payBuyTimes_;
    }
    private com.google.protobuf.MapField<java.lang.Integer, java.lang.Integer>
        internalGetMutablePayBuyTimes() {
      if (payBuyTimes_ == null) {
        payBuyTimes_ = com.google.protobuf.MapField.newMapField(
            PayBuyTimesDefaultEntryHolder.defaultEntry);
      }
      if (!payBuyTimes_.isMutable()) {
        payBuyTimes_ = payBuyTimes_.copy();
      }
      bitField0_ |= 0x00000020;
      onChanged();
      return payBuyTimes_;
    }
    public int getPayBuyTimesCount() {
      return internalGetPayBuyTimes().getMap().size();
    }
    /**
     * <pre>
     * SevenDayPay的购买记录
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; payBuyTimes = 6;</code>
     */
    @java.lang.Override
    public boolean containsPayBuyTimes(
        int key) {

      return internalGetPayBuyTimes().getMap().containsKey(key);
    }
    /**
     * Use {@link #getPayBuyTimesMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.Integer, java.lang.Integer> getPayBuyTimes() {
      return getPayBuyTimesMap();
    }
    /**
     * <pre>
     * SevenDayPay的购买记录
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; payBuyTimes = 6;</code>
     */
    @java.lang.Override
    public java.util.Map<java.lang.Integer, java.lang.Integer> getPayBuyTimesMap() {
      return internalGetPayBuyTimes().getMap();
    }
    /**
     * <pre>
     * SevenDayPay的购买记录
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; payBuyTimes = 6;</code>
     */
    @java.lang.Override
    public int getPayBuyTimesOrDefault(
        int key,
        int defaultValue) {

      java.util.Map<java.lang.Integer, java.lang.Integer> map =
          internalGetPayBuyTimes().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     * SevenDayPay的购买记录
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; payBuyTimes = 6;</code>
     */
    @java.lang.Override
    public int getPayBuyTimesOrThrow(
        int key) {

      java.util.Map<java.lang.Integer, java.lang.Integer> map =
          internalGetPayBuyTimes().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }
    public Builder clearPayBuyTimes() {
      bitField0_ = (bitField0_ & ~0x00000020);
      internalGetMutablePayBuyTimes().getMutableMap()
          .clear();
      return this;
    }
    /**
     * <pre>
     * SevenDayPay的购买记录
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; payBuyTimes = 6;</code>
     */
    public Builder removePayBuyTimes(
        int key) {

      internalGetMutablePayBuyTimes().getMutableMap()
          .remove(key);
      return this;
    }
    /**
     * Use alternate mutation accessors instead.
     */
    @java.lang.Deprecated
    public java.util.Map<java.lang.Integer, java.lang.Integer>
        getMutablePayBuyTimes() {
      bitField0_ |= 0x00000020;
      return internalGetMutablePayBuyTimes().getMutableMap();
    }
    /**
     * <pre>
     * SevenDayPay的购买记录
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; payBuyTimes = 6;</code>
     */
    public Builder putPayBuyTimes(
        int key,
        int value) {


      internalGetMutablePayBuyTimes().getMutableMap()
          .put(key, value);
      bitField0_ |= 0x00000020;
      return this;
    }
    /**
     * <pre>
     * SevenDayPay的购买记录
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; payBuyTimes = 6;</code>
     */
    public Builder putAllPayBuyTimes(
        java.util.Map<java.lang.Integer, java.lang.Integer> values) {
      internalGetMutablePayBuyTimes().getMutableMap()
          .putAll(values);
      bitField0_ |= 0x00000020;
      return this;
    }

    // @@protoc_insertion_point(builder_scope:Proto.SevenDay.SevenDayDto)
  }

  // @@protoc_insertion_point(class_scope:Proto.SevenDay.SevenDayDto)
  private static final com.dxx.game.dto.SevenDayDto DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.dxx.game.dto.SevenDayDto();
  }

  public static com.dxx.game.dto.SevenDayDto getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<SevenDayDto>
      PARSER = new com.google.protobuf.AbstractParser<SevenDayDto>() {
    @java.lang.Override
    public SevenDayDto parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<SevenDayDto> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<SevenDayDto> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.dxx.game.dto.SevenDayDto getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

