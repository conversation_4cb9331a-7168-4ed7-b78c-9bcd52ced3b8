// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: signin.proto
// Protobuf Java Version: 4.29.0

package com.dxx.game.dto;

public final class SignInProto {
  private SignInProto() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 29,
      /* patch= */ 0,
      /* suffix= */ "",
      SignInProto.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface SignInGetInfoRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.SignIn.SignInGetInfoRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();
  }
  /**
   * <pre>
   * CMD PackageId=11501 签到-获取数据
   * </pre>
   *
   * Protobuf type {@code Proto.SignIn.SignInGetInfoRequest}
   */
  public static final class SignInGetInfoRequest extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.SignIn.SignInGetInfoRequest)
      SignInGetInfoRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        SignInGetInfoRequest.class.getName());
    }
    // Use SignInGetInfoRequest.newBuilder() to construct.
    private SignInGetInfoRequest(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private SignInGetInfoRequest() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.SignInProto.internal_static_Proto_SignIn_SignInGetInfoRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.SignInProto.internal_static_Proto_SignIn_SignInGetInfoRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.SignInProto.SignInGetInfoRequest.class, com.dxx.game.dto.SignInProto.SignInGetInfoRequest.Builder.class);
    }

    private int bitField0_;
    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getCommonParams());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.SignInProto.SignInGetInfoRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.SignInProto.SignInGetInfoRequest other = (com.dxx.game.dto.SignInProto.SignInGetInfoRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.SignInProto.SignInGetInfoRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.SignInProto.SignInGetInfoRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.SignInProto.SignInGetInfoRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.SignInProto.SignInGetInfoRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.SignInProto.SignInGetInfoRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.SignInProto.SignInGetInfoRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.SignInProto.SignInGetInfoRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.SignInProto.SignInGetInfoRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.SignInProto.SignInGetInfoRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.SignInProto.SignInGetInfoRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.SignInProto.SignInGetInfoRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.SignInProto.SignInGetInfoRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.SignInProto.SignInGetInfoRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=11501 签到-获取数据
     * </pre>
     *
     * Protobuf type {@code Proto.SignIn.SignInGetInfoRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.SignIn.SignInGetInfoRequest)
        com.dxx.game.dto.SignInProto.SignInGetInfoRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.SignInProto.internal_static_Proto_SignIn_SignInGetInfoRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.SignInProto.internal_static_Proto_SignIn_SignInGetInfoRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.SignInProto.SignInGetInfoRequest.class, com.dxx.game.dto.SignInProto.SignInGetInfoRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.SignInProto.SignInGetInfoRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getCommonParamsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.SignInProto.internal_static_Proto_SignIn_SignInGetInfoRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.SignInProto.SignInGetInfoRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.SignInProto.SignInGetInfoRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.SignInProto.SignInGetInfoRequest build() {
        com.dxx.game.dto.SignInProto.SignInGetInfoRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.SignInProto.SignInGetInfoRequest buildPartial() {
        com.dxx.game.dto.SignInProto.SignInGetInfoRequest result = new com.dxx.game.dto.SignInProto.SignInGetInfoRequest(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.SignInProto.SignInGetInfoRequest result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.commonParams_ = commonParamsBuilder_ == null
              ? commonParams_
              : commonParamsBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.SignInProto.SignInGetInfoRequest) {
          return mergeFrom((com.dxx.game.dto.SignInProto.SignInGetInfoRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.SignInProto.SignInGetInfoRequest other) {
        if (other == com.dxx.game.dto.SignInProto.SignInGetInfoRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getCommonParamsFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
        } else {
          commonParamsBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
            commonParams_ != null &&
            commonParams_ != com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance()) {
            getCommonParamsBuilder().mergeFrom(value);
          } else {
            commonParams_ = value;
          }
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }
        if (commonParams_ != null) {
          bitField0_ |= 0x00000001;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        bitField0_ = (bitField0_ & ~0x00000001);
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:Proto.SignIn.SignInGetInfoRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.SignIn.SignInGetInfoRequest)
    private static final com.dxx.game.dto.SignInProto.SignInGetInfoRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.SignInProto.SignInGetInfoRequest();
    }

    public static com.dxx.game.dto.SignInProto.SignInGetInfoRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<SignInGetInfoRequest>
        PARSER = new com.google.protobuf.AbstractParser<SignInGetInfoRequest>() {
      @java.lang.Override
      public SignInGetInfoRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<SignInGetInfoRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SignInGetInfoRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.SignInProto.SignInGetInfoRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SignInGetInfoResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.SignIn.SignInGetInfoResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <code>.Proto.SignIn.SignInData signInData = 2;</code>
     * @return Whether the signInData field is set.
     */
    boolean hasSignInData();
    /**
     * <code>.Proto.SignIn.SignInData signInData = 2;</code>
     * @return The signInData.
     */
    com.dxx.game.dto.SignInProto.SignInData getSignInData();
    /**
     * <code>.Proto.SignIn.SignInData signInData = 2;</code>
     */
    com.dxx.game.dto.SignInProto.SignInDataOrBuilder getSignInDataOrBuilder();
  }
  /**
   * <pre>
   * CMD PackageId=11502 
   * </pre>
   *
   * Protobuf type {@code Proto.SignIn.SignInGetInfoResponse}
   */
  public static final class SignInGetInfoResponse extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.SignIn.SignInGetInfoResponse)
      SignInGetInfoResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        SignInGetInfoResponse.class.getName());
    }
    // Use SignInGetInfoResponse.newBuilder() to construct.
    private SignInGetInfoResponse(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private SignInGetInfoResponse() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.SignInProto.internal_static_Proto_SignIn_SignInGetInfoResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.SignInProto.internal_static_Proto_SignIn_SignInGetInfoResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.SignInProto.SignInGetInfoResponse.class, com.dxx.game.dto.SignInProto.SignInGetInfoResponse.Builder.class);
    }

    private int bitField0_;
    public static final int CODE_FIELD_NUMBER = 1;
    private int code_ = 0;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int SIGNINDATA_FIELD_NUMBER = 2;
    private com.dxx.game.dto.SignInProto.SignInData signInData_;
    /**
     * <code>.Proto.SignIn.SignInData signInData = 2;</code>
     * @return Whether the signInData field is set.
     */
    @java.lang.Override
    public boolean hasSignInData() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.Proto.SignIn.SignInData signInData = 2;</code>
     * @return The signInData.
     */
    @java.lang.Override
    public com.dxx.game.dto.SignInProto.SignInData getSignInData() {
      return signInData_ == null ? com.dxx.game.dto.SignInProto.SignInData.getDefaultInstance() : signInData_;
    }
    /**
     * <code>.Proto.SignIn.SignInData signInData = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.SignInProto.SignInDataOrBuilder getSignInDataOrBuilder() {
      return signInData_ == null ? com.dxx.game.dto.SignInProto.SignInData.getDefaultInstance() : signInData_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(2, getSignInData());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getSignInData());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.SignInProto.SignInGetInfoResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.SignInProto.SignInGetInfoResponse other = (com.dxx.game.dto.SignInProto.SignInGetInfoResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (hasSignInData() != other.hasSignInData()) return false;
      if (hasSignInData()) {
        if (!getSignInData()
            .equals(other.getSignInData())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      if (hasSignInData()) {
        hash = (37 * hash) + SIGNINDATA_FIELD_NUMBER;
        hash = (53 * hash) + getSignInData().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.SignInProto.SignInGetInfoResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.SignInProto.SignInGetInfoResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.SignInProto.SignInGetInfoResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.SignInProto.SignInGetInfoResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.SignInProto.SignInGetInfoResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.SignInProto.SignInGetInfoResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.SignInProto.SignInGetInfoResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.SignInProto.SignInGetInfoResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.SignInProto.SignInGetInfoResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.SignInProto.SignInGetInfoResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.SignInProto.SignInGetInfoResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.SignInProto.SignInGetInfoResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.SignInProto.SignInGetInfoResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=11502 
     * </pre>
     *
     * Protobuf type {@code Proto.SignIn.SignInGetInfoResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.SignIn.SignInGetInfoResponse)
        com.dxx.game.dto.SignInProto.SignInGetInfoResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.SignInProto.internal_static_Proto_SignIn_SignInGetInfoResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.SignInProto.internal_static_Proto_SignIn_SignInGetInfoResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.SignInProto.SignInGetInfoResponse.class, com.dxx.game.dto.SignInProto.SignInGetInfoResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.SignInProto.SignInGetInfoResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getSignInDataFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        code_ = 0;
        signInData_ = null;
        if (signInDataBuilder_ != null) {
          signInDataBuilder_.dispose();
          signInDataBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.SignInProto.internal_static_Proto_SignIn_SignInGetInfoResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.SignInProto.SignInGetInfoResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.SignInProto.SignInGetInfoResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.SignInProto.SignInGetInfoResponse build() {
        com.dxx.game.dto.SignInProto.SignInGetInfoResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.SignInProto.SignInGetInfoResponse buildPartial() {
        com.dxx.game.dto.SignInProto.SignInGetInfoResponse result = new com.dxx.game.dto.SignInProto.SignInGetInfoResponse(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.SignInProto.SignInGetInfoResponse result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.code_ = code_;
        }
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.signInData_ = signInDataBuilder_ == null
              ? signInData_
              : signInDataBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.SignInProto.SignInGetInfoResponse) {
          return mergeFrom((com.dxx.game.dto.SignInProto.SignInGetInfoResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.SignInProto.SignInGetInfoResponse other) {
        if (other == com.dxx.game.dto.SignInProto.SignInGetInfoResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.hasSignInData()) {
          mergeSignInData(other.getSignInData());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                code_ = input.readInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 18: {
                input.readMessage(
                    getSignInDataFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {

        code_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        bitField0_ = (bitField0_ & ~0x00000001);
        code_ = 0;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.SignInProto.SignInData signInData_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.SignInProto.SignInData, com.dxx.game.dto.SignInProto.SignInData.Builder, com.dxx.game.dto.SignInProto.SignInDataOrBuilder> signInDataBuilder_;
      /**
       * <code>.Proto.SignIn.SignInData signInData = 2;</code>
       * @return Whether the signInData field is set.
       */
      public boolean hasSignInData() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>.Proto.SignIn.SignInData signInData = 2;</code>
       * @return The signInData.
       */
      public com.dxx.game.dto.SignInProto.SignInData getSignInData() {
        if (signInDataBuilder_ == null) {
          return signInData_ == null ? com.dxx.game.dto.SignInProto.SignInData.getDefaultInstance() : signInData_;
        } else {
          return signInDataBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.SignIn.SignInData signInData = 2;</code>
       */
      public Builder setSignInData(com.dxx.game.dto.SignInProto.SignInData value) {
        if (signInDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          signInData_ = value;
        } else {
          signInDataBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.SignIn.SignInData signInData = 2;</code>
       */
      public Builder setSignInData(
          com.dxx.game.dto.SignInProto.SignInData.Builder builderForValue) {
        if (signInDataBuilder_ == null) {
          signInData_ = builderForValue.build();
        } else {
          signInDataBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.SignIn.SignInData signInData = 2;</code>
       */
      public Builder mergeSignInData(com.dxx.game.dto.SignInProto.SignInData value) {
        if (signInDataBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
            signInData_ != null &&
            signInData_ != com.dxx.game.dto.SignInProto.SignInData.getDefaultInstance()) {
            getSignInDataBuilder().mergeFrom(value);
          } else {
            signInData_ = value;
          }
        } else {
          signInDataBuilder_.mergeFrom(value);
        }
        if (signInData_ != null) {
          bitField0_ |= 0x00000002;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Proto.SignIn.SignInData signInData = 2;</code>
       */
      public Builder clearSignInData() {
        bitField0_ = (bitField0_ & ~0x00000002);
        signInData_ = null;
        if (signInDataBuilder_ != null) {
          signInDataBuilder_.dispose();
          signInDataBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.SignIn.SignInData signInData = 2;</code>
       */
      public com.dxx.game.dto.SignInProto.SignInData.Builder getSignInDataBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getSignInDataFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.SignIn.SignInData signInData = 2;</code>
       */
      public com.dxx.game.dto.SignInProto.SignInDataOrBuilder getSignInDataOrBuilder() {
        if (signInDataBuilder_ != null) {
          return signInDataBuilder_.getMessageOrBuilder();
        } else {
          return signInData_ == null ?
              com.dxx.game.dto.SignInProto.SignInData.getDefaultInstance() : signInData_;
        }
      }
      /**
       * <code>.Proto.SignIn.SignInData signInData = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.SignInProto.SignInData, com.dxx.game.dto.SignInProto.SignInData.Builder, com.dxx.game.dto.SignInProto.SignInDataOrBuilder> 
          getSignInDataFieldBuilder() {
        if (signInDataBuilder_ == null) {
          signInDataBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.SignInProto.SignInData, com.dxx.game.dto.SignInProto.SignInData.Builder, com.dxx.game.dto.SignInProto.SignInDataOrBuilder>(
                  getSignInData(),
                  getParentForChildren(),
                  isClean());
          signInData_ = null;
        }
        return signInDataBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:Proto.SignIn.SignInGetInfoResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.SignIn.SignInGetInfoResponse)
    private static final com.dxx.game.dto.SignInProto.SignInGetInfoResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.SignInProto.SignInGetInfoResponse();
    }

    public static com.dxx.game.dto.SignInProto.SignInGetInfoResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<SignInGetInfoResponse>
        PARSER = new com.google.protobuf.AbstractParser<SignInGetInfoResponse>() {
      @java.lang.Override
      public SignInGetInfoResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<SignInGetInfoResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SignInGetInfoResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.SignInProto.SignInGetInfoResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SignInDoSignRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.SignIn.SignInDoSignRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();
  }
  /**
   * <pre>
   * CMD PackageId=11503 签到-领取签到奖励
   * </pre>
   *
   * Protobuf type {@code Proto.SignIn.SignInDoSignRequest}
   */
  public static final class SignInDoSignRequest extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.SignIn.SignInDoSignRequest)
      SignInDoSignRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        SignInDoSignRequest.class.getName());
    }
    // Use SignInDoSignRequest.newBuilder() to construct.
    private SignInDoSignRequest(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private SignInDoSignRequest() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.SignInProto.internal_static_Proto_SignIn_SignInDoSignRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.SignInProto.internal_static_Proto_SignIn_SignInDoSignRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.SignInProto.SignInDoSignRequest.class, com.dxx.game.dto.SignInProto.SignInDoSignRequest.Builder.class);
    }

    private int bitField0_;
    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getCommonParams());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.SignInProto.SignInDoSignRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.SignInProto.SignInDoSignRequest other = (com.dxx.game.dto.SignInProto.SignInDoSignRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.SignInProto.SignInDoSignRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.SignInProto.SignInDoSignRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.SignInProto.SignInDoSignRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.SignInProto.SignInDoSignRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.SignInProto.SignInDoSignRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.SignInProto.SignInDoSignRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.SignInProto.SignInDoSignRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.SignInProto.SignInDoSignRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.SignInProto.SignInDoSignRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.SignInProto.SignInDoSignRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.SignInProto.SignInDoSignRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.SignInProto.SignInDoSignRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.SignInProto.SignInDoSignRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=11503 签到-领取签到奖励
     * </pre>
     *
     * Protobuf type {@code Proto.SignIn.SignInDoSignRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.SignIn.SignInDoSignRequest)
        com.dxx.game.dto.SignInProto.SignInDoSignRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.SignInProto.internal_static_Proto_SignIn_SignInDoSignRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.SignInProto.internal_static_Proto_SignIn_SignInDoSignRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.SignInProto.SignInDoSignRequest.class, com.dxx.game.dto.SignInProto.SignInDoSignRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.SignInProto.SignInDoSignRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getCommonParamsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.SignInProto.internal_static_Proto_SignIn_SignInDoSignRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.SignInProto.SignInDoSignRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.SignInProto.SignInDoSignRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.SignInProto.SignInDoSignRequest build() {
        com.dxx.game.dto.SignInProto.SignInDoSignRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.SignInProto.SignInDoSignRequest buildPartial() {
        com.dxx.game.dto.SignInProto.SignInDoSignRequest result = new com.dxx.game.dto.SignInProto.SignInDoSignRequest(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.SignInProto.SignInDoSignRequest result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.commonParams_ = commonParamsBuilder_ == null
              ? commonParams_
              : commonParamsBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.SignInProto.SignInDoSignRequest) {
          return mergeFrom((com.dxx.game.dto.SignInProto.SignInDoSignRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.SignInProto.SignInDoSignRequest other) {
        if (other == com.dxx.game.dto.SignInProto.SignInDoSignRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getCommonParamsFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
        } else {
          commonParamsBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
            commonParams_ != null &&
            commonParams_ != com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance()) {
            getCommonParamsBuilder().mergeFrom(value);
          } else {
            commonParams_ = value;
          }
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }
        if (commonParams_ != null) {
          bitField0_ |= 0x00000001;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        bitField0_ = (bitField0_ & ~0x00000001);
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:Proto.SignIn.SignInDoSignRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.SignIn.SignInDoSignRequest)
    private static final com.dxx.game.dto.SignInProto.SignInDoSignRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.SignInProto.SignInDoSignRequest();
    }

    public static com.dxx.game.dto.SignInProto.SignInDoSignRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<SignInDoSignRequest>
        PARSER = new com.google.protobuf.AbstractParser<SignInDoSignRequest>() {
      @java.lang.Override
      public SignInDoSignRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<SignInDoSignRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SignInDoSignRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.SignInProto.SignInDoSignRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SignInDoSignResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.SignIn.SignInDoSignResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <code>.Proto.SignIn.SignInData signInData = 2;</code>
     * @return Whether the signInData field is set.
     */
    boolean hasSignInData();
    /**
     * <code>.Proto.SignIn.SignInData signInData = 2;</code>
     * @return The signInData.
     */
    com.dxx.game.dto.SignInProto.SignInData getSignInData();
    /**
     * <code>.Proto.SignIn.SignInData signInData = 2;</code>
     */
    com.dxx.game.dto.SignInProto.SignInDataOrBuilder getSignInDataOrBuilder();

    /**
     * <code>.Proto.Common.CommonData commonData = 3;</code>
     * @return Whether the commonData field is set.
     */
    boolean hasCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 3;</code>
     * @return The commonData.
     */
    com.dxx.game.dto.CommonProto.CommonData getCommonData();
    /**
     * <code>.Proto.Common.CommonData commonData = 3;</code>
     */
    com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder();
  }
  /**
   * <pre>
   * CMD PackageId=11504 
   * </pre>
   *
   * Protobuf type {@code Proto.SignIn.SignInDoSignResponse}
   */
  public static final class SignInDoSignResponse extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.SignIn.SignInDoSignResponse)
      SignInDoSignResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        SignInDoSignResponse.class.getName());
    }
    // Use SignInDoSignResponse.newBuilder() to construct.
    private SignInDoSignResponse(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private SignInDoSignResponse() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.SignInProto.internal_static_Proto_SignIn_SignInDoSignResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.SignInProto.internal_static_Proto_SignIn_SignInDoSignResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.SignInProto.SignInDoSignResponse.class, com.dxx.game.dto.SignInProto.SignInDoSignResponse.Builder.class);
    }

    private int bitField0_;
    public static final int CODE_FIELD_NUMBER = 1;
    private int code_ = 0;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int SIGNINDATA_FIELD_NUMBER = 2;
    private com.dxx.game.dto.SignInProto.SignInData signInData_;
    /**
     * <code>.Proto.SignIn.SignInData signInData = 2;</code>
     * @return Whether the signInData field is set.
     */
    @java.lang.Override
    public boolean hasSignInData() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.Proto.SignIn.SignInData signInData = 2;</code>
     * @return The signInData.
     */
    @java.lang.Override
    public com.dxx.game.dto.SignInProto.SignInData getSignInData() {
      return signInData_ == null ? com.dxx.game.dto.SignInProto.SignInData.getDefaultInstance() : signInData_;
    }
    /**
     * <code>.Proto.SignIn.SignInData signInData = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.SignInProto.SignInDataOrBuilder getSignInDataOrBuilder() {
      return signInData_ == null ? com.dxx.game.dto.SignInProto.SignInData.getDefaultInstance() : signInData_;
    }

    public static final int COMMONDATA_FIELD_NUMBER = 3;
    private com.dxx.game.dto.CommonProto.CommonData commonData_;
    /**
     * <code>.Proto.Common.CommonData commonData = 3;</code>
     * @return Whether the commonData field is set.
     */
    @java.lang.Override
    public boolean hasCommonData() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 3;</code>
     * @return The commonData.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }
    /**
     * <code>.Proto.Common.CommonData commonData = 3;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
      return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(2, getSignInData());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(3, getCommonData());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getSignInData());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getCommonData());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.SignInProto.SignInDoSignResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.SignInProto.SignInDoSignResponse other = (com.dxx.game.dto.SignInProto.SignInDoSignResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (hasSignInData() != other.hasSignInData()) return false;
      if (hasSignInData()) {
        if (!getSignInData()
            .equals(other.getSignInData())) return false;
      }
      if (hasCommonData() != other.hasCommonData()) return false;
      if (hasCommonData()) {
        if (!getCommonData()
            .equals(other.getCommonData())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      if (hasSignInData()) {
        hash = (37 * hash) + SIGNINDATA_FIELD_NUMBER;
        hash = (53 * hash) + getSignInData().hashCode();
      }
      if (hasCommonData()) {
        hash = (37 * hash) + COMMONDATA_FIELD_NUMBER;
        hash = (53 * hash) + getCommonData().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.SignInProto.SignInDoSignResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.SignInProto.SignInDoSignResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.SignInProto.SignInDoSignResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.SignInProto.SignInDoSignResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.SignInProto.SignInDoSignResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.SignInProto.SignInDoSignResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.SignInProto.SignInDoSignResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.SignInProto.SignInDoSignResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.SignInProto.SignInDoSignResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.SignInProto.SignInDoSignResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.SignInProto.SignInDoSignResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.SignInProto.SignInDoSignResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.SignInProto.SignInDoSignResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=11504 
     * </pre>
     *
     * Protobuf type {@code Proto.SignIn.SignInDoSignResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.SignIn.SignInDoSignResponse)
        com.dxx.game.dto.SignInProto.SignInDoSignResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.SignInProto.internal_static_Proto_SignIn_SignInDoSignResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.SignInProto.internal_static_Proto_SignIn_SignInDoSignResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.SignInProto.SignInDoSignResponse.class, com.dxx.game.dto.SignInProto.SignInDoSignResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.SignInProto.SignInDoSignResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getSignInDataFieldBuilder();
          getCommonDataFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        code_ = 0;
        signInData_ = null;
        if (signInDataBuilder_ != null) {
          signInDataBuilder_.dispose();
          signInDataBuilder_ = null;
        }
        commonData_ = null;
        if (commonDataBuilder_ != null) {
          commonDataBuilder_.dispose();
          commonDataBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.SignInProto.internal_static_Proto_SignIn_SignInDoSignResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.SignInProto.SignInDoSignResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.SignInProto.SignInDoSignResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.SignInProto.SignInDoSignResponse build() {
        com.dxx.game.dto.SignInProto.SignInDoSignResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.SignInProto.SignInDoSignResponse buildPartial() {
        com.dxx.game.dto.SignInProto.SignInDoSignResponse result = new com.dxx.game.dto.SignInProto.SignInDoSignResponse(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.SignInProto.SignInDoSignResponse result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.code_ = code_;
        }
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.signInData_ = signInDataBuilder_ == null
              ? signInData_
              : signInDataBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.commonData_ = commonDataBuilder_ == null
              ? commonData_
              : commonDataBuilder_.build();
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.SignInProto.SignInDoSignResponse) {
          return mergeFrom((com.dxx.game.dto.SignInProto.SignInDoSignResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.SignInProto.SignInDoSignResponse other) {
        if (other == com.dxx.game.dto.SignInProto.SignInDoSignResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.hasSignInData()) {
          mergeSignInData(other.getSignInData());
        }
        if (other.hasCommonData()) {
          mergeCommonData(other.getCommonData());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                code_ = input.readInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 18: {
                input.readMessage(
                    getSignInDataFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              case 26: {
                input.readMessage(
                    getCommonDataFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000004;
                break;
              } // case 26
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {

        code_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        bitField0_ = (bitField0_ & ~0x00000001);
        code_ = 0;
        onChanged();
        return this;
      }

      private com.dxx.game.dto.SignInProto.SignInData signInData_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.SignInProto.SignInData, com.dxx.game.dto.SignInProto.SignInData.Builder, com.dxx.game.dto.SignInProto.SignInDataOrBuilder> signInDataBuilder_;
      /**
       * <code>.Proto.SignIn.SignInData signInData = 2;</code>
       * @return Whether the signInData field is set.
       */
      public boolean hasSignInData() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>.Proto.SignIn.SignInData signInData = 2;</code>
       * @return The signInData.
       */
      public com.dxx.game.dto.SignInProto.SignInData getSignInData() {
        if (signInDataBuilder_ == null) {
          return signInData_ == null ? com.dxx.game.dto.SignInProto.SignInData.getDefaultInstance() : signInData_;
        } else {
          return signInDataBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.SignIn.SignInData signInData = 2;</code>
       */
      public Builder setSignInData(com.dxx.game.dto.SignInProto.SignInData value) {
        if (signInDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          signInData_ = value;
        } else {
          signInDataBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.SignIn.SignInData signInData = 2;</code>
       */
      public Builder setSignInData(
          com.dxx.game.dto.SignInProto.SignInData.Builder builderForValue) {
        if (signInDataBuilder_ == null) {
          signInData_ = builderForValue.build();
        } else {
          signInDataBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.SignIn.SignInData signInData = 2;</code>
       */
      public Builder mergeSignInData(com.dxx.game.dto.SignInProto.SignInData value) {
        if (signInDataBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
            signInData_ != null &&
            signInData_ != com.dxx.game.dto.SignInProto.SignInData.getDefaultInstance()) {
            getSignInDataBuilder().mergeFrom(value);
          } else {
            signInData_ = value;
          }
        } else {
          signInDataBuilder_.mergeFrom(value);
        }
        if (signInData_ != null) {
          bitField0_ |= 0x00000002;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Proto.SignIn.SignInData signInData = 2;</code>
       */
      public Builder clearSignInData() {
        bitField0_ = (bitField0_ & ~0x00000002);
        signInData_ = null;
        if (signInDataBuilder_ != null) {
          signInDataBuilder_.dispose();
          signInDataBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.SignIn.SignInData signInData = 2;</code>
       */
      public com.dxx.game.dto.SignInProto.SignInData.Builder getSignInDataBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getSignInDataFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.SignIn.SignInData signInData = 2;</code>
       */
      public com.dxx.game.dto.SignInProto.SignInDataOrBuilder getSignInDataOrBuilder() {
        if (signInDataBuilder_ != null) {
          return signInDataBuilder_.getMessageOrBuilder();
        } else {
          return signInData_ == null ?
              com.dxx.game.dto.SignInProto.SignInData.getDefaultInstance() : signInData_;
        }
      }
      /**
       * <code>.Proto.SignIn.SignInData signInData = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.SignInProto.SignInData, com.dxx.game.dto.SignInProto.SignInData.Builder, com.dxx.game.dto.SignInProto.SignInDataOrBuilder> 
          getSignInDataFieldBuilder() {
        if (signInDataBuilder_ == null) {
          signInDataBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.SignInProto.SignInData, com.dxx.game.dto.SignInProto.SignInData.Builder, com.dxx.game.dto.SignInProto.SignInDataOrBuilder>(
                  getSignInData(),
                  getParentForChildren(),
                  isClean());
          signInData_ = null;
        }
        return signInDataBuilder_;
      }

      private com.dxx.game.dto.CommonProto.CommonData commonData_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> commonDataBuilder_;
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       * @return Whether the commonData field is set.
       */
      public boolean hasCommonData() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       * @return The commonData.
       */
      public com.dxx.game.dto.CommonProto.CommonData getCommonData() {
        if (commonDataBuilder_ == null) {
          return commonData_ == null ? com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        } else {
          return commonDataBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       */
      public Builder setCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonData_ = value;
        } else {
          commonDataBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       */
      public Builder setCommonData(
          com.dxx.game.dto.CommonProto.CommonData.Builder builderForValue) {
        if (commonDataBuilder_ == null) {
          commonData_ = builderForValue.build();
        } else {
          commonDataBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       */
      public Builder mergeCommonData(com.dxx.game.dto.CommonProto.CommonData value) {
        if (commonDataBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0) &&
            commonData_ != null &&
            commonData_ != com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance()) {
            getCommonDataBuilder().mergeFrom(value);
          } else {
            commonData_ = value;
          }
        } else {
          commonDataBuilder_.mergeFrom(value);
        }
        if (commonData_ != null) {
          bitField0_ |= 0x00000004;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       */
      public Builder clearCommonData() {
        bitField0_ = (bitField0_ & ~0x00000004);
        commonData_ = null;
        if (commonDataBuilder_ != null) {
          commonDataBuilder_.dispose();
          commonDataBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonData.Builder getCommonDataBuilder() {
        bitField0_ |= 0x00000004;
        onChanged();
        return getCommonDataFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonDataOrBuilder getCommonDataOrBuilder() {
        if (commonDataBuilder_ != null) {
          return commonDataBuilder_.getMessageOrBuilder();
        } else {
          return commonData_ == null ?
              com.dxx.game.dto.CommonProto.CommonData.getDefaultInstance() : commonData_;
        }
      }
      /**
       * <code>.Proto.Common.CommonData commonData = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder> 
          getCommonDataFieldBuilder() {
        if (commonDataBuilder_ == null) {
          commonDataBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.CommonData, com.dxx.game.dto.CommonProto.CommonData.Builder, com.dxx.game.dto.CommonProto.CommonDataOrBuilder>(
                  getCommonData(),
                  getParentForChildren(),
                  isClean());
          commonData_ = null;
        }
        return commonDataBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:Proto.SignIn.SignInDoSignResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.SignIn.SignInDoSignResponse)
    private static final com.dxx.game.dto.SignInProto.SignInDoSignResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.SignInProto.SignInDoSignResponse();
    }

    public static com.dxx.game.dto.SignInProto.SignInDoSignResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<SignInDoSignResponse>
        PARSER = new com.google.protobuf.AbstractParser<SignInDoSignResponse>() {
      @java.lang.Override
      public SignInDoSignResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<SignInDoSignResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SignInDoSignResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.SignInProto.SignInDoSignResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SignInDataOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.SignIn.SignInData)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 是否可以签到
     * </pre>
     *
     * <code>bool isCanSignIn = 1;</code>
     * @return The isCanSignIn.
     */
    boolean getIsCanSignIn();

    /**
     * <pre>
     * 下次签到时间戳(isCanSignIn == true 时 此时间戳返回的是0)
     * </pre>
     *
     * <code>uint64 timestamp = 2;</code>
     * @return The timestamp.
     */
    long getTimestamp();

    /**
     * <pre>
     * 签到记录 (1 - 7), 一天都没签到过 = 0
     * </pre>
     *
     * <code>uint32 log = 3;</code>
     * @return The log.
     */
    int getLog();

    /**
     * <pre>
     * 签到奖励二维列表 
     * </pre>
     *
     * <code>repeated .Proto.Common.RewardDtoListDto rewardDtoList = 4;</code>
     */
    java.util.List<com.dxx.game.dto.CommonProto.RewardDtoListDto> 
        getRewardDtoListList();
    /**
     * <pre>
     * 签到奖励二维列表 
     * </pre>
     *
     * <code>repeated .Proto.Common.RewardDtoListDto rewardDtoList = 4;</code>
     */
    com.dxx.game.dto.CommonProto.RewardDtoListDto getRewardDtoList(int index);
    /**
     * <pre>
     * 签到奖励二维列表 
     * </pre>
     *
     * <code>repeated .Proto.Common.RewardDtoListDto rewardDtoList = 4;</code>
     */
    int getRewardDtoListCount();
    /**
     * <pre>
     * 签到奖励二维列表 
     * </pre>
     *
     * <code>repeated .Proto.Common.RewardDtoListDto rewardDtoList = 4;</code>
     */
    java.util.List<? extends com.dxx.game.dto.CommonProto.RewardDtoListDtoOrBuilder> 
        getRewardDtoListOrBuilderList();
    /**
     * <pre>
     * 签到奖励二维列表 
     * </pre>
     *
     * <code>repeated .Proto.Common.RewardDtoListDto rewardDtoList = 4;</code>
     */
    com.dxx.game.dto.CommonProto.RewardDtoListDtoOrBuilder getRewardDtoListOrBuilder(
        int index);

    /**
     * <pre>
     * 配置表ID
     * </pre>
     *
     * <code>uint32 configId = 5;</code>
     * @return The configId.
     */
    int getConfigId();
  }
  /**
   * Protobuf type {@code Proto.SignIn.SignInData}
   */
  public static final class SignInData extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.SignIn.SignInData)
      SignInDataOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        SignInData.class.getName());
    }
    // Use SignInData.newBuilder() to construct.
    private SignInData(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private SignInData() {
      rewardDtoList_ = java.util.Collections.emptyList();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.SignInProto.internal_static_Proto_SignIn_SignInData_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.SignInProto.internal_static_Proto_SignIn_SignInData_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.SignInProto.SignInData.class, com.dxx.game.dto.SignInProto.SignInData.Builder.class);
    }

    public static final int ISCANSIGNIN_FIELD_NUMBER = 1;
    private boolean isCanSignIn_ = false;
    /**
     * <pre>
     * 是否可以签到
     * </pre>
     *
     * <code>bool isCanSignIn = 1;</code>
     * @return The isCanSignIn.
     */
    @java.lang.Override
    public boolean getIsCanSignIn() {
      return isCanSignIn_;
    }

    public static final int TIMESTAMP_FIELD_NUMBER = 2;
    private long timestamp_ = 0L;
    /**
     * <pre>
     * 下次签到时间戳(isCanSignIn == true 时 此时间戳返回的是0)
     * </pre>
     *
     * <code>uint64 timestamp = 2;</code>
     * @return The timestamp.
     */
    @java.lang.Override
    public long getTimestamp() {
      return timestamp_;
    }

    public static final int LOG_FIELD_NUMBER = 3;
    private int log_ = 0;
    /**
     * <pre>
     * 签到记录 (1 - 7), 一天都没签到过 = 0
     * </pre>
     *
     * <code>uint32 log = 3;</code>
     * @return The log.
     */
    @java.lang.Override
    public int getLog() {
      return log_;
    }

    public static final int REWARDDTOLIST_FIELD_NUMBER = 4;
    @SuppressWarnings("serial")
    private java.util.List<com.dxx.game.dto.CommonProto.RewardDtoListDto> rewardDtoList_;
    /**
     * <pre>
     * 签到奖励二维列表 
     * </pre>
     *
     * <code>repeated .Proto.Common.RewardDtoListDto rewardDtoList = 4;</code>
     */
    @java.lang.Override
    public java.util.List<com.dxx.game.dto.CommonProto.RewardDtoListDto> getRewardDtoListList() {
      return rewardDtoList_;
    }
    /**
     * <pre>
     * 签到奖励二维列表 
     * </pre>
     *
     * <code>repeated .Proto.Common.RewardDtoListDto rewardDtoList = 4;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.dxx.game.dto.CommonProto.RewardDtoListDtoOrBuilder> 
        getRewardDtoListOrBuilderList() {
      return rewardDtoList_;
    }
    /**
     * <pre>
     * 签到奖励二维列表 
     * </pre>
     *
     * <code>repeated .Proto.Common.RewardDtoListDto rewardDtoList = 4;</code>
     */
    @java.lang.Override
    public int getRewardDtoListCount() {
      return rewardDtoList_.size();
    }
    /**
     * <pre>
     * 签到奖励二维列表 
     * </pre>
     *
     * <code>repeated .Proto.Common.RewardDtoListDto rewardDtoList = 4;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.RewardDtoListDto getRewardDtoList(int index) {
      return rewardDtoList_.get(index);
    }
    /**
     * <pre>
     * 签到奖励二维列表 
     * </pre>
     *
     * <code>repeated .Proto.Common.RewardDtoListDto rewardDtoList = 4;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.RewardDtoListDtoOrBuilder getRewardDtoListOrBuilder(
        int index) {
      return rewardDtoList_.get(index);
    }

    public static final int CONFIGID_FIELD_NUMBER = 5;
    private int configId_ = 0;
    /**
     * <pre>
     * 配置表ID
     * </pre>
     *
     * <code>uint32 configId = 5;</code>
     * @return The configId.
     */
    @java.lang.Override
    public int getConfigId() {
      return configId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (isCanSignIn_ != false) {
        output.writeBool(1, isCanSignIn_);
      }
      if (timestamp_ != 0L) {
        output.writeUInt64(2, timestamp_);
      }
      if (log_ != 0) {
        output.writeUInt32(3, log_);
      }
      for (int i = 0; i < rewardDtoList_.size(); i++) {
        output.writeMessage(4, rewardDtoList_.get(i));
      }
      if (configId_ != 0) {
        output.writeUInt32(5, configId_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (isCanSignIn_ != false) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(1, isCanSignIn_);
      }
      if (timestamp_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(2, timestamp_);
      }
      if (log_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(3, log_);
      }
      for (int i = 0; i < rewardDtoList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, rewardDtoList_.get(i));
      }
      if (configId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(5, configId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.SignInProto.SignInData)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.SignInProto.SignInData other = (com.dxx.game.dto.SignInProto.SignInData) obj;

      if (getIsCanSignIn()
          != other.getIsCanSignIn()) return false;
      if (getTimestamp()
          != other.getTimestamp()) return false;
      if (getLog()
          != other.getLog()) return false;
      if (!getRewardDtoListList()
          .equals(other.getRewardDtoListList())) return false;
      if (getConfigId()
          != other.getConfigId()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ISCANSIGNIN_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
          getIsCanSignIn());
      hash = (37 * hash) + TIMESTAMP_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTimestamp());
      hash = (37 * hash) + LOG_FIELD_NUMBER;
      hash = (53 * hash) + getLog();
      if (getRewardDtoListCount() > 0) {
        hash = (37 * hash) + REWARDDTOLIST_FIELD_NUMBER;
        hash = (53 * hash) + getRewardDtoListList().hashCode();
      }
      hash = (37 * hash) + CONFIGID_FIELD_NUMBER;
      hash = (53 * hash) + getConfigId();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.SignInProto.SignInData parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.SignInProto.SignInData parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.SignInProto.SignInData parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.SignInProto.SignInData parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.SignInProto.SignInData parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.SignInProto.SignInData parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.SignInProto.SignInData parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.SignInProto.SignInData parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.SignInProto.SignInData parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.SignInProto.SignInData parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.SignInProto.SignInData parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.SignInProto.SignInData parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.SignInProto.SignInData prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code Proto.SignIn.SignInData}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.SignIn.SignInData)
        com.dxx.game.dto.SignInProto.SignInDataOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.SignInProto.internal_static_Proto_SignIn_SignInData_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.SignInProto.internal_static_Proto_SignIn_SignInData_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.SignInProto.SignInData.class, com.dxx.game.dto.SignInProto.SignInData.Builder.class);
      }

      // Construct using com.dxx.game.dto.SignInProto.SignInData.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        isCanSignIn_ = false;
        timestamp_ = 0L;
        log_ = 0;
        if (rewardDtoListBuilder_ == null) {
          rewardDtoList_ = java.util.Collections.emptyList();
        } else {
          rewardDtoList_ = null;
          rewardDtoListBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        configId_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.SignInProto.internal_static_Proto_SignIn_SignInData_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.SignInProto.SignInData getDefaultInstanceForType() {
        return com.dxx.game.dto.SignInProto.SignInData.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.SignInProto.SignInData build() {
        com.dxx.game.dto.SignInProto.SignInData result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.SignInProto.SignInData buildPartial() {
        com.dxx.game.dto.SignInProto.SignInData result = new com.dxx.game.dto.SignInProto.SignInData(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(com.dxx.game.dto.SignInProto.SignInData result) {
        if (rewardDtoListBuilder_ == null) {
          if (((bitField0_ & 0x00000008) != 0)) {
            rewardDtoList_ = java.util.Collections.unmodifiableList(rewardDtoList_);
            bitField0_ = (bitField0_ & ~0x00000008);
          }
          result.rewardDtoList_ = rewardDtoList_;
        } else {
          result.rewardDtoList_ = rewardDtoListBuilder_.build();
        }
      }

      private void buildPartial0(com.dxx.game.dto.SignInProto.SignInData result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.isCanSignIn_ = isCanSignIn_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.timestamp_ = timestamp_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.log_ = log_;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.configId_ = configId_;
        }
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.SignInProto.SignInData) {
          return mergeFrom((com.dxx.game.dto.SignInProto.SignInData)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.SignInProto.SignInData other) {
        if (other == com.dxx.game.dto.SignInProto.SignInData.getDefaultInstance()) return this;
        if (other.getIsCanSignIn() != false) {
          setIsCanSignIn(other.getIsCanSignIn());
        }
        if (other.getTimestamp() != 0L) {
          setTimestamp(other.getTimestamp());
        }
        if (other.getLog() != 0) {
          setLog(other.getLog());
        }
        if (rewardDtoListBuilder_ == null) {
          if (!other.rewardDtoList_.isEmpty()) {
            if (rewardDtoList_.isEmpty()) {
              rewardDtoList_ = other.rewardDtoList_;
              bitField0_ = (bitField0_ & ~0x00000008);
            } else {
              ensureRewardDtoListIsMutable();
              rewardDtoList_.addAll(other.rewardDtoList_);
            }
            onChanged();
          }
        } else {
          if (!other.rewardDtoList_.isEmpty()) {
            if (rewardDtoListBuilder_.isEmpty()) {
              rewardDtoListBuilder_.dispose();
              rewardDtoListBuilder_ = null;
              rewardDtoList_ = other.rewardDtoList_;
              bitField0_ = (bitField0_ & ~0x00000008);
              rewardDtoListBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getRewardDtoListFieldBuilder() : null;
            } else {
              rewardDtoListBuilder_.addAllMessages(other.rewardDtoList_);
            }
          }
        }
        if (other.getConfigId() != 0) {
          setConfigId(other.getConfigId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                isCanSignIn_ = input.readBool();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                timestamp_ = input.readUInt64();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 24: {
                log_ = input.readUInt32();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              case 34: {
                com.dxx.game.dto.CommonProto.RewardDtoListDto m =
                    input.readMessage(
                        com.dxx.game.dto.CommonProto.RewardDtoListDto.parser(),
                        extensionRegistry);
                if (rewardDtoListBuilder_ == null) {
                  ensureRewardDtoListIsMutable();
                  rewardDtoList_.add(m);
                } else {
                  rewardDtoListBuilder_.addMessage(m);
                }
                break;
              } // case 34
              case 40: {
                configId_ = input.readUInt32();
                bitField0_ |= 0x00000010;
                break;
              } // case 40
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private boolean isCanSignIn_ ;
      /**
       * <pre>
       * 是否可以签到
       * </pre>
       *
       * <code>bool isCanSignIn = 1;</code>
       * @return The isCanSignIn.
       */
      @java.lang.Override
      public boolean getIsCanSignIn() {
        return isCanSignIn_;
      }
      /**
       * <pre>
       * 是否可以签到
       * </pre>
       *
       * <code>bool isCanSignIn = 1;</code>
       * @param value The isCanSignIn to set.
       * @return This builder for chaining.
       */
      public Builder setIsCanSignIn(boolean value) {

        isCanSignIn_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 是否可以签到
       * </pre>
       *
       * <code>bool isCanSignIn = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsCanSignIn() {
        bitField0_ = (bitField0_ & ~0x00000001);
        isCanSignIn_ = false;
        onChanged();
        return this;
      }

      private long timestamp_ ;
      /**
       * <pre>
       * 下次签到时间戳(isCanSignIn == true 时 此时间戳返回的是0)
       * </pre>
       *
       * <code>uint64 timestamp = 2;</code>
       * @return The timestamp.
       */
      @java.lang.Override
      public long getTimestamp() {
        return timestamp_;
      }
      /**
       * <pre>
       * 下次签到时间戳(isCanSignIn == true 时 此时间戳返回的是0)
       * </pre>
       *
       * <code>uint64 timestamp = 2;</code>
       * @param value The timestamp to set.
       * @return This builder for chaining.
       */
      public Builder setTimestamp(long value) {

        timestamp_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 下次签到时间戳(isCanSignIn == true 时 此时间戳返回的是0)
       * </pre>
       *
       * <code>uint64 timestamp = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearTimestamp() {
        bitField0_ = (bitField0_ & ~0x00000002);
        timestamp_ = 0L;
        onChanged();
        return this;
      }

      private int log_ ;
      /**
       * <pre>
       * 签到记录 (1 - 7), 一天都没签到过 = 0
       * </pre>
       *
       * <code>uint32 log = 3;</code>
       * @return The log.
       */
      @java.lang.Override
      public int getLog() {
        return log_;
      }
      /**
       * <pre>
       * 签到记录 (1 - 7), 一天都没签到过 = 0
       * </pre>
       *
       * <code>uint32 log = 3;</code>
       * @param value The log to set.
       * @return This builder for chaining.
       */
      public Builder setLog(int value) {

        log_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 签到记录 (1 - 7), 一天都没签到过 = 0
       * </pre>
       *
       * <code>uint32 log = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearLog() {
        bitField0_ = (bitField0_ & ~0x00000004);
        log_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.dxx.game.dto.CommonProto.RewardDtoListDto> rewardDtoList_ =
        java.util.Collections.emptyList();
      private void ensureRewardDtoListIsMutable() {
        if (!((bitField0_ & 0x00000008) != 0)) {
          rewardDtoList_ = new java.util.ArrayList<com.dxx.game.dto.CommonProto.RewardDtoListDto>(rewardDtoList_);
          bitField0_ |= 0x00000008;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          com.dxx.game.dto.CommonProto.RewardDtoListDto, com.dxx.game.dto.CommonProto.RewardDtoListDto.Builder, com.dxx.game.dto.CommonProto.RewardDtoListDtoOrBuilder> rewardDtoListBuilder_;

      /**
       * <pre>
       * 签到奖励二维列表 
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDtoListDto rewardDtoList = 4;</code>
       */
      public java.util.List<com.dxx.game.dto.CommonProto.RewardDtoListDto> getRewardDtoListList() {
        if (rewardDtoListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(rewardDtoList_);
        } else {
          return rewardDtoListBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 签到奖励二维列表 
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDtoListDto rewardDtoList = 4;</code>
       */
      public int getRewardDtoListCount() {
        if (rewardDtoListBuilder_ == null) {
          return rewardDtoList_.size();
        } else {
          return rewardDtoListBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 签到奖励二维列表 
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDtoListDto rewardDtoList = 4;</code>
       */
      public com.dxx.game.dto.CommonProto.RewardDtoListDto getRewardDtoList(int index) {
        if (rewardDtoListBuilder_ == null) {
          return rewardDtoList_.get(index);
        } else {
          return rewardDtoListBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 签到奖励二维列表 
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDtoListDto rewardDtoList = 4;</code>
       */
      public Builder setRewardDtoList(
          int index, com.dxx.game.dto.CommonProto.RewardDtoListDto value) {
        if (rewardDtoListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRewardDtoListIsMutable();
          rewardDtoList_.set(index, value);
          onChanged();
        } else {
          rewardDtoListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 签到奖励二维列表 
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDtoListDto rewardDtoList = 4;</code>
       */
      public Builder setRewardDtoList(
          int index, com.dxx.game.dto.CommonProto.RewardDtoListDto.Builder builderForValue) {
        if (rewardDtoListBuilder_ == null) {
          ensureRewardDtoListIsMutable();
          rewardDtoList_.set(index, builderForValue.build());
          onChanged();
        } else {
          rewardDtoListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 签到奖励二维列表 
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDtoListDto rewardDtoList = 4;</code>
       */
      public Builder addRewardDtoList(com.dxx.game.dto.CommonProto.RewardDtoListDto value) {
        if (rewardDtoListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRewardDtoListIsMutable();
          rewardDtoList_.add(value);
          onChanged();
        } else {
          rewardDtoListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 签到奖励二维列表 
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDtoListDto rewardDtoList = 4;</code>
       */
      public Builder addRewardDtoList(
          int index, com.dxx.game.dto.CommonProto.RewardDtoListDto value) {
        if (rewardDtoListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRewardDtoListIsMutable();
          rewardDtoList_.add(index, value);
          onChanged();
        } else {
          rewardDtoListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 签到奖励二维列表 
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDtoListDto rewardDtoList = 4;</code>
       */
      public Builder addRewardDtoList(
          com.dxx.game.dto.CommonProto.RewardDtoListDto.Builder builderForValue) {
        if (rewardDtoListBuilder_ == null) {
          ensureRewardDtoListIsMutable();
          rewardDtoList_.add(builderForValue.build());
          onChanged();
        } else {
          rewardDtoListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 签到奖励二维列表 
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDtoListDto rewardDtoList = 4;</code>
       */
      public Builder addRewardDtoList(
          int index, com.dxx.game.dto.CommonProto.RewardDtoListDto.Builder builderForValue) {
        if (rewardDtoListBuilder_ == null) {
          ensureRewardDtoListIsMutable();
          rewardDtoList_.add(index, builderForValue.build());
          onChanged();
        } else {
          rewardDtoListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 签到奖励二维列表 
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDtoListDto rewardDtoList = 4;</code>
       */
      public Builder addAllRewardDtoList(
          java.lang.Iterable<? extends com.dxx.game.dto.CommonProto.RewardDtoListDto> values) {
        if (rewardDtoListBuilder_ == null) {
          ensureRewardDtoListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, rewardDtoList_);
          onChanged();
        } else {
          rewardDtoListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 签到奖励二维列表 
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDtoListDto rewardDtoList = 4;</code>
       */
      public Builder clearRewardDtoList() {
        if (rewardDtoListBuilder_ == null) {
          rewardDtoList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000008);
          onChanged();
        } else {
          rewardDtoListBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 签到奖励二维列表 
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDtoListDto rewardDtoList = 4;</code>
       */
      public Builder removeRewardDtoList(int index) {
        if (rewardDtoListBuilder_ == null) {
          ensureRewardDtoListIsMutable();
          rewardDtoList_.remove(index);
          onChanged();
        } else {
          rewardDtoListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 签到奖励二维列表 
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDtoListDto rewardDtoList = 4;</code>
       */
      public com.dxx.game.dto.CommonProto.RewardDtoListDto.Builder getRewardDtoListBuilder(
          int index) {
        return getRewardDtoListFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 签到奖励二维列表 
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDtoListDto rewardDtoList = 4;</code>
       */
      public com.dxx.game.dto.CommonProto.RewardDtoListDtoOrBuilder getRewardDtoListOrBuilder(
          int index) {
        if (rewardDtoListBuilder_ == null) {
          return rewardDtoList_.get(index);  } else {
          return rewardDtoListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 签到奖励二维列表 
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDtoListDto rewardDtoList = 4;</code>
       */
      public java.util.List<? extends com.dxx.game.dto.CommonProto.RewardDtoListDtoOrBuilder> 
           getRewardDtoListOrBuilderList() {
        if (rewardDtoListBuilder_ != null) {
          return rewardDtoListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(rewardDtoList_);
        }
      }
      /**
       * <pre>
       * 签到奖励二维列表 
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDtoListDto rewardDtoList = 4;</code>
       */
      public com.dxx.game.dto.CommonProto.RewardDtoListDto.Builder addRewardDtoListBuilder() {
        return getRewardDtoListFieldBuilder().addBuilder(
            com.dxx.game.dto.CommonProto.RewardDtoListDto.getDefaultInstance());
      }
      /**
       * <pre>
       * 签到奖励二维列表 
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDtoListDto rewardDtoList = 4;</code>
       */
      public com.dxx.game.dto.CommonProto.RewardDtoListDto.Builder addRewardDtoListBuilder(
          int index) {
        return getRewardDtoListFieldBuilder().addBuilder(
            index, com.dxx.game.dto.CommonProto.RewardDtoListDto.getDefaultInstance());
      }
      /**
       * <pre>
       * 签到奖励二维列表 
       * </pre>
       *
       * <code>repeated .Proto.Common.RewardDtoListDto rewardDtoList = 4;</code>
       */
      public java.util.List<com.dxx.game.dto.CommonProto.RewardDtoListDto.Builder> 
           getRewardDtoListBuilderList() {
        return getRewardDtoListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          com.dxx.game.dto.CommonProto.RewardDtoListDto, com.dxx.game.dto.CommonProto.RewardDtoListDto.Builder, com.dxx.game.dto.CommonProto.RewardDtoListDtoOrBuilder> 
          getRewardDtoListFieldBuilder() {
        if (rewardDtoListBuilder_ == null) {
          rewardDtoListBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              com.dxx.game.dto.CommonProto.RewardDtoListDto, com.dxx.game.dto.CommonProto.RewardDtoListDto.Builder, com.dxx.game.dto.CommonProto.RewardDtoListDtoOrBuilder>(
                  rewardDtoList_,
                  ((bitField0_ & 0x00000008) != 0),
                  getParentForChildren(),
                  isClean());
          rewardDtoList_ = null;
        }
        return rewardDtoListBuilder_;
      }

      private int configId_ ;
      /**
       * <pre>
       * 配置表ID
       * </pre>
       *
       * <code>uint32 configId = 5;</code>
       * @return The configId.
       */
      @java.lang.Override
      public int getConfigId() {
        return configId_;
      }
      /**
       * <pre>
       * 配置表ID
       * </pre>
       *
       * <code>uint32 configId = 5;</code>
       * @param value The configId to set.
       * @return This builder for chaining.
       */
      public Builder setConfigId(int value) {

        configId_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 配置表ID
       * </pre>
       *
       * <code>uint32 configId = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearConfigId() {
        bitField0_ = (bitField0_ & ~0x00000010);
        configId_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:Proto.SignIn.SignInData)
    }

    // @@protoc_insertion_point(class_scope:Proto.SignIn.SignInData)
    private static final com.dxx.game.dto.SignInProto.SignInData DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.SignInProto.SignInData();
    }

    public static com.dxx.game.dto.SignInProto.SignInData getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<SignInData>
        PARSER = new com.google.protobuf.AbstractParser<SignInData>() {
      @java.lang.Override
      public SignInData parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<SignInData> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SignInData> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.SignInProto.SignInData getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_SignIn_SignInGetInfoRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_SignIn_SignInGetInfoRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_SignIn_SignInGetInfoResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_SignIn_SignInGetInfoResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_SignIn_SignInDoSignRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_SignIn_SignInDoSignRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_SignIn_SignInDoSignResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_SignIn_SignInDoSignResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_SignIn_SignInData_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_SignIn_SignInData_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\014signin.proto\022\014Proto.SignIn\032\014common.pro" +
      "to\"H\n\024SignInGetInfoRequest\0220\n\014commonPara" +
      "ms\030\001 \001(\0132\032.Proto.Common.CommonParams\"S\n\025" +
      "SignInGetInfoResponse\022\014\n\004code\030\001 \001(\005\022,\n\ns" +
      "ignInData\030\002 \001(\0132\030.Proto.SignIn.SignInDat" +
      "a\"G\n\023SignInDoSignRequest\0220\n\014commonParams" +
      "\030\001 \001(\0132\032.Proto.Common.CommonParams\"\200\001\n\024S" +
      "ignInDoSignResponse\022\014\n\004code\030\001 \001(\005\022,\n\nsig" +
      "nInData\030\002 \001(\0132\030.Proto.SignIn.SignInData\022" +
      ",\n\ncommonData\030\003 \001(\0132\030.Proto.Common.Commo" +
      "nData\"\212\001\n\nSignInData\022\023\n\013isCanSignIn\030\001 \001(" +
      "\010\022\021\n\ttimestamp\030\002 \001(\004\022\013\n\003log\030\003 \001(\r\0225\n\rrew" +
      "ardDtoList\030\004 \003(\0132\036.Proto.Common.RewardDt" +
      "oListDto\022\020\n\010configId\030\005 \001(\rB\037\n\020com.dxx.ga" +
      "me.dtoB\013SignInProtob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.dxx.game.dto.CommonProto.getDescriptor(),
        });
    internal_static_Proto_SignIn_SignInGetInfoRequest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_Proto_SignIn_SignInGetInfoRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_SignIn_SignInGetInfoRequest_descriptor,
        new java.lang.String[] { "CommonParams", });
    internal_static_Proto_SignIn_SignInGetInfoResponse_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_Proto_SignIn_SignInGetInfoResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_SignIn_SignInGetInfoResponse_descriptor,
        new java.lang.String[] { "Code", "SignInData", });
    internal_static_Proto_SignIn_SignInDoSignRequest_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_Proto_SignIn_SignInDoSignRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_SignIn_SignInDoSignRequest_descriptor,
        new java.lang.String[] { "CommonParams", });
    internal_static_Proto_SignIn_SignInDoSignResponse_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_Proto_SignIn_SignInDoSignResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_SignIn_SignInDoSignResponse_descriptor,
        new java.lang.String[] { "Code", "SignInData", "CommonData", });
    internal_static_Proto_SignIn_SignInData_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_Proto_SignIn_SignInData_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_SignIn_SignInData_descriptor,
        new java.lang.String[] { "IsCanSignIn", "Timestamp", "Log", "RewardDtoList", "ConfigId", });
    descriptor.resolveAllFeaturesImmutable();
    com.dxx.game.dto.CommonProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
