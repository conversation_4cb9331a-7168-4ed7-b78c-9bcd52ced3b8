// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: task.proto
// Protobuf Java Version: 4.29.0

package com.dxx.game.dto;

public final class TaskProto {
  private TaskProto() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 29,
      /* patch= */ 0,
      /* suffix= */ "",
      TaskProto.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface TaskGetInfoRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Task.TaskGetInfoRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();
  }
  /**
   * <pre>
   * CMD PackageId=10501 任务-获取数据
   * </pre>
   *
   * Protobuf type {@code Proto.Task.TaskGetInfoRequest}
   */
  public static final class TaskGetInfoRequest extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Task.TaskGetInfoRequest)
      TaskGetInfoRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        TaskGetInfoRequest.class.getName());
    }
    // Use TaskGetInfoRequest.newBuilder() to construct.
    private TaskGetInfoRequest(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private TaskGetInfoRequest() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskGetInfoRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskGetInfoRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.TaskProto.TaskGetInfoRequest.class, com.dxx.game.dto.TaskProto.TaskGetInfoRequest.Builder.class);
    }

    private int bitField0_;
    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getCommonParams());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.TaskProto.TaskGetInfoRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.TaskProto.TaskGetInfoRequest other = (com.dxx.game.dto.TaskProto.TaskGetInfoRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.TaskProto.TaskGetInfoRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskGetInfoRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskGetInfoRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskGetInfoRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskGetInfoRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskGetInfoRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskGetInfoRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TaskProto.TaskGetInfoRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.TaskProto.TaskGetInfoRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.TaskProto.TaskGetInfoRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskGetInfoRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TaskProto.TaskGetInfoRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.TaskProto.TaskGetInfoRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=10501 任务-获取数据
     * </pre>
     *
     * Protobuf type {@code Proto.Task.TaskGetInfoRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Task.TaskGetInfoRequest)
        com.dxx.game.dto.TaskProto.TaskGetInfoRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskGetInfoRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskGetInfoRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.TaskProto.TaskGetInfoRequest.class, com.dxx.game.dto.TaskProto.TaskGetInfoRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.TaskProto.TaskGetInfoRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getCommonParamsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskGetInfoRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskGetInfoRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.TaskProto.TaskGetInfoRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskGetInfoRequest build() {
        com.dxx.game.dto.TaskProto.TaskGetInfoRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskGetInfoRequest buildPartial() {
        com.dxx.game.dto.TaskProto.TaskGetInfoRequest result = new com.dxx.game.dto.TaskProto.TaskGetInfoRequest(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.TaskProto.TaskGetInfoRequest result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.commonParams_ = commonParamsBuilder_ == null
              ? commonParams_
              : commonParamsBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.TaskProto.TaskGetInfoRequest) {
          return mergeFrom((com.dxx.game.dto.TaskProto.TaskGetInfoRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.TaskProto.TaskGetInfoRequest other) {
        if (other == com.dxx.game.dto.TaskProto.TaskGetInfoRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getCommonParamsFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
        } else {
          commonParamsBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
            commonParams_ != null &&
            commonParams_ != com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance()) {
            getCommonParamsBuilder().mergeFrom(value);
          } else {
            commonParams_ = value;
          }
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }
        if (commonParams_ != null) {
          bitField0_ |= 0x00000001;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        bitField0_ = (bitField0_ & ~0x00000001);
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Task.TaskGetInfoRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.Task.TaskGetInfoRequest)
    private static final com.dxx.game.dto.TaskProto.TaskGetInfoRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.TaskProto.TaskGetInfoRequest();
    }

    public static com.dxx.game.dto.TaskProto.TaskGetInfoRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<TaskGetInfoRequest>
        PARSER = new com.google.protobuf.AbstractParser<TaskGetInfoRequest>() {
      @java.lang.Override
      public TaskGetInfoRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<TaskGetInfoRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<TaskGetInfoRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.TaskProto.TaskGetInfoRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface TaskGetInfoResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Task.TaskGetInfoResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <pre>
     * 日常和成就任务列表
     * </pre>
     *
     * <code>repeated .Proto.Common.TaskInfoDto tasks = 2;</code>
     */
    java.util.List<com.dxx.game.dto.CommonProto.TaskInfoDto> 
        getTasksList();
    /**
     * <pre>
     * 日常和成就任务列表
     * </pre>
     *
     * <code>repeated .Proto.Common.TaskInfoDto tasks = 2;</code>
     */
    com.dxx.game.dto.CommonProto.TaskInfoDto getTasks(int index);
    /**
     * <pre>
     * 日常和成就任务列表
     * </pre>
     *
     * <code>repeated .Proto.Common.TaskInfoDto tasks = 2;</code>
     */
    int getTasksCount();
    /**
     * <pre>
     * 日常和成就任务列表
     * </pre>
     *
     * <code>repeated .Proto.Common.TaskInfoDto tasks = 2;</code>
     */
    java.util.List<? extends com.dxx.game.dto.CommonProto.TaskInfoDtoOrBuilder> 
        getTasksOrBuilderList();
    /**
     * <pre>
     * 日常和成就任务列表
     * </pre>
     *
     * <code>repeated .Proto.Common.TaskInfoDto tasks = 2;</code>
     */
    com.dxx.game.dto.CommonProto.TaskInfoDtoOrBuilder getTasksOrBuilder(
        int index);

    /**
     * <code>.Proto.Task.TaskActive activeInfo = 3;</code>
     * @return Whether the activeInfo field is set.
     */
    boolean hasActiveInfo();
    /**
     * <code>.Proto.Task.TaskActive activeInfo = 3;</code>
     * @return The activeInfo.
     */
    com.dxx.game.dto.TaskProto.TaskActive getActiveInfo();
    /**
     * <code>.Proto.Task.TaskActive activeInfo = 3;</code>
     */
    com.dxx.game.dto.TaskProto.TaskActiveOrBuilder getActiveInfoOrBuilder();
  }
  /**
   * <pre>
   * CMD PackageId=10502 
   * </pre>
   *
   * Protobuf type {@code Proto.Task.TaskGetInfoResponse}
   */
  public static final class TaskGetInfoResponse extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Task.TaskGetInfoResponse)
      TaskGetInfoResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        TaskGetInfoResponse.class.getName());
    }
    // Use TaskGetInfoResponse.newBuilder() to construct.
    private TaskGetInfoResponse(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private TaskGetInfoResponse() {
      tasks_ = java.util.Collections.emptyList();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskGetInfoResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskGetInfoResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.TaskProto.TaskGetInfoResponse.class, com.dxx.game.dto.TaskProto.TaskGetInfoResponse.Builder.class);
    }

    private int bitField0_;
    public static final int CODE_FIELD_NUMBER = 1;
    private int code_ = 0;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int TASKS_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private java.util.List<com.dxx.game.dto.CommonProto.TaskInfoDto> tasks_;
    /**
     * <pre>
     * 日常和成就任务列表
     * </pre>
     *
     * <code>repeated .Proto.Common.TaskInfoDto tasks = 2;</code>
     */
    @java.lang.Override
    public java.util.List<com.dxx.game.dto.CommonProto.TaskInfoDto> getTasksList() {
      return tasks_;
    }
    /**
     * <pre>
     * 日常和成就任务列表
     * </pre>
     *
     * <code>repeated .Proto.Common.TaskInfoDto tasks = 2;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.dxx.game.dto.CommonProto.TaskInfoDtoOrBuilder> 
        getTasksOrBuilderList() {
      return tasks_;
    }
    /**
     * <pre>
     * 日常和成就任务列表
     * </pre>
     *
     * <code>repeated .Proto.Common.TaskInfoDto tasks = 2;</code>
     */
    @java.lang.Override
    public int getTasksCount() {
      return tasks_.size();
    }
    /**
     * <pre>
     * 日常和成就任务列表
     * </pre>
     *
     * <code>repeated .Proto.Common.TaskInfoDto tasks = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.TaskInfoDto getTasks(int index) {
      return tasks_.get(index);
    }
    /**
     * <pre>
     * 日常和成就任务列表
     * </pre>
     *
     * <code>repeated .Proto.Common.TaskInfoDto tasks = 2;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.TaskInfoDtoOrBuilder getTasksOrBuilder(
        int index) {
      return tasks_.get(index);
    }

    public static final int ACTIVEINFO_FIELD_NUMBER = 3;
    private com.dxx.game.dto.TaskProto.TaskActive activeInfo_;
    /**
     * <code>.Proto.Task.TaskActive activeInfo = 3;</code>
     * @return Whether the activeInfo field is set.
     */
    @java.lang.Override
    public boolean hasActiveInfo() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.Proto.Task.TaskActive activeInfo = 3;</code>
     * @return The activeInfo.
     */
    @java.lang.Override
    public com.dxx.game.dto.TaskProto.TaskActive getActiveInfo() {
      return activeInfo_ == null ? com.dxx.game.dto.TaskProto.TaskActive.getDefaultInstance() : activeInfo_;
    }
    /**
     * <code>.Proto.Task.TaskActive activeInfo = 3;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.TaskProto.TaskActiveOrBuilder getActiveInfoOrBuilder() {
      return activeInfo_ == null ? com.dxx.game.dto.TaskProto.TaskActive.getDefaultInstance() : activeInfo_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      for (int i = 0; i < tasks_.size(); i++) {
        output.writeMessage(2, tasks_.get(i));
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(3, getActiveInfo());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      for (int i = 0; i < tasks_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, tasks_.get(i));
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getActiveInfo());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.TaskProto.TaskGetInfoResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.TaskProto.TaskGetInfoResponse other = (com.dxx.game.dto.TaskProto.TaskGetInfoResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (!getTasksList()
          .equals(other.getTasksList())) return false;
      if (hasActiveInfo() != other.hasActiveInfo()) return false;
      if (hasActiveInfo()) {
        if (!getActiveInfo()
            .equals(other.getActiveInfo())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      if (getTasksCount() > 0) {
        hash = (37 * hash) + TASKS_FIELD_NUMBER;
        hash = (53 * hash) + getTasksList().hashCode();
      }
      if (hasActiveInfo()) {
        hash = (37 * hash) + ACTIVEINFO_FIELD_NUMBER;
        hash = (53 * hash) + getActiveInfo().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.TaskProto.TaskGetInfoResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskGetInfoResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskGetInfoResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskGetInfoResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskGetInfoResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskGetInfoResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskGetInfoResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TaskProto.TaskGetInfoResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.TaskProto.TaskGetInfoResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.TaskProto.TaskGetInfoResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskGetInfoResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TaskProto.TaskGetInfoResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.TaskProto.TaskGetInfoResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=10502 
     * </pre>
     *
     * Protobuf type {@code Proto.Task.TaskGetInfoResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Task.TaskGetInfoResponse)
        com.dxx.game.dto.TaskProto.TaskGetInfoResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskGetInfoResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskGetInfoResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.TaskProto.TaskGetInfoResponse.class, com.dxx.game.dto.TaskProto.TaskGetInfoResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.TaskProto.TaskGetInfoResponse.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getTasksFieldBuilder();
          getActiveInfoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        code_ = 0;
        if (tasksBuilder_ == null) {
          tasks_ = java.util.Collections.emptyList();
        } else {
          tasks_ = null;
          tasksBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        activeInfo_ = null;
        if (activeInfoBuilder_ != null) {
          activeInfoBuilder_.dispose();
          activeInfoBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskGetInfoResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskGetInfoResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.TaskProto.TaskGetInfoResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskGetInfoResponse build() {
        com.dxx.game.dto.TaskProto.TaskGetInfoResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskGetInfoResponse buildPartial() {
        com.dxx.game.dto.TaskProto.TaskGetInfoResponse result = new com.dxx.game.dto.TaskProto.TaskGetInfoResponse(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(com.dxx.game.dto.TaskProto.TaskGetInfoResponse result) {
        if (tasksBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0)) {
            tasks_ = java.util.Collections.unmodifiableList(tasks_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.tasks_ = tasks_;
        } else {
          result.tasks_ = tasksBuilder_.build();
        }
      }

      private void buildPartial0(com.dxx.game.dto.TaskProto.TaskGetInfoResponse result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.code_ = code_;
        }
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.activeInfo_ = activeInfoBuilder_ == null
              ? activeInfo_
              : activeInfoBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.TaskProto.TaskGetInfoResponse) {
          return mergeFrom((com.dxx.game.dto.TaskProto.TaskGetInfoResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.TaskProto.TaskGetInfoResponse other) {
        if (other == com.dxx.game.dto.TaskProto.TaskGetInfoResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (tasksBuilder_ == null) {
          if (!other.tasks_.isEmpty()) {
            if (tasks_.isEmpty()) {
              tasks_ = other.tasks_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensureTasksIsMutable();
              tasks_.addAll(other.tasks_);
            }
            onChanged();
          }
        } else {
          if (!other.tasks_.isEmpty()) {
            if (tasksBuilder_.isEmpty()) {
              tasksBuilder_.dispose();
              tasksBuilder_ = null;
              tasks_ = other.tasks_;
              bitField0_ = (bitField0_ & ~0x00000002);
              tasksBuilder_ = 
                com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                   getTasksFieldBuilder() : null;
            } else {
              tasksBuilder_.addAllMessages(other.tasks_);
            }
          }
        }
        if (other.hasActiveInfo()) {
          mergeActiveInfo(other.getActiveInfo());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                code_ = input.readInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 18: {
                com.dxx.game.dto.CommonProto.TaskInfoDto m =
                    input.readMessage(
                        com.dxx.game.dto.CommonProto.TaskInfoDto.parser(),
                        extensionRegistry);
                if (tasksBuilder_ == null) {
                  ensureTasksIsMutable();
                  tasks_.add(m);
                } else {
                  tasksBuilder_.addMessage(m);
                }
                break;
              } // case 18
              case 26: {
                input.readMessage(
                    getActiveInfoFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000004;
                break;
              } // case 26
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {

        code_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        bitField0_ = (bitField0_ & ~0x00000001);
        code_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.dxx.game.dto.CommonProto.TaskInfoDto> tasks_ =
        java.util.Collections.emptyList();
      private void ensureTasksIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          tasks_ = new java.util.ArrayList<com.dxx.game.dto.CommonProto.TaskInfoDto>(tasks_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilder<
          com.dxx.game.dto.CommonProto.TaskInfoDto, com.dxx.game.dto.CommonProto.TaskInfoDto.Builder, com.dxx.game.dto.CommonProto.TaskInfoDtoOrBuilder> tasksBuilder_;

      /**
       * <pre>
       * 日常和成就任务列表
       * </pre>
       *
       * <code>repeated .Proto.Common.TaskInfoDto tasks = 2;</code>
       */
      public java.util.List<com.dxx.game.dto.CommonProto.TaskInfoDto> getTasksList() {
        if (tasksBuilder_ == null) {
          return java.util.Collections.unmodifiableList(tasks_);
        } else {
          return tasksBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 日常和成就任务列表
       * </pre>
       *
       * <code>repeated .Proto.Common.TaskInfoDto tasks = 2;</code>
       */
      public int getTasksCount() {
        if (tasksBuilder_ == null) {
          return tasks_.size();
        } else {
          return tasksBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 日常和成就任务列表
       * </pre>
       *
       * <code>repeated .Proto.Common.TaskInfoDto tasks = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.TaskInfoDto getTasks(int index) {
        if (tasksBuilder_ == null) {
          return tasks_.get(index);
        } else {
          return tasksBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 日常和成就任务列表
       * </pre>
       *
       * <code>repeated .Proto.Common.TaskInfoDto tasks = 2;</code>
       */
      public Builder setTasks(
          int index, com.dxx.game.dto.CommonProto.TaskInfoDto value) {
        if (tasksBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTasksIsMutable();
          tasks_.set(index, value);
          onChanged();
        } else {
          tasksBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 日常和成就任务列表
       * </pre>
       *
       * <code>repeated .Proto.Common.TaskInfoDto tasks = 2;</code>
       */
      public Builder setTasks(
          int index, com.dxx.game.dto.CommonProto.TaskInfoDto.Builder builderForValue) {
        if (tasksBuilder_ == null) {
          ensureTasksIsMutable();
          tasks_.set(index, builderForValue.build());
          onChanged();
        } else {
          tasksBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 日常和成就任务列表
       * </pre>
       *
       * <code>repeated .Proto.Common.TaskInfoDto tasks = 2;</code>
       */
      public Builder addTasks(com.dxx.game.dto.CommonProto.TaskInfoDto value) {
        if (tasksBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTasksIsMutable();
          tasks_.add(value);
          onChanged();
        } else {
          tasksBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 日常和成就任务列表
       * </pre>
       *
       * <code>repeated .Proto.Common.TaskInfoDto tasks = 2;</code>
       */
      public Builder addTasks(
          int index, com.dxx.game.dto.CommonProto.TaskInfoDto value) {
        if (tasksBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTasksIsMutable();
          tasks_.add(index, value);
          onChanged();
        } else {
          tasksBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 日常和成就任务列表
       * </pre>
       *
       * <code>repeated .Proto.Common.TaskInfoDto tasks = 2;</code>
       */
      public Builder addTasks(
          com.dxx.game.dto.CommonProto.TaskInfoDto.Builder builderForValue) {
        if (tasksBuilder_ == null) {
          ensureTasksIsMutable();
          tasks_.add(builderForValue.build());
          onChanged();
        } else {
          tasksBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 日常和成就任务列表
       * </pre>
       *
       * <code>repeated .Proto.Common.TaskInfoDto tasks = 2;</code>
       */
      public Builder addTasks(
          int index, com.dxx.game.dto.CommonProto.TaskInfoDto.Builder builderForValue) {
        if (tasksBuilder_ == null) {
          ensureTasksIsMutable();
          tasks_.add(index, builderForValue.build());
          onChanged();
        } else {
          tasksBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 日常和成就任务列表
       * </pre>
       *
       * <code>repeated .Proto.Common.TaskInfoDto tasks = 2;</code>
       */
      public Builder addAllTasks(
          java.lang.Iterable<? extends com.dxx.game.dto.CommonProto.TaskInfoDto> values) {
        if (tasksBuilder_ == null) {
          ensureTasksIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, tasks_);
          onChanged();
        } else {
          tasksBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 日常和成就任务列表
       * </pre>
       *
       * <code>repeated .Proto.Common.TaskInfoDto tasks = 2;</code>
       */
      public Builder clearTasks() {
        if (tasksBuilder_ == null) {
          tasks_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          tasksBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 日常和成就任务列表
       * </pre>
       *
       * <code>repeated .Proto.Common.TaskInfoDto tasks = 2;</code>
       */
      public Builder removeTasks(int index) {
        if (tasksBuilder_ == null) {
          ensureTasksIsMutable();
          tasks_.remove(index);
          onChanged();
        } else {
          tasksBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 日常和成就任务列表
       * </pre>
       *
       * <code>repeated .Proto.Common.TaskInfoDto tasks = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.TaskInfoDto.Builder getTasksBuilder(
          int index) {
        return getTasksFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 日常和成就任务列表
       * </pre>
       *
       * <code>repeated .Proto.Common.TaskInfoDto tasks = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.TaskInfoDtoOrBuilder getTasksOrBuilder(
          int index) {
        if (tasksBuilder_ == null) {
          return tasks_.get(index);  } else {
          return tasksBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 日常和成就任务列表
       * </pre>
       *
       * <code>repeated .Proto.Common.TaskInfoDto tasks = 2;</code>
       */
      public java.util.List<? extends com.dxx.game.dto.CommonProto.TaskInfoDtoOrBuilder> 
           getTasksOrBuilderList() {
        if (tasksBuilder_ != null) {
          return tasksBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(tasks_);
        }
      }
      /**
       * <pre>
       * 日常和成就任务列表
       * </pre>
       *
       * <code>repeated .Proto.Common.TaskInfoDto tasks = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.TaskInfoDto.Builder addTasksBuilder() {
        return getTasksFieldBuilder().addBuilder(
            com.dxx.game.dto.CommonProto.TaskInfoDto.getDefaultInstance());
      }
      /**
       * <pre>
       * 日常和成就任务列表
       * </pre>
       *
       * <code>repeated .Proto.Common.TaskInfoDto tasks = 2;</code>
       */
      public com.dxx.game.dto.CommonProto.TaskInfoDto.Builder addTasksBuilder(
          int index) {
        return getTasksFieldBuilder().addBuilder(
            index, com.dxx.game.dto.CommonProto.TaskInfoDto.getDefaultInstance());
      }
      /**
       * <pre>
       * 日常和成就任务列表
       * </pre>
       *
       * <code>repeated .Proto.Common.TaskInfoDto tasks = 2;</code>
       */
      public java.util.List<com.dxx.game.dto.CommonProto.TaskInfoDto.Builder> 
           getTasksBuilderList() {
        return getTasksFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilder<
          com.dxx.game.dto.CommonProto.TaskInfoDto, com.dxx.game.dto.CommonProto.TaskInfoDto.Builder, com.dxx.game.dto.CommonProto.TaskInfoDtoOrBuilder> 
          getTasksFieldBuilder() {
        if (tasksBuilder_ == null) {
          tasksBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
              com.dxx.game.dto.CommonProto.TaskInfoDto, com.dxx.game.dto.CommonProto.TaskInfoDto.Builder, com.dxx.game.dto.CommonProto.TaskInfoDtoOrBuilder>(
                  tasks_,
                  ((bitField0_ & 0x00000002) != 0),
                  getParentForChildren(),
                  isClean());
          tasks_ = null;
        }
        return tasksBuilder_;
      }

      private com.dxx.game.dto.TaskProto.TaskActive activeInfo_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.TaskProto.TaskActive, com.dxx.game.dto.TaskProto.TaskActive.Builder, com.dxx.game.dto.TaskProto.TaskActiveOrBuilder> activeInfoBuilder_;
      /**
       * <code>.Proto.Task.TaskActive activeInfo = 3;</code>
       * @return Whether the activeInfo field is set.
       */
      public boolean hasActiveInfo() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>.Proto.Task.TaskActive activeInfo = 3;</code>
       * @return The activeInfo.
       */
      public com.dxx.game.dto.TaskProto.TaskActive getActiveInfo() {
        if (activeInfoBuilder_ == null) {
          return activeInfo_ == null ? com.dxx.game.dto.TaskProto.TaskActive.getDefaultInstance() : activeInfo_;
        } else {
          return activeInfoBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Task.TaskActive activeInfo = 3;</code>
       */
      public Builder setActiveInfo(com.dxx.game.dto.TaskProto.TaskActive value) {
        if (activeInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          activeInfo_ = value;
        } else {
          activeInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Task.TaskActive activeInfo = 3;</code>
       */
      public Builder setActiveInfo(
          com.dxx.game.dto.TaskProto.TaskActive.Builder builderForValue) {
        if (activeInfoBuilder_ == null) {
          activeInfo_ = builderForValue.build();
        } else {
          activeInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Task.TaskActive activeInfo = 3;</code>
       */
      public Builder mergeActiveInfo(com.dxx.game.dto.TaskProto.TaskActive value) {
        if (activeInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0) &&
            activeInfo_ != null &&
            activeInfo_ != com.dxx.game.dto.TaskProto.TaskActive.getDefaultInstance()) {
            getActiveInfoBuilder().mergeFrom(value);
          } else {
            activeInfo_ = value;
          }
        } else {
          activeInfoBuilder_.mergeFrom(value);
        }
        if (activeInfo_ != null) {
          bitField0_ |= 0x00000004;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Proto.Task.TaskActive activeInfo = 3;</code>
       */
      public Builder clearActiveInfo() {
        bitField0_ = (bitField0_ & ~0x00000004);
        activeInfo_ = null;
        if (activeInfoBuilder_ != null) {
          activeInfoBuilder_.dispose();
          activeInfoBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Task.TaskActive activeInfo = 3;</code>
       */
      public com.dxx.game.dto.TaskProto.TaskActive.Builder getActiveInfoBuilder() {
        bitField0_ |= 0x00000004;
        onChanged();
        return getActiveInfoFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Task.TaskActive activeInfo = 3;</code>
       */
      public com.dxx.game.dto.TaskProto.TaskActiveOrBuilder getActiveInfoOrBuilder() {
        if (activeInfoBuilder_ != null) {
          return activeInfoBuilder_.getMessageOrBuilder();
        } else {
          return activeInfo_ == null ?
              com.dxx.game.dto.TaskProto.TaskActive.getDefaultInstance() : activeInfo_;
        }
      }
      /**
       * <code>.Proto.Task.TaskActive activeInfo = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.TaskProto.TaskActive, com.dxx.game.dto.TaskProto.TaskActive.Builder, com.dxx.game.dto.TaskProto.TaskActiveOrBuilder> 
          getActiveInfoFieldBuilder() {
        if (activeInfoBuilder_ == null) {
          activeInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.TaskProto.TaskActive, com.dxx.game.dto.TaskProto.TaskActive.Builder, com.dxx.game.dto.TaskProto.TaskActiveOrBuilder>(
                  getActiveInfo(),
                  getParentForChildren(),
                  isClean());
          activeInfo_ = null;
        }
        return activeInfoBuilder_;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Task.TaskGetInfoResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.Task.TaskGetInfoResponse)
    private static final com.dxx.game.dto.TaskProto.TaskGetInfoResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.TaskProto.TaskGetInfoResponse();
    }

    public static com.dxx.game.dto.TaskProto.TaskGetInfoResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<TaskGetInfoResponse>
        PARSER = new com.google.protobuf.AbstractParser<TaskGetInfoResponse>() {
      @java.lang.Override
      public TaskGetInfoResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<TaskGetInfoResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<TaskGetInfoResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.TaskProto.TaskGetInfoResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface TaskActiveOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Task.TaskActive)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 日常活跃度
     * </pre>
     *
     * <code>int32 dailyTaskActive = 1;</code>
     * @return The dailyTaskActive.
     */
    int getDailyTaskActive();

    /**
     * <pre>
     * 日常任务刷新时间戳
     * </pre>
     *
     * <code>int64 dailyTaskResetTime = 2;</code>
     * @return The dailyTaskResetTime.
     */
    long getDailyTaskResetTime();

    /**
     * <pre>
     * 日常任务活跃度奖励领取记录-位运算 0未领取，1已领取 从第一位开始
     * </pre>
     *
     * <code>int64 dailyTaskRewardLog = 3;</code>
     * @return The dailyTaskRewardLog.
     */
    long getDailyTaskRewardLog();

    /**
     * <pre>
     * 周常活跃度
     * </pre>
     *
     * <code>int32 weeklyTaskActive = 4;</code>
     * @return The weeklyTaskActive.
     */
    int getWeeklyTaskActive();

    /**
     * <pre>
     * 周常刷新时间戳
     * </pre>
     *
     * <code>int64 weeklyTaskResetTime = 5;</code>
     * @return The weeklyTaskResetTime.
     */
    long getWeeklyTaskResetTime();

    /**
     * <pre>
     * 周常任务活跃度奖励领取记录-位运算 0未领取，1已领取 从第一位开始
     * </pre>
     *
     * <code>int64 weeklyTaskRewardLog = 6;</code>
     * @return The weeklyTaskRewardLog.
     */
    long getWeeklyTaskRewardLog();
  }
  /**
   * Protobuf type {@code Proto.Task.TaskActive}
   */
  public static final class TaskActive extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Task.TaskActive)
      TaskActiveOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        TaskActive.class.getName());
    }
    // Use TaskActive.newBuilder() to construct.
    private TaskActive(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private TaskActive() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskActive_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskActive_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.TaskProto.TaskActive.class, com.dxx.game.dto.TaskProto.TaskActive.Builder.class);
    }

    public static final int DAILYTASKACTIVE_FIELD_NUMBER = 1;
    private int dailyTaskActive_ = 0;
    /**
     * <pre>
     * 日常活跃度
     * </pre>
     *
     * <code>int32 dailyTaskActive = 1;</code>
     * @return The dailyTaskActive.
     */
    @java.lang.Override
    public int getDailyTaskActive() {
      return dailyTaskActive_;
    }

    public static final int DAILYTASKRESETTIME_FIELD_NUMBER = 2;
    private long dailyTaskResetTime_ = 0L;
    /**
     * <pre>
     * 日常任务刷新时间戳
     * </pre>
     *
     * <code>int64 dailyTaskResetTime = 2;</code>
     * @return The dailyTaskResetTime.
     */
    @java.lang.Override
    public long getDailyTaskResetTime() {
      return dailyTaskResetTime_;
    }

    public static final int DAILYTASKREWARDLOG_FIELD_NUMBER = 3;
    private long dailyTaskRewardLog_ = 0L;
    /**
     * <pre>
     * 日常任务活跃度奖励领取记录-位运算 0未领取，1已领取 从第一位开始
     * </pre>
     *
     * <code>int64 dailyTaskRewardLog = 3;</code>
     * @return The dailyTaskRewardLog.
     */
    @java.lang.Override
    public long getDailyTaskRewardLog() {
      return dailyTaskRewardLog_;
    }

    public static final int WEEKLYTASKACTIVE_FIELD_NUMBER = 4;
    private int weeklyTaskActive_ = 0;
    /**
     * <pre>
     * 周常活跃度
     * </pre>
     *
     * <code>int32 weeklyTaskActive = 4;</code>
     * @return The weeklyTaskActive.
     */
    @java.lang.Override
    public int getWeeklyTaskActive() {
      return weeklyTaskActive_;
    }

    public static final int WEEKLYTASKRESETTIME_FIELD_NUMBER = 5;
    private long weeklyTaskResetTime_ = 0L;
    /**
     * <pre>
     * 周常刷新时间戳
     * </pre>
     *
     * <code>int64 weeklyTaskResetTime = 5;</code>
     * @return The weeklyTaskResetTime.
     */
    @java.lang.Override
    public long getWeeklyTaskResetTime() {
      return weeklyTaskResetTime_;
    }

    public static final int WEEKLYTASKREWARDLOG_FIELD_NUMBER = 6;
    private long weeklyTaskRewardLog_ = 0L;
    /**
     * <pre>
     * 周常任务活跃度奖励领取记录-位运算 0未领取，1已领取 从第一位开始
     * </pre>
     *
     * <code>int64 weeklyTaskRewardLog = 6;</code>
     * @return The weeklyTaskRewardLog.
     */
    @java.lang.Override
    public long getWeeklyTaskRewardLog() {
      return weeklyTaskRewardLog_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (dailyTaskActive_ != 0) {
        output.writeInt32(1, dailyTaskActive_);
      }
      if (dailyTaskResetTime_ != 0L) {
        output.writeInt64(2, dailyTaskResetTime_);
      }
      if (dailyTaskRewardLog_ != 0L) {
        output.writeInt64(3, dailyTaskRewardLog_);
      }
      if (weeklyTaskActive_ != 0) {
        output.writeInt32(4, weeklyTaskActive_);
      }
      if (weeklyTaskResetTime_ != 0L) {
        output.writeInt64(5, weeklyTaskResetTime_);
      }
      if (weeklyTaskRewardLog_ != 0L) {
        output.writeInt64(6, weeklyTaskRewardLog_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (dailyTaskActive_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, dailyTaskActive_);
      }
      if (dailyTaskResetTime_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, dailyTaskResetTime_);
      }
      if (dailyTaskRewardLog_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, dailyTaskRewardLog_);
      }
      if (weeklyTaskActive_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, weeklyTaskActive_);
      }
      if (weeklyTaskResetTime_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(5, weeklyTaskResetTime_);
      }
      if (weeklyTaskRewardLog_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(6, weeklyTaskRewardLog_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.TaskProto.TaskActive)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.TaskProto.TaskActive other = (com.dxx.game.dto.TaskProto.TaskActive) obj;

      if (getDailyTaskActive()
          != other.getDailyTaskActive()) return false;
      if (getDailyTaskResetTime()
          != other.getDailyTaskResetTime()) return false;
      if (getDailyTaskRewardLog()
          != other.getDailyTaskRewardLog()) return false;
      if (getWeeklyTaskActive()
          != other.getWeeklyTaskActive()) return false;
      if (getWeeklyTaskResetTime()
          != other.getWeeklyTaskResetTime()) return false;
      if (getWeeklyTaskRewardLog()
          != other.getWeeklyTaskRewardLog()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + DAILYTASKACTIVE_FIELD_NUMBER;
      hash = (53 * hash) + getDailyTaskActive();
      hash = (37 * hash) + DAILYTASKRESETTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getDailyTaskResetTime());
      hash = (37 * hash) + DAILYTASKREWARDLOG_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getDailyTaskRewardLog());
      hash = (37 * hash) + WEEKLYTASKACTIVE_FIELD_NUMBER;
      hash = (53 * hash) + getWeeklyTaskActive();
      hash = (37 * hash) + WEEKLYTASKRESETTIME_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getWeeklyTaskResetTime());
      hash = (37 * hash) + WEEKLYTASKREWARDLOG_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getWeeklyTaskRewardLog());
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.TaskProto.TaskActive parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskActive parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskActive parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskActive parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskActive parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskActive parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskActive parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TaskProto.TaskActive parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.TaskProto.TaskActive parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.TaskProto.TaskActive parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskActive parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TaskProto.TaskActive parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.TaskProto.TaskActive prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code Proto.Task.TaskActive}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Task.TaskActive)
        com.dxx.game.dto.TaskProto.TaskActiveOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskActive_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskActive_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.TaskProto.TaskActive.class, com.dxx.game.dto.TaskProto.TaskActive.Builder.class);
      }

      // Construct using com.dxx.game.dto.TaskProto.TaskActive.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        dailyTaskActive_ = 0;
        dailyTaskResetTime_ = 0L;
        dailyTaskRewardLog_ = 0L;
        weeklyTaskActive_ = 0;
        weeklyTaskResetTime_ = 0L;
        weeklyTaskRewardLog_ = 0L;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskActive_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskActive getDefaultInstanceForType() {
        return com.dxx.game.dto.TaskProto.TaskActive.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskActive build() {
        com.dxx.game.dto.TaskProto.TaskActive result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskActive buildPartial() {
        com.dxx.game.dto.TaskProto.TaskActive result = new com.dxx.game.dto.TaskProto.TaskActive(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.TaskProto.TaskActive result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.dailyTaskActive_ = dailyTaskActive_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.dailyTaskResetTime_ = dailyTaskResetTime_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.dailyTaskRewardLog_ = dailyTaskRewardLog_;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.weeklyTaskActive_ = weeklyTaskActive_;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.weeklyTaskResetTime_ = weeklyTaskResetTime_;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.weeklyTaskRewardLog_ = weeklyTaskRewardLog_;
        }
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.TaskProto.TaskActive) {
          return mergeFrom((com.dxx.game.dto.TaskProto.TaskActive)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.TaskProto.TaskActive other) {
        if (other == com.dxx.game.dto.TaskProto.TaskActive.getDefaultInstance()) return this;
        if (other.getDailyTaskActive() != 0) {
          setDailyTaskActive(other.getDailyTaskActive());
        }
        if (other.getDailyTaskResetTime() != 0L) {
          setDailyTaskResetTime(other.getDailyTaskResetTime());
        }
        if (other.getDailyTaskRewardLog() != 0L) {
          setDailyTaskRewardLog(other.getDailyTaskRewardLog());
        }
        if (other.getWeeklyTaskActive() != 0) {
          setWeeklyTaskActive(other.getWeeklyTaskActive());
        }
        if (other.getWeeklyTaskResetTime() != 0L) {
          setWeeklyTaskResetTime(other.getWeeklyTaskResetTime());
        }
        if (other.getWeeklyTaskRewardLog() != 0L) {
          setWeeklyTaskRewardLog(other.getWeeklyTaskRewardLog());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                dailyTaskActive_ = input.readInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                dailyTaskResetTime_ = input.readInt64();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 24: {
                dailyTaskRewardLog_ = input.readInt64();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              case 32: {
                weeklyTaskActive_ = input.readInt32();
                bitField0_ |= 0x00000008;
                break;
              } // case 32
              case 40: {
                weeklyTaskResetTime_ = input.readInt64();
                bitField0_ |= 0x00000010;
                break;
              } // case 40
              case 48: {
                weeklyTaskRewardLog_ = input.readInt64();
                bitField0_ |= 0x00000020;
                break;
              } // case 48
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int dailyTaskActive_ ;
      /**
       * <pre>
       * 日常活跃度
       * </pre>
       *
       * <code>int32 dailyTaskActive = 1;</code>
       * @return The dailyTaskActive.
       */
      @java.lang.Override
      public int getDailyTaskActive() {
        return dailyTaskActive_;
      }
      /**
       * <pre>
       * 日常活跃度
       * </pre>
       *
       * <code>int32 dailyTaskActive = 1;</code>
       * @param value The dailyTaskActive to set.
       * @return This builder for chaining.
       */
      public Builder setDailyTaskActive(int value) {

        dailyTaskActive_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 日常活跃度
       * </pre>
       *
       * <code>int32 dailyTaskActive = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearDailyTaskActive() {
        bitField0_ = (bitField0_ & ~0x00000001);
        dailyTaskActive_ = 0;
        onChanged();
        return this;
      }

      private long dailyTaskResetTime_ ;
      /**
       * <pre>
       * 日常任务刷新时间戳
       * </pre>
       *
       * <code>int64 dailyTaskResetTime = 2;</code>
       * @return The dailyTaskResetTime.
       */
      @java.lang.Override
      public long getDailyTaskResetTime() {
        return dailyTaskResetTime_;
      }
      /**
       * <pre>
       * 日常任务刷新时间戳
       * </pre>
       *
       * <code>int64 dailyTaskResetTime = 2;</code>
       * @param value The dailyTaskResetTime to set.
       * @return This builder for chaining.
       */
      public Builder setDailyTaskResetTime(long value) {

        dailyTaskResetTime_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 日常任务刷新时间戳
       * </pre>
       *
       * <code>int64 dailyTaskResetTime = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearDailyTaskResetTime() {
        bitField0_ = (bitField0_ & ~0x00000002);
        dailyTaskResetTime_ = 0L;
        onChanged();
        return this;
      }

      private long dailyTaskRewardLog_ ;
      /**
       * <pre>
       * 日常任务活跃度奖励领取记录-位运算 0未领取，1已领取 从第一位开始
       * </pre>
       *
       * <code>int64 dailyTaskRewardLog = 3;</code>
       * @return The dailyTaskRewardLog.
       */
      @java.lang.Override
      public long getDailyTaskRewardLog() {
        return dailyTaskRewardLog_;
      }
      /**
       * <pre>
       * 日常任务活跃度奖励领取记录-位运算 0未领取，1已领取 从第一位开始
       * </pre>
       *
       * <code>int64 dailyTaskRewardLog = 3;</code>
       * @param value The dailyTaskRewardLog to set.
       * @return This builder for chaining.
       */
      public Builder setDailyTaskRewardLog(long value) {

        dailyTaskRewardLog_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 日常任务活跃度奖励领取记录-位运算 0未领取，1已领取 从第一位开始
       * </pre>
       *
       * <code>int64 dailyTaskRewardLog = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearDailyTaskRewardLog() {
        bitField0_ = (bitField0_ & ~0x00000004);
        dailyTaskRewardLog_ = 0L;
        onChanged();
        return this;
      }

      private int weeklyTaskActive_ ;
      /**
       * <pre>
       * 周常活跃度
       * </pre>
       *
       * <code>int32 weeklyTaskActive = 4;</code>
       * @return The weeklyTaskActive.
       */
      @java.lang.Override
      public int getWeeklyTaskActive() {
        return weeklyTaskActive_;
      }
      /**
       * <pre>
       * 周常活跃度
       * </pre>
       *
       * <code>int32 weeklyTaskActive = 4;</code>
       * @param value The weeklyTaskActive to set.
       * @return This builder for chaining.
       */
      public Builder setWeeklyTaskActive(int value) {

        weeklyTaskActive_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 周常活跃度
       * </pre>
       *
       * <code>int32 weeklyTaskActive = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearWeeklyTaskActive() {
        bitField0_ = (bitField0_ & ~0x00000008);
        weeklyTaskActive_ = 0;
        onChanged();
        return this;
      }

      private long weeklyTaskResetTime_ ;
      /**
       * <pre>
       * 周常刷新时间戳
       * </pre>
       *
       * <code>int64 weeklyTaskResetTime = 5;</code>
       * @return The weeklyTaskResetTime.
       */
      @java.lang.Override
      public long getWeeklyTaskResetTime() {
        return weeklyTaskResetTime_;
      }
      /**
       * <pre>
       * 周常刷新时间戳
       * </pre>
       *
       * <code>int64 weeklyTaskResetTime = 5;</code>
       * @param value The weeklyTaskResetTime to set.
       * @return This builder for chaining.
       */
      public Builder setWeeklyTaskResetTime(long value) {

        weeklyTaskResetTime_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 周常刷新时间戳
       * </pre>
       *
       * <code>int64 weeklyTaskResetTime = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearWeeklyTaskResetTime() {
        bitField0_ = (bitField0_ & ~0x00000010);
        weeklyTaskResetTime_ = 0L;
        onChanged();
        return this;
      }

      private long weeklyTaskRewardLog_ ;
      /**
       * <pre>
       * 周常任务活跃度奖励领取记录-位运算 0未领取，1已领取 从第一位开始
       * </pre>
       *
       * <code>int64 weeklyTaskRewardLog = 6;</code>
       * @return The weeklyTaskRewardLog.
       */
      @java.lang.Override
      public long getWeeklyTaskRewardLog() {
        return weeklyTaskRewardLog_;
      }
      /**
       * <pre>
       * 周常任务活跃度奖励领取记录-位运算 0未领取，1已领取 从第一位开始
       * </pre>
       *
       * <code>int64 weeklyTaskRewardLog = 6;</code>
       * @param value The weeklyTaskRewardLog to set.
       * @return This builder for chaining.
       */
      public Builder setWeeklyTaskRewardLog(long value) {

        weeklyTaskRewardLog_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 周常任务活跃度奖励领取记录-位运算 0未领取，1已领取 从第一位开始
       * </pre>
       *
       * <code>int64 weeklyTaskRewardLog = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearWeeklyTaskRewardLog() {
        bitField0_ = (bitField0_ & ~0x00000020);
        weeklyTaskRewardLog_ = 0L;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Task.TaskActive)
    }

    // @@protoc_insertion_point(class_scope:Proto.Task.TaskActive)
    private static final com.dxx.game.dto.TaskProto.TaskActive DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.TaskProto.TaskActive();
    }

    public static com.dxx.game.dto.TaskProto.TaskActive getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<TaskActive>
        PARSER = new com.google.protobuf.AbstractParser<TaskActive>() {
      @java.lang.Override
      public TaskActive parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<TaskActive> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<TaskActive> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.TaskProto.TaskActive getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface TaskRewardDailyRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Task.TaskRewardDailyRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();

    /**
     * <pre>
     * 任务模板id
     * </pre>
     *
     * <code>int32 templateId = 2;</code>
     * @return The templateId.
     */
    int getTemplateId();
  }
  /**
   * <pre>
   * CMD PackageId=10503 任务-每日领取奖励
   * </pre>
   *
   * Protobuf type {@code Proto.Task.TaskRewardDailyRequest}
   */
  public static final class TaskRewardDailyRequest extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Task.TaskRewardDailyRequest)
      TaskRewardDailyRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        TaskRewardDailyRequest.class.getName());
    }
    // Use TaskRewardDailyRequest.newBuilder() to construct.
    private TaskRewardDailyRequest(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private TaskRewardDailyRequest() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskRewardDailyRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskRewardDailyRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.TaskProto.TaskRewardDailyRequest.class, com.dxx.game.dto.TaskProto.TaskRewardDailyRequest.Builder.class);
    }

    private int bitField0_;
    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }

    public static final int TEMPLATEID_FIELD_NUMBER = 2;
    private int templateId_ = 0;
    /**
     * <pre>
     * 任务模板id
     * </pre>
     *
     * <code>int32 templateId = 2;</code>
     * @return The templateId.
     */
    @java.lang.Override
    public int getTemplateId() {
      return templateId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getCommonParams());
      }
      if (templateId_ != 0) {
        output.writeInt32(2, templateId_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      if (templateId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, templateId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.TaskProto.TaskRewardDailyRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.TaskProto.TaskRewardDailyRequest other = (com.dxx.game.dto.TaskProto.TaskRewardDailyRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (getTemplateId()
          != other.getTemplateId()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (37 * hash) + TEMPLATEID_FIELD_NUMBER;
      hash = (53 * hash) + getTemplateId();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.TaskProto.TaskRewardDailyRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardDailyRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardDailyRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardDailyRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardDailyRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardDailyRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardDailyRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardDailyRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.TaskProto.TaskRewardDailyRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.TaskProto.TaskRewardDailyRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardDailyRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardDailyRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.TaskProto.TaskRewardDailyRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=10503 任务-每日领取奖励
     * </pre>
     *
     * Protobuf type {@code Proto.Task.TaskRewardDailyRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Task.TaskRewardDailyRequest)
        com.dxx.game.dto.TaskProto.TaskRewardDailyRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskRewardDailyRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskRewardDailyRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.TaskProto.TaskRewardDailyRequest.class, com.dxx.game.dto.TaskProto.TaskRewardDailyRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.TaskProto.TaskRewardDailyRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getCommonParamsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        templateId_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskRewardDailyRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskRewardDailyRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.TaskProto.TaskRewardDailyRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskRewardDailyRequest build() {
        com.dxx.game.dto.TaskProto.TaskRewardDailyRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskRewardDailyRequest buildPartial() {
        com.dxx.game.dto.TaskProto.TaskRewardDailyRequest result = new com.dxx.game.dto.TaskProto.TaskRewardDailyRequest(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.TaskProto.TaskRewardDailyRequest result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.commonParams_ = commonParamsBuilder_ == null
              ? commonParams_
              : commonParamsBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.templateId_ = templateId_;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.TaskProto.TaskRewardDailyRequest) {
          return mergeFrom((com.dxx.game.dto.TaskProto.TaskRewardDailyRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.TaskProto.TaskRewardDailyRequest other) {
        if (other == com.dxx.game.dto.TaskProto.TaskRewardDailyRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        if (other.getTemplateId() != 0) {
          setTemplateId(other.getTemplateId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getCommonParamsFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 16: {
                templateId_ = input.readInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
        } else {
          commonParamsBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
            commonParams_ != null &&
            commonParams_ != com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance()) {
            getCommonParamsBuilder().mergeFrom(value);
          } else {
            commonParams_ = value;
          }
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }
        if (commonParams_ != null) {
          bitField0_ |= 0x00000001;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        bitField0_ = (bitField0_ & ~0x00000001);
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }

      private int templateId_ ;
      /**
       * <pre>
       * 任务模板id
       * </pre>
       *
       * <code>int32 templateId = 2;</code>
       * @return The templateId.
       */
      @java.lang.Override
      public int getTemplateId() {
        return templateId_;
      }
      /**
       * <pre>
       * 任务模板id
       * </pre>
       *
       * <code>int32 templateId = 2;</code>
       * @param value The templateId to set.
       * @return This builder for chaining.
       */
      public Builder setTemplateId(int value) {

        templateId_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 任务模板id
       * </pre>
       *
       * <code>int32 templateId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearTemplateId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        templateId_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Task.TaskRewardDailyRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.Task.TaskRewardDailyRequest)
    private static final com.dxx.game.dto.TaskProto.TaskRewardDailyRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.TaskProto.TaskRewardDailyRequest();
    }

    public static com.dxx.game.dto.TaskProto.TaskRewardDailyRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<TaskRewardDailyRequest>
        PARSER = new com.google.protobuf.AbstractParser<TaskRewardDailyRequest>() {
      @java.lang.Override
      public TaskRewardDailyRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<TaskRewardDailyRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<TaskRewardDailyRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.TaskProto.TaskRewardDailyRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface TaskRewardDailyResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Task.TaskRewardDailyResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <pre>
     * 活跃度-日
     * </pre>
     *
     * <code>int32 activeDaily = 2;</code>
     * @return The activeDaily.
     */
    int getActiveDaily();

    /**
     * <pre>
     * 活跃度-周
     * </pre>
     *
     * <code>int32 activeWeekly = 3;</code>
     * @return The activeWeekly.
     */
    int getActiveWeekly();
  }
  /**
   * <pre>
   * CMD PackageId=10504
   * </pre>
   *
   * Protobuf type {@code Proto.Task.TaskRewardDailyResponse}
   */
  public static final class TaskRewardDailyResponse extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Task.TaskRewardDailyResponse)
      TaskRewardDailyResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        TaskRewardDailyResponse.class.getName());
    }
    // Use TaskRewardDailyResponse.newBuilder() to construct.
    private TaskRewardDailyResponse(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private TaskRewardDailyResponse() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskRewardDailyResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskRewardDailyResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.TaskProto.TaskRewardDailyResponse.class, com.dxx.game.dto.TaskProto.TaskRewardDailyResponse.Builder.class);
    }

    public static final int CODE_FIELD_NUMBER = 1;
    private int code_ = 0;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int ACTIVEDAILY_FIELD_NUMBER = 2;
    private int activeDaily_ = 0;
    /**
     * <pre>
     * 活跃度-日
     * </pre>
     *
     * <code>int32 activeDaily = 2;</code>
     * @return The activeDaily.
     */
    @java.lang.Override
    public int getActiveDaily() {
      return activeDaily_;
    }

    public static final int ACTIVEWEEKLY_FIELD_NUMBER = 3;
    private int activeWeekly_ = 0;
    /**
     * <pre>
     * 活跃度-周
     * </pre>
     *
     * <code>int32 activeWeekly = 3;</code>
     * @return The activeWeekly.
     */
    @java.lang.Override
    public int getActiveWeekly() {
      return activeWeekly_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (activeDaily_ != 0) {
        output.writeInt32(2, activeDaily_);
      }
      if (activeWeekly_ != 0) {
        output.writeInt32(3, activeWeekly_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (activeDaily_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, activeDaily_);
      }
      if (activeWeekly_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, activeWeekly_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.TaskProto.TaskRewardDailyResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.TaskProto.TaskRewardDailyResponse other = (com.dxx.game.dto.TaskProto.TaskRewardDailyResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (getActiveDaily()
          != other.getActiveDaily()) return false;
      if (getActiveWeekly()
          != other.getActiveWeekly()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      hash = (37 * hash) + ACTIVEDAILY_FIELD_NUMBER;
      hash = (53 * hash) + getActiveDaily();
      hash = (37 * hash) + ACTIVEWEEKLY_FIELD_NUMBER;
      hash = (53 * hash) + getActiveWeekly();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.TaskProto.TaskRewardDailyResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardDailyResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardDailyResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardDailyResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardDailyResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardDailyResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardDailyResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardDailyResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.TaskProto.TaskRewardDailyResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.TaskProto.TaskRewardDailyResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardDailyResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardDailyResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.TaskProto.TaskRewardDailyResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=10504
     * </pre>
     *
     * Protobuf type {@code Proto.Task.TaskRewardDailyResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Task.TaskRewardDailyResponse)
        com.dxx.game.dto.TaskProto.TaskRewardDailyResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskRewardDailyResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskRewardDailyResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.TaskProto.TaskRewardDailyResponse.class, com.dxx.game.dto.TaskProto.TaskRewardDailyResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.TaskProto.TaskRewardDailyResponse.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        code_ = 0;
        activeDaily_ = 0;
        activeWeekly_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskRewardDailyResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskRewardDailyResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.TaskProto.TaskRewardDailyResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskRewardDailyResponse build() {
        com.dxx.game.dto.TaskProto.TaskRewardDailyResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskRewardDailyResponse buildPartial() {
        com.dxx.game.dto.TaskProto.TaskRewardDailyResponse result = new com.dxx.game.dto.TaskProto.TaskRewardDailyResponse(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.TaskProto.TaskRewardDailyResponse result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.code_ = code_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.activeDaily_ = activeDaily_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.activeWeekly_ = activeWeekly_;
        }
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.TaskProto.TaskRewardDailyResponse) {
          return mergeFrom((com.dxx.game.dto.TaskProto.TaskRewardDailyResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.TaskProto.TaskRewardDailyResponse other) {
        if (other == com.dxx.game.dto.TaskProto.TaskRewardDailyResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.getActiveDaily() != 0) {
          setActiveDaily(other.getActiveDaily());
        }
        if (other.getActiveWeekly() != 0) {
          setActiveWeekly(other.getActiveWeekly());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                code_ = input.readInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                activeDaily_ = input.readInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 24: {
                activeWeekly_ = input.readInt32();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {

        code_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        bitField0_ = (bitField0_ & ~0x00000001);
        code_ = 0;
        onChanged();
        return this;
      }

      private int activeDaily_ ;
      /**
       * <pre>
       * 活跃度-日
       * </pre>
       *
       * <code>int32 activeDaily = 2;</code>
       * @return The activeDaily.
       */
      @java.lang.Override
      public int getActiveDaily() {
        return activeDaily_;
      }
      /**
       * <pre>
       * 活跃度-日
       * </pre>
       *
       * <code>int32 activeDaily = 2;</code>
       * @param value The activeDaily to set.
       * @return This builder for chaining.
       */
      public Builder setActiveDaily(int value) {

        activeDaily_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 活跃度-日
       * </pre>
       *
       * <code>int32 activeDaily = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearActiveDaily() {
        bitField0_ = (bitField0_ & ~0x00000002);
        activeDaily_ = 0;
        onChanged();
        return this;
      }

      private int activeWeekly_ ;
      /**
       * <pre>
       * 活跃度-周
       * </pre>
       *
       * <code>int32 activeWeekly = 3;</code>
       * @return The activeWeekly.
       */
      @java.lang.Override
      public int getActiveWeekly() {
        return activeWeekly_;
      }
      /**
       * <pre>
       * 活跃度-周
       * </pre>
       *
       * <code>int32 activeWeekly = 3;</code>
       * @param value The activeWeekly to set.
       * @return This builder for chaining.
       */
      public Builder setActiveWeekly(int value) {

        activeWeekly_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 活跃度-周
       * </pre>
       *
       * <code>int32 activeWeekly = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearActiveWeekly() {
        bitField0_ = (bitField0_ & ~0x00000004);
        activeWeekly_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Task.TaskRewardDailyResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.Task.TaskRewardDailyResponse)
    private static final com.dxx.game.dto.TaskProto.TaskRewardDailyResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.TaskProto.TaskRewardDailyResponse();
    }

    public static com.dxx.game.dto.TaskProto.TaskRewardDailyResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<TaskRewardDailyResponse>
        PARSER = new com.google.protobuf.AbstractParser<TaskRewardDailyResponse>() {
      @java.lang.Override
      public TaskRewardDailyResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<TaskRewardDailyResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<TaskRewardDailyResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.TaskProto.TaskRewardDailyResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface TaskRewardAchieveRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Task.TaskRewardAchieveRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();

    /**
     * <pre>
     * 成就模板id
     * </pre>
     *
     * <code>int32 templateId = 2;</code>
     * @return The templateId.
     */
    int getTemplateId();
  }
  /**
   * <pre>
   * CMD PackageId=10505 成就-领取奖励
   * </pre>
   *
   * Protobuf type {@code Proto.Task.TaskRewardAchieveRequest}
   */
  public static final class TaskRewardAchieveRequest extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Task.TaskRewardAchieveRequest)
      TaskRewardAchieveRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        TaskRewardAchieveRequest.class.getName());
    }
    // Use TaskRewardAchieveRequest.newBuilder() to construct.
    private TaskRewardAchieveRequest(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private TaskRewardAchieveRequest() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskRewardAchieveRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskRewardAchieveRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest.class, com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest.Builder.class);
    }

    private int bitField0_;
    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }

    public static final int TEMPLATEID_FIELD_NUMBER = 2;
    private int templateId_ = 0;
    /**
     * <pre>
     * 成就模板id
     * </pre>
     *
     * <code>int32 templateId = 2;</code>
     * @return The templateId.
     */
    @java.lang.Override
    public int getTemplateId() {
      return templateId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getCommonParams());
      }
      if (templateId_ != 0) {
        output.writeInt32(2, templateId_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      if (templateId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, templateId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest other = (com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (getTemplateId()
          != other.getTemplateId()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (37 * hash) + TEMPLATEID_FIELD_NUMBER;
      hash = (53 * hash) + getTemplateId();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=10505 成就-领取奖励
     * </pre>
     *
     * Protobuf type {@code Proto.Task.TaskRewardAchieveRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Task.TaskRewardAchieveRequest)
        com.dxx.game.dto.TaskProto.TaskRewardAchieveRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskRewardAchieveRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskRewardAchieveRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest.class, com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getCommonParamsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        templateId_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskRewardAchieveRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest build() {
        com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest buildPartial() {
        com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest result = new com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.commonParams_ = commonParamsBuilder_ == null
              ? commonParams_
              : commonParamsBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.templateId_ = templateId_;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest) {
          return mergeFrom((com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest other) {
        if (other == com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        if (other.getTemplateId() != 0) {
          setTemplateId(other.getTemplateId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getCommonParamsFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 16: {
                templateId_ = input.readInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
        } else {
          commonParamsBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
            commonParams_ != null &&
            commonParams_ != com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance()) {
            getCommonParamsBuilder().mergeFrom(value);
          } else {
            commonParams_ = value;
          }
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }
        if (commonParams_ != null) {
          bitField0_ |= 0x00000001;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        bitField0_ = (bitField0_ & ~0x00000001);
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }

      private int templateId_ ;
      /**
       * <pre>
       * 成就模板id
       * </pre>
       *
       * <code>int32 templateId = 2;</code>
       * @return The templateId.
       */
      @java.lang.Override
      public int getTemplateId() {
        return templateId_;
      }
      /**
       * <pre>
       * 成就模板id
       * </pre>
       *
       * <code>int32 templateId = 2;</code>
       * @param value The templateId to set.
       * @return This builder for chaining.
       */
      public Builder setTemplateId(int value) {

        templateId_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 成就模板id
       * </pre>
       *
       * <code>int32 templateId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearTemplateId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        templateId_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Task.TaskRewardAchieveRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.Task.TaskRewardAchieveRequest)
    private static final com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest();
    }

    public static com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<TaskRewardAchieveRequest>
        PARSER = new com.google.protobuf.AbstractParser<TaskRewardAchieveRequest>() {
      @java.lang.Override
      public TaskRewardAchieveRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<TaskRewardAchieveRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<TaskRewardAchieveRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface TaskRewardAchieveResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Task.TaskRewardAchieveResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();
  }
  /**
   * <pre>
   * CMD PackageId=10506
   * </pre>
   *
   * Protobuf type {@code Proto.Task.TaskRewardAchieveResponse}
   */
  public static final class TaskRewardAchieveResponse extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Task.TaskRewardAchieveResponse)
      TaskRewardAchieveResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        TaskRewardAchieveResponse.class.getName());
    }
    // Use TaskRewardAchieveResponse.newBuilder() to construct.
    private TaskRewardAchieveResponse(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private TaskRewardAchieveResponse() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskRewardAchieveResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskRewardAchieveResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse.class, com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse.Builder.class);
    }

    public static final int CODE_FIELD_NUMBER = 1;
    private int code_ = 0;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse other = (com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=10506
     * </pre>
     *
     * Protobuf type {@code Proto.Task.TaskRewardAchieveResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Task.TaskRewardAchieveResponse)
        com.dxx.game.dto.TaskProto.TaskRewardAchieveResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskRewardAchieveResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskRewardAchieveResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse.class, com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        code_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskRewardAchieveResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse build() {
        com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse buildPartial() {
        com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse result = new com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.code_ = code_;
        }
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse) {
          return mergeFrom((com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse other) {
        if (other == com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                code_ = input.readInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {

        code_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        bitField0_ = (bitField0_ & ~0x00000001);
        code_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Task.TaskRewardAchieveResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.Task.TaskRewardAchieveResponse)
    private static final com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse();
    }

    public static com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<TaskRewardAchieveResponse>
        PARSER = new com.google.protobuf.AbstractParser<TaskRewardAchieveResponse>() {
      @java.lang.Override
      public TaskRewardAchieveResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<TaskRewardAchieveResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<TaskRewardAchieveResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface TaskActiveRewardAllRequestOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Task.TaskActiveRewardAllRequest)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    boolean hasCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    com.dxx.game.dto.CommonProto.CommonParams getCommonParams();
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder();

    /**
     * <pre>
     * 活跃度类型： 1每日，2每周
     * </pre>
     *
     * <code>int32 type = 2;</code>
     * @return The type.
     */
    int getType();
  }
  /**
   * <pre>
   * CMD PackageId=10507 任务-领取全活跃度奖励
   * </pre>
   *
   * Protobuf type {@code Proto.Task.TaskActiveRewardAllRequest}
   */
  public static final class TaskActiveRewardAllRequest extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Task.TaskActiveRewardAllRequest)
      TaskActiveRewardAllRequestOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        TaskActiveRewardAllRequest.class.getName());
    }
    // Use TaskActiveRewardAllRequest.newBuilder() to construct.
    private TaskActiveRewardAllRequest(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private TaskActiveRewardAllRequest() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskActiveRewardAllRequest_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskActiveRewardAllRequest_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest.class, com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest.Builder.class);
    }

    private int bitField0_;
    public static final int COMMONPARAMS_FIELD_NUMBER = 1;
    private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return Whether the commonParams field is set.
     */
    @java.lang.Override
    public boolean hasCommonParams() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     * @return The commonParams.
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }
    /**
     * <code>.Proto.Common.CommonParams commonParams = 1;</code>
     */
    @java.lang.Override
    public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
      return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
    }

    public static final int TYPE_FIELD_NUMBER = 2;
    private int type_ = 0;
    /**
     * <pre>
     * 活跃度类型： 1每日，2每周
     * </pre>
     *
     * <code>int32 type = 2;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getCommonParams());
      }
      if (type_ != 0) {
        output.writeInt32(2, type_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCommonParams());
      }
      if (type_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, type_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest other = (com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest) obj;

      if (hasCommonParams() != other.hasCommonParams()) return false;
      if (hasCommonParams()) {
        if (!getCommonParams()
            .equals(other.getCommonParams())) return false;
      }
      if (getType()
          != other.getType()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCommonParams()) {
        hash = (37 * hash) + COMMONPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getCommonParams().hashCode();
      }
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=10507 任务-领取全活跃度奖励
     * </pre>
     *
     * Protobuf type {@code Proto.Task.TaskActiveRewardAllRequest}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Task.TaskActiveRewardAllRequest)
        com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequestOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskActiveRewardAllRequest_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskActiveRewardAllRequest_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest.class, com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest.Builder.class);
      }

      // Construct using com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessage
                .alwaysUseFieldBuilders) {
          getCommonParamsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        type_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskActiveRewardAllRequest_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest getDefaultInstanceForType() {
        return com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest build() {
        com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest buildPartial() {
        com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest result = new com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest result) {
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.commonParams_ = commonParamsBuilder_ == null
              ? commonParams_
              : commonParamsBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.type_ = type_;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest) {
          return mergeFrom((com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest other) {
        if (other == com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest.getDefaultInstance()) return this;
        if (other.hasCommonParams()) {
          mergeCommonParams(other.getCommonParams());
        }
        if (other.getType() != 0) {
          setType(other.getType());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                input.readMessage(
                    getCommonParamsFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 16: {
                type_ = input.readInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private com.dxx.game.dto.CommonProto.CommonParams commonParams_;
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> commonParamsBuilder_;
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return Whether the commonParams field is set.
       */
      public boolean hasCommonParams() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       * @return The commonParams.
       */
      public com.dxx.game.dto.CommonProto.CommonParams getCommonParams() {
        if (commonParamsBuilder_ == null) {
          return commonParams_ == null ? com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        } else {
          return commonParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          commonParams_ = value;
        } else {
          commonParamsBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder setCommonParams(
          com.dxx.game.dto.CommonProto.CommonParams.Builder builderForValue) {
        if (commonParamsBuilder_ == null) {
          commonParams_ = builderForValue.build();
        } else {
          commonParamsBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder mergeCommonParams(com.dxx.game.dto.CommonProto.CommonParams value) {
        if (commonParamsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
            commonParams_ != null &&
            commonParams_ != com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance()) {
            getCommonParamsBuilder().mergeFrom(value);
          } else {
            commonParams_ = value;
          }
        } else {
          commonParamsBuilder_.mergeFrom(value);
        }
        if (commonParams_ != null) {
          bitField0_ |= 0x00000001;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public Builder clearCommonParams() {
        bitField0_ = (bitField0_ & ~0x00000001);
        commonParams_ = null;
        if (commonParamsBuilder_ != null) {
          commonParamsBuilder_.dispose();
          commonParamsBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParams.Builder getCommonParamsBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getCommonParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      public com.dxx.game.dto.CommonProto.CommonParamsOrBuilder getCommonParamsOrBuilder() {
        if (commonParamsBuilder_ != null) {
          return commonParamsBuilder_.getMessageOrBuilder();
        } else {
          return commonParams_ == null ?
              com.dxx.game.dto.CommonProto.CommonParams.getDefaultInstance() : commonParams_;
        }
      }
      /**
       * <code>.Proto.Common.CommonParams commonParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilder<
          com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder> 
          getCommonParamsFieldBuilder() {
        if (commonParamsBuilder_ == null) {
          commonParamsBuilder_ = new com.google.protobuf.SingleFieldBuilder<
              com.dxx.game.dto.CommonProto.CommonParams, com.dxx.game.dto.CommonProto.CommonParams.Builder, com.dxx.game.dto.CommonProto.CommonParamsOrBuilder>(
                  getCommonParams(),
                  getParentForChildren(),
                  isClean());
          commonParams_ = null;
        }
        return commonParamsBuilder_;
      }

      private int type_ ;
      /**
       * <pre>
       * 活跃度类型： 1每日，2每周
       * </pre>
       *
       * <code>int32 type = 2;</code>
       * @return The type.
       */
      @java.lang.Override
      public int getType() {
        return type_;
      }
      /**
       * <pre>
       * 活跃度类型： 1每日，2每周
       * </pre>
       *
       * <code>int32 type = 2;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(int value) {

        type_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 活跃度类型： 1每日，2每周
       * </pre>
       *
       * <code>int32 type = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000002);
        type_ = 0;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Task.TaskActiveRewardAllRequest)
    }

    // @@protoc_insertion_point(class_scope:Proto.Task.TaskActiveRewardAllRequest)
    private static final com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest();
    }

    public static com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<TaskActiveRewardAllRequest>
        PARSER = new com.google.protobuf.AbstractParser<TaskActiveRewardAllRequest>() {
      @java.lang.Override
      public TaskActiveRewardAllRequest parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<TaskActiveRewardAllRequest> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<TaskActiveRewardAllRequest> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface TaskActiveRewardAllResponseOrBuilder extends
      // @@protoc_insertion_point(interface_extends:Proto.Task.TaskActiveRewardAllResponse)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <pre>
     * 类型 1每日，2每周
     * </pre>
     *
     * <code>int32 type = 2;</code>
     * @return The type.
     */
    int getType();

    /**
     * <pre>
     * 活跃度奖励领取记录
     * </pre>
     *
     * <code>int64 rewardLog = 3;</code>
     * @return The rewardLog.
     */
    long getRewardLog();
  }
  /**
   * <pre>
   * CMD PackageId=10508
   * </pre>
   *
   * Protobuf type {@code Proto.Task.TaskActiveRewardAllResponse}
   */
  public static final class TaskActiveRewardAllResponse extends
      com.google.protobuf.GeneratedMessage implements
      // @@protoc_insertion_point(message_implements:Proto.Task.TaskActiveRewardAllResponse)
      TaskActiveRewardAllResponseOrBuilder {
  private static final long serialVersionUID = 0L;
    static {
      com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
        com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
        /* major= */ 4,
        /* minor= */ 29,
        /* patch= */ 0,
        /* suffix= */ "",
        TaskActiveRewardAllResponse.class.getName());
    }
    // Use TaskActiveRewardAllResponse.newBuilder() to construct.
    private TaskActiveRewardAllResponse(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
      super(builder);
    }
    private TaskActiveRewardAllResponse() {
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskActiveRewardAllResponse_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskActiveRewardAllResponse_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse.class, com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse.Builder.class);
    }

    public static final int CODE_FIELD_NUMBER = 1;
    private int code_ = 0;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int TYPE_FIELD_NUMBER = 2;
    private int type_ = 0;
    /**
     * <pre>
     * 类型 1每日，2每周
     * </pre>
     *
     * <code>int32 type = 2;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }

    public static final int REWARDLOG_FIELD_NUMBER = 3;
    private long rewardLog_ = 0L;
    /**
     * <pre>
     * 活跃度奖励领取记录
     * </pre>
     *
     * <code>int64 rewardLog = 3;</code>
     * @return The rewardLog.
     */
    @java.lang.Override
    public long getRewardLog() {
      return rewardLog_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (type_ != 0) {
        output.writeInt32(2, type_);
      }
      if (rewardLog_ != 0L) {
        output.writeInt64(3, rewardLog_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (type_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, type_);
      }
      if (rewardLog_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, rewardLog_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse)) {
        return super.equals(obj);
      }
      com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse other = (com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse) obj;

      if (getCode()
          != other.getCode()) return false;
      if (getType()
          != other.getType()) return false;
      if (getRewardLog()
          != other.getRewardLog()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
      hash = (37 * hash) + REWARDLOG_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getRewardLog());
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input);
    }
    public static com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessage
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * CMD PackageId=10508
     * </pre>
     *
     * Protobuf type {@code Proto.Task.TaskActiveRewardAllResponse}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessage.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:Proto.Task.TaskActiveRewardAllResponse)
        com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponseOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskActiveRewardAllResponse_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskActiveRewardAllResponse_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse.class, com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse.Builder.class);
      }

      // Construct using com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessage.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        code_ = 0;
        type_ = 0;
        rewardLog_ = 0L;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.dxx.game.dto.TaskProto.internal_static_Proto_Task_TaskActiveRewardAllResponse_descriptor;
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse getDefaultInstanceForType() {
        return com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse.getDefaultInstance();
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse build() {
        com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse buildPartial() {
        com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse result = new com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.code_ = code_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.type_ = type_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.rewardLog_ = rewardLog_;
        }
      }

      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse) {
          return mergeFrom((com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse other) {
        if (other == com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.getType() != 0) {
          setType(other.getType());
        }
        if (other.getRewardLog() != 0L) {
          setRewardLog(other.getRewardLog());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                code_ = input.readInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                type_ = input.readInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 24: {
                rewardLog_ = input.readInt64();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {

        code_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        bitField0_ = (bitField0_ & ~0x00000001);
        code_ = 0;
        onChanged();
        return this;
      }

      private int type_ ;
      /**
       * <pre>
       * 类型 1每日，2每周
       * </pre>
       *
       * <code>int32 type = 2;</code>
       * @return The type.
       */
      @java.lang.Override
      public int getType() {
        return type_;
      }
      /**
       * <pre>
       * 类型 1每日，2每周
       * </pre>
       *
       * <code>int32 type = 2;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(int value) {

        type_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 类型 1每日，2每周
       * </pre>
       *
       * <code>int32 type = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000002);
        type_ = 0;
        onChanged();
        return this;
      }

      private long rewardLog_ ;
      /**
       * <pre>
       * 活跃度奖励领取记录
       * </pre>
       *
       * <code>int64 rewardLog = 3;</code>
       * @return The rewardLog.
       */
      @java.lang.Override
      public long getRewardLog() {
        return rewardLog_;
      }
      /**
       * <pre>
       * 活跃度奖励领取记录
       * </pre>
       *
       * <code>int64 rewardLog = 3;</code>
       * @param value The rewardLog to set.
       * @return This builder for chaining.
       */
      public Builder setRewardLog(long value) {

        rewardLog_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 活跃度奖励领取记录
       * </pre>
       *
       * <code>int64 rewardLog = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearRewardLog() {
        bitField0_ = (bitField0_ & ~0x00000004);
        rewardLog_ = 0L;
        onChanged();
        return this;
      }

      // @@protoc_insertion_point(builder_scope:Proto.Task.TaskActiveRewardAllResponse)
    }

    // @@protoc_insertion_point(class_scope:Proto.Task.TaskActiveRewardAllResponse)
    private static final com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse();
    }

    public static com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<TaskActiveRewardAllResponse>
        PARSER = new com.google.protobuf.AbstractParser<TaskActiveRewardAllResponse>() {
      @java.lang.Override
      public TaskActiveRewardAllResponse parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<TaskActiveRewardAllResponse> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<TaskActiveRewardAllResponse> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Task_TaskGetInfoRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Task_TaskGetInfoRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Task_TaskGetInfoResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Task_TaskGetInfoResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Task_TaskActive_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Task_TaskActive_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Task_TaskRewardDailyRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Task_TaskRewardDailyRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Task_TaskRewardDailyResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Task_TaskRewardDailyResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Task_TaskRewardAchieveRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Task_TaskRewardAchieveRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Task_TaskRewardAchieveResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Task_TaskRewardAchieveResponse_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Task_TaskActiveRewardAllRequest_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Task_TaskActiveRewardAllRequest_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Task_TaskActiveRewardAllResponse_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Task_TaskActiveRewardAllResponse_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\ntask.proto\022\nProto.Task\032\014common.proto\"F" +
      "\n\022TaskGetInfoRequest\0220\n\014commonParams\030\001 \001" +
      "(\0132\032.Proto.Common.CommonParams\"y\n\023TaskGe" +
      "tInfoResponse\022\014\n\004code\030\001 \001(\005\022(\n\005tasks\030\002 \003" +
      "(\0132\031.Proto.Common.TaskInfoDto\022*\n\nactiveI" +
      "nfo\030\003 \001(\0132\026.Proto.Task.TaskActive\"\261\001\n\nTa" +
      "skActive\022\027\n\017dailyTaskActive\030\001 \001(\005\022\032\n\022dai" +
      "lyTaskResetTime\030\002 \001(\003\022\032\n\022dailyTaskReward" +
      "Log\030\003 \001(\003\022\030\n\020weeklyTaskActive\030\004 \001(\005\022\033\n\023w" +
      "eeklyTaskResetTime\030\005 \001(\003\022\033\n\023weeklyTaskRe" +
      "wardLog\030\006 \001(\003\"^\n\026TaskRewardDailyRequest\022" +
      "0\n\014commonParams\030\001 \001(\0132\032.Proto.Common.Com" +
      "monParams\022\022\n\ntemplateId\030\002 \001(\005\"R\n\027TaskRew" +
      "ardDailyResponse\022\014\n\004code\030\001 \001(\005\022\023\n\013active" +
      "Daily\030\002 \001(\005\022\024\n\014activeWeekly\030\003 \001(\005\"`\n\030Tas" +
      "kRewardAchieveRequest\0220\n\014commonParams\030\001 " +
      "\001(\0132\032.Proto.Common.CommonParams\022\022\n\ntempl" +
      "ateId\030\002 \001(\005\")\n\031TaskRewardAchieveResponse" +
      "\022\014\n\004code\030\001 \001(\005\"\\\n\032TaskActiveRewardAllReq" +
      "uest\0220\n\014commonParams\030\001 \001(\0132\032.Proto.Commo" +
      "n.CommonParams\022\014\n\004type\030\002 \001(\005\"L\n\033TaskActi" +
      "veRewardAllResponse\022\014\n\004code\030\001 \001(\005\022\014\n\004typ" +
      "e\030\002 \001(\005\022\021\n\trewardLog\030\003 \001(\003B\035\n\020com.dxx.ga" +
      "me.dtoB\tTaskProtob\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.dxx.game.dto.CommonProto.getDescriptor(),
        });
    internal_static_Proto_Task_TaskGetInfoRequest_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_Proto_Task_TaskGetInfoRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Task_TaskGetInfoRequest_descriptor,
        new java.lang.String[] { "CommonParams", });
    internal_static_Proto_Task_TaskGetInfoResponse_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_Proto_Task_TaskGetInfoResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Task_TaskGetInfoResponse_descriptor,
        new java.lang.String[] { "Code", "Tasks", "ActiveInfo", });
    internal_static_Proto_Task_TaskActive_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_Proto_Task_TaskActive_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Task_TaskActive_descriptor,
        new java.lang.String[] { "DailyTaskActive", "DailyTaskResetTime", "DailyTaskRewardLog", "WeeklyTaskActive", "WeeklyTaskResetTime", "WeeklyTaskRewardLog", });
    internal_static_Proto_Task_TaskRewardDailyRequest_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_Proto_Task_TaskRewardDailyRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Task_TaskRewardDailyRequest_descriptor,
        new java.lang.String[] { "CommonParams", "TemplateId", });
    internal_static_Proto_Task_TaskRewardDailyResponse_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_Proto_Task_TaskRewardDailyResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Task_TaskRewardDailyResponse_descriptor,
        new java.lang.String[] { "Code", "ActiveDaily", "ActiveWeekly", });
    internal_static_Proto_Task_TaskRewardAchieveRequest_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_Proto_Task_TaskRewardAchieveRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Task_TaskRewardAchieveRequest_descriptor,
        new java.lang.String[] { "CommonParams", "TemplateId", });
    internal_static_Proto_Task_TaskRewardAchieveResponse_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_Proto_Task_TaskRewardAchieveResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Task_TaskRewardAchieveResponse_descriptor,
        new java.lang.String[] { "Code", });
    internal_static_Proto_Task_TaskActiveRewardAllRequest_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_Proto_Task_TaskActiveRewardAllRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Task_TaskActiveRewardAllRequest_descriptor,
        new java.lang.String[] { "CommonParams", "Type", });
    internal_static_Proto_Task_TaskActiveRewardAllResponse_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_Proto_Task_TaskActiveRewardAllResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Task_TaskActiveRewardAllResponse_descriptor,
        new java.lang.String[] { "Code", "Type", "RewardLog", });
    descriptor.resolveAllFeaturesImmutable();
    com.dxx.game.dto.CommonProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
