// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: vip.proto
// Protobuf Java Version: 4.29.0

package com.dxx.game.dto;

/**
 * Protobuf type {@code Proto.Vip.VipInfo}
 */
public final class VipInfo extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:Proto.Vip.VipInfo)
    VipInfoOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 29,
      /* patch= */ 0,
      /* suffix= */ "",
      VipInfo.class.getName());
  }
  // Use VipInfo.newBuilder() to construct.
  private VipInfo(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private VipInfo() {
    details_ = java.util.Collections.emptyList();
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.dxx.game.dto.VipProto.internal_static_Proto_Vip_VipInfo_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.dxx.game.dto.VipProto.internal_static_Proto_Vip_VipInfo_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.dxx.game.dto.VipInfo.class, com.dxx.game.dto.VipInfo.Builder.class);
  }

  private int bitField0_;
  public static final int LEVELINFO_FIELD_NUMBER = 1;
  private com.dxx.game.dto.CommonProto.UserVipLevel levelInfo_;
  /**
   * <code>.Proto.Common.UserVipLevel levelInfo = 1;</code>
   * @return Whether the levelInfo field is set.
   */
  @java.lang.Override
  public boolean hasLevelInfo() {
    return ((bitField0_ & 0x00000001) != 0);
  }
  /**
   * <code>.Proto.Common.UserVipLevel levelInfo = 1;</code>
   * @return The levelInfo.
   */
  @java.lang.Override
  public com.dxx.game.dto.CommonProto.UserVipLevel getLevelInfo() {
    return levelInfo_ == null ? com.dxx.game.dto.CommonProto.UserVipLevel.getDefaultInstance() : levelInfo_;
  }
  /**
   * <code>.Proto.Common.UserVipLevel levelInfo = 1;</code>
   */
  @java.lang.Override
  public com.dxx.game.dto.CommonProto.UserVipLevelOrBuilder getLevelInfoOrBuilder() {
    return levelInfo_ == null ? com.dxx.game.dto.CommonProto.UserVipLevel.getDefaultInstance() : levelInfo_;
  }

  public static final int DETAILS_FIELD_NUMBER = 2;
  @SuppressWarnings("serial")
  private java.util.List<com.dxx.game.dto.VipLevelDetail> details_;
  /**
   * <pre>
   * 一次都没有买过礼包或者领取过每日奖励的等级在这里不存在
   * </pre>
   *
   * <code>repeated .Proto.Vip.VipLevelDetail details = 2;</code>
   */
  @java.lang.Override
  public java.util.List<com.dxx.game.dto.VipLevelDetail> getDetailsList() {
    return details_;
  }
  /**
   * <pre>
   * 一次都没有买过礼包或者领取过每日奖励的等级在这里不存在
   * </pre>
   *
   * <code>repeated .Proto.Vip.VipLevelDetail details = 2;</code>
   */
  @java.lang.Override
  public java.util.List<? extends com.dxx.game.dto.VipLevelDetailOrBuilder> 
      getDetailsOrBuilderList() {
    return details_;
  }
  /**
   * <pre>
   * 一次都没有买过礼包或者领取过每日奖励的等级在这里不存在
   * </pre>
   *
   * <code>repeated .Proto.Vip.VipLevelDetail details = 2;</code>
   */
  @java.lang.Override
  public int getDetailsCount() {
    return details_.size();
  }
  /**
   * <pre>
   * 一次都没有买过礼包或者领取过每日奖励的等级在这里不存在
   * </pre>
   *
   * <code>repeated .Proto.Vip.VipLevelDetail details = 2;</code>
   */
  @java.lang.Override
  public com.dxx.game.dto.VipLevelDetail getDetails(int index) {
    return details_.get(index);
  }
  /**
   * <pre>
   * 一次都没有买过礼包或者领取过每日奖励的等级在这里不存在
   * </pre>
   *
   * <code>repeated .Proto.Vip.VipLevelDetail details = 2;</code>
   */
  @java.lang.Override
  public com.dxx.game.dto.VipLevelDetailOrBuilder getDetailsOrBuilder(
      int index) {
    return details_.get(index);
  }

  public static final int NEXTRESETTIME_FIELD_NUMBER = 3;
  private long nextResetTime_ = 0L;
  /**
   * <pre>
   * </pre>
   *
   * <code>int64 nextResetTime = 3;</code>
   * @return The nextResetTime.
   */
  @java.lang.Override
  public long getNextResetTime() {
    return nextResetTime_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (((bitField0_ & 0x00000001) != 0)) {
      output.writeMessage(1, getLevelInfo());
    }
    for (int i = 0; i < details_.size(); i++) {
      output.writeMessage(2, details_.get(i));
    }
    if (nextResetTime_ != 0L) {
      output.writeInt64(3, nextResetTime_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (((bitField0_ & 0x00000001) != 0)) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(1, getLevelInfo());
    }
    for (int i = 0; i < details_.size(); i++) {
      size += com.google.protobuf.CodedOutputStream
        .computeMessageSize(2, details_.get(i));
    }
    if (nextResetTime_ != 0L) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt64Size(3, nextResetTime_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.dxx.game.dto.VipInfo)) {
      return super.equals(obj);
    }
    com.dxx.game.dto.VipInfo other = (com.dxx.game.dto.VipInfo) obj;

    if (hasLevelInfo() != other.hasLevelInfo()) return false;
    if (hasLevelInfo()) {
      if (!getLevelInfo()
          .equals(other.getLevelInfo())) return false;
    }
    if (!getDetailsList()
        .equals(other.getDetailsList())) return false;
    if (getNextResetTime()
        != other.getNextResetTime()) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    if (hasLevelInfo()) {
      hash = (37 * hash) + LEVELINFO_FIELD_NUMBER;
      hash = (53 * hash) + getLevelInfo().hashCode();
    }
    if (getDetailsCount() > 0) {
      hash = (37 * hash) + DETAILS_FIELD_NUMBER;
      hash = (53 * hash) + getDetailsList().hashCode();
    }
    hash = (37 * hash) + NEXTRESETTIME_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
        getNextResetTime());
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.dxx.game.dto.VipInfo parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.dxx.game.dto.VipInfo parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.dxx.game.dto.VipInfo parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.dxx.game.dto.VipInfo parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.dxx.game.dto.VipInfo parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.dxx.game.dto.VipInfo parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.dxx.game.dto.VipInfo parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static com.dxx.game.dto.VipInfo parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.dxx.game.dto.VipInfo parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.dxx.game.dto.VipInfo parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.dxx.game.dto.VipInfo parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static com.dxx.game.dto.VipInfo parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.dxx.game.dto.VipInfo prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code Proto.Vip.VipInfo}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:Proto.Vip.VipInfo)
      com.dxx.game.dto.VipInfoOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.VipProto.internal_static_Proto_Vip_VipInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.VipProto.internal_static_Proto_Vip_VipInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.VipInfo.class, com.dxx.game.dto.VipInfo.Builder.class);
    }

    // Construct using com.dxx.game.dto.VipInfo.newBuilder()
    private Builder() {
      maybeForceBuilderInitialization();
    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);
      maybeForceBuilderInitialization();
    }
    private void maybeForceBuilderInitialization() {
      if (com.google.protobuf.GeneratedMessage
              .alwaysUseFieldBuilders) {
        getLevelInfoFieldBuilder();
        getDetailsFieldBuilder();
      }
    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      levelInfo_ = null;
      if (levelInfoBuilder_ != null) {
        levelInfoBuilder_.dispose();
        levelInfoBuilder_ = null;
      }
      if (detailsBuilder_ == null) {
        details_ = java.util.Collections.emptyList();
      } else {
        details_ = null;
        detailsBuilder_.clear();
      }
      bitField0_ = (bitField0_ & ~0x00000002);
      nextResetTime_ = 0L;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.dxx.game.dto.VipProto.internal_static_Proto_Vip_VipInfo_descriptor;
    }

    @java.lang.Override
    public com.dxx.game.dto.VipInfo getDefaultInstanceForType() {
      return com.dxx.game.dto.VipInfo.getDefaultInstance();
    }

    @java.lang.Override
    public com.dxx.game.dto.VipInfo build() {
      com.dxx.game.dto.VipInfo result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.dxx.game.dto.VipInfo buildPartial() {
      com.dxx.game.dto.VipInfo result = new com.dxx.game.dto.VipInfo(this);
      buildPartialRepeatedFields(result);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartialRepeatedFields(com.dxx.game.dto.VipInfo result) {
      if (detailsBuilder_ == null) {
        if (((bitField0_ & 0x00000002) != 0)) {
          details_ = java.util.Collections.unmodifiableList(details_);
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.details_ = details_;
      } else {
        result.details_ = detailsBuilder_.build();
      }
    }

    private void buildPartial0(com.dxx.game.dto.VipInfo result) {
      int from_bitField0_ = bitField0_;
      int to_bitField0_ = 0;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.levelInfo_ = levelInfoBuilder_ == null
            ? levelInfo_
            : levelInfoBuilder_.build();
        to_bitField0_ |= 0x00000001;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.nextResetTime_ = nextResetTime_;
      }
      result.bitField0_ |= to_bitField0_;
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.dxx.game.dto.VipInfo) {
        return mergeFrom((com.dxx.game.dto.VipInfo)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.dxx.game.dto.VipInfo other) {
      if (other == com.dxx.game.dto.VipInfo.getDefaultInstance()) return this;
      if (other.hasLevelInfo()) {
        mergeLevelInfo(other.getLevelInfo());
      }
      if (detailsBuilder_ == null) {
        if (!other.details_.isEmpty()) {
          if (details_.isEmpty()) {
            details_ = other.details_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureDetailsIsMutable();
            details_.addAll(other.details_);
          }
          onChanged();
        }
      } else {
        if (!other.details_.isEmpty()) {
          if (detailsBuilder_.isEmpty()) {
            detailsBuilder_.dispose();
            detailsBuilder_ = null;
            details_ = other.details_;
            bitField0_ = (bitField0_ & ~0x00000002);
            detailsBuilder_ = 
              com.google.protobuf.GeneratedMessage.alwaysUseFieldBuilders ?
                 getDetailsFieldBuilder() : null;
          } else {
            detailsBuilder_.addAllMessages(other.details_);
          }
        }
      }
      if (other.getNextResetTime() != 0L) {
        setNextResetTime(other.getNextResetTime());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              input.readMessage(
                  getLevelInfoFieldBuilder().getBuilder(),
                  extensionRegistry);
              bitField0_ |= 0x00000001;
              break;
            } // case 10
            case 18: {
              com.dxx.game.dto.VipLevelDetail m =
                  input.readMessage(
                      com.dxx.game.dto.VipLevelDetail.parser(),
                      extensionRegistry);
              if (detailsBuilder_ == null) {
                ensureDetailsIsMutable();
                details_.add(m);
              } else {
                detailsBuilder_.addMessage(m);
              }
              break;
            } // case 18
            case 24: {
              nextResetTime_ = input.readInt64();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private com.dxx.game.dto.CommonProto.UserVipLevel levelInfo_;
    private com.google.protobuf.SingleFieldBuilder<
        com.dxx.game.dto.CommonProto.UserVipLevel, com.dxx.game.dto.CommonProto.UserVipLevel.Builder, com.dxx.game.dto.CommonProto.UserVipLevelOrBuilder> levelInfoBuilder_;
    /**
     * <code>.Proto.Common.UserVipLevel levelInfo = 1;</code>
     * @return Whether the levelInfo field is set.
     */
    public boolean hasLevelInfo() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.Proto.Common.UserVipLevel levelInfo = 1;</code>
     * @return The levelInfo.
     */
    public com.dxx.game.dto.CommonProto.UserVipLevel getLevelInfo() {
      if (levelInfoBuilder_ == null) {
        return levelInfo_ == null ? com.dxx.game.dto.CommonProto.UserVipLevel.getDefaultInstance() : levelInfo_;
      } else {
        return levelInfoBuilder_.getMessage();
      }
    }
    /**
     * <code>.Proto.Common.UserVipLevel levelInfo = 1;</code>
     */
    public Builder setLevelInfo(com.dxx.game.dto.CommonProto.UserVipLevel value) {
      if (levelInfoBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        levelInfo_ = value;
      } else {
        levelInfoBuilder_.setMessage(value);
      }
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>.Proto.Common.UserVipLevel levelInfo = 1;</code>
     */
    public Builder setLevelInfo(
        com.dxx.game.dto.CommonProto.UserVipLevel.Builder builderForValue) {
      if (levelInfoBuilder_ == null) {
        levelInfo_ = builderForValue.build();
      } else {
        levelInfoBuilder_.setMessage(builderForValue.build());
      }
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>.Proto.Common.UserVipLevel levelInfo = 1;</code>
     */
    public Builder mergeLevelInfo(com.dxx.game.dto.CommonProto.UserVipLevel value) {
      if (levelInfoBuilder_ == null) {
        if (((bitField0_ & 0x00000001) != 0) &&
          levelInfo_ != null &&
          levelInfo_ != com.dxx.game.dto.CommonProto.UserVipLevel.getDefaultInstance()) {
          getLevelInfoBuilder().mergeFrom(value);
        } else {
          levelInfo_ = value;
        }
      } else {
        levelInfoBuilder_.mergeFrom(value);
      }
      if (levelInfo_ != null) {
        bitField0_ |= 0x00000001;
        onChanged();
      }
      return this;
    }
    /**
     * <code>.Proto.Common.UserVipLevel levelInfo = 1;</code>
     */
    public Builder clearLevelInfo() {
      bitField0_ = (bitField0_ & ~0x00000001);
      levelInfo_ = null;
      if (levelInfoBuilder_ != null) {
        levelInfoBuilder_.dispose();
        levelInfoBuilder_ = null;
      }
      onChanged();
      return this;
    }
    /**
     * <code>.Proto.Common.UserVipLevel levelInfo = 1;</code>
     */
    public com.dxx.game.dto.CommonProto.UserVipLevel.Builder getLevelInfoBuilder() {
      bitField0_ |= 0x00000001;
      onChanged();
      return getLevelInfoFieldBuilder().getBuilder();
    }
    /**
     * <code>.Proto.Common.UserVipLevel levelInfo = 1;</code>
     */
    public com.dxx.game.dto.CommonProto.UserVipLevelOrBuilder getLevelInfoOrBuilder() {
      if (levelInfoBuilder_ != null) {
        return levelInfoBuilder_.getMessageOrBuilder();
      } else {
        return levelInfo_ == null ?
            com.dxx.game.dto.CommonProto.UserVipLevel.getDefaultInstance() : levelInfo_;
      }
    }
    /**
     * <code>.Proto.Common.UserVipLevel levelInfo = 1;</code>
     */
    private com.google.protobuf.SingleFieldBuilder<
        com.dxx.game.dto.CommonProto.UserVipLevel, com.dxx.game.dto.CommonProto.UserVipLevel.Builder, com.dxx.game.dto.CommonProto.UserVipLevelOrBuilder> 
        getLevelInfoFieldBuilder() {
      if (levelInfoBuilder_ == null) {
        levelInfoBuilder_ = new com.google.protobuf.SingleFieldBuilder<
            com.dxx.game.dto.CommonProto.UserVipLevel, com.dxx.game.dto.CommonProto.UserVipLevel.Builder, com.dxx.game.dto.CommonProto.UserVipLevelOrBuilder>(
                getLevelInfo(),
                getParentForChildren(),
                isClean());
        levelInfo_ = null;
      }
      return levelInfoBuilder_;
    }

    private java.util.List<com.dxx.game.dto.VipLevelDetail> details_ =
      java.util.Collections.emptyList();
    private void ensureDetailsIsMutable() {
      if (!((bitField0_ & 0x00000002) != 0)) {
        details_ = new java.util.ArrayList<com.dxx.game.dto.VipLevelDetail>(details_);
        bitField0_ |= 0x00000002;
       }
    }

    private com.google.protobuf.RepeatedFieldBuilder<
        com.dxx.game.dto.VipLevelDetail, com.dxx.game.dto.VipLevelDetail.Builder, com.dxx.game.dto.VipLevelDetailOrBuilder> detailsBuilder_;

    /**
     * <pre>
     * 一次都没有买过礼包或者领取过每日奖励的等级在这里不存在
     * </pre>
     *
     * <code>repeated .Proto.Vip.VipLevelDetail details = 2;</code>
     */
    public java.util.List<com.dxx.game.dto.VipLevelDetail> getDetailsList() {
      if (detailsBuilder_ == null) {
        return java.util.Collections.unmodifiableList(details_);
      } else {
        return detailsBuilder_.getMessageList();
      }
    }
    /**
     * <pre>
     * 一次都没有买过礼包或者领取过每日奖励的等级在这里不存在
     * </pre>
     *
     * <code>repeated .Proto.Vip.VipLevelDetail details = 2;</code>
     */
    public int getDetailsCount() {
      if (detailsBuilder_ == null) {
        return details_.size();
      } else {
        return detailsBuilder_.getCount();
      }
    }
    /**
     * <pre>
     * 一次都没有买过礼包或者领取过每日奖励的等级在这里不存在
     * </pre>
     *
     * <code>repeated .Proto.Vip.VipLevelDetail details = 2;</code>
     */
    public com.dxx.game.dto.VipLevelDetail getDetails(int index) {
      if (detailsBuilder_ == null) {
        return details_.get(index);
      } else {
        return detailsBuilder_.getMessage(index);
      }
    }
    /**
     * <pre>
     * 一次都没有买过礼包或者领取过每日奖励的等级在这里不存在
     * </pre>
     *
     * <code>repeated .Proto.Vip.VipLevelDetail details = 2;</code>
     */
    public Builder setDetails(
        int index, com.dxx.game.dto.VipLevelDetail value) {
      if (detailsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDetailsIsMutable();
        details_.set(index, value);
        onChanged();
      } else {
        detailsBuilder_.setMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 一次都没有买过礼包或者领取过每日奖励的等级在这里不存在
     * </pre>
     *
     * <code>repeated .Proto.Vip.VipLevelDetail details = 2;</code>
     */
    public Builder setDetails(
        int index, com.dxx.game.dto.VipLevelDetail.Builder builderForValue) {
      if (detailsBuilder_ == null) {
        ensureDetailsIsMutable();
        details_.set(index, builderForValue.build());
        onChanged();
      } else {
        detailsBuilder_.setMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 一次都没有买过礼包或者领取过每日奖励的等级在这里不存在
     * </pre>
     *
     * <code>repeated .Proto.Vip.VipLevelDetail details = 2;</code>
     */
    public Builder addDetails(com.dxx.game.dto.VipLevelDetail value) {
      if (detailsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDetailsIsMutable();
        details_.add(value);
        onChanged();
      } else {
        detailsBuilder_.addMessage(value);
      }
      return this;
    }
    /**
     * <pre>
     * 一次都没有买过礼包或者领取过每日奖励的等级在这里不存在
     * </pre>
     *
     * <code>repeated .Proto.Vip.VipLevelDetail details = 2;</code>
     */
    public Builder addDetails(
        int index, com.dxx.game.dto.VipLevelDetail value) {
      if (detailsBuilder_ == null) {
        if (value == null) {
          throw new NullPointerException();
        }
        ensureDetailsIsMutable();
        details_.add(index, value);
        onChanged();
      } else {
        detailsBuilder_.addMessage(index, value);
      }
      return this;
    }
    /**
     * <pre>
     * 一次都没有买过礼包或者领取过每日奖励的等级在这里不存在
     * </pre>
     *
     * <code>repeated .Proto.Vip.VipLevelDetail details = 2;</code>
     */
    public Builder addDetails(
        com.dxx.game.dto.VipLevelDetail.Builder builderForValue) {
      if (detailsBuilder_ == null) {
        ensureDetailsIsMutable();
        details_.add(builderForValue.build());
        onChanged();
      } else {
        detailsBuilder_.addMessage(builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 一次都没有买过礼包或者领取过每日奖励的等级在这里不存在
     * </pre>
     *
     * <code>repeated .Proto.Vip.VipLevelDetail details = 2;</code>
     */
    public Builder addDetails(
        int index, com.dxx.game.dto.VipLevelDetail.Builder builderForValue) {
      if (detailsBuilder_ == null) {
        ensureDetailsIsMutable();
        details_.add(index, builderForValue.build());
        onChanged();
      } else {
        detailsBuilder_.addMessage(index, builderForValue.build());
      }
      return this;
    }
    /**
     * <pre>
     * 一次都没有买过礼包或者领取过每日奖励的等级在这里不存在
     * </pre>
     *
     * <code>repeated .Proto.Vip.VipLevelDetail details = 2;</code>
     */
    public Builder addAllDetails(
        java.lang.Iterable<? extends com.dxx.game.dto.VipLevelDetail> values) {
      if (detailsBuilder_ == null) {
        ensureDetailsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, details_);
        onChanged();
      } else {
        detailsBuilder_.addAllMessages(values);
      }
      return this;
    }
    /**
     * <pre>
     * 一次都没有买过礼包或者领取过每日奖励的等级在这里不存在
     * </pre>
     *
     * <code>repeated .Proto.Vip.VipLevelDetail details = 2;</code>
     */
    public Builder clearDetails() {
      if (detailsBuilder_ == null) {
        details_ = java.util.Collections.emptyList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
      } else {
        detailsBuilder_.clear();
      }
      return this;
    }
    /**
     * <pre>
     * 一次都没有买过礼包或者领取过每日奖励的等级在这里不存在
     * </pre>
     *
     * <code>repeated .Proto.Vip.VipLevelDetail details = 2;</code>
     */
    public Builder removeDetails(int index) {
      if (detailsBuilder_ == null) {
        ensureDetailsIsMutable();
        details_.remove(index);
        onChanged();
      } else {
        detailsBuilder_.remove(index);
      }
      return this;
    }
    /**
     * <pre>
     * 一次都没有买过礼包或者领取过每日奖励的等级在这里不存在
     * </pre>
     *
     * <code>repeated .Proto.Vip.VipLevelDetail details = 2;</code>
     */
    public com.dxx.game.dto.VipLevelDetail.Builder getDetailsBuilder(
        int index) {
      return getDetailsFieldBuilder().getBuilder(index);
    }
    /**
     * <pre>
     * 一次都没有买过礼包或者领取过每日奖励的等级在这里不存在
     * </pre>
     *
     * <code>repeated .Proto.Vip.VipLevelDetail details = 2;</code>
     */
    public com.dxx.game.dto.VipLevelDetailOrBuilder getDetailsOrBuilder(
        int index) {
      if (detailsBuilder_ == null) {
        return details_.get(index);  } else {
        return detailsBuilder_.getMessageOrBuilder(index);
      }
    }
    /**
     * <pre>
     * 一次都没有买过礼包或者领取过每日奖励的等级在这里不存在
     * </pre>
     *
     * <code>repeated .Proto.Vip.VipLevelDetail details = 2;</code>
     */
    public java.util.List<? extends com.dxx.game.dto.VipLevelDetailOrBuilder> 
         getDetailsOrBuilderList() {
      if (detailsBuilder_ != null) {
        return detailsBuilder_.getMessageOrBuilderList();
      } else {
        return java.util.Collections.unmodifiableList(details_);
      }
    }
    /**
     * <pre>
     * 一次都没有买过礼包或者领取过每日奖励的等级在这里不存在
     * </pre>
     *
     * <code>repeated .Proto.Vip.VipLevelDetail details = 2;</code>
     */
    public com.dxx.game.dto.VipLevelDetail.Builder addDetailsBuilder() {
      return getDetailsFieldBuilder().addBuilder(
          com.dxx.game.dto.VipLevelDetail.getDefaultInstance());
    }
    /**
     * <pre>
     * 一次都没有买过礼包或者领取过每日奖励的等级在这里不存在
     * </pre>
     *
     * <code>repeated .Proto.Vip.VipLevelDetail details = 2;</code>
     */
    public com.dxx.game.dto.VipLevelDetail.Builder addDetailsBuilder(
        int index) {
      return getDetailsFieldBuilder().addBuilder(
          index, com.dxx.game.dto.VipLevelDetail.getDefaultInstance());
    }
    /**
     * <pre>
     * 一次都没有买过礼包或者领取过每日奖励的等级在这里不存在
     * </pre>
     *
     * <code>repeated .Proto.Vip.VipLevelDetail details = 2;</code>
     */
    public java.util.List<com.dxx.game.dto.VipLevelDetail.Builder> 
         getDetailsBuilderList() {
      return getDetailsFieldBuilder().getBuilderList();
    }
    private com.google.protobuf.RepeatedFieldBuilder<
        com.dxx.game.dto.VipLevelDetail, com.dxx.game.dto.VipLevelDetail.Builder, com.dxx.game.dto.VipLevelDetailOrBuilder> 
        getDetailsFieldBuilder() {
      if (detailsBuilder_ == null) {
        detailsBuilder_ = new com.google.protobuf.RepeatedFieldBuilder<
            com.dxx.game.dto.VipLevelDetail, com.dxx.game.dto.VipLevelDetail.Builder, com.dxx.game.dto.VipLevelDetailOrBuilder>(
                details_,
                ((bitField0_ & 0x00000002) != 0),
                getParentForChildren(),
                isClean());
        details_ = null;
      }
      return detailsBuilder_;
    }

    private long nextResetTime_ ;
    /**
     * <pre>
     * </pre>
     *
     * <code>int64 nextResetTime = 3;</code>
     * @return The nextResetTime.
     */
    @java.lang.Override
    public long getNextResetTime() {
      return nextResetTime_;
    }
    /**
     * <pre>
     * </pre>
     *
     * <code>int64 nextResetTime = 3;</code>
     * @param value The nextResetTime to set.
     * @return This builder for chaining.
     */
    public Builder setNextResetTime(long value) {

      nextResetTime_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * </pre>
     *
     * <code>int64 nextResetTime = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearNextResetTime() {
      bitField0_ = (bitField0_ & ~0x00000004);
      nextResetTime_ = 0L;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:Proto.Vip.VipInfo)
  }

  // @@protoc_insertion_point(class_scope:Proto.Vip.VipInfo)
  private static final com.dxx.game.dto.VipInfo DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.dxx.game.dto.VipInfo();
  }

  public static com.dxx.game.dto.VipInfo getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<VipInfo>
      PARSER = new com.google.protobuf.AbstractParser<VipInfo>() {
    @java.lang.Override
    public VipInfo parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<VipInfo> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<VipInfo> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.dxx.game.dto.VipInfo getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

