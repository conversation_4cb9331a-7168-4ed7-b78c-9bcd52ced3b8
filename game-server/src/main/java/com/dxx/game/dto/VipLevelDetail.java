// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: vip.proto
// Protobuf Java Version: 4.29.0

package com.dxx.game.dto;

/**
 * Protobuf type {@code Proto.Vip.VipLevelDetail}
 */
public final class VipLevelDetail extends
    com.google.protobuf.GeneratedMessage implements
    // @@protoc_insertion_point(message_implements:Proto.Vip.VipLevelDetail)
    VipLevelDetailOrBuilder {
private static final long serialVersionUID = 0L;
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 29,
      /* patch= */ 0,
      /* suffix= */ "",
      VipLevelDetail.class.getName());
  }
  // Use VipLevelDetail.newBuilder() to construct.
  private VipLevelDetail(com.google.protobuf.GeneratedMessage.Builder<?> builder) {
    super(builder);
  }
  private VipLevelDetail() {
  }

  public static final com.google.protobuf.Descriptors.Descriptor
      getDescriptor() {
    return com.dxx.game.dto.VipProto.internal_static_Proto_Vip_VipLevelDetail_descriptor;
  }

  @java.lang.Override
  protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internalGetFieldAccessorTable() {
    return com.dxx.game.dto.VipProto.internal_static_Proto_Vip_VipLevelDetail_fieldAccessorTable
        .ensureFieldAccessorsInitialized(
            com.dxx.game.dto.VipLevelDetail.class, com.dxx.game.dto.VipLevelDetail.Builder.class);
  }

  public static final int LEVEL_FIELD_NUMBER = 1;
  private int level_ = 0;
  /**
   * <code>int32 level = 1;</code>
   * @return The level.
   */
  @java.lang.Override
  public int getLevel() {
    return level_;
  }

  public static final int GOTDAILYREWARD_FIELD_NUMBER = 2;
  private boolean gotDailyReward_ = false;
  /**
   * <pre>
   * 是否领取了当前等级的每日奖励
   * </pre>
   *
   * <code>bool gotDailyReward = 2;</code>
   * @return The gotDailyReward.
   */
  @java.lang.Override
  public boolean getGotDailyReward() {
    return gotDailyReward_;
  }

  public static final int BUYCOUNT_FIELD_NUMBER = 3;
  private int buyCount_ = 0;
  /**
   * <pre>
   * 当前等级礼包购买的次数
   * </pre>
   *
   * <code>int32 buyCount = 3;</code>
   * @return The buyCount.
   */
  @java.lang.Override
  public int getBuyCount() {
    return buyCount_;
  }

  private byte memoizedIsInitialized = -1;
  @java.lang.Override
  public final boolean isInitialized() {
    byte isInitialized = memoizedIsInitialized;
    if (isInitialized == 1) return true;
    if (isInitialized == 0) return false;

    memoizedIsInitialized = 1;
    return true;
  }

  @java.lang.Override
  public void writeTo(com.google.protobuf.CodedOutputStream output)
                      throws java.io.IOException {
    if (level_ != 0) {
      output.writeInt32(1, level_);
    }
    if (gotDailyReward_ != false) {
      output.writeBool(2, gotDailyReward_);
    }
    if (buyCount_ != 0) {
      output.writeInt32(3, buyCount_);
    }
    getUnknownFields().writeTo(output);
  }

  @java.lang.Override
  public int getSerializedSize() {
    int size = memoizedSize;
    if (size != -1) return size;

    size = 0;
    if (level_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(1, level_);
    }
    if (gotDailyReward_ != false) {
      size += com.google.protobuf.CodedOutputStream
        .computeBoolSize(2, gotDailyReward_);
    }
    if (buyCount_ != 0) {
      size += com.google.protobuf.CodedOutputStream
        .computeInt32Size(3, buyCount_);
    }
    size += getUnknownFields().getSerializedSize();
    memoizedSize = size;
    return size;
  }

  @java.lang.Override
  public boolean equals(final java.lang.Object obj) {
    if (obj == this) {
     return true;
    }
    if (!(obj instanceof com.dxx.game.dto.VipLevelDetail)) {
      return super.equals(obj);
    }
    com.dxx.game.dto.VipLevelDetail other = (com.dxx.game.dto.VipLevelDetail) obj;

    if (getLevel()
        != other.getLevel()) return false;
    if (getGotDailyReward()
        != other.getGotDailyReward()) return false;
    if (getBuyCount()
        != other.getBuyCount()) return false;
    if (!getUnknownFields().equals(other.getUnknownFields())) return false;
    return true;
  }

  @java.lang.Override
  public int hashCode() {
    if (memoizedHashCode != 0) {
      return memoizedHashCode;
    }
    int hash = 41;
    hash = (19 * hash) + getDescriptor().hashCode();
    hash = (37 * hash) + LEVEL_FIELD_NUMBER;
    hash = (53 * hash) + getLevel();
    hash = (37 * hash) + GOTDAILYREWARD_FIELD_NUMBER;
    hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
        getGotDailyReward());
    hash = (37 * hash) + BUYCOUNT_FIELD_NUMBER;
    hash = (53 * hash) + getBuyCount();
    hash = (29 * hash) + getUnknownFields().hashCode();
    memoizedHashCode = hash;
    return hash;
  }

  public static com.dxx.game.dto.VipLevelDetail parseFrom(
      java.nio.ByteBuffer data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.dxx.game.dto.VipLevelDetail parseFrom(
      java.nio.ByteBuffer data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.dxx.game.dto.VipLevelDetail parseFrom(
      com.google.protobuf.ByteString data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.dxx.game.dto.VipLevelDetail parseFrom(
      com.google.protobuf.ByteString data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.dxx.game.dto.VipLevelDetail parseFrom(byte[] data)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data);
  }
  public static com.dxx.game.dto.VipLevelDetail parseFrom(
      byte[] data,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws com.google.protobuf.InvalidProtocolBufferException {
    return PARSER.parseFrom(data, extensionRegistry);
  }
  public static com.dxx.game.dto.VipLevelDetail parseFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static com.dxx.game.dto.VipLevelDetail parseFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  public static com.dxx.game.dto.VipLevelDetail parseDelimitedFrom(java.io.InputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input);
  }

  public static com.dxx.game.dto.VipLevelDetail parseDelimitedFrom(
      java.io.InputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
  }
  public static com.dxx.game.dto.VipLevelDetail parseFrom(
      com.google.protobuf.CodedInputStream input)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input);
  }
  public static com.dxx.game.dto.VipLevelDetail parseFrom(
      com.google.protobuf.CodedInputStream input,
      com.google.protobuf.ExtensionRegistryLite extensionRegistry)
      throws java.io.IOException {
    return com.google.protobuf.GeneratedMessage
        .parseWithIOException(PARSER, input, extensionRegistry);
  }

  @java.lang.Override
  public Builder newBuilderForType() { return newBuilder(); }
  public static Builder newBuilder() {
    return DEFAULT_INSTANCE.toBuilder();
  }
  public static Builder newBuilder(com.dxx.game.dto.VipLevelDetail prototype) {
    return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
  }
  @java.lang.Override
  public Builder toBuilder() {
    return this == DEFAULT_INSTANCE
        ? new Builder() : new Builder().mergeFrom(this);
  }

  @java.lang.Override
  protected Builder newBuilderForType(
      com.google.protobuf.GeneratedMessage.BuilderParent parent) {
    Builder builder = new Builder(parent);
    return builder;
  }
  /**
   * Protobuf type {@code Proto.Vip.VipLevelDetail}
   */
  public static final class Builder extends
      com.google.protobuf.GeneratedMessage.Builder<Builder> implements
      // @@protoc_insertion_point(builder_implements:Proto.Vip.VipLevelDetail)
      com.dxx.game.dto.VipLevelDetailOrBuilder {
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.dxx.game.dto.VipProto.internal_static_Proto_Vip_VipLevelDetail_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessage.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.dxx.game.dto.VipProto.internal_static_Proto_Vip_VipLevelDetail_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.dxx.game.dto.VipLevelDetail.class, com.dxx.game.dto.VipLevelDetail.Builder.class);
    }

    // Construct using com.dxx.game.dto.VipLevelDetail.newBuilder()
    private Builder() {

    }

    private Builder(
        com.google.protobuf.GeneratedMessage.BuilderParent parent) {
      super(parent);

    }
    @java.lang.Override
    public Builder clear() {
      super.clear();
      bitField0_ = 0;
      level_ = 0;
      gotDailyReward_ = false;
      buyCount_ = 0;
      return this;
    }

    @java.lang.Override
    public com.google.protobuf.Descriptors.Descriptor
        getDescriptorForType() {
      return com.dxx.game.dto.VipProto.internal_static_Proto_Vip_VipLevelDetail_descriptor;
    }

    @java.lang.Override
    public com.dxx.game.dto.VipLevelDetail getDefaultInstanceForType() {
      return com.dxx.game.dto.VipLevelDetail.getDefaultInstance();
    }

    @java.lang.Override
    public com.dxx.game.dto.VipLevelDetail build() {
      com.dxx.game.dto.VipLevelDetail result = buildPartial();
      if (!result.isInitialized()) {
        throw newUninitializedMessageException(result);
      }
      return result;
    }

    @java.lang.Override
    public com.dxx.game.dto.VipLevelDetail buildPartial() {
      com.dxx.game.dto.VipLevelDetail result = new com.dxx.game.dto.VipLevelDetail(this);
      if (bitField0_ != 0) { buildPartial0(result); }
      onBuilt();
      return result;
    }

    private void buildPartial0(com.dxx.game.dto.VipLevelDetail result) {
      int from_bitField0_ = bitField0_;
      if (((from_bitField0_ & 0x00000001) != 0)) {
        result.level_ = level_;
      }
      if (((from_bitField0_ & 0x00000002) != 0)) {
        result.gotDailyReward_ = gotDailyReward_;
      }
      if (((from_bitField0_ & 0x00000004) != 0)) {
        result.buyCount_ = buyCount_;
      }
    }

    @java.lang.Override
    public Builder mergeFrom(com.google.protobuf.Message other) {
      if (other instanceof com.dxx.game.dto.VipLevelDetail) {
        return mergeFrom((com.dxx.game.dto.VipLevelDetail)other);
      } else {
        super.mergeFrom(other);
        return this;
      }
    }

    public Builder mergeFrom(com.dxx.game.dto.VipLevelDetail other) {
      if (other == com.dxx.game.dto.VipLevelDetail.getDefaultInstance()) return this;
      if (other.getLevel() != 0) {
        setLevel(other.getLevel());
      }
      if (other.getGotDailyReward() != false) {
        setGotDailyReward(other.getGotDailyReward());
      }
      if (other.getBuyCount() != 0) {
        setBuyCount(other.getBuyCount());
      }
      this.mergeUnknownFields(other.getUnknownFields());
      onChanged();
      return this;
    }

    @java.lang.Override
    public final boolean isInitialized() {
      return true;
    }

    @java.lang.Override
    public Builder mergeFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              level_ = input.readInt32();
              bitField0_ |= 0x00000001;
              break;
            } // case 8
            case 16: {
              gotDailyReward_ = input.readBool();
              bitField0_ |= 0x00000002;
              break;
            } // case 16
            case 24: {
              buyCount_ = input.readInt32();
              bitField0_ |= 0x00000004;
              break;
            } // case 24
            default: {
              if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                done = true; // was an endgroup tag
              }
              break;
            } // default:
          } // switch (tag)
        } // while (!done)
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.unwrapIOException();
      } finally {
        onChanged();
      } // finally
      return this;
    }
    private int bitField0_;

    private int level_ ;
    /**
     * <code>int32 level = 1;</code>
     * @return The level.
     */
    @java.lang.Override
    public int getLevel() {
      return level_;
    }
    /**
     * <code>int32 level = 1;</code>
     * @param value The level to set.
     * @return This builder for chaining.
     */
    public Builder setLevel(int value) {

      level_ = value;
      bitField0_ |= 0x00000001;
      onChanged();
      return this;
    }
    /**
     * <code>int32 level = 1;</code>
     * @return This builder for chaining.
     */
    public Builder clearLevel() {
      bitField0_ = (bitField0_ & ~0x00000001);
      level_ = 0;
      onChanged();
      return this;
    }

    private boolean gotDailyReward_ ;
    /**
     * <pre>
     * 是否领取了当前等级的每日奖励
     * </pre>
     *
     * <code>bool gotDailyReward = 2;</code>
     * @return The gotDailyReward.
     */
    @java.lang.Override
    public boolean getGotDailyReward() {
      return gotDailyReward_;
    }
    /**
     * <pre>
     * 是否领取了当前等级的每日奖励
     * </pre>
     *
     * <code>bool gotDailyReward = 2;</code>
     * @param value The gotDailyReward to set.
     * @return This builder for chaining.
     */
    public Builder setGotDailyReward(boolean value) {

      gotDailyReward_ = value;
      bitField0_ |= 0x00000002;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 是否领取了当前等级的每日奖励
     * </pre>
     *
     * <code>bool gotDailyReward = 2;</code>
     * @return This builder for chaining.
     */
    public Builder clearGotDailyReward() {
      bitField0_ = (bitField0_ & ~0x00000002);
      gotDailyReward_ = false;
      onChanged();
      return this;
    }

    private int buyCount_ ;
    /**
     * <pre>
     * 当前等级礼包购买的次数
     * </pre>
     *
     * <code>int32 buyCount = 3;</code>
     * @return The buyCount.
     */
    @java.lang.Override
    public int getBuyCount() {
      return buyCount_;
    }
    /**
     * <pre>
     * 当前等级礼包购买的次数
     * </pre>
     *
     * <code>int32 buyCount = 3;</code>
     * @param value The buyCount to set.
     * @return This builder for chaining.
     */
    public Builder setBuyCount(int value) {

      buyCount_ = value;
      bitField0_ |= 0x00000004;
      onChanged();
      return this;
    }
    /**
     * <pre>
     * 当前等级礼包购买的次数
     * </pre>
     *
     * <code>int32 buyCount = 3;</code>
     * @return This builder for chaining.
     */
    public Builder clearBuyCount() {
      bitField0_ = (bitField0_ & ~0x00000004);
      buyCount_ = 0;
      onChanged();
      return this;
    }

    // @@protoc_insertion_point(builder_scope:Proto.Vip.VipLevelDetail)
  }

  // @@protoc_insertion_point(class_scope:Proto.Vip.VipLevelDetail)
  private static final com.dxx.game.dto.VipLevelDetail DEFAULT_INSTANCE;
  static {
    DEFAULT_INSTANCE = new com.dxx.game.dto.VipLevelDetail();
  }

  public static com.dxx.game.dto.VipLevelDetail getDefaultInstance() {
    return DEFAULT_INSTANCE;
  }

  private static final com.google.protobuf.Parser<VipLevelDetail>
      PARSER = new com.google.protobuf.AbstractParser<VipLevelDetail>() {
    @java.lang.Override
    public VipLevelDetail parsePartialFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      Builder builder = newBuilder();
      try {
        builder.mergeFrom(input, extensionRegistry);
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(builder.buildPartial());
      } catch (com.google.protobuf.UninitializedMessageException e) {
        throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(e)
            .setUnfinishedMessage(builder.buildPartial());
      }
      return builder.buildPartial();
    }
  };

  public static com.google.protobuf.Parser<VipLevelDetail> parser() {
    return PARSER;
  }

  @java.lang.Override
  public com.google.protobuf.Parser<VipLevelDetail> getParserForType() {
    return PARSER;
  }

  @java.lang.Override
  public com.dxx.game.dto.VipLevelDetail getDefaultInstanceForType() {
    return DEFAULT_INSTANCE;
  }

}

