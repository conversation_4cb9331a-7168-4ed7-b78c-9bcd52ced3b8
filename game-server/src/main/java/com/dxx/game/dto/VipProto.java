// Generated by the protocol buffer compiler.  DO NOT EDIT!
// NO CHECKED-IN PROTOBUF GENCODE
// source: vip.proto
// Protobuf Java Version: 4.29.0

package com.dxx.game.dto;

public final class VipProto {
  private VipProto() {}
  static {
    com.google.protobuf.RuntimeVersion.validateProtobufGencodeVersion(
      com.google.protobuf.RuntimeVersion.RuntimeDomain.PUBLIC,
      /* major= */ 4,
      /* minor= */ 29,
      /* patch= */ 0,
      /* suffix= */ "",
      VipProto.class.getName());
  }
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Vip_VipInfo_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Vip_VipInfo_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Vip_VipLevelDetail_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Vip_VipLevelDetail_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Vip_VipGetInfoRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Vip_VipGetInfoRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Vip_VipGetInfoResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Vip_VipGetInfoResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Vip_VipDailyRewardRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Vip_VipDailyRewardRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Vip_VipDailyRewardResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Vip_VipDailyRewardResponse_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Vip_VipBuyGiftRequest_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Vip_VipBuyGiftRequest_fieldAccessorTable;
  static final com.google.protobuf.Descriptors.Descriptor
    internal_static_Proto_Vip_VipBuyGiftResponse_descriptor;
  static final 
    com.google.protobuf.GeneratedMessage.FieldAccessorTable
      internal_static_Proto_Vip_VipBuyGiftResponse_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\tvip.proto\022\tProto.Vip\032\014common.proto\"{\n\007" +
      "VipInfo\022-\n\tlevelInfo\030\001 \001(\0132\032.Proto.Commo" +
      "n.UserVipLevel\022*\n\007details\030\002 \003(\0132\031.Proto." +
      "Vip.VipLevelDetail\022\025\n\rnextResetTime\030\003 \001(" +
      "\003\"I\n\016VipLevelDetail\022\r\n\005level\030\001 \001(\005\022\026\n\016go" +
      "tDailyReward\030\002 \001(\010\022\020\n\010buyCount\030\003 \001(\005\"E\n\021" +
      "VipGetInfoRequest\0220\n\014commonParams\030\001 \001(\0132" +
      "\032.Proto.Common.CommonParams\"D\n\022VipGetInf" +
      "oResponse\022\014\n\004code\030\001 \001(\005\022 \n\004info\030\002 \001(\0132\022." +
      "Proto.Vip.VipInfo\"X\n\025VipDailyRewardReque" +
      "st\0220\n\014commonParams\030\001 \001(\0132\032.Proto.Common." +
      "CommonParams\022\r\n\005level\030\002 \001(\005\"\177\n\026VipDailyR" +
      "ewardResponse\022\014\n\004code\030\001 \001(\005\022,\n\ncommonDat" +
      "a\030\002 \001(\0132\030.Proto.Common.CommonData\022)\n\006det" +
      "ail\030\003 \001(\0132\031.Proto.Vip.VipLevelDetail\"T\n\021" +
      "VipBuyGiftRequest\0220\n\014commonParams\030\001 \001(\0132" +
      "\032.Proto.Common.CommonParams\022\r\n\005level\030\002 \001" +
      "(\005\"{\n\022VipBuyGiftResponse\022\014\n\004code\030\001 \001(\005\022," +
      "\n\ncommonData\030\002 \001(\0132\030.Proto.Common.Common" +
      "Data\022)\n\006detail\030\003 \001(\0132\031.Proto.Vip.VipLeve" +
      "lDetailB\036\n\020com.dxx.game.dtoB\010VipProtoP\001b" +
      "\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.dxx.game.dto.CommonProto.getDescriptor(),
        });
    internal_static_Proto_Vip_VipInfo_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_Proto_Vip_VipInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Vip_VipInfo_descriptor,
        new java.lang.String[] { "LevelInfo", "Details", "NextResetTime", });
    internal_static_Proto_Vip_VipLevelDetail_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_Proto_Vip_VipLevelDetail_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Vip_VipLevelDetail_descriptor,
        new java.lang.String[] { "Level", "GotDailyReward", "BuyCount", });
    internal_static_Proto_Vip_VipGetInfoRequest_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_Proto_Vip_VipGetInfoRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Vip_VipGetInfoRequest_descriptor,
        new java.lang.String[] { "CommonParams", });
    internal_static_Proto_Vip_VipGetInfoResponse_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_Proto_Vip_VipGetInfoResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Vip_VipGetInfoResponse_descriptor,
        new java.lang.String[] { "Code", "Info", });
    internal_static_Proto_Vip_VipDailyRewardRequest_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_Proto_Vip_VipDailyRewardRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Vip_VipDailyRewardRequest_descriptor,
        new java.lang.String[] { "CommonParams", "Level", });
    internal_static_Proto_Vip_VipDailyRewardResponse_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_Proto_Vip_VipDailyRewardResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Vip_VipDailyRewardResponse_descriptor,
        new java.lang.String[] { "Code", "CommonData", "Detail", });
    internal_static_Proto_Vip_VipBuyGiftRequest_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_Proto_Vip_VipBuyGiftRequest_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Vip_VipBuyGiftRequest_descriptor,
        new java.lang.String[] { "CommonParams", "Level", });
    internal_static_Proto_Vip_VipBuyGiftResponse_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_Proto_Vip_VipBuyGiftResponse_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessage.FieldAccessorTable(
        internal_static_Proto_Vip_VipBuyGiftResponse_descriptor,
        new java.lang.String[] { "Code", "CommonData", "Detail", });
    descriptor.resolveAllFeaturesImmutable();
    com.dxx.game.dto.CommonProto.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
