// Generated by tools/proto/createMsgId.js.  DO NOT EDIT!

package com.dxx.game.dto.support;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.dxx.game.common.server.protocol.MessageProto;

@javax.annotation.Generated("tools/proto/createMsgId.js")
@Component
public class GameProtoSupport {
	
	@Autowired
	private MessageProto messageProto;
	
	@PostConstruct
	private void initialize() {
		messageProto.<com.dxx.game.dto.PayProto.PayInAppPurchaseRequest, com.dxx.game.dto.PayProto.PayInAppPurchaseRequest.Builder>registerMessage((short)1, com.dxx.game.dto.PayProto.PayInAppPurchaseRequest.class, null, null);
		messageProto.<com.dxx.game.dto.PayProto.PayInAppPurchaseResponse, com.dxx.game.dto.PayProto.PayInAppPurchaseResponse.Builder>registerMessage((short)2, com.dxx.game.dto.PayProto.PayInAppPurchaseResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.PayProto.PayPreOrderRequest, com.dxx.game.dto.PayProto.PayPreOrderRequest.Builder>registerMessage((short)3, com.dxx.game.dto.PayProto.PayPreOrderRequest.class, null, null);
		messageProto.<com.dxx.game.dto.PayProto.PayPreOrderResponse, com.dxx.game.dto.PayProto.PayPreOrderResponse.Builder>registerMessage((short)4, com.dxx.game.dto.PayProto.PayPreOrderResponse.class, null, null);
		messageProto.<com.dxx.game.dto.ShopProto.ShopBuyIAPItemRequest, com.dxx.game.dto.ShopProto.ShopBuyIAPItemRequest.Builder>registerMessage((short)5, com.dxx.game.dto.ShopProto.ShopBuyIAPItemRequest.class, null, null);
		messageProto.<com.dxx.game.dto.ShopProto.ShopBuyIAPItemResponse, com.dxx.game.dto.ShopProto.ShopBuyIAPItemResponse.Builder>registerMessage((short)6, com.dxx.game.dto.ShopProto.ShopBuyIAPItemResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.ShopProto.ShopFreeIAPItemRequest, com.dxx.game.dto.ShopProto.ShopFreeIAPItemRequest.Builder>registerMessage((short)7, com.dxx.game.dto.ShopProto.ShopFreeIAPItemRequest.class, null, null);
		messageProto.<com.dxx.game.dto.ShopProto.ShopFreeIAPItemResponse, com.dxx.game.dto.ShopProto.ShopFreeIAPItemResponse.Builder>registerMessage((short)8, com.dxx.game.dto.ShopProto.ShopFreeIAPItemResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.PayProto.FirstRechargeRewardRequest, com.dxx.game.dto.PayProto.FirstRechargeRewardRequest.Builder>registerMessage((short)9, com.dxx.game.dto.PayProto.FirstRechargeRewardRequest.class, null, null);
		messageProto.<com.dxx.game.dto.PayProto.FirstRechargeRewardResponse, com.dxx.game.dto.PayProto.FirstRechargeRewardResponse.Builder>registerMessage((short)10, com.dxx.game.dto.PayProto.FirstRechargeRewardResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.DevelopProto.DevelopLoginRequest, com.dxx.game.dto.DevelopProto.DevelopLoginRequest.Builder>registerMessage((short)9001, com.dxx.game.dto.DevelopProto.DevelopLoginRequest.class, null, null);
		messageProto.<com.dxx.game.dto.DevelopProto.DevelopLoginResponse, com.dxx.game.dto.DevelopProto.DevelopLoginResponse.Builder>registerMessage((short)9002, com.dxx.game.dto.DevelopProto.DevelopLoginResponse.class, null, null);
		messageProto.<com.dxx.game.dto.DevelopProto.DevelopChangeResourceRequest, com.dxx.game.dto.DevelopProto.DevelopChangeResourceRequest.Builder>registerMessage((short)9003, com.dxx.game.dto.DevelopProto.DevelopChangeResourceRequest.class, null, null);
		messageProto.<com.dxx.game.dto.DevelopProto.DevelopChangeResourceResponse, com.dxx.game.dto.DevelopProto.DevelopChangeResourceResponse.Builder>registerMessage((short)9004, com.dxx.game.dto.DevelopProto.DevelopChangeResourceResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.DevelopProto.DevelopToolsRequest, com.dxx.game.dto.DevelopProto.DevelopToolsRequest.Builder>registerMessage((short)9005, com.dxx.game.dto.DevelopProto.DevelopToolsRequest.class, null, null);
		messageProto.<com.dxx.game.dto.DevelopProto.DevelopToolsResponse, com.dxx.game.dto.DevelopProto.DevelopToolsResponse.Builder>registerMessage((short)9006, com.dxx.game.dto.DevelopProto.DevelopToolsResponse.class, null, null);
		messageProto.<com.dxx.game.dto.DevelopProto.DevelopCmdRequest, com.dxx.game.dto.DevelopProto.DevelopCmdRequest.Builder>registerMessage((short)9007, com.dxx.game.dto.DevelopProto.DevelopCmdRequest.class, null, null);
		messageProto.<com.dxx.game.dto.DevelopProto.DevelopCmdResponse, com.dxx.game.dto.DevelopProto.DevelopCmdResponse.Builder>registerMessage((short)9008, com.dxx.game.dto.DevelopProto.DevelopCmdResponse.class, null, null);
		messageProto.<com.dxx.game.dto.CommonProto.ErrorMsg, com.dxx.game.dto.CommonProto.ErrorMsg.Builder>registerMessage((short)9999, com.dxx.game.dto.CommonProto.ErrorMsg.class, null, null);
		messageProto.<com.dxx.game.dto.UserProto.UserLoginRequest, com.dxx.game.dto.UserProto.UserLoginRequest.Builder>registerMessage((short)10101, com.dxx.game.dto.UserProto.UserLoginRequest.class, null, null);
		messageProto.<com.dxx.game.dto.UserProto.UserLoginResponse, com.dxx.game.dto.UserProto.UserLoginResponse.Builder>registerMessage((short)10102, com.dxx.game.dto.UserProto.UserLoginResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.UserProto.UserGetInfoRequest, com.dxx.game.dto.UserProto.UserGetInfoRequest.Builder>registerMessage((short)10103, com.dxx.game.dto.UserProto.UserGetInfoRequest.class, null, null);
		messageProto.<com.dxx.game.dto.UserProto.UserGetInfoResponse, com.dxx.game.dto.UserProto.UserGetInfoResponse.Builder>registerMessage((short)10104, com.dxx.game.dto.UserProto.UserGetInfoResponse.class, null, null);
		messageProto.<com.dxx.game.dto.UserProto.UserHeartbeatRequest, com.dxx.game.dto.UserProto.UserHeartbeatRequest.Builder>registerMessage((short)10105, com.dxx.game.dto.UserProto.UserHeartbeatRequest.class, null, null);
		messageProto.<com.dxx.game.dto.UserProto.UserHeartbeatResponse, com.dxx.game.dto.UserProto.UserHeartbeatResponse.Builder>registerMessage((short)10106, com.dxx.game.dto.UserProto.UserHeartbeatResponse.class, null, null);
		messageProto.<com.dxx.game.dto.UserProto.UserUpdateSystemMaskRequest, com.dxx.game.dto.UserProto.UserUpdateSystemMaskRequest.Builder>registerMessage((short)10107, com.dxx.game.dto.UserProto.UserUpdateSystemMaskRequest.class, null, null);
		messageProto.<com.dxx.game.dto.UserProto.UserUpdateSystemMaskResponse, com.dxx.game.dto.UserProto.UserUpdateSystemMaskResponse.Builder>registerMessage((short)10108, com.dxx.game.dto.UserProto.UserUpdateSystemMaskResponse.class, null, null);
		messageProto.<com.dxx.game.dto.UserProto.UserUpdateGuideMaskRequest, com.dxx.game.dto.UserProto.UserUpdateGuideMaskRequest.Builder>registerMessage((short)10109, com.dxx.game.dto.UserProto.UserUpdateGuideMaskRequest.class, null, null);
		messageProto.<com.dxx.game.dto.UserProto.UserUpdateGuideMaskResponse, com.dxx.game.dto.UserProto.UserUpdateGuideMaskResponse.Builder>registerMessage((short)10110, com.dxx.game.dto.UserProto.UserUpdateGuideMaskResponse.class, null, null);
		messageProto.<com.dxx.game.dto.UserProto.UserCancelAccountRequest, com.dxx.game.dto.UserProto.UserCancelAccountRequest.Builder>registerMessage((short)10111, com.dxx.game.dto.UserProto.UserCancelAccountRequest.class, null, null);
		messageProto.<com.dxx.game.dto.UserProto.UserCancelAccountResponse, com.dxx.game.dto.UserProto.UserCancelAccountResponse.Builder>registerMessage((short)10112, com.dxx.game.dto.UserProto.UserCancelAccountResponse.class, null, null);
		messageProto.<com.dxx.game.dto.UserProto.UserUpdateInfoRequest, com.dxx.game.dto.UserProto.UserUpdateInfoRequest.Builder>registerMessage((short)10113, com.dxx.game.dto.UserProto.UserUpdateInfoRequest.class, null, null);
		messageProto.<com.dxx.game.dto.UserProto.UserUpdateInfoResponse, com.dxx.game.dto.UserProto.UserUpdateInfoResponse.Builder>registerMessage((short)10114, com.dxx.game.dto.UserProto.UserUpdateInfoResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.UserProto.UserGetPlayerInfoRequest, com.dxx.game.dto.UserProto.UserGetPlayerInfoRequest.Builder>registerMessage((short)10115, com.dxx.game.dto.UserProto.UserGetPlayerInfoRequest.class, null, null);
		messageProto.<com.dxx.game.dto.UserProto.UserGetPlayerInfoResponse, com.dxx.game.dto.UserProto.UserGetPlayerInfoResponse.Builder>registerMessage((short)10116, com.dxx.game.dto.UserProto.UserGetPlayerInfoResponse.class, null, null);
		messageProto.<com.dxx.game.dto.UserProto.UserGetCityInfoRequest, com.dxx.game.dto.UserProto.UserGetCityInfoRequest.Builder>registerMessage((short)10119, com.dxx.game.dto.UserProto.UserGetCityInfoRequest.class, null, null);
		messageProto.<com.dxx.game.dto.UserProto.UserGetCityInfoResponse, com.dxx.game.dto.UserProto.UserGetCityInfoResponse.Builder>registerMessage((short)10120, com.dxx.game.dto.UserProto.UserGetCityInfoResponse.class, null, null);
		messageProto.<com.dxx.game.dto.UserProto.UserHeartbeatSyncRequest, com.dxx.game.dto.UserProto.UserHeartbeatSyncRequest.Builder>registerMessage((short)10121, com.dxx.game.dto.UserProto.UserHeartbeatSyncRequest.class, null, null);
		messageProto.<com.dxx.game.dto.UserProto.UserHeartbeatSyncResponse, com.dxx.game.dto.UserProto.UserHeartbeatSyncResponse.Builder>registerMessage((short)10122, com.dxx.game.dto.UserProto.UserHeartbeatSyncResponse.class, null, null);
		messageProto.<com.dxx.game.dto.UserProto.UserGetBattleReportRequest, com.dxx.game.dto.UserProto.UserGetBattleReportRequest.Builder>registerMessage((short)10123, com.dxx.game.dto.UserProto.UserGetBattleReportRequest.class, null, null);
		messageProto.<com.dxx.game.dto.UserProto.UserGetBattleReportResponse, com.dxx.game.dto.UserProto.UserGetBattleReportResponse.Builder>registerMessage((short)10124, com.dxx.game.dto.UserProto.UserGetBattleReportResponse.class, null, null);
		messageProto.<com.dxx.game.dto.UserProto.UserOpenModelRequest, com.dxx.game.dto.UserProto.UserOpenModelRequest.Builder>registerMessage((short)10125, com.dxx.game.dto.UserProto.UserOpenModelRequest.class, null, null);
		messageProto.<com.dxx.game.dto.UserProto.UserOpenModelResponse, com.dxx.game.dto.UserProto.UserOpenModelResponse.Builder>registerMessage((short)10126, com.dxx.game.dto.UserProto.UserOpenModelResponse.class, null, null);
		messageProto.<com.dxx.game.dto.UserProto.UserRefDataRequest, com.dxx.game.dto.UserProto.UserRefDataRequest.Builder>registerMessage((short)10127, com.dxx.game.dto.UserProto.UserRefDataRequest.class, null, null);
		messageProto.<com.dxx.game.dto.UserProto.UserRefDataResponse, com.dxx.game.dto.UserProto.UserRefDataResponse.Builder>registerMessage((short)10128, com.dxx.game.dto.UserProto.UserRefDataResponse.class, null, null);
		messageProto.<com.dxx.game.dto.UserProto.UserCloseModelRequest, com.dxx.game.dto.UserProto.UserCloseModelRequest.Builder>registerMessage((short)10129, com.dxx.game.dto.UserProto.UserCloseModelRequest.class, null, null);
		messageProto.<com.dxx.game.dto.UserProto.UserCloseModelResponse, com.dxx.game.dto.UserProto.UserCloseModelResponse.Builder>registerMessage((short)10130, com.dxx.game.dto.UserProto.UserCloseModelResponse.class, null, null);
		messageProto.<com.dxx.game.dto.UserProto.UserSetShenFenRequest, com.dxx.game.dto.UserProto.UserSetShenFenRequest.Builder>registerMessage((short)10131, com.dxx.game.dto.UserProto.UserSetShenFenRequest.class, null, null);
		messageProto.<com.dxx.game.dto.UserProto.UserSetShenFenResponse, com.dxx.game.dto.UserProto.UserSetShenFenResponse.Builder>registerMessage((short)10132, com.dxx.game.dto.UserProto.UserSetShenFenResponse.class, null, null);
		messageProto.<com.dxx.game.dto.UserProto.AccountSignInRequest, com.dxx.game.dto.UserProto.AccountSignInRequest.Builder>registerMessage((short)10133, com.dxx.game.dto.UserProto.AccountSignInRequest.class, null, null);
		messageProto.<com.dxx.game.dto.UserProto.AccountSignInResponse, com.dxx.game.dto.UserProto.AccountSignInResponse.Builder>registerMessage((short)10134, com.dxx.game.dto.UserProto.AccountSignInResponse.class, null, null);
		messageProto.<com.dxx.game.dto.UserProto.AccountLoginRequest, com.dxx.game.dto.UserProto.AccountLoginRequest.Builder>registerMessage((short)10135, com.dxx.game.dto.UserProto.AccountLoginRequest.class, null, null);
		messageProto.<com.dxx.game.dto.UserProto.AccountLoginResponse, com.dxx.game.dto.UserProto.AccountLoginResponse.Builder>registerMessage((short)10136, com.dxx.game.dto.UserProto.AccountLoginResponse.class, null, null);
		messageProto.<com.dxx.game.dto.UserProto.UnlockAvatarRequest, com.dxx.game.dto.UserProto.UnlockAvatarRequest.Builder>registerMessage((short)10137, com.dxx.game.dto.UserProto.UnlockAvatarRequest.class, null, null);
		messageProto.<com.dxx.game.dto.UserProto.UnlockAvatarResponse, com.dxx.game.dto.UserProto.UnlockAvatarResponse.Builder>registerMessage((short)10138, com.dxx.game.dto.UserProto.UnlockAvatarResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.UserProto.UserGetOtherPlayerInfoRequest, com.dxx.game.dto.UserProto.UserGetOtherPlayerInfoRequest.Builder>registerMessage((short)10139, com.dxx.game.dto.UserProto.UserGetOtherPlayerInfoRequest.class, null, null);
		messageProto.<com.dxx.game.dto.UserProto.UserGetOtherPlayerInfoResponse, com.dxx.game.dto.UserProto.UserGetOtherPlayerInfoResponse.Builder>registerMessage((short)10140, com.dxx.game.dto.UserProto.UserGetOtherPlayerInfoResponse.class, null, null);
		messageProto.<com.dxx.game.dto.WatchAdRequest, com.dxx.game.dto.WatchAdRequest.Builder>registerMessage((short)10141, com.dxx.game.dto.WatchAdRequest.class, null, null);
		messageProto.<com.dxx.game.dto.WatchAdResponse, com.dxx.game.dto.WatchAdResponse.Builder>registerMessage((short)10142, com.dxx.game.dto.WatchAdResponse.class, null, null);
		messageProto.<com.dxx.game.dto.UserProto.UserHabbyMailBindRequest, com.dxx.game.dto.UserProto.UserHabbyMailBindRequest.Builder>registerMessage((short)10143, com.dxx.game.dto.UserProto.UserHabbyMailBindRequest.class, null, null);
		messageProto.<com.dxx.game.dto.UserProto.UserHabbyMailBindResponse, com.dxx.game.dto.UserProto.UserHabbyMailBindResponse.Builder>registerMessage((short)10144, com.dxx.game.dto.UserProto.UserHabbyMailBindResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.UserProto.BattlePowerChangeRequest, com.dxx.game.dto.UserProto.BattlePowerChangeRequest.Builder>registerMessage((short)10145, com.dxx.game.dto.UserProto.BattlePowerChangeRequest.class, null, null);
		messageProto.<com.dxx.game.dto.UserProto.BattlePowerChangeResponse, com.dxx.game.dto.UserProto.BattlePowerChangeResponse.Builder>registerMessage((short)10146, com.dxx.game.dto.UserProto.BattlePowerChangeResponse.class, null, null);
		messageProto.<com.dxx.game.dto.BaoWuPutOnRequest, com.dxx.game.dto.BaoWuPutOnRequest.Builder>registerMessage((short)10151, com.dxx.game.dto.BaoWuPutOnRequest.class, null, null);
		messageProto.<com.dxx.game.dto.BaoWuPutOnResponse, com.dxx.game.dto.BaoWuPutOnResponse.Builder>registerMessage((short)10152, com.dxx.game.dto.BaoWuPutOnResponse.class, null, null);
		messageProto.<com.dxx.game.dto.BaoWuTakeOffRequest, com.dxx.game.dto.BaoWuTakeOffRequest.Builder>registerMessage((short)10153, com.dxx.game.dto.BaoWuTakeOffRequest.class, null, null);
		messageProto.<com.dxx.game.dto.BaoWuTakeOffResponse, com.dxx.game.dto.BaoWuTakeOffResponse.Builder>registerMessage((short)10154, com.dxx.game.dto.BaoWuTakeOffResponse.class, null, null);
		messageProto.<com.dxx.game.dto.BaoWuMergeRequest, com.dxx.game.dto.BaoWuMergeRequest.Builder>registerMessage((short)10155, com.dxx.game.dto.BaoWuMergeRequest.class, null, null);
		messageProto.<com.dxx.game.dto.BaoWuMergeResponse, com.dxx.game.dto.BaoWuMergeResponse.Builder>registerMessage((short)10156, com.dxx.game.dto.BaoWuMergeResponse.class, null, null);
		messageProto.<com.dxx.game.dto.BaoWuDowngradeRequest, com.dxx.game.dto.BaoWuDowngradeRequest.Builder>registerMessage((short)10157, com.dxx.game.dto.BaoWuDowngradeRequest.class, null, null);
		messageProto.<com.dxx.game.dto.BaoWuDowngradeResponse, com.dxx.game.dto.BaoWuDowngradeResponse.Builder>registerMessage((short)10158, com.dxx.game.dto.BaoWuDowngradeResponse.class, null, null);
		messageProto.<com.dxx.game.dto.FormationProto.FormationSyncHeroRequest, com.dxx.game.dto.FormationProto.FormationSyncHeroRequest.Builder>registerMessage((short)10161, com.dxx.game.dto.FormationProto.FormationSyncHeroRequest.class, null, null);
		messageProto.<com.dxx.game.dto.FormationProto.FormationSyncHeroResponse, com.dxx.game.dto.FormationProto.FormationSyncHeroResponse.Builder>registerMessage((short)10162, com.dxx.game.dto.FormationProto.FormationSyncHeroResponse.class, null, null);
		messageProto.<com.dxx.game.dto.FormationProto.FormationBaoWuPutOnRequest, com.dxx.game.dto.FormationProto.FormationBaoWuPutOnRequest.Builder>registerMessage((short)10163, com.dxx.game.dto.FormationProto.FormationBaoWuPutOnRequest.class, null, null);
		messageProto.<com.dxx.game.dto.FormationProto.FormationBaoWuPutOnResponse, com.dxx.game.dto.FormationProto.FormationBaoWuPutOnResponse.Builder>registerMessage((short)10164, com.dxx.game.dto.FormationProto.FormationBaoWuPutOnResponse.class, null, null);
		messageProto.<com.dxx.game.dto.FormationProto.FormationBaoWuTakeOffRequest, com.dxx.game.dto.FormationProto.FormationBaoWuTakeOffRequest.Builder>registerMessage((short)10165, com.dxx.game.dto.FormationProto.FormationBaoWuTakeOffRequest.class, null, null);
		messageProto.<com.dxx.game.dto.FormationProto.FormationBaoWuTakeOffResponse, com.dxx.game.dto.FormationProto.FormationBaoWuTakeOffResponse.Builder>registerMessage((short)10166, com.dxx.game.dto.FormationProto.FormationBaoWuTakeOffResponse.class, null, null);
		messageProto.<com.dxx.game.dto.FormationProto.FormationGetInfoRequest, com.dxx.game.dto.FormationProto.FormationGetInfoRequest.Builder>registerMessage((short)10167, com.dxx.game.dto.FormationProto.FormationGetInfoRequest.class, null, null);
		messageProto.<com.dxx.game.dto.FormationProto.FormationGetInfoResponse, com.dxx.game.dto.FormationProto.FormationGetInfoResponse.Builder>registerMessage((short)10168, com.dxx.game.dto.FormationProto.FormationGetInfoResponse.class, null, null);
		messageProto.<com.dxx.game.dto.UserProto.BossSkillSelectRequest, com.dxx.game.dto.UserProto.BossSkillSelectRequest.Builder>registerMessage((short)10173, com.dxx.game.dto.UserProto.BossSkillSelectRequest.class, null, null);
		messageProto.<com.dxx.game.dto.UserProto.BossSkillSelectResponse, com.dxx.game.dto.UserProto.BossSkillSelectResponse.Builder>registerMessage((short)10174, com.dxx.game.dto.UserProto.BossSkillSelectResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.ItemProto.ItemUseRequest, com.dxx.game.dto.ItemProto.ItemUseRequest.Builder>registerMessage((short)10201, com.dxx.game.dto.ItemProto.ItemUseRequest.class, null, null);
		messageProto.<com.dxx.game.dto.ItemProto.ItemUseResponse, com.dxx.game.dto.ItemProto.ItemUseResponse.Builder>registerMessage((short)10202, com.dxx.game.dto.ItemProto.ItemUseResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.ItemProto.UseBattleReviveItemRequest, com.dxx.game.dto.ItemProto.UseBattleReviveItemRequest.Builder>registerMessage((short)10203, com.dxx.game.dto.ItemProto.UseBattleReviveItemRequest.class, null, null);
		messageProto.<com.dxx.game.dto.ItemProto.UseBattleReviveItemResponse, com.dxx.game.dto.ItemProto.UseBattleReviveItemResponse.Builder>registerMessage((short)10204, com.dxx.game.dto.ItemProto.UseBattleReviveItemResponse.class, null, null);
		messageProto.<com.dxx.game.dto.RankGetListRequest, com.dxx.game.dto.RankGetListRequest.Builder>registerMessage((short)10211, com.dxx.game.dto.RankGetListRequest.class, null, null);
		messageProto.<com.dxx.game.dto.RankGetListResponse, com.dxx.game.dto.RankGetListResponse.Builder>registerMessage((short)10212, com.dxx.game.dto.RankGetListResponse.class, null, null);
		messageProto.<com.dxx.game.dto.RankGetSectionListRequest, com.dxx.game.dto.RankGetSectionListRequest.Builder>registerMessage((short)10213, com.dxx.game.dto.RankGetSectionListRequest.class, null, null);
		messageProto.<com.dxx.game.dto.RankGetSectionListResponse, com.dxx.game.dto.RankGetSectionListResponse.Builder>registerMessage((short)10214, com.dxx.game.dto.RankGetSectionListResponse.class, null, null);
		messageProto.<com.dxx.game.dto.RankGetSectionListByUserRequest, com.dxx.game.dto.RankGetSectionListByUserRequest.Builder>registerMessage((short)10215, com.dxx.game.dto.RankGetSectionListByUserRequest.class, null, null);
		messageProto.<com.dxx.game.dto.RankGetSectionListByUserResponse, com.dxx.game.dto.RankGetSectionListByUserResponse.Builder>registerMessage((short)10216, com.dxx.game.dto.RankGetSectionListByUserResponse.class, null, null);
		messageProto.<com.dxx.game.dto.CycleActivityGetInfoRequest, com.dxx.game.dto.CycleActivityGetInfoRequest.Builder>registerMessage((short)10231, com.dxx.game.dto.CycleActivityGetInfoRequest.class, null, null);
		messageProto.<com.dxx.game.dto.CycleActivityGetInfoResponse, com.dxx.game.dto.CycleActivityGetInfoResponse.Builder>registerMessage((short)10232, com.dxx.game.dto.CycleActivityGetInfoResponse.class, null, null);
		messageProto.<com.dxx.game.dto.CycleFundRewardRequest, com.dxx.game.dto.CycleFundRewardRequest.Builder>registerMessage((short)10233, com.dxx.game.dto.CycleFundRewardRequest.class, null, null);
		messageProto.<com.dxx.game.dto.CycleFundRewardResponse, com.dxx.game.dto.CycleFundRewardResponse.Builder>registerMessage((short)10234, com.dxx.game.dto.CycleFundRewardResponse.class, null, null);
		messageProto.<com.dxx.game.dto.CycleGiftPackBuyRewardRequest, com.dxx.game.dto.CycleGiftPackBuyRewardRequest.Builder>registerMessage((short)10235, com.dxx.game.dto.CycleGiftPackBuyRewardRequest.class, null, null);
		messageProto.<com.dxx.game.dto.CycleGiftPackBuyRewardResponse, com.dxx.game.dto.CycleGiftPackBuyRewardResponse.Builder>registerMessage((short)10236, com.dxx.game.dto.CycleGiftPackBuyRewardResponse.class, null, null);
		messageProto.<com.dxx.game.dto.CycleTaskRewardRequest, com.dxx.game.dto.CycleTaskRewardRequest.Builder>registerMessage((short)10237, com.dxx.game.dto.CycleTaskRewardRequest.class, null, null);
		messageProto.<com.dxx.game.dto.CycleTaskRewardResponse, com.dxx.game.dto.CycleTaskRewardResponse.Builder>registerMessage((short)10238, com.dxx.game.dto.CycleTaskRewardResponse.class, null, null);
		messageProto.<com.dxx.game.dto.CycleTaskRoundRewardRequest, com.dxx.game.dto.CycleTaskRoundRewardRequest.Builder>registerMessage((short)10239, com.dxx.game.dto.CycleTaskRoundRewardRequest.class, null, null);
		messageProto.<com.dxx.game.dto.CycleTaskRoundRewardResponse, com.dxx.game.dto.CycleTaskRoundRewardResponse.Builder>registerMessage((short)10240, com.dxx.game.dto.CycleTaskRoundRewardResponse.class, null, null);
		messageProto.<com.dxx.game.dto.CycleDailyLeiChongRewardRequest, com.dxx.game.dto.CycleDailyLeiChongRewardRequest.Builder>registerMessage((short)10241, com.dxx.game.dto.CycleDailyLeiChongRewardRequest.class, null, null);
		messageProto.<com.dxx.game.dto.CycleDailyLeiChongRewardResponse, com.dxx.game.dto.CycleDailyLeiChongRewardResponse.Builder>registerMessage((short)10242, com.dxx.game.dto.CycleDailyLeiChongRewardResponse.class, null, null);
		messageProto.<com.dxx.game.dto.CycleLeiChongRewardRequest, com.dxx.game.dto.CycleLeiChongRewardRequest.Builder>registerMessage((short)10243, com.dxx.game.dto.CycleLeiChongRewardRequest.class, null, null);
		messageProto.<com.dxx.game.dto.CycleLeiChongRewardResponse, com.dxx.game.dto.CycleLeiChongRewardResponse.Builder>registerMessage((short)10244, com.dxx.game.dto.CycleLeiChongRewardResponse.class, null, null);
		messageProto.<com.dxx.game.dto.GoldExpDungeonStartRequest, com.dxx.game.dto.GoldExpDungeonStartRequest.Builder>registerMessage((short)10301, com.dxx.game.dto.GoldExpDungeonStartRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GoldExpDungeonStartResponse, com.dxx.game.dto.GoldExpDungeonStartResponse.Builder>registerMessage((short)10302, com.dxx.game.dto.GoldExpDungeonStartResponse.class, null, null);
		messageProto.<com.dxx.game.dto.GoldExpDungeonEndRequest, com.dxx.game.dto.GoldExpDungeonEndRequest.Builder>registerMessage((short)10303, com.dxx.game.dto.GoldExpDungeonEndRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GoldExpDungeonEndResponse, com.dxx.game.dto.GoldExpDungeonEndResponse.Builder>registerMessage((short)10304, com.dxx.game.dto.GoldExpDungeonEndResponse.class, null, null);
		messageProto.<com.dxx.game.dto.GoldExpDungeonAutoClearRequest, com.dxx.game.dto.GoldExpDungeonAutoClearRequest.Builder>registerMessage((short)10305, com.dxx.game.dto.GoldExpDungeonAutoClearRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GoldExpDungeonAutoClearResponse, com.dxx.game.dto.GoldExpDungeonAutoClearResponse.Builder>registerMessage((short)10306, com.dxx.game.dto.GoldExpDungeonAutoClearResponse.class, null, null);
		messageProto.<com.dxx.game.dto.EquipDungeonStartRequest, com.dxx.game.dto.EquipDungeonStartRequest.Builder>registerMessage((short)10307, com.dxx.game.dto.EquipDungeonStartRequest.class, null, null);
		messageProto.<com.dxx.game.dto.EquipDungeonStartResponse, com.dxx.game.dto.EquipDungeonStartResponse.Builder>registerMessage((short)10308, com.dxx.game.dto.EquipDungeonStartResponse.class, null, null);
		messageProto.<com.dxx.game.dto.EquipDungeonEndRequest, com.dxx.game.dto.EquipDungeonEndRequest.Builder>registerMessage((short)10309, com.dxx.game.dto.EquipDungeonEndRequest.class, null, null);
		messageProto.<com.dxx.game.dto.EquipDungeonEndResponse, com.dxx.game.dto.EquipDungeonEndResponse.Builder>registerMessage((short)10310, com.dxx.game.dto.EquipDungeonEndResponse.class, null, null);
		messageProto.<com.dxx.game.dto.EquipDungeonAutoClearRequest, com.dxx.game.dto.EquipDungeonAutoClearRequest.Builder>registerMessage((short)10311, com.dxx.game.dto.EquipDungeonAutoClearRequest.class, null, null);
		messageProto.<com.dxx.game.dto.EquipDungeonAutoClearResponse, com.dxx.game.dto.EquipDungeonAutoClearResponse.Builder>registerMessage((short)10312, com.dxx.game.dto.EquipDungeonAutoClearResponse.class, null, null);
		messageProto.<com.dxx.game.dto.EquipDungeonUpgradeRequest, com.dxx.game.dto.EquipDungeonUpgradeRequest.Builder>registerMessage((short)10313, com.dxx.game.dto.EquipDungeonUpgradeRequest.class, null, null);
		messageProto.<com.dxx.game.dto.EquipDungeonUpgradeResponse, com.dxx.game.dto.EquipDungeonUpgradeResponse.Builder>registerMessage((short)10314, com.dxx.game.dto.EquipDungeonUpgradeResponse.class, null, null);
		messageProto.<com.dxx.game.dto.BossDungeonStartRequest, com.dxx.game.dto.BossDungeonStartRequest.Builder>registerMessage((short)10315, com.dxx.game.dto.BossDungeonStartRequest.class, null, null);
		messageProto.<com.dxx.game.dto.BossDungeonStartResponse, com.dxx.game.dto.BossDungeonStartResponse.Builder>registerMessage((short)10316, com.dxx.game.dto.BossDungeonStartResponse.class, null, null);
		messageProto.<com.dxx.game.dto.BossDungeonEndRequest, com.dxx.game.dto.BossDungeonEndRequest.Builder>registerMessage((short)10317, com.dxx.game.dto.BossDungeonEndRequest.class, null, null);
		messageProto.<com.dxx.game.dto.BossDungeonEndResponse, com.dxx.game.dto.BossDungeonEndResponse.Builder>registerMessage((short)10318, com.dxx.game.dto.BossDungeonEndResponse.class, null, null);
		messageProto.<com.dxx.game.dto.BossDungeonGetInfoRequest, com.dxx.game.dto.BossDungeonGetInfoRequest.Builder>registerMessage((short)10319, com.dxx.game.dto.BossDungeonGetInfoRequest.class, null, null);
		messageProto.<com.dxx.game.dto.BossDungeonGetInfoResponse, com.dxx.game.dto.BossDungeonGetInfoResponse.Builder>registerMessage((short)10320, com.dxx.game.dto.BossDungeonGetInfoResponse.class, null, null);
		messageProto.<com.dxx.game.dto.EquipDungeonDowngradeRequest, com.dxx.game.dto.EquipDungeonDowngradeRequest.Builder>registerMessage((short)10321, com.dxx.game.dto.EquipDungeonDowngradeRequest.class, null, null);
		messageProto.<com.dxx.game.dto.EquipDungeonDowngradeResponse, com.dxx.game.dto.EquipDungeonDowngradeResponse.Builder>registerMessage((short)10322, com.dxx.game.dto.EquipDungeonDowngradeResponse.class, null, null);
		messageProto.<com.dxx.game.dto.RuneDungeonStartRequest, com.dxx.game.dto.RuneDungeonStartRequest.Builder>registerMessage((short)10323, com.dxx.game.dto.RuneDungeonStartRequest.class, null, null);
		messageProto.<com.dxx.game.dto.RuneDungeonStartResponse, com.dxx.game.dto.RuneDungeonStartResponse.Builder>registerMessage((short)10324, com.dxx.game.dto.RuneDungeonStartResponse.class, null, null);
		messageProto.<com.dxx.game.dto.RuneDungeonEndRequest, com.dxx.game.dto.RuneDungeonEndRequest.Builder>registerMessage((short)10325, com.dxx.game.dto.RuneDungeonEndRequest.class, null, null);
		messageProto.<com.dxx.game.dto.RuneDungeonEndResponse, com.dxx.game.dto.RuneDungeonEndResponse.Builder>registerMessage((short)10326, com.dxx.game.dto.RuneDungeonEndResponse.class, null, null);
		messageProto.<com.dxx.game.dto.MissionProto.MissionGetInfoRequest, com.dxx.game.dto.MissionProto.MissionGetInfoRequest.Builder>registerMessage((short)10401, com.dxx.game.dto.MissionProto.MissionGetInfoRequest.class, null, null);
		messageProto.<com.dxx.game.dto.MissionProto.MissionGetInfoResponse, com.dxx.game.dto.MissionProto.MissionGetInfoResponse.Builder>registerMessage((short)10402, com.dxx.game.dto.MissionProto.MissionGetInfoResponse.class, null, null);
		messageProto.<com.dxx.game.dto.MissionProto.MissionStartRequest, com.dxx.game.dto.MissionProto.MissionStartRequest.Builder>registerMessage((short)10403, com.dxx.game.dto.MissionProto.MissionStartRequest.class, null, null);
		messageProto.<com.dxx.game.dto.MissionProto.MissionStartResponse, com.dxx.game.dto.MissionProto.MissionStartResponse.Builder>registerMessage((short)10404, com.dxx.game.dto.MissionProto.MissionStartResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.MissionProto.MissionCompleteRequest, com.dxx.game.dto.MissionProto.MissionCompleteRequest.Builder>registerMessage((short)10405, com.dxx.game.dto.MissionProto.MissionCompleteRequest.class, null, null);
		messageProto.<com.dxx.game.dto.MissionProto.MissionCompleteResponse, com.dxx.game.dto.MissionProto.MissionCompleteResponse.Builder>registerMessage((short)10406, com.dxx.game.dto.MissionProto.MissionCompleteResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.MissionProto.MissionGetHangUpItemsRequest, com.dxx.game.dto.MissionProto.MissionGetHangUpItemsRequest.Builder>registerMessage((short)10407, com.dxx.game.dto.MissionProto.MissionGetHangUpItemsRequest.class, null, null);
		messageProto.<com.dxx.game.dto.MissionProto.MissionGetHangUpItemsResponse, com.dxx.game.dto.MissionProto.MissionGetHangUpItemsResponse.Builder>registerMessage((short)10408, com.dxx.game.dto.MissionProto.MissionGetHangUpItemsResponse.class, null, null);
		messageProto.<com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsRequest, com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsRequest.Builder>registerMessage((short)10409, com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsRequest.class, null, null);
		messageProto.<com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsResponse, com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsResponse.Builder>registerMessage((short)10410, com.dxx.game.dto.MissionProto.MissionReceiveHangUpItemsResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.MissionProto.MissionQuickHangUpRequest, com.dxx.game.dto.MissionProto.MissionQuickHangUpRequest.Builder>registerMessage((short)10411, com.dxx.game.dto.MissionProto.MissionQuickHangUpRequest.class, null, null);
		messageProto.<com.dxx.game.dto.MissionProto.MissionQuickHangUpResponse, com.dxx.game.dto.MissionProto.MissionQuickHangUpResponse.Builder>registerMessage((short)10412, com.dxx.game.dto.MissionProto.MissionQuickHangUpResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.TaskProto.TaskGetInfoRequest, com.dxx.game.dto.TaskProto.TaskGetInfoRequest.Builder>registerMessage((short)10501, com.dxx.game.dto.TaskProto.TaskGetInfoRequest.class, null, null);
		messageProto.<com.dxx.game.dto.TaskProto.TaskGetInfoResponse, com.dxx.game.dto.TaskProto.TaskGetInfoResponse.Builder>registerMessage((short)10502, com.dxx.game.dto.TaskProto.TaskGetInfoResponse.class, null, null);
		messageProto.<com.dxx.game.dto.TaskProto.TaskRewardDailyRequest, com.dxx.game.dto.TaskProto.TaskRewardDailyRequest.Builder>registerMessage((short)10503, com.dxx.game.dto.TaskProto.TaskRewardDailyRequest.class, null, null);
		messageProto.<com.dxx.game.dto.TaskProto.TaskRewardDailyResponse, com.dxx.game.dto.TaskProto.TaskRewardDailyResponse.Builder>registerMessage((short)10504, com.dxx.game.dto.TaskProto.TaskRewardDailyResponse.class, null, null);
		messageProto.<com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest, com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest.Builder>registerMessage((short)10505, com.dxx.game.dto.TaskProto.TaskRewardAchieveRequest.class, null, null);
		messageProto.<com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse, com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse.Builder>registerMessage((short)10506, com.dxx.game.dto.TaskProto.TaskRewardAchieveResponse.class, null, null);
		messageProto.<com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest, com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest.Builder>registerMessage((short)10507, com.dxx.game.dto.TaskProto.TaskActiveRewardAllRequest.class, null, null);
		messageProto.<com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse, com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse.Builder>registerMessage((short)10508, com.dxx.game.dto.TaskProto.TaskActiveRewardAllResponse.class, null, null);
		messageProto.<com.dxx.game.dto.MainTaskRewardRequest, com.dxx.game.dto.MainTaskRewardRequest.Builder>registerMessage((short)10531, com.dxx.game.dto.MainTaskRewardRequest.class, null, null);
		messageProto.<com.dxx.game.dto.MainTaskRewardResponse, com.dxx.game.dto.MainTaskRewardResponse.Builder>registerMessage((short)10532, com.dxx.game.dto.MainTaskRewardResponse.class, null, null);
		messageProto.<com.dxx.game.dto.MainTaskStageRewardRequest, com.dxx.game.dto.MainTaskStageRewardRequest.Builder>registerMessage((short)10533, com.dxx.game.dto.MainTaskStageRewardRequest.class, null, null);
		messageProto.<com.dxx.game.dto.MainTaskStageRewardResponse, com.dxx.game.dto.MainTaskStageRewardResponse.Builder>registerMessage((short)10534, com.dxx.game.dto.MainTaskStageRewardResponse.class, null, null);
		messageProto.<com.dxx.game.dto.RechargePreOrderRequest, com.dxx.game.dto.RechargePreOrderRequest.Builder>registerMessage((short)10601, com.dxx.game.dto.RechargePreOrderRequest.class, null, null);
		messageProto.<com.dxx.game.dto.RechargePreOrderResponse, com.dxx.game.dto.RechargePreOrderResponse.Builder>registerMessage((short)10602, com.dxx.game.dto.RechargePreOrderResponse.class, null, null);
		messageProto.<com.dxx.game.dto.RechargeVerifyReceiptRequest, com.dxx.game.dto.RechargeVerifyReceiptRequest.Builder>registerMessage((short)10603, com.dxx.game.dto.RechargeVerifyReceiptRequest.class, null, null);
		messageProto.<com.dxx.game.dto.RechargeVerifyReceiptResponse, com.dxx.game.dto.RechargeVerifyReceiptResponse.Builder>registerMessage((short)10604, com.dxx.game.dto.RechargeVerifyReceiptResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.RechargeQueryResultRequest, com.dxx.game.dto.RechargeQueryResultRequest.Builder>registerMessage((short)10605, com.dxx.game.dto.RechargeQueryResultRequest.class, null, null);
		messageProto.<com.dxx.game.dto.RechargeQueryResultResponse, com.dxx.game.dto.RechargeQueryResultResponse.Builder>registerMessage((short)10606, com.dxx.game.dto.RechargeQueryResultResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.RechargeBuyFreeRequest, com.dxx.game.dto.RechargeBuyFreeRequest.Builder>registerMessage((short)10607, com.dxx.game.dto.RechargeBuyFreeRequest.class, null, null);
		messageProto.<com.dxx.game.dto.RechargeBuyFreeResponse, com.dxx.game.dto.RechargeBuyFreeResponse.Builder>registerMessage((short)10608, com.dxx.game.dto.RechargeBuyFreeResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.RechargeTestDeliveryRequest, com.dxx.game.dto.RechargeTestDeliveryRequest.Builder>registerMessage((short)10609, com.dxx.game.dto.RechargeTestDeliveryRequest.class, null, null);
		messageProto.<com.dxx.game.dto.RechargeTestDeliveryResponse, com.dxx.game.dto.RechargeTestDeliveryResponse.Builder>registerMessage((short)10610, com.dxx.game.dto.RechargeTestDeliveryResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.RechargeCancelPreOrderRequest, com.dxx.game.dto.RechargeCancelPreOrderRequest.Builder>registerMessage((short)10611, com.dxx.game.dto.RechargeCancelPreOrderRequest.class, null, null);
		messageProto.<com.dxx.game.dto.RechargeCancelPreOrderResponse, com.dxx.game.dto.RechargeCancelPreOrderResponse.Builder>registerMessage((short)10612, com.dxx.game.dto.RechargeCancelPreOrderResponse.class, null, null);
		messageProto.<com.dxx.game.dto.RechargeExchangeVerifyRequest, com.dxx.game.dto.RechargeExchangeVerifyRequest.Builder>registerMessage((short)10613, com.dxx.game.dto.RechargeExchangeVerifyRequest.class, null, null);
		messageProto.<com.dxx.game.dto.RechargeExchangeVerifyResponse, com.dxx.game.dto.RechargeExchangeVerifyResponse.Builder>registerMessage((short)10614, com.dxx.game.dto.RechargeExchangeVerifyResponse.class, null, null);
		messageProto.<com.dxx.game.dto.DailyLeiChongGetInfoRequest, com.dxx.game.dto.DailyLeiChongGetInfoRequest.Builder>registerMessage((short)10701, com.dxx.game.dto.DailyLeiChongGetInfoRequest.class, null, null);
		messageProto.<com.dxx.game.dto.DailyLeiChongGetInfoResponse, com.dxx.game.dto.DailyLeiChongGetInfoResponse.Builder>registerMessage((short)10702, com.dxx.game.dto.DailyLeiChongGetInfoResponse.class, null, null);
		messageProto.<com.dxx.game.dto.DailyLeiChongRewardRequest, com.dxx.game.dto.DailyLeiChongRewardRequest.Builder>registerMessage((short)10703, com.dxx.game.dto.DailyLeiChongRewardRequest.class, null, null);
		messageProto.<com.dxx.game.dto.DailyLeiChongRewardResponse, com.dxx.game.dto.DailyLeiChongRewardResponse.Builder>registerMessage((short)10704, com.dxx.game.dto.DailyLeiChongRewardResponse.class, null, null);
		messageProto.<com.dxx.game.dto.LeiChongRewardRequest, com.dxx.game.dto.LeiChongRewardRequest.Builder>registerMessage((short)10705, com.dxx.game.dto.LeiChongRewardRequest.class, null, null);
		messageProto.<com.dxx.game.dto.LeiChongRewardResponse, com.dxx.game.dto.LeiChongRewardResponse.Builder>registerMessage((short)10706, com.dxx.game.dto.LeiChongRewardResponse.class, null, null);
		messageProto.<com.dxx.game.dto.VipGetInfoRequest, com.dxx.game.dto.VipGetInfoRequest.Builder>registerMessage((short)10711, com.dxx.game.dto.VipGetInfoRequest.class, null, null);
		messageProto.<com.dxx.game.dto.VipGetInfoResponse, com.dxx.game.dto.VipGetInfoResponse.Builder>registerMessage((short)10712, com.dxx.game.dto.VipGetInfoResponse.class, null, null);
		messageProto.<com.dxx.game.dto.VipDailyRewardRequest, com.dxx.game.dto.VipDailyRewardRequest.Builder>registerMessage((short)10713, com.dxx.game.dto.VipDailyRewardRequest.class, null, null);
		messageProto.<com.dxx.game.dto.VipDailyRewardResponse, com.dxx.game.dto.VipDailyRewardResponse.Builder>registerMessage((short)10714, com.dxx.game.dto.VipDailyRewardResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.VipBuyGiftRequest, com.dxx.game.dto.VipBuyGiftRequest.Builder>registerMessage((short)10715, com.dxx.game.dto.VipBuyGiftRequest.class, null, null);
		messageProto.<com.dxx.game.dto.VipBuyGiftResponse, com.dxx.game.dto.VipBuyGiftResponse.Builder>registerMessage((short)10716, com.dxx.game.dto.VipBuyGiftResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.LevelFundRewardRequest, com.dxx.game.dto.LevelFundRewardRequest.Builder>registerMessage((short)10719, com.dxx.game.dto.LevelFundRewardRequest.class, null, null);
		messageProto.<com.dxx.game.dto.LevelFundRewardResponse, com.dxx.game.dto.LevelFundRewardResponse.Builder>registerMessage((short)10720, com.dxx.game.dto.LevelFundRewardResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.MonthCardGetRewardRequest, com.dxx.game.dto.MonthCardGetRewardRequest.Builder>registerMessage((short)10721, com.dxx.game.dto.MonthCardGetRewardRequest.class, null, null);
		messageProto.<com.dxx.game.dto.MonthCardGetRewardResponse, com.dxx.game.dto.MonthCardGetRewardResponse.Builder>registerMessage((short)10722, com.dxx.game.dto.MonthCardGetRewardResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.BattlePassRewardRequest, com.dxx.game.dto.BattlePassRewardRequest.Builder>registerMessage((short)10723, com.dxx.game.dto.BattlePassRewardRequest.class, null, null);
		messageProto.<com.dxx.game.dto.BattlePassRewardResponse, com.dxx.game.dto.BattlePassRewardResponse.Builder>registerMessage((short)10724, com.dxx.game.dto.BattlePassRewardResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.BattlePassBuyLevelRequest, com.dxx.game.dto.BattlePassBuyLevelRequest.Builder>registerMessage((short)10725, com.dxx.game.dto.BattlePassBuyLevelRequest.class, null, null);
		messageProto.<com.dxx.game.dto.BattlePassBuyLevelResponse, com.dxx.game.dto.BattlePassBuyLevelResponse.Builder>registerMessage((short)10726, com.dxx.game.dto.BattlePassBuyLevelResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.BattlePassFinalRewardRequest, com.dxx.game.dto.BattlePassFinalRewardRequest.Builder>registerMessage((short)10727, com.dxx.game.dto.BattlePassFinalRewardRequest.class, null, null);
		messageProto.<com.dxx.game.dto.BattlePassFinalRewardResponse, com.dxx.game.dto.BattlePassFinalRewardResponse.Builder>registerMessage((short)10728, com.dxx.game.dto.BattlePassFinalRewardResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.ChainPackGetListRequest, com.dxx.game.dto.ChainPackGetListRequest.Builder>registerMessage((short)10729, com.dxx.game.dto.ChainPackGetListRequest.class, null, null);
		messageProto.<com.dxx.game.dto.ChainPackGetListResponse, com.dxx.game.dto.ChainPackGetListResponse.Builder>registerMessage((short)10730, com.dxx.game.dto.ChainPackGetListResponse.class, null, null);
		messageProto.<com.dxx.game.dto.ChainPackGetFreeRewardRequest, com.dxx.game.dto.ChainPackGetFreeRewardRequest.Builder>registerMessage((short)10731, com.dxx.game.dto.ChainPackGetFreeRewardRequest.class, null, null);
		messageProto.<com.dxx.game.dto.ChainPackGetFreeRewardResponse, com.dxx.game.dto.ChainPackGetFreeRewardResponse.Builder>registerMessage((short)10732, com.dxx.game.dto.ChainPackGetFreeRewardResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.FirstChargeGetRewardRequest, com.dxx.game.dto.FirstChargeGetRewardRequest.Builder>registerMessage((short)10733, com.dxx.game.dto.FirstChargeGetRewardRequest.class, null, null);
		messageProto.<com.dxx.game.dto.FirstChargeGetRewardResponse, com.dxx.game.dto.FirstChargeGetRewardResponse.Builder>registerMessage((short)10734, com.dxx.game.dto.FirstChargeGetRewardResponse.class, null, null);
		messageProto.<com.dxx.game.dto.GiftPackWatchAdRequest, com.dxx.game.dto.GiftPackWatchAdRequest.Builder>registerMessage((short)10735, com.dxx.game.dto.GiftPackWatchAdRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GiftPackWatchAdResponse, com.dxx.game.dto.GiftPackWatchAdResponse.Builder>registerMessage((short)10736, com.dxx.game.dto.GiftPackWatchAdResponse.class, null, null);
		messageProto.<com.dxx.game.dto.NewBattlePassRewardRequest, com.dxx.game.dto.NewBattlePassRewardRequest.Builder>registerMessage((short)10737, com.dxx.game.dto.NewBattlePassRewardRequest.class, null, null);
		messageProto.<com.dxx.game.dto.NewBattlePassRewardResponse, com.dxx.game.dto.NewBattlePassRewardResponse.Builder>registerMessage((short)10738, com.dxx.game.dto.NewBattlePassRewardResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.NewBattlePassBuyLevelRequest, com.dxx.game.dto.NewBattlePassBuyLevelRequest.Builder>registerMessage((short)10739, com.dxx.game.dto.NewBattlePassBuyLevelRequest.class, null, null);
		messageProto.<com.dxx.game.dto.NewBattlePassBuyLevelResponse, com.dxx.game.dto.NewBattlePassBuyLevelResponse.Builder>registerMessage((short)10740, com.dxx.game.dto.NewBattlePassBuyLevelResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.NewBattlePassFinalRewardRequest, com.dxx.game.dto.NewBattlePassFinalRewardRequest.Builder>registerMessage((short)10741, com.dxx.game.dto.NewBattlePassFinalRewardRequest.class, null, null);
		messageProto.<com.dxx.game.dto.NewBattlePassFinalRewardResponse, com.dxx.game.dto.NewBattlePassFinalRewardResponse.Builder>registerMessage((short)10742, com.dxx.game.dto.NewBattlePassFinalRewardResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.MailProto.MailGetListRequest, com.dxx.game.dto.MailProto.MailGetListRequest.Builder>registerMessage((short)10801, com.dxx.game.dto.MailProto.MailGetListRequest.class, null, null);
		messageProto.<com.dxx.game.dto.MailProto.MailGetListResponse, com.dxx.game.dto.MailProto.MailGetListResponse.Builder>registerMessage((short)10802, com.dxx.game.dto.MailProto.MailGetListResponse.class, null, null);
		messageProto.<com.dxx.game.dto.MailProto.MailReceiveAwardsRequest, com.dxx.game.dto.MailProto.MailReceiveAwardsRequest.Builder>registerMessage((short)10803, com.dxx.game.dto.MailProto.MailReceiveAwardsRequest.class, null, null);
		messageProto.<com.dxx.game.dto.MailProto.MailReceiveAwardsResponse, com.dxx.game.dto.MailProto.MailReceiveAwardsResponse.Builder>registerMessage((short)10804, com.dxx.game.dto.MailProto.MailReceiveAwardsResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.MailProto.MailDeleteRequest, com.dxx.game.dto.MailProto.MailDeleteRequest.Builder>registerMessage((short)10805, com.dxx.game.dto.MailProto.MailDeleteRequest.class, null, null);
		messageProto.<com.dxx.game.dto.MailProto.MailDeleteResponse, com.dxx.game.dto.MailProto.MailDeleteResponse.Builder>registerMessage((short)10806, com.dxx.game.dto.MailProto.MailDeleteResponse.class, null, null);
		messageProto.<com.dxx.game.dto.KingRushTalentLevelUpRequest, com.dxx.game.dto.KingRushTalentLevelUpRequest.Builder>registerMessage((short)10851, com.dxx.game.dto.KingRushTalentLevelUpRequest.class, null, null);
		messageProto.<com.dxx.game.dto.KingRushTalentLevelUpResponse, com.dxx.game.dto.KingRushTalentLevelUpResponse.Builder>registerMessage((short)10852, com.dxx.game.dto.KingRushTalentLevelUpResponse.class, null, null);
		messageProto.<com.dxx.game.dto.HeroUpgradeRequest, com.dxx.game.dto.HeroUpgradeRequest.Builder>registerMessage((short)10901, com.dxx.game.dto.HeroUpgradeRequest.class, null, null);
		messageProto.<com.dxx.game.dto.HeroUpgradeResponse, com.dxx.game.dto.HeroUpgradeResponse.Builder>registerMessage((short)10902, com.dxx.game.dto.HeroUpgradeResponse.class, null, null);
		messageProto.<com.dxx.game.dto.HeroStarUpgradeRequest, com.dxx.game.dto.HeroStarUpgradeRequest.Builder>registerMessage((short)10903, com.dxx.game.dto.HeroStarUpgradeRequest.class, null, null);
		messageProto.<com.dxx.game.dto.HeroStarUpgradeResponse, com.dxx.game.dto.HeroStarUpgradeResponse.Builder>registerMessage((short)10904, com.dxx.game.dto.HeroStarUpgradeResponse.class, null, null);
		messageProto.<com.dxx.game.dto.HeroFinishStarTaskRequest, com.dxx.game.dto.HeroFinishStarTaskRequest.Builder>registerMessage((short)10905, com.dxx.game.dto.HeroFinishStarTaskRequest.class, null, null);
		messageProto.<com.dxx.game.dto.HeroFinishStarTaskResponse, com.dxx.game.dto.HeroFinishStarTaskResponse.Builder>registerMessage((short)10906, com.dxx.game.dto.HeroFinishStarTaskResponse.class, null, null);
		messageProto.<com.dxx.game.dto.HeroFormationSyncRequest, com.dxx.game.dto.HeroFormationSyncRequest.Builder>registerMessage((short)10907, com.dxx.game.dto.HeroFormationSyncRequest.class, null, null);
		messageProto.<com.dxx.game.dto.HeroFormationSyncResponse, com.dxx.game.dto.HeroFormationSyncResponse.Builder>registerMessage((short)10908, com.dxx.game.dto.HeroFormationSyncResponse.class, null, null);
		messageProto.<com.dxx.game.dto.HeroCodexRewardRequest, com.dxx.game.dto.HeroCodexRewardRequest.Builder>registerMessage((short)10909, com.dxx.game.dto.HeroCodexRewardRequest.class, null, null);
		messageProto.<com.dxx.game.dto.HeroCodexRewardResponse, com.dxx.game.dto.HeroCodexRewardResponse.Builder>registerMessage((short)10910, com.dxx.game.dto.HeroCodexRewardResponse.class, null, null);
		messageProto.<com.dxx.game.dto.MonsterCodexRewardRequest, com.dxx.game.dto.MonsterCodexRewardRequest.Builder>registerMessage((short)10911, com.dxx.game.dto.MonsterCodexRewardRequest.class, null, null);
		messageProto.<com.dxx.game.dto.MonsterCodexRewardResponse, com.dxx.game.dto.MonsterCodexRewardResponse.Builder>registerMessage((short)10912, com.dxx.game.dto.MonsterCodexRewardResponse.class, null, null);
		messageProto.<com.dxx.game.dto.MonsterCodexAddRequest, com.dxx.game.dto.MonsterCodexAddRequest.Builder>registerMessage((short)10913, com.dxx.game.dto.MonsterCodexAddRequest.class, null, null);
		messageProto.<com.dxx.game.dto.MonsterCodexAddResponse, com.dxx.game.dto.MonsterCodexAddResponse.Builder>registerMessage((short)10914, com.dxx.game.dto.MonsterCodexAddResponse.class, null, null);
		messageProto.<com.dxx.game.dto.HeroFragmentRecycleRequest, com.dxx.game.dto.HeroFragmentRecycleRequest.Builder>registerMessage((short)10915, com.dxx.game.dto.HeroFragmentRecycleRequest.class, null, null);
		messageProto.<com.dxx.game.dto.HeroFragmentRecycleResponse, com.dxx.game.dto.HeroFragmentRecycleResponse.Builder>registerMessage((short)10916, com.dxx.game.dto.HeroFragmentRecycleResponse.class, null, null);
		messageProto.<com.dxx.game.dto.HeroSkinUseRequest, com.dxx.game.dto.HeroSkinUseRequest.Builder>registerMessage((short)10917, com.dxx.game.dto.HeroSkinUseRequest.class, null, null);
		messageProto.<com.dxx.game.dto.HeroSkinUseResponse, com.dxx.game.dto.HeroSkinUseResponse.Builder>registerMessage((short)10918, com.dxx.game.dto.HeroSkinUseResponse.class, null, null);
		messageProto.<com.dxx.game.dto.ConditionChapterStartRequest, com.dxx.game.dto.ConditionChapterStartRequest.Builder>registerMessage((short)11001, com.dxx.game.dto.ConditionChapterStartRequest.class, null, null);
		messageProto.<com.dxx.game.dto.ConditionChapterStartResponse, com.dxx.game.dto.ConditionChapterStartResponse.Builder>registerMessage((short)11002, com.dxx.game.dto.ConditionChapterStartResponse.class, null, null);
		messageProto.<com.dxx.game.dto.ConditionChapterEndRequest, com.dxx.game.dto.ConditionChapterEndRequest.Builder>registerMessage((short)11003, com.dxx.game.dto.ConditionChapterEndRequest.class, null, null);
		messageProto.<com.dxx.game.dto.ConditionChapterEndResponse, com.dxx.game.dto.ConditionChapterEndResponse.Builder>registerMessage((short)11004, com.dxx.game.dto.ConditionChapterEndResponse.class, null, null);
		messageProto.<com.dxx.game.dto.ConditionChapterStarRewardRequest, com.dxx.game.dto.ConditionChapterStarRewardRequest.Builder>registerMessage((short)11005, com.dxx.game.dto.ConditionChapterStarRewardRequest.class, null, null);
		messageProto.<com.dxx.game.dto.ConditionChapterStarRewardResponse, com.dxx.game.dto.ConditionChapterStarRewardResponse.Builder>registerMessage((short)11006, com.dxx.game.dto.ConditionChapterStarRewardResponse.class, null, null);
		messageProto.<com.dxx.game.dto.FakeLevelGetRewardRequest, com.dxx.game.dto.FakeLevelGetRewardRequest.Builder>registerMessage((short)11031, com.dxx.game.dto.FakeLevelGetRewardRequest.class, null, null);
		messageProto.<com.dxx.game.dto.FakeLevelGetRewardResponse, com.dxx.game.dto.FakeLevelGetRewardResponse.Builder>registerMessage((short)11032, com.dxx.game.dto.FakeLevelGetRewardResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.FakeLevelGetAFKRewardRequest, com.dxx.game.dto.FakeLevelGetAFKRewardRequest.Builder>registerMessage((short)11033, com.dxx.game.dto.FakeLevelGetAFKRewardRequest.class, null, null);
		messageProto.<com.dxx.game.dto.FakeLevelGetAFKRewardResponse, com.dxx.game.dto.FakeLevelGetAFKRewardResponse.Builder>registerMessage((short)11034, com.dxx.game.dto.FakeLevelGetAFKRewardResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.LevelStartRequest, com.dxx.game.dto.LevelStartRequest.Builder>registerMessage((short)11035, com.dxx.game.dto.LevelStartRequest.class, null, null);
		messageProto.<com.dxx.game.dto.LevelStartResponse, com.dxx.game.dto.LevelStartResponse.Builder>registerMessage((short)11036, com.dxx.game.dto.LevelStartResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.LevelEndRequest, com.dxx.game.dto.LevelEndRequest.Builder>registerMessage((short)11037, com.dxx.game.dto.LevelEndRequest.class, null, null);
		messageProto.<com.dxx.game.dto.LevelEndResponse, com.dxx.game.dto.LevelEndResponse.Builder>registerMessage((short)11038, com.dxx.game.dto.LevelEndResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.EquipProto.EquipPutOnRequest, com.dxx.game.dto.EquipProto.EquipPutOnRequest.Builder>registerMessage((short)11101, com.dxx.game.dto.EquipProto.EquipPutOnRequest.class, null, null);
		messageProto.<com.dxx.game.dto.EquipProto.EquipPutOnResponse, com.dxx.game.dto.EquipProto.EquipPutOnResponse.Builder>registerMessage((short)11102, com.dxx.game.dto.EquipProto.EquipPutOnResponse.class, null, null);
		messageProto.<com.dxx.game.dto.EquipProto.EquipUpgradeRequest, com.dxx.game.dto.EquipProto.EquipUpgradeRequest.Builder>registerMessage((short)11103, com.dxx.game.dto.EquipProto.EquipUpgradeRequest.class, null, null);
		messageProto.<com.dxx.game.dto.EquipProto.EquipUpgradeResponse, com.dxx.game.dto.EquipProto.EquipUpgradeResponse.Builder>registerMessage((short)11104, com.dxx.game.dto.EquipProto.EquipUpgradeResponse.class, null, null);
		messageProto.<com.dxx.game.dto.EquipProto.EquipPurifyUpgradeRequest, com.dxx.game.dto.EquipProto.EquipPurifyUpgradeRequest.Builder>registerMessage((short)11105, com.dxx.game.dto.EquipProto.EquipPurifyUpgradeRequest.class, null, null);
		messageProto.<com.dxx.game.dto.EquipProto.EquipPurifyUpgradeResponse, com.dxx.game.dto.EquipProto.EquipPurifyUpgradeResponse.Builder>registerMessage((short)11106, com.dxx.game.dto.EquipProto.EquipPurifyUpgradeResponse.class, null, null);
		messageProto.<com.dxx.game.dto.EquipProto.EquipDecomposeRequest, com.dxx.game.dto.EquipProto.EquipDecomposeRequest.Builder>registerMessage((short)11107, com.dxx.game.dto.EquipProto.EquipDecomposeRequest.class, null, null);
		messageProto.<com.dxx.game.dto.EquipProto.EquipDecomposeResponse, com.dxx.game.dto.EquipProto.EquipDecomposeResponse.Builder>registerMessage((short)11108, com.dxx.game.dto.EquipProto.EquipDecomposeResponse.class, null, null);
		messageProto.<com.dxx.game.dto.RuneProto.RuneStrengthenRequest, com.dxx.game.dto.RuneProto.RuneStrengthenRequest.Builder>registerMessage((short)11121, com.dxx.game.dto.RuneProto.RuneStrengthenRequest.class, null, null);
		messageProto.<com.dxx.game.dto.RuneProto.RuneStrengthenResponse, com.dxx.game.dto.RuneProto.RuneStrengthenResponse.Builder>registerMessage((short)11122, com.dxx.game.dto.RuneProto.RuneStrengthenResponse.class, null, null);
		messageProto.<com.dxx.game.dto.RuneProto.RuneLockOrUnLockRequest, com.dxx.game.dto.RuneProto.RuneLockOrUnLockRequest.Builder>registerMessage((short)11123, com.dxx.game.dto.RuneProto.RuneLockOrUnLockRequest.class, null, null);
		messageProto.<com.dxx.game.dto.RuneProto.RuneLockOrUnLockResponse, com.dxx.game.dto.RuneProto.RuneLockOrUnLockResponse.Builder>registerMessage((short)11124, com.dxx.game.dto.RuneProto.RuneLockOrUnLockResponse.class, null, null);
		messageProto.<com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffRequest, com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffRequest.Builder>registerMessage((short)11125, com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffRequest.class, null, null);
		messageProto.<com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffResponse, com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffResponse.Builder>registerMessage((short)11126, com.dxx.game.dto.RuneProto.RunePutOnOrTakeOffResponse.class, null, null);
		messageProto.<com.dxx.game.dto.RuneProto.RuneSnatchRequest, com.dxx.game.dto.RuneProto.RuneSnatchRequest.Builder>registerMessage((short)11127, com.dxx.game.dto.RuneProto.RuneSnatchRequest.class, null, null);
		messageProto.<com.dxx.game.dto.RuneProto.RuneSnatchResponse, com.dxx.game.dto.RuneProto.RuneSnatchResponse.Builder>registerMessage((short)11128, com.dxx.game.dto.RuneProto.RuneSnatchResponse.class, null, null);
		messageProto.<com.dxx.game.dto.RuneProto.RuneDecomposeRequest, com.dxx.game.dto.RuneProto.RuneDecomposeRequest.Builder>registerMessage((short)11129, com.dxx.game.dto.RuneProto.RuneDecomposeRequest.class, null, null);
		messageProto.<com.dxx.game.dto.RuneProto.RuneDecomposeResponse, com.dxx.game.dto.RuneProto.RuneDecomposeResponse.Builder>registerMessage((short)11130, com.dxx.game.dto.RuneProto.RuneDecomposeResponse.class, null, null);
		messageProto.<com.dxx.game.dto.ShopProto.ShopGetInfoRequest, com.dxx.game.dto.ShopProto.ShopGetInfoRequest.Builder>registerMessage((short)11201, com.dxx.game.dto.ShopProto.ShopGetInfoRequest.class, null, null);
		messageProto.<com.dxx.game.dto.ShopProto.ShopGetInfoResponse, com.dxx.game.dto.ShopProto.ShopGetInfoResponse.Builder>registerMessage((short)11202, com.dxx.game.dto.ShopProto.ShopGetInfoResponse.class, null, null);
		messageProto.<com.dxx.game.dto.ShopProto.ShopBuyItemRequest, com.dxx.game.dto.ShopProto.ShopBuyItemRequest.Builder>registerMessage((short)11203, com.dxx.game.dto.ShopProto.ShopBuyItemRequest.class, null, null);
		messageProto.<com.dxx.game.dto.ShopProto.ShopBuyItemResponse, com.dxx.game.dto.ShopProto.ShopBuyItemResponse.Builder>registerMessage((short)11204, com.dxx.game.dto.ShopProto.ShopBuyItemResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.ShopProto.ShopDoDrawRequest, com.dxx.game.dto.ShopProto.ShopDoDrawRequest.Builder>registerMessage((short)11207, com.dxx.game.dto.ShopProto.ShopDoDrawRequest.class, null, null);
		messageProto.<com.dxx.game.dto.ShopProto.ShopDoDrawResponse, com.dxx.game.dto.ShopProto.ShopDoDrawResponse.Builder>registerMessage((short)11208, com.dxx.game.dto.ShopProto.ShopDoDrawResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.ShopProto.ShopIntegralGetInfoRequest, com.dxx.game.dto.ShopProto.ShopIntegralGetInfoRequest.Builder>registerMessage((short)11209, com.dxx.game.dto.ShopProto.ShopIntegralGetInfoRequest.class, null, null);
		messageProto.<com.dxx.game.dto.ShopProto.ShopIntegralGetInfoResponse, com.dxx.game.dto.ShopProto.ShopIntegralGetInfoResponse.Builder>registerMessage((short)11210, com.dxx.game.dto.ShopProto.ShopIntegralGetInfoResponse.class, null, null);
		messageProto.<com.dxx.game.dto.ShopProto.ShopIntegralRefreshItemRequest, com.dxx.game.dto.ShopProto.ShopIntegralRefreshItemRequest.Builder>registerMessage((short)11211, com.dxx.game.dto.ShopProto.ShopIntegralRefreshItemRequest.class, null, null);
		messageProto.<com.dxx.game.dto.ShopProto.ShopIntegralRefreshItemResponse, com.dxx.game.dto.ShopProto.ShopIntegralRefreshItemResponse.Builder>registerMessage((short)11212, com.dxx.game.dto.ShopProto.ShopIntegralRefreshItemResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.ShopProto.ShopIntegralBuyItemRequest, com.dxx.game.dto.ShopProto.ShopIntegralBuyItemRequest.Builder>registerMessage((short)11213, com.dxx.game.dto.ShopProto.ShopIntegralBuyItemRequest.class, null, null);
		messageProto.<com.dxx.game.dto.ShopProto.ShopIntegralBuyItemResponse, com.dxx.game.dto.ShopProto.ShopIntegralBuyItemResponse.Builder>registerMessage((short)11214, com.dxx.game.dto.ShopProto.ShopIntegralBuyItemResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.ShopProto.ShopGacheWishRequest, com.dxx.game.dto.ShopProto.ShopGacheWishRequest.Builder>registerMessage((short)11215, com.dxx.game.dto.ShopProto.ShopGacheWishRequest.class, null, null);
		messageProto.<com.dxx.game.dto.ShopProto.ShopGacheWishResponse, com.dxx.game.dto.ShopProto.ShopGacheWishResponse.Builder>registerMessage((short)11216, com.dxx.game.dto.ShopProto.ShopGacheWishResponse.class, null, null);
		messageProto.<com.dxx.game.dto.ShopProto.ShopBuyTicketsRequest, com.dxx.game.dto.ShopProto.ShopBuyTicketsRequest.Builder>registerMessage((short)11217, com.dxx.game.dto.ShopProto.ShopBuyTicketsRequest.class, null, null);
		messageProto.<com.dxx.game.dto.ShopProto.ShopBuyTicketsResponse, com.dxx.game.dto.ShopProto.ShopBuyTicketsResponse.Builder>registerMessage((short)11218, com.dxx.game.dto.ShopProto.ShopBuyTicketsResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.ShopProto.DungeonAdGetItemRequest, com.dxx.game.dto.ShopProto.DungeonAdGetItemRequest.Builder>registerMessage((short)11221, com.dxx.game.dto.ShopProto.DungeonAdGetItemRequest.class, null, null);
		messageProto.<com.dxx.game.dto.ShopProto.DungeonAdGetItemResponse, com.dxx.game.dto.ShopProto.DungeonAdGetItemResponse.Builder>registerMessage((short)11222, com.dxx.game.dto.ShopProto.DungeonAdGetItemResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.ShopProto.TicketsGetListRequest, com.dxx.game.dto.ShopProto.TicketsGetListRequest.Builder>registerMessage((short)11223, com.dxx.game.dto.ShopProto.TicketsGetListRequest.class, null, null);
		messageProto.<com.dxx.game.dto.ShopProto.TicketsGetListResponse, com.dxx.game.dto.ShopProto.TicketsGetListResponse.Builder>registerMessage((short)11224, com.dxx.game.dto.ShopProto.TicketsGetListResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.ShopProto.FinishAdvertRequest, com.dxx.game.dto.ShopProto.FinishAdvertRequest.Builder>registerMessage((short)11225, com.dxx.game.dto.ShopProto.FinishAdvertRequest.class, null, null);
		messageProto.<com.dxx.game.dto.ShopProto.FinishAdvertResponse, com.dxx.game.dto.ShopProto.FinishAdvertResponse.Builder>registerMessage((short)11226, com.dxx.game.dto.ShopProto.FinishAdvertResponse.class, null, null);
		messageProto.<com.dxx.game.dto.ShopProto.ShopDoGachaRequest, com.dxx.game.dto.ShopProto.ShopDoGachaRequest.Builder>registerMessage((short)11231, com.dxx.game.dto.ShopProto.ShopDoGachaRequest.class, null, null);
		messageProto.<com.dxx.game.dto.ShopProto.ShopDoGachaResponse, com.dxx.game.dto.ShopProto.ShopDoGachaResponse.Builder>registerMessage((short)11232, com.dxx.game.dto.ShopProto.ShopDoGachaResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.ShopProto.ShopGetGachaInfoRequest, com.dxx.game.dto.ShopProto.ShopGetGachaInfoRequest.Builder>registerMessage((short)11233, com.dxx.game.dto.ShopProto.ShopGetGachaInfoRequest.class, null, null);
		messageProto.<com.dxx.game.dto.ShopProto.ShopGetGachaInfoResponse, com.dxx.game.dto.ShopProto.ShopGetGachaInfoResponse.Builder>registerMessage((short)11234, com.dxx.game.dto.ShopProto.ShopGetGachaInfoResponse.class, null, null);
		messageProto.<com.dxx.game.dto.ShopProto.ShopGachaSelectHeroRequest, com.dxx.game.dto.ShopProto.ShopGachaSelectHeroRequest.Builder>registerMessage((short)11235, com.dxx.game.dto.ShopProto.ShopGachaSelectHeroRequest.class, null, null);
		messageProto.<com.dxx.game.dto.ShopProto.ShopGachaSelectHeroResponse, com.dxx.game.dto.ShopProto.ShopGachaSelectHeroResponse.Builder>registerMessage((short)11236, com.dxx.game.dto.ShopProto.ShopGachaSelectHeroResponse.class, null, null);
		messageProto.<com.dxx.game.dto.ShopProto.ShopGachaPrivilegeRequest, com.dxx.game.dto.ShopProto.ShopGachaPrivilegeRequest.Builder>registerMessage((short)11237, com.dxx.game.dto.ShopProto.ShopGachaPrivilegeRequest.class, null, null);
		messageProto.<com.dxx.game.dto.ShopProto.ShopGachaPrivilegeResponse, com.dxx.game.dto.ShopProto.ShopGachaPrivilegeResponse.Builder>registerMessage((short)11238, com.dxx.game.dto.ShopProto.ShopGachaPrivilegeResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.ExchangeShopBuyRequest, com.dxx.game.dto.ExchangeShopBuyRequest.Builder>registerMessage((short)11251, com.dxx.game.dto.ExchangeShopBuyRequest.class, null, null);
		messageProto.<com.dxx.game.dto.ExchangeShopBuyResponse, com.dxx.game.dto.ExchangeShopBuyResponse.Builder>registerMessage((short)11252, com.dxx.game.dto.ExchangeShopBuyResponse.class, null, null);
		messageProto.<com.dxx.game.dto.KActivityGetInfoRequest, com.dxx.game.dto.KActivityGetInfoRequest.Builder>registerMessage((short)11301, com.dxx.game.dto.KActivityGetInfoRequest.class, null, null);
		messageProto.<com.dxx.game.dto.KActivityGetInfoResponse, com.dxx.game.dto.KActivityGetInfoResponse.Builder>registerMessage((short)11302, com.dxx.game.dto.KActivityGetInfoResponse.class, null, null);
		messageProto.<com.dxx.game.dto.CollectTaskRewardRequest, com.dxx.game.dto.CollectTaskRewardRequest.Builder>registerMessage((short)11303, com.dxx.game.dto.CollectTaskRewardRequest.class, null, null);
		messageProto.<com.dxx.game.dto.CollectTaskRewardResponse, com.dxx.game.dto.CollectTaskRewardResponse.Builder>registerMessage((short)11304, com.dxx.game.dto.CollectTaskRewardResponse.class, null, null);
		messageProto.<com.dxx.game.dto.ActivityProto.ActivityGetListRequest, com.dxx.game.dto.ActivityProto.ActivityGetListRequest.Builder>registerMessage((short)11401, com.dxx.game.dto.ActivityProto.ActivityGetListRequest.class, null, null);
		messageProto.<com.dxx.game.dto.ActivityProto.ActivityGetListResponse, com.dxx.game.dto.ActivityProto.ActivityGetListResponse.Builder>registerMessage((short)11402, com.dxx.game.dto.ActivityProto.ActivityGetListResponse.class, null, null);
		messageProto.<com.dxx.game.dto.SignInProto.SignInGetInfoRequest, com.dxx.game.dto.SignInProto.SignInGetInfoRequest.Builder>registerMessage((short)11501, com.dxx.game.dto.SignInProto.SignInGetInfoRequest.class, null, null);
		messageProto.<com.dxx.game.dto.SignInProto.SignInGetInfoResponse, com.dxx.game.dto.SignInProto.SignInGetInfoResponse.Builder>registerMessage((short)11502, com.dxx.game.dto.SignInProto.SignInGetInfoResponse.class, null, null);
		messageProto.<com.dxx.game.dto.SignInProto.SignInDoSignRequest, com.dxx.game.dto.SignInProto.SignInDoSignRequest.Builder>registerMessage((short)11503, com.dxx.game.dto.SignInProto.SignInDoSignRequest.class, null, null);
		messageProto.<com.dxx.game.dto.SignInProto.SignInDoSignResponse, com.dxx.game.dto.SignInProto.SignInDoSignResponse.Builder>registerMessage((short)11504, com.dxx.game.dto.SignInProto.SignInDoSignResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.NewPlayerSignInRewardRequest, com.dxx.game.dto.NewPlayerSignInRewardRequest.Builder>registerMessage((short)11505, com.dxx.game.dto.NewPlayerSignInRewardRequest.class, null, null);
		messageProto.<com.dxx.game.dto.NewPlayerSignInRewardResponse, com.dxx.game.dto.NewPlayerSignInRewardResponse.Builder>registerMessage((short)11506, com.dxx.game.dto.NewPlayerSignInRewardResponse.class, null, null);
		messageProto.<com.dxx.game.dto.SevenDayGetInfoRequest, com.dxx.game.dto.SevenDayGetInfoRequest.Builder>registerMessage((short)11551, com.dxx.game.dto.SevenDayGetInfoRequest.class, null, null);
		messageProto.<com.dxx.game.dto.SevenDayGetInfoResponse, com.dxx.game.dto.SevenDayGetInfoResponse.Builder>registerMessage((short)11552, com.dxx.game.dto.SevenDayGetInfoResponse.class, null, null);
		messageProto.<com.dxx.game.dto.SevenDayTaskRewardRequest, com.dxx.game.dto.SevenDayTaskRewardRequest.Builder>registerMessage((short)11553, com.dxx.game.dto.SevenDayTaskRewardRequest.class, null, null);
		messageProto.<com.dxx.game.dto.SevenDayTaskRewardResponse, com.dxx.game.dto.SevenDayTaskRewardResponse.Builder>registerMessage((short)11554, com.dxx.game.dto.SevenDayTaskRewardResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.SevenDayActiveRewardRequest, com.dxx.game.dto.SevenDayActiveRewardRequest.Builder>registerMessage((short)11555, com.dxx.game.dto.SevenDayActiveRewardRequest.class, null, null);
		messageProto.<com.dxx.game.dto.SevenDayActiveRewardResponse, com.dxx.game.dto.SevenDayActiveRewardResponse.Builder>registerMessage((short)11556, com.dxx.game.dto.SevenDayActiveRewardResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.SevenDayPayFreeRewardRequest, com.dxx.game.dto.SevenDayPayFreeRewardRequest.Builder>registerMessage((short)11557, com.dxx.game.dto.SevenDayPayFreeRewardRequest.class, null, null);
		messageProto.<com.dxx.game.dto.SevenDayPayFreeRewardResponse, com.dxx.game.dto.SevenDayPayFreeRewardResponse.Builder>registerMessage((short)11558, com.dxx.game.dto.SevenDayPayFreeRewardResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.ActTimeProto.ActShopBuyRequest, com.dxx.game.dto.ActTimeProto.ActShopBuyRequest.Builder>registerMessage((short)11601, com.dxx.game.dto.ActTimeProto.ActShopBuyRequest.class, null, null);
		messageProto.<com.dxx.game.dto.ActTimeProto.ActShopBuyResponse, com.dxx.game.dto.ActTimeProto.ActShopBuyResponse.Builder>registerMessage((short)11602, com.dxx.game.dto.ActTimeProto.ActShopBuyResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.ActTimeProto.ActDropBuyRequest, com.dxx.game.dto.ActTimeProto.ActDropBuyRequest.Builder>registerMessage((short)11603, com.dxx.game.dto.ActTimeProto.ActDropBuyRequest.class, null, null);
		messageProto.<com.dxx.game.dto.ActTimeProto.ActDropBuyResponse, com.dxx.game.dto.ActTimeProto.ActDropBuyResponse.Builder>registerMessage((short)11604, com.dxx.game.dto.ActTimeProto.ActDropBuyResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.ActTimeProto.ActTimeInfoRequest, com.dxx.game.dto.ActTimeProto.ActTimeInfoRequest.Builder>registerMessage((short)11605, com.dxx.game.dto.ActTimeProto.ActTimeInfoRequest.class, null, null);
		messageProto.<com.dxx.game.dto.ActTimeProto.ActTimeInfoResponse, com.dxx.game.dto.ActTimeProto.ActTimeInfoResponse.Builder>registerMessage((short)11606, com.dxx.game.dto.ActTimeProto.ActTimeInfoResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.ActTimeProto.ActTimeRewardRequest, com.dxx.game.dto.ActTimeProto.ActTimeRewardRequest.Builder>registerMessage((short)11607, com.dxx.game.dto.ActTimeProto.ActTimeRewardRequest.class, null, null);
		messageProto.<com.dxx.game.dto.ActTimeProto.ActTimeRewardResponse, com.dxx.game.dto.ActTimeProto.ActTimeRewardResponse.Builder>registerMessage((short)11608, com.dxx.game.dto.ActTimeProto.ActTimeRewardResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.ActTimeProto.ActTimePayFreeRewardRequest, com.dxx.game.dto.ActTimeProto.ActTimePayFreeRewardRequest.Builder>registerMessage((short)11609, com.dxx.game.dto.ActTimeProto.ActTimePayFreeRewardRequest.class, null, null);
		messageProto.<com.dxx.game.dto.ActTimeProto.ActTimePayFreeRewardResponse, com.dxx.game.dto.ActTimeProto.ActTimePayFreeRewardResponse.Builder>registerMessage((short)11610, com.dxx.game.dto.ActTimeProto.ActTimePayFreeRewardResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.ActTimeProto.ActTimeRankRequest, com.dxx.game.dto.ActTimeProto.ActTimeRankRequest.Builder>registerMessage((short)11611, com.dxx.game.dto.ActTimeProto.ActTimeRankRequest.class, null, null);
		messageProto.<com.dxx.game.dto.ActTimeProto.ActTimeRankResponse, com.dxx.game.dto.ActTimeProto.ActTimeRankResponse.Builder>registerMessage((short)11612, com.dxx.game.dto.ActTimeProto.ActTimeRankResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.ServerListProto.UserGetLastLoginRequest, com.dxx.game.dto.ServerListProto.UserGetLastLoginRequest.Builder>registerMessage((short)11621, com.dxx.game.dto.ServerListProto.UserGetLastLoginRequest.class, null, null);
		messageProto.<com.dxx.game.dto.ServerListProto.UserGetLastLoginResponse, com.dxx.game.dto.ServerListProto.UserGetLastLoginResponse.Builder>registerMessage((short)11622, com.dxx.game.dto.ServerListProto.UserGetLastLoginResponse.class, null, null);
		messageProto.<com.dxx.game.dto.ServerListProto.FindServerListRequest, com.dxx.game.dto.ServerListProto.FindServerListRequest.Builder>registerMessage((short)11623, com.dxx.game.dto.ServerListProto.FindServerListRequest.class, null, null);
		messageProto.<com.dxx.game.dto.ServerListProto.FindServerListResponse, com.dxx.game.dto.ServerListProto.FindServerListResponse.Builder>registerMessage((short)11624, com.dxx.game.dto.ServerListProto.FindServerListResponse.class, null, null);
		messageProto.<com.dxx.game.dto.SystemEquipProto.SystemEquipUpgradeRequest, com.dxx.game.dto.SystemEquipProto.SystemEquipUpgradeRequest.Builder>registerMessage((short)11701, com.dxx.game.dto.SystemEquipProto.SystemEquipUpgradeRequest.class, null, null);
		messageProto.<com.dxx.game.dto.SystemEquipProto.SystemEquipUpgradeResponse, com.dxx.game.dto.SystemEquipProto.SystemEquipUpgradeResponse.Builder>registerMessage((short)11702, com.dxx.game.dto.SystemEquipProto.SystemEquipUpgradeResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.SystemEquipProto.SystemEquipResetRequest, com.dxx.game.dto.SystemEquipProto.SystemEquipResetRequest.Builder>registerMessage((short)11703, com.dxx.game.dto.SystemEquipProto.SystemEquipResetRequest.class, null, null);
		messageProto.<com.dxx.game.dto.SystemEquipProto.SystemEquipResetResponse, com.dxx.game.dto.SystemEquipProto.SystemEquipResetResponse.Builder>registerMessage((short)11704, com.dxx.game.dto.SystemEquipProto.SystemEquipResetResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.SystemEquipProto.SystemEquipAdvanceRequest, com.dxx.game.dto.SystemEquipProto.SystemEquipAdvanceRequest.Builder>registerMessage((short)11705, com.dxx.game.dto.SystemEquipProto.SystemEquipAdvanceRequest.class, null, null);
		messageProto.<com.dxx.game.dto.SystemEquipProto.SystemEquipAdvanceResponse, com.dxx.game.dto.SystemEquipProto.SystemEquipAdvanceResponse.Builder>registerMessage((short)11706, com.dxx.game.dto.SystemEquipProto.SystemEquipAdvanceResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.SystemEquipProto.SystemEquipOneKeyUpgradeRequest, com.dxx.game.dto.SystemEquipProto.SystemEquipOneKeyUpgradeRequest.Builder>registerMessage((short)11707, com.dxx.game.dto.SystemEquipProto.SystemEquipOneKeyUpgradeRequest.class, null, null);
		messageProto.<com.dxx.game.dto.SystemEquipProto.SystemEquipOneKeyUpgradeResponse, com.dxx.game.dto.SystemEquipProto.SystemEquipOneKeyUpgradeResponse.Builder>registerMessage((short)11708, com.dxx.game.dto.SystemEquipProto.SystemEquipOneKeyUpgradeResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.MazeProto.StartMazeRequest, com.dxx.game.dto.MazeProto.StartMazeRequest.Builder>registerMessage((short)11801, com.dxx.game.dto.MazeProto.StartMazeRequest.class, null, null);
		messageProto.<com.dxx.game.dto.MazeProto.StartMazeResponse, com.dxx.game.dto.MazeProto.StartMazeResponse.Builder>registerMessage((short)11802, com.dxx.game.dto.MazeProto.StartMazeResponse.class, null, null);
		messageProto.<com.dxx.game.dto.MazeProto.MazeRoomStartRequest, com.dxx.game.dto.MazeProto.MazeRoomStartRequest.Builder>registerMessage((short)11803, com.dxx.game.dto.MazeProto.MazeRoomStartRequest.class, null, null);
		messageProto.<com.dxx.game.dto.MazeProto.MazeRoomStartResponse, com.dxx.game.dto.MazeProto.MazeRoomStartResponse.Builder>registerMessage((short)11804, com.dxx.game.dto.MazeProto.MazeRoomStartResponse.class, null, null);
		messageProto.<com.dxx.game.dto.MazeProto.MazeRoomFinishRequest, com.dxx.game.dto.MazeProto.MazeRoomFinishRequest.Builder>registerMessage((short)11805, com.dxx.game.dto.MazeProto.MazeRoomFinishRequest.class, null, null);
		messageProto.<com.dxx.game.dto.MazeProto.MazeRoomFinishResponse, com.dxx.game.dto.MazeProto.MazeRoomFinishResponse.Builder>registerMessage((short)11806, com.dxx.game.dto.MazeProto.MazeRoomFinishResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.MazeProto.MazeRoomOpenRequest, com.dxx.game.dto.MazeProto.MazeRoomOpenRequest.Builder>registerMessage((short)11807, com.dxx.game.dto.MazeProto.MazeRoomOpenRequest.class, null, null);
		messageProto.<com.dxx.game.dto.MazeProto.MazeRoomOpenResponse, com.dxx.game.dto.MazeProto.MazeRoomOpenResponse.Builder>registerMessage((short)11808, com.dxx.game.dto.MazeProto.MazeRoomOpenResponse.class, null, null);
		messageProto.<com.dxx.game.dto.MazeProto.QuitMazeRequest, com.dxx.game.dto.MazeProto.QuitMazeRequest.Builder>registerMessage((short)11809, com.dxx.game.dto.MazeProto.QuitMazeRequest.class, null, null);
		messageProto.<com.dxx.game.dto.MazeProto.QuitMazeResponse, com.dxx.game.dto.MazeProto.QuitMazeResponse.Builder>registerMessage((short)11810, com.dxx.game.dto.MazeProto.QuitMazeResponse.class, null, null);
		messageProto.<com.dxx.game.dto.MazeProto.MazeNextLayerRequest, com.dxx.game.dto.MazeProto.MazeNextLayerRequest.Builder>registerMessage((short)11811, com.dxx.game.dto.MazeProto.MazeNextLayerRequest.class, null, null);
		messageProto.<com.dxx.game.dto.MazeProto.MazeNextLayerResponse, com.dxx.game.dto.MazeProto.MazeNextLayerResponse.Builder>registerMessage((short)11812, com.dxx.game.dto.MazeProto.MazeNextLayerResponse.class, null, null);
		messageProto.<com.dxx.game.dto.MazeProto.MazeSetFormationDataRequest, com.dxx.game.dto.MazeProto.MazeSetFormationDataRequest.Builder>registerMessage((short)11813, com.dxx.game.dto.MazeProto.MazeSetFormationDataRequest.class, null, null);
		messageProto.<com.dxx.game.dto.MazeProto.MazeSetFormationDataResponse, com.dxx.game.dto.MazeProto.MazeSetFormationDataResponse.Builder>registerMessage((short)11814, com.dxx.game.dto.MazeProto.MazeSetFormationDataResponse.class, null, null);
		messageProto.<com.dxx.game.dto.MazeProto.MazeGetInfoRequest, com.dxx.game.dto.MazeProto.MazeGetInfoRequest.Builder>registerMessage((short)11815, com.dxx.game.dto.MazeProto.MazeGetInfoRequest.class, null, null);
		messageProto.<com.dxx.game.dto.MazeProto.MazeGetInfoResponse, com.dxx.game.dto.MazeProto.MazeGetInfoResponse.Builder>registerMessage((short)11816, com.dxx.game.dto.MazeProto.MazeGetInfoResponse.class, null, null);
		messageProto.<com.dxx.game.dto.RoleProto.RoleUpgradeRequest, com.dxx.game.dto.RoleProto.RoleUpgradeRequest.Builder>registerMessage((short)12201, com.dxx.game.dto.RoleProto.RoleUpgradeRequest.class, null, null);
		messageProto.<com.dxx.game.dto.RoleProto.RoleUpgradeResponse, com.dxx.game.dto.RoleProto.RoleUpgradeResponse.Builder>registerMessage((short)12202, com.dxx.game.dto.RoleProto.RoleUpgradeResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.RoleProto.RoleUnlockRequest, com.dxx.game.dto.RoleProto.RoleUnlockRequest.Builder>registerMessage((short)12203, com.dxx.game.dto.RoleProto.RoleUnlockRequest.class, null, null);
		messageProto.<com.dxx.game.dto.RoleProto.RoleUnlockResponse, com.dxx.game.dto.RoleProto.RoleUnlockResponse.Builder>registerMessage((short)12204, com.dxx.game.dto.RoleProto.RoleUnlockResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.RoleProto.RoleSetRequest, com.dxx.game.dto.RoleProto.RoleSetRequest.Builder>registerMessage((short)12205, com.dxx.game.dto.RoleProto.RoleSetRequest.class, null, null);
		messageProto.<com.dxx.game.dto.RoleProto.RoleSetResponse, com.dxx.game.dto.RoleProto.RoleSetResponse.Builder>registerMessage((short)12206, com.dxx.game.dto.RoleProto.RoleSetResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.RoleProto.RoleLinkRoleRequest, com.dxx.game.dto.RoleProto.RoleLinkRoleRequest.Builder>registerMessage((short)12207, com.dxx.game.dto.RoleProto.RoleLinkRoleRequest.class, null, null);
		messageProto.<com.dxx.game.dto.RoleProto.RoleLinkRoleResponse, com.dxx.game.dto.RoleProto.RoleLinkRoleResponse.Builder>registerMessage((short)12208, com.dxx.game.dto.RoleProto.RoleLinkRoleResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.RoleProto.RoleSetSkinRequest, com.dxx.game.dto.RoleProto.RoleSetSkinRequest.Builder>registerMessage((short)12209, com.dxx.game.dto.RoleProto.RoleSetSkinRequest.class, null, null);
		messageProto.<com.dxx.game.dto.RoleProto.RoleSetSkinResponse, com.dxx.game.dto.RoleProto.RoleSetSkinResponse.Builder>registerMessage((short)12210, com.dxx.game.dto.RoleProto.RoleSetSkinResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.RoleProto.UpgradeValhallaLevelRequest, com.dxx.game.dto.RoleProto.UpgradeValhallaLevelRequest.Builder>registerMessage((short)12211, com.dxx.game.dto.RoleProto.UpgradeValhallaLevelRequest.class, null, null);
		messageProto.<com.dxx.game.dto.RoleProto.UpgradeValhallaLevelResponse, com.dxx.game.dto.RoleProto.UpgradeValhallaLevelResponse.Builder>registerMessage((short)12212, com.dxx.game.dto.RoleProto.UpgradeValhallaLevelResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.RoleProto.SetValhallaRequest, com.dxx.game.dto.RoleProto.SetValhallaRequest.Builder>registerMessage((short)12213, com.dxx.game.dto.RoleProto.SetValhallaRequest.class, null, null);
		messageProto.<com.dxx.game.dto.RoleProto.SetValhallaResponse, com.dxx.game.dto.RoleProto.SetValhallaResponse.Builder>registerMessage((short)12214, com.dxx.game.dto.RoleProto.SetValhallaResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.TalentSystemProto.TalentSystemUpgradeRequest, com.dxx.game.dto.TalentSystemProto.TalentSystemUpgradeRequest.Builder>registerMessage((short)12401, com.dxx.game.dto.TalentSystemProto.TalentSystemUpgradeRequest.class, null, null);
		messageProto.<com.dxx.game.dto.TalentSystemProto.TalentSystemUpgradeResponse, com.dxx.game.dto.TalentSystemProto.TalentSystemUpgradeResponse.Builder>registerMessage((short)12402, com.dxx.game.dto.TalentSystemProto.TalentSystemUpgradeResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.ActorProto.ActorLevelUpRequest, com.dxx.game.dto.ActorProto.ActorLevelUpRequest.Builder>registerMessage((short)12501, com.dxx.game.dto.ActorProto.ActorLevelUpRequest.class, null, null);
		messageProto.<com.dxx.game.dto.ActorProto.ActorLevelUpResponse, com.dxx.game.dto.ActorProto.ActorLevelUpResponse.Builder>registerMessage((short)12502, com.dxx.game.dto.ActorProto.ActorLevelUpResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.ActorProto.ActorAdvanceUpRequest, com.dxx.game.dto.ActorProto.ActorAdvanceUpRequest.Builder>registerMessage((short)12503, com.dxx.game.dto.ActorProto.ActorAdvanceUpRequest.class, null, null);
		messageProto.<com.dxx.game.dto.ActorProto.ActorAdvanceUpResponse, com.dxx.game.dto.ActorProto.ActorAdvanceUpResponse.Builder>registerMessage((short)12504, com.dxx.game.dto.ActorProto.ActorAdvanceUpResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.CityProto.CityGoldmineLevelUpRequest, com.dxx.game.dto.CityProto.CityGoldmineLevelUpRequest.Builder>registerMessage((short)12601, com.dxx.game.dto.CityProto.CityGoldmineLevelUpRequest.class, null, null);
		messageProto.<com.dxx.game.dto.CityProto.CityGoldmineLevelUpResponse, com.dxx.game.dto.CityProto.CityGoldmineLevelUpResponse.Builder>registerMessage((short)12602, com.dxx.game.dto.CityProto.CityGoldmineLevelUpResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.CityProto.CityGoldmineHangRewardRequest, com.dxx.game.dto.CityProto.CityGoldmineHangRewardRequest.Builder>registerMessage((short)12603, com.dxx.game.dto.CityProto.CityGoldmineHangRewardRequest.class, null, null);
		messageProto.<com.dxx.game.dto.CityProto.CityGoldmineHangRewardResponse, com.dxx.game.dto.CityProto.CityGoldmineHangRewardResponse.Builder>registerMessage((short)12604, com.dxx.game.dto.CityProto.CityGoldmineHangRewardResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.CityProto.CityGetChestInfoRequest, com.dxx.game.dto.CityProto.CityGetChestInfoRequest.Builder>registerMessage((short)12605, com.dxx.game.dto.CityProto.CityGetChestInfoRequest.class, null, null);
		messageProto.<com.dxx.game.dto.CityProto.CityGetChestInfoResponse, com.dxx.game.dto.CityProto.CityGetChestInfoResponse.Builder>registerMessage((short)12606, com.dxx.game.dto.CityProto.CityGetChestInfoResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.CityProto.CityOpenChestRequest, com.dxx.game.dto.CityProto.CityOpenChestRequest.Builder>registerMessage((short)12607, com.dxx.game.dto.CityProto.CityOpenChestRequest.class, null, null);
		messageProto.<com.dxx.game.dto.CityProto.CityOpenChestResponse, com.dxx.game.dto.CityProto.CityOpenChestResponse.Builder>registerMessage((short)12608, com.dxx.game.dto.CityProto.CityOpenChestResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.CityProto.CityTakeScoreRewardRequest, com.dxx.game.dto.CityProto.CityTakeScoreRewardRequest.Builder>registerMessage((short)12609, com.dxx.game.dto.CityProto.CityTakeScoreRewardRequest.class, null, null);
		messageProto.<com.dxx.game.dto.CityProto.CityTakeScoreRewardResponse, com.dxx.game.dto.CityProto.CityTakeScoreRewardResponse.Builder>registerMessage((short)12610, com.dxx.game.dto.CityProto.CityTakeScoreRewardResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.CityProto.CitySyncPowerRequest, com.dxx.game.dto.CityProto.CitySyncPowerRequest.Builder>registerMessage((short)12611, com.dxx.game.dto.CityProto.CitySyncPowerRequest.class, null, null);
		messageProto.<com.dxx.game.dto.CityProto.CitySyncPowerResponse, com.dxx.game.dto.CityProto.CitySyncPowerResponse.Builder>registerMessage((short)12612, com.dxx.game.dto.CityProto.CitySyncPowerResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.CityProto.CityGetInfoRequest, com.dxx.game.dto.CityProto.CityGetInfoRequest.Builder>registerMessage((short)12613, com.dxx.game.dto.CityProto.CityGetInfoRequest.class, null, null);
		messageProto.<com.dxx.game.dto.CityProto.CityGetInfoResponse, com.dxx.game.dto.CityProto.CityGetInfoResponse.Builder>registerMessage((short)12614, com.dxx.game.dto.CityProto.CityGetInfoResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.SocialProto.InteractListRequest, com.dxx.game.dto.SocialProto.InteractListRequest.Builder>registerMessage((short)12701, com.dxx.game.dto.SocialProto.InteractListRequest.class, null, null);
		messageProto.<com.dxx.game.dto.SocialProto.InteractListResponse, com.dxx.game.dto.SocialProto.InteractListResponse.Builder>registerMessage((short)12702, com.dxx.game.dto.SocialProto.InteractListResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.SocialProto.SocialPowerRankRequest, com.dxx.game.dto.SocialProto.SocialPowerRankRequest.Builder>registerMessage((short)12703, com.dxx.game.dto.SocialProto.SocialPowerRankRequest.class, null, null);
		messageProto.<com.dxx.game.dto.SocialProto.SocialPowerRankResponse, com.dxx.game.dto.SocialProto.SocialPowerRankResponse.Builder>registerMessage((short)12704, com.dxx.game.dto.SocialProto.SocialPowerRankResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.SocialProto.InteractDetailRequest, com.dxx.game.dto.SocialProto.InteractDetailRequest.Builder>registerMessage((short)12705, com.dxx.game.dto.SocialProto.InteractDetailRequest.class, null, null);
		messageProto.<com.dxx.game.dto.SocialProto.InteractDetailResponse, com.dxx.game.dto.SocialProto.InteractDetailResponse.Builder>registerMessage((short)12706, com.dxx.game.dto.SocialProto.InteractDetailResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.ConquerProto.ConquerListRequest, com.dxx.game.dto.ConquerProto.ConquerListRequest.Builder>registerMessage((short)12801, com.dxx.game.dto.ConquerProto.ConquerListRequest.class, null, null);
		messageProto.<com.dxx.game.dto.ConquerProto.ConquerListResponse, com.dxx.game.dto.ConquerProto.ConquerListResponse.Builder>registerMessage((short)12802, com.dxx.game.dto.ConquerProto.ConquerListResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.ConquerProto.ConquerBattleRequest, com.dxx.game.dto.ConquerProto.ConquerBattleRequest.Builder>registerMessage((short)12803, com.dxx.game.dto.ConquerProto.ConquerBattleRequest.class, null, null);
		messageProto.<com.dxx.game.dto.ConquerProto.ConquerBattleResponse, com.dxx.game.dto.ConquerProto.ConquerBattleResponse.Builder>registerMessage((short)12804, com.dxx.game.dto.ConquerProto.ConquerBattleResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.ConquerProto.ConquerRevoltRequest, com.dxx.game.dto.ConquerProto.ConquerRevoltRequest.Builder>registerMessage((short)12805, com.dxx.game.dto.ConquerProto.ConquerRevoltRequest.class, null, null);
		messageProto.<com.dxx.game.dto.ConquerProto.ConquerRevoltResponse, com.dxx.game.dto.ConquerProto.ConquerRevoltResponse.Builder>registerMessage((short)12806, com.dxx.game.dto.ConquerProto.ConquerRevoltResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.ConquerProto.ConquerLootRequest, com.dxx.game.dto.ConquerProto.ConquerLootRequest.Builder>registerMessage((short)12807, com.dxx.game.dto.ConquerProto.ConquerLootRequest.class, null, null);
		messageProto.<com.dxx.game.dto.ConquerProto.ConquerLootResponse, com.dxx.game.dto.ConquerProto.ConquerLootResponse.Builder>registerMessage((short)12808, com.dxx.game.dto.ConquerProto.ConquerLootResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.ConquerProto.ConquerPardonRequest, com.dxx.game.dto.ConquerProto.ConquerPardonRequest.Builder>registerMessage((short)12809, com.dxx.game.dto.ConquerProto.ConquerPardonRequest.class, null, null);
		messageProto.<com.dxx.game.dto.ConquerProto.ConquerPardonResponse, com.dxx.game.dto.ConquerProto.ConquerPardonResponse.Builder>registerMessage((short)12810, com.dxx.game.dto.ConquerProto.ConquerPardonResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.IntegralShopProto.IntegralShopBuyRequest, com.dxx.game.dto.IntegralShopProto.IntegralShopBuyRequest.Builder>registerMessage((short)12901, com.dxx.game.dto.IntegralShopProto.IntegralShopBuyRequest.class, null, null);
		messageProto.<com.dxx.game.dto.IntegralShopProto.IntegralShopBuyResponse, com.dxx.game.dto.IntegralShopProto.IntegralShopBuyResponse.Builder>registerMessage((short)12902, com.dxx.game.dto.IntegralShopProto.IntegralShopBuyResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.IntegralShopProto.IntegralShopRefreshRequest, com.dxx.game.dto.IntegralShopProto.IntegralShopRefreshRequest.Builder>registerMessage((short)12903, com.dxx.game.dto.IntegralShopProto.IntegralShopRefreshRequest.class, null, null);
		messageProto.<com.dxx.game.dto.IntegralShopProto.IntegralShopRefreshResponse, com.dxx.game.dto.IntegralShopProto.IntegralShopRefreshResponse.Builder>registerMessage((short)12904, com.dxx.game.dto.IntegralShopProto.IntegralShopRefreshResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.IntegralShopProto.IntegralShopGetInfoRequest, com.dxx.game.dto.IntegralShopProto.IntegralShopGetInfoRequest.Builder>registerMessage((short)12905, com.dxx.game.dto.IntegralShopProto.IntegralShopGetInfoRequest.class, null, null);
		messageProto.<com.dxx.game.dto.IntegralShopProto.IntegralShopGetInfoResponse, com.dxx.game.dto.IntegralShopProto.IntegralShopGetInfoResponse.Builder>registerMessage((short)12906, com.dxx.game.dto.IntegralShopProto.IntegralShopGetInfoResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.TowerProto.TowerChallengeStartRequest, com.dxx.game.dto.TowerProto.TowerChallengeStartRequest.Builder>registerMessage((short)13001, com.dxx.game.dto.TowerProto.TowerChallengeStartRequest.class, null, null);
		messageProto.<com.dxx.game.dto.TowerProto.TowerChallengeStartResponse, com.dxx.game.dto.TowerProto.TowerChallengeStartResponse.Builder>registerMessage((short)13002, com.dxx.game.dto.TowerProto.TowerChallengeStartResponse.class, null, null);
		messageProto.<com.dxx.game.dto.TowerProto.TowerChallengeEndRequest, com.dxx.game.dto.TowerProto.TowerChallengeEndRequest.Builder>registerMessage((short)13003, com.dxx.game.dto.TowerProto.TowerChallengeEndRequest.class, null, null);
		messageProto.<com.dxx.game.dto.TowerProto.TowerChallengeEndResponse, com.dxx.game.dto.TowerProto.TowerChallengeEndResponse.Builder>registerMessage((short)13004, com.dxx.game.dto.TowerProto.TowerChallengeEndResponse.class, null, null);
		messageProto.<com.dxx.game.dto.TowerProto.TowerGetInfoRequest, com.dxx.game.dto.TowerProto.TowerGetInfoRequest.Builder>registerMessage((short)13005, com.dxx.game.dto.TowerProto.TowerGetInfoRequest.class, null, null);
		messageProto.<com.dxx.game.dto.TowerProto.TowerGetInfoResponse, com.dxx.game.dto.TowerProto.TowerGetInfoResponse.Builder>registerMessage((short)13006, com.dxx.game.dto.TowerProto.TowerGetInfoResponse.class, null, null);
		messageProto.<com.dxx.game.dto.TowerProto.TowerChallengeSuccessNumRequest, com.dxx.game.dto.TowerProto.TowerChallengeSuccessNumRequest.Builder>registerMessage((short)13007, com.dxx.game.dto.TowerProto.TowerChallengeSuccessNumRequest.class, null, null);
		messageProto.<com.dxx.game.dto.TowerProto.TowerChallengeSuccessNumResponse, com.dxx.game.dto.TowerProto.TowerChallengeSuccessNumResponse.Builder>registerMessage((short)13008, com.dxx.game.dto.TowerProto.TowerChallengeSuccessNumResponse.class, null, null);
		messageProto.<com.dxx.game.dto.OreProto.OreGetInfoRequest, com.dxx.game.dto.OreProto.OreGetInfoRequest.Builder>registerMessage((short)13201, com.dxx.game.dto.OreProto.OreGetInfoRequest.class, null, null);
		messageProto.<com.dxx.game.dto.OreProto.OreGetInfoResponse, com.dxx.game.dto.OreProto.OreGetInfoResponse.Builder>registerMessage((short)13202, com.dxx.game.dto.OreProto.OreGetInfoResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.OreProto.OreHoldRequest, com.dxx.game.dto.OreProto.OreHoldRequest.Builder>registerMessage((short)13203, com.dxx.game.dto.OreProto.OreHoldRequest.class, null, null);
		messageProto.<com.dxx.game.dto.OreProto.OreHoldResponse, com.dxx.game.dto.OreProto.OreHoldResponse.Builder>registerMessage((short)13204, com.dxx.game.dto.OreProto.OreHoldResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.OreProto.OreRewardRequest, com.dxx.game.dto.OreProto.OreRewardRequest.Builder>registerMessage((short)13205, com.dxx.game.dto.OreProto.OreRewardRequest.class, null, null);
		messageProto.<com.dxx.game.dto.OreProto.OreRewardResponse, com.dxx.game.dto.OreProto.OreRewardResponse.Builder>registerMessage((short)13206, com.dxx.game.dto.OreProto.OreRewardResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.OreProto.OreLogRequest, com.dxx.game.dto.OreProto.OreLogRequest.Builder>registerMessage((short)13207, com.dxx.game.dto.OreProto.OreLogRequest.class, null, null);
		messageProto.<com.dxx.game.dto.OreProto.OreLogResponse, com.dxx.game.dto.OreProto.OreLogResponse.Builder>registerMessage((short)13208, com.dxx.game.dto.OreProto.OreLogResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.FriendProto.FriendListRequest, com.dxx.game.dto.FriendProto.FriendListRequest.Builder>registerMessage((short)15001, com.dxx.game.dto.FriendProto.FriendListRequest.class, null, null);
		messageProto.<com.dxx.game.dto.FriendProto.FriendListResponse, com.dxx.game.dto.FriendProto.FriendListResponse.Builder>registerMessage((short)15002, com.dxx.game.dto.FriendProto.FriendListResponse.class, null, null);
		messageProto.<com.dxx.game.dto.FriendProto.FriendRecommendRequest, com.dxx.game.dto.FriendProto.FriendRecommendRequest.Builder>registerMessage((short)15003, com.dxx.game.dto.FriendProto.FriendRecommendRequest.class, null, null);
		messageProto.<com.dxx.game.dto.FriendProto.FriendRecommendResponse, com.dxx.game.dto.FriendProto.FriendRecommendResponse.Builder>registerMessage((short)15004, com.dxx.game.dto.FriendProto.FriendRecommendResponse.class, null, null);
		messageProto.<com.dxx.game.dto.FriendProto.SearchUserRequest, com.dxx.game.dto.FriendProto.SearchUserRequest.Builder>registerMessage((short)15005, com.dxx.game.dto.FriendProto.SearchUserRequest.class, null, null);
		messageProto.<com.dxx.game.dto.FriendProto.SearchUserResponse, com.dxx.game.dto.FriendProto.SearchUserResponse.Builder>registerMessage((short)15006, com.dxx.game.dto.FriendProto.SearchUserResponse.class, null, null);
		messageProto.<com.dxx.game.dto.FriendProto.FriendApplyListRequest, com.dxx.game.dto.FriendProto.FriendApplyListRequest.Builder>registerMessage((short)15007, com.dxx.game.dto.FriendProto.FriendApplyListRequest.class, null, null);
		messageProto.<com.dxx.game.dto.FriendProto.FriendApplyListResponse, com.dxx.game.dto.FriendProto.FriendApplyListResponse.Builder>registerMessage((short)15008, com.dxx.game.dto.FriendProto.FriendApplyListResponse.class, null, null);
		messageProto.<com.dxx.game.dto.FriendProto.FriendOperateRequest, com.dxx.game.dto.FriendProto.FriendOperateRequest.Builder>registerMessage((short)15009, com.dxx.game.dto.FriendProto.FriendOperateRequest.class, null, null);
		messageProto.<com.dxx.game.dto.FriendProto.FriendOperateResponse, com.dxx.game.dto.FriendProto.FriendOperateResponse.Builder>registerMessage((short)15010, com.dxx.game.dto.FriendProto.FriendOperateResponse.class, null, null);
		messageProto.<com.dxx.game.dto.FriendProto.BlackListRequest, com.dxx.game.dto.FriendProto.BlackListRequest.Builder>registerMessage((short)15011, com.dxx.game.dto.FriendProto.BlackListRequest.class, null, null);
		messageProto.<com.dxx.game.dto.FriendProto.BlackListResponse, com.dxx.game.dto.FriendProto.BlackListResponse.Builder>registerMessage((short)15012, com.dxx.game.dto.FriendProto.BlackListResponse.class, null, null);
		messageProto.<com.dxx.game.dto.FriendProto.FriendLifeValueRequest, com.dxx.game.dto.FriendProto.FriendLifeValueRequest.Builder>registerMessage((short)15013, com.dxx.game.dto.FriendProto.FriendLifeValueRequest.class, null, null);
		messageProto.<com.dxx.game.dto.FriendProto.FriendLifeValueResponse, com.dxx.game.dto.FriendProto.FriendLifeValueResponse.Builder>registerMessage((short)15014, com.dxx.game.dto.FriendProto.FriendLifeValueResponse.class, null, null);
		messageProto.<com.dxx.game.dto.TestProto.TestStartBattleRequest, com.dxx.game.dto.TestProto.TestStartBattleRequest.Builder>registerMessage((short)20001, com.dxx.game.dto.TestProto.TestStartBattleRequest.class, null, null);
		messageProto.<com.dxx.game.dto.TestProto.TestStartBattleResponse, com.dxx.game.dto.TestProto.TestStartBattleResponse.Builder>registerMessage((short)20002, com.dxx.game.dto.TestProto.TestStartBattleResponse.class, null, null);
		messageProto.<com.dxx.game.dto.ChestProto.ChestUseRequest, com.dxx.game.dto.ChestProto.ChestUseRequest.Builder>registerMessage((short)20231, com.dxx.game.dto.ChestProto.ChestUseRequest.class, null, null);
		messageProto.<com.dxx.game.dto.ChestProto.ChestUseResponse, com.dxx.game.dto.ChestProto.ChestUseResponse.Builder>registerMessage((short)20232, com.dxx.game.dto.ChestProto.ChestUseResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.ChestProto.ChestRewardRequest, com.dxx.game.dto.ChestProto.ChestRewardRequest.Builder>registerMessage((short)20233, com.dxx.game.dto.ChestProto.ChestRewardRequest.class, null, null);
		messageProto.<com.dxx.game.dto.ChestProto.ChestRewardResponse, com.dxx.game.dto.ChestProto.ChestRewardResponse.Builder>registerMessage((short)20234, com.dxx.game.dto.ChestProto.ChestRewardResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.GuildProto.GuildGetInfoRequest, com.dxx.game.dto.GuildProto.GuildGetInfoRequest.Builder>registerMessage((short)30101, com.dxx.game.dto.GuildProto.GuildGetInfoRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildGetInfoResponse, com.dxx.game.dto.GuildProto.GuildGetInfoResponse.Builder>registerMessage((short)30102, com.dxx.game.dto.GuildProto.GuildGetInfoResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.GuildProto.GuildCreateRequest, com.dxx.game.dto.GuildProto.GuildCreateRequest.Builder>registerMessage((short)30103, com.dxx.game.dto.GuildProto.GuildCreateRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildCreateResponse, com.dxx.game.dto.GuildProto.GuildCreateResponse.Builder>registerMessage((short)30104, com.dxx.game.dto.GuildProto.GuildCreateResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.GuildProto.GuildSearchRequest, com.dxx.game.dto.GuildProto.GuildSearchRequest.Builder>registerMessage((short)30105, com.dxx.game.dto.GuildProto.GuildSearchRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildSearchResponse, com.dxx.game.dto.GuildProto.GuildSearchResponse.Builder>registerMessage((short)30106, com.dxx.game.dto.GuildProto.GuildSearchResponse.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildGetDetailRequest, com.dxx.game.dto.GuildProto.GuildGetDetailRequest.Builder>registerMessage((short)30107, com.dxx.game.dto.GuildProto.GuildGetDetailRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildGetDetailResponse, com.dxx.game.dto.GuildProto.GuildGetDetailResponse.Builder>registerMessage((short)30108, com.dxx.game.dto.GuildProto.GuildGetDetailResponse.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildGetMemberListRequest, com.dxx.game.dto.GuildProto.GuildGetMemberListRequest.Builder>registerMessage((short)30109, com.dxx.game.dto.GuildProto.GuildGetMemberListRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildGetMemberListResponse, com.dxx.game.dto.GuildProto.GuildGetMemberListResponse.Builder>registerMessage((short)30110, com.dxx.game.dto.GuildProto.GuildGetMemberListResponse.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildModifyRequest, com.dxx.game.dto.GuildProto.GuildModifyRequest.Builder>registerMessage((short)30111, com.dxx.game.dto.GuildProto.GuildModifyRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildModifyResponse, com.dxx.game.dto.GuildProto.GuildModifyResponse.Builder>registerMessage((short)30112, com.dxx.game.dto.GuildProto.GuildModifyResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.GuildProto.GuildDismissRequest, com.dxx.game.dto.GuildProto.GuildDismissRequest.Builder>registerMessage((short)30113, com.dxx.game.dto.GuildProto.GuildDismissRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildDismissResponse, com.dxx.game.dto.GuildProto.GuildDismissResponse.Builder>registerMessage((short)30114, com.dxx.game.dto.GuildProto.GuildDismissResponse.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildApplyJoinRequest, com.dxx.game.dto.GuildProto.GuildApplyJoinRequest.Builder>registerMessage((short)30115, com.dxx.game.dto.GuildProto.GuildApplyJoinRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildApplyJoinResponse, com.dxx.game.dto.GuildProto.GuildApplyJoinResponse.Builder>registerMessage((short)30116, com.dxx.game.dto.GuildProto.GuildApplyJoinResponse.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildCancelApplyRequest, com.dxx.game.dto.GuildProto.GuildCancelApplyRequest.Builder>registerMessage((short)30117, com.dxx.game.dto.GuildProto.GuildCancelApplyRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildCancelApplyResponse, com.dxx.game.dto.GuildProto.GuildCancelApplyResponse.Builder>registerMessage((short)30118, com.dxx.game.dto.GuildProto.GuildCancelApplyResponse.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildAutoJoinRequest, com.dxx.game.dto.GuildProto.GuildAutoJoinRequest.Builder>registerMessage((short)30119, com.dxx.game.dto.GuildProto.GuildAutoJoinRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildAutoJoinResponse, com.dxx.game.dto.GuildProto.GuildAutoJoinResponse.Builder>registerMessage((short)30120, com.dxx.game.dto.GuildProto.GuildAutoJoinResponse.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildGetApplyListRequest, com.dxx.game.dto.GuildProto.GuildGetApplyListRequest.Builder>registerMessage((short)30121, com.dxx.game.dto.GuildProto.GuildGetApplyListRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildGetApplyListResponse, com.dxx.game.dto.GuildProto.GuildGetApplyListResponse.Builder>registerMessage((short)30122, com.dxx.game.dto.GuildProto.GuildGetApplyListResponse.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildAgreeJoinRequest, com.dxx.game.dto.GuildProto.GuildAgreeJoinRequest.Builder>registerMessage((short)30123, com.dxx.game.dto.GuildProto.GuildAgreeJoinRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildAgreeJoinResponse, com.dxx.game.dto.GuildProto.GuildAgreeJoinResponse.Builder>registerMessage((short)30124, com.dxx.game.dto.GuildProto.GuildAgreeJoinResponse.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildRefuseJoinRequest, com.dxx.game.dto.GuildProto.GuildRefuseJoinRequest.Builder>registerMessage((short)30125, com.dxx.game.dto.GuildProto.GuildRefuseJoinRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildRefuseJoinResponse, com.dxx.game.dto.GuildProto.GuildRefuseJoinResponse.Builder>registerMessage((short)30126, com.dxx.game.dto.GuildProto.GuildRefuseJoinResponse.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildKickOutRequest, com.dxx.game.dto.GuildProto.GuildKickOutRequest.Builder>registerMessage((short)30127, com.dxx.game.dto.GuildProto.GuildKickOutRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildKickOutResponse, com.dxx.game.dto.GuildProto.GuildKickOutResponse.Builder>registerMessage((short)30128, com.dxx.game.dto.GuildProto.GuildKickOutResponse.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildLeaveRequest, com.dxx.game.dto.GuildProto.GuildLeaveRequest.Builder>registerMessage((short)30129, com.dxx.game.dto.GuildProto.GuildLeaveRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildLeaveResponse, com.dxx.game.dto.GuildProto.GuildLeaveResponse.Builder>registerMessage((short)30130, com.dxx.game.dto.GuildProto.GuildLeaveResponse.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildUpPositionRequest, com.dxx.game.dto.GuildProto.GuildUpPositionRequest.Builder>registerMessage((short)30131, com.dxx.game.dto.GuildProto.GuildUpPositionRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildUpPositionResponse, com.dxx.game.dto.GuildProto.GuildUpPositionResponse.Builder>registerMessage((short)30132, com.dxx.game.dto.GuildProto.GuildUpPositionResponse.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildTransferPresidentRequest, com.dxx.game.dto.GuildProto.GuildTransferPresidentRequest.Builder>registerMessage((short)30133, com.dxx.game.dto.GuildProto.GuildTransferPresidentRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildTransferPresidentResponse, com.dxx.game.dto.GuildProto.GuildTransferPresidentResponse.Builder>registerMessage((short)30134, com.dxx.game.dto.GuildProto.GuildTransferPresidentResponse.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildGetFeaturesInfoRequest, com.dxx.game.dto.GuildProto.GuildGetFeaturesInfoRequest.Builder>registerMessage((short)30135, com.dxx.game.dto.GuildProto.GuildGetFeaturesInfoRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildGetFeaturesInfoResponse, com.dxx.game.dto.GuildProto.GuildGetFeaturesInfoResponse.Builder>registerMessage((short)30136, com.dxx.game.dto.GuildProto.GuildGetFeaturesInfoResponse.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildLevelUpRequest, com.dxx.game.dto.GuildProto.GuildLevelUpRequest.Builder>registerMessage((short)30137, com.dxx.game.dto.GuildProto.GuildLevelUpRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildLevelUpResponse, com.dxx.game.dto.GuildProto.GuildLevelUpResponse.Builder>registerMessage((short)30138, com.dxx.game.dto.GuildProto.GuildLevelUpResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.GuildProto.GuildSignInRequest, com.dxx.game.dto.GuildProto.GuildSignInRequest.Builder>registerMessage((short)30141, com.dxx.game.dto.GuildProto.GuildSignInRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildSignInResponse, com.dxx.game.dto.GuildProto.GuildSignInResponse.Builder>registerMessage((short)30142, com.dxx.game.dto.GuildProto.GuildSignInResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.GuildProto.GuildShopBuyRequest, com.dxx.game.dto.GuildProto.GuildShopBuyRequest.Builder>registerMessage((short)30151, com.dxx.game.dto.GuildProto.GuildShopBuyRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildShopBuyResponse, com.dxx.game.dto.GuildProto.GuildShopBuyResponse.Builder>registerMessage((short)30152, com.dxx.game.dto.GuildProto.GuildShopBuyResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.GuildProto.GuildShopRefreshRequest, com.dxx.game.dto.GuildProto.GuildShopRefreshRequest.Builder>registerMessage((short)30153, com.dxx.game.dto.GuildProto.GuildShopRefreshRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildShopRefreshResponse, com.dxx.game.dto.GuildProto.GuildShopRefreshResponse.Builder>registerMessage((short)30154, com.dxx.game.dto.GuildProto.GuildShopRefreshResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.GuildProto.GuildTaskRewardRequest, com.dxx.game.dto.GuildProto.GuildTaskRewardRequest.Builder>registerMessage((short)30161, com.dxx.game.dto.GuildProto.GuildTaskRewardRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildTaskRewardResponse, com.dxx.game.dto.GuildProto.GuildTaskRewardResponse.Builder>registerMessage((short)30162, com.dxx.game.dto.GuildProto.GuildTaskRewardResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.GuildProto.GuildTaskRefreshRequest, com.dxx.game.dto.GuildProto.GuildTaskRefreshRequest.Builder>registerMessage((short)30163, com.dxx.game.dto.GuildProto.GuildTaskRefreshRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildTaskRefreshResponse, com.dxx.game.dto.GuildProto.GuildTaskRefreshResponse.Builder>registerMessage((short)30164, com.dxx.game.dto.GuildProto.GuildTaskRefreshResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.GuildProto.GuildGetMessageRecordsRequest, com.dxx.game.dto.GuildProto.GuildGetMessageRecordsRequest.Builder>registerMessage((short)30171, com.dxx.game.dto.GuildProto.GuildGetMessageRecordsRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildGetMessageRecordsResponse, com.dxx.game.dto.GuildProto.GuildGetMessageRecordsResponse.Builder>registerMessage((short)30172, com.dxx.game.dto.GuildProto.GuildGetMessageRecordsResponse.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildBossGetInfoRequest, com.dxx.game.dto.GuildProto.GuildBossGetInfoRequest.Builder>registerMessage((short)30181, com.dxx.game.dto.GuildProto.GuildBossGetInfoRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildBossGetInfoResponse, com.dxx.game.dto.GuildProto.GuildBossGetInfoResponse.Builder>registerMessage((short)30182, com.dxx.game.dto.GuildProto.GuildBossGetInfoResponse.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildBossStartRequest, com.dxx.game.dto.GuildProto.GuildBossStartRequest.Builder>registerMessage((short)30183, com.dxx.game.dto.GuildProto.GuildBossStartRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildBossStartResponse, com.dxx.game.dto.GuildProto.GuildBossStartResponse.Builder>registerMessage((short)30184, com.dxx.game.dto.GuildProto.GuildBossStartResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.GuildProto.GuildBossEndRequest, com.dxx.game.dto.GuildProto.GuildBossEndRequest.Builder>registerMessage((short)30185, com.dxx.game.dto.GuildProto.GuildBossEndRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildBossEndResponse, com.dxx.game.dto.GuildProto.GuildBossEndResponse.Builder>registerMessage((short)30186, com.dxx.game.dto.GuildProto.GuildBossEndResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.GuildProto.GuildBossBuyCntRequest, com.dxx.game.dto.GuildProto.GuildBossBuyCntRequest.Builder>registerMessage((short)30187, com.dxx.game.dto.GuildProto.GuildBossBuyCntRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildBossBuyCntResponse, com.dxx.game.dto.GuildProto.GuildBossBuyCntResponse.Builder>registerMessage((short)30188, com.dxx.game.dto.GuildProto.GuildBossBuyCntResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.GuildProto.GuildBossTaskRewardRequest, com.dxx.game.dto.GuildProto.GuildBossTaskRewardRequest.Builder>registerMessage((short)30189, com.dxx.game.dto.GuildProto.GuildBossTaskRewardRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildBossTaskRewardResponse, com.dxx.game.dto.GuildProto.GuildBossTaskRewardResponse.Builder>registerMessage((short)30190, com.dxx.game.dto.GuildProto.GuildBossTaskRewardResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.GuildProto.GuildBossBoxRewardRequest, com.dxx.game.dto.GuildProto.GuildBossBoxRewardRequest.Builder>registerMessage((short)30191, com.dxx.game.dto.GuildProto.GuildBossBoxRewardRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildBossBoxRewardResponse, com.dxx.game.dto.GuildProto.GuildBossBoxRewardResponse.Builder>registerMessage((short)30192, com.dxx.game.dto.GuildProto.GuildBossBoxRewardResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.GuildProto.GuildBossGetChallengeListRequest, com.dxx.game.dto.GuildProto.GuildBossGetChallengeListRequest.Builder>registerMessage((short)30193, com.dxx.game.dto.GuildProto.GuildBossGetChallengeListRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildBossGetChallengeListResponse, com.dxx.game.dto.GuildProto.GuildBossGetChallengeListResponse.Builder>registerMessage((short)30194, com.dxx.game.dto.GuildProto.GuildBossGetChallengeListResponse.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildDonationReqItemRequest, com.dxx.game.dto.GuildProto.GuildDonationReqItemRequest.Builder>registerMessage((short)30201, com.dxx.game.dto.GuildProto.GuildDonationReqItemRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildDonationReqItemResponse, com.dxx.game.dto.GuildProto.GuildDonationReqItemResponse.Builder>registerMessage((short)30202, com.dxx.game.dto.GuildProto.GuildDonationReqItemResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.GuildProto.GuildDonationSendItemRequest, com.dxx.game.dto.GuildProto.GuildDonationSendItemRequest.Builder>registerMessage((short)30203, com.dxx.game.dto.GuildProto.GuildDonationSendItemRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildDonationSendItemResponse, com.dxx.game.dto.GuildProto.GuildDonationSendItemResponse.Builder>registerMessage((short)30204, com.dxx.game.dto.GuildProto.GuildDonationSendItemResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.GuildProto.GuildDonationReceiveRequest, com.dxx.game.dto.GuildProto.GuildDonationReceiveRequest.Builder>registerMessage((short)30205, com.dxx.game.dto.GuildProto.GuildDonationReceiveRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildDonationReceiveResponse, com.dxx.game.dto.GuildProto.GuildDonationReceiveResponse.Builder>registerMessage((short)30206, com.dxx.game.dto.GuildProto.GuildDonationReceiveResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.GuildProto.GuildDonationGetRecordsRequest, com.dxx.game.dto.GuildProto.GuildDonationGetRecordsRequest.Builder>registerMessage((short)30207, com.dxx.game.dto.GuildProto.GuildDonationGetRecordsRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildDonationGetRecordsResponse, com.dxx.game.dto.GuildProto.GuildDonationGetRecordsResponse.Builder>registerMessage((short)30208, com.dxx.game.dto.GuildProto.GuildDonationGetRecordsResponse.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildDonationGetOperationRecordsRequest, com.dxx.game.dto.GuildProto.GuildDonationGetOperationRecordsRequest.Builder>registerMessage((short)30209, com.dxx.game.dto.GuildProto.GuildDonationGetOperationRecordsRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildDonationGetOperationRecordsResponse, com.dxx.game.dto.GuildProto.GuildDonationGetOperationRecordsResponse.Builder>registerMessage((short)30210, com.dxx.game.dto.GuildProto.GuildDonationGetOperationRecordsResponse.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildBossEndBattleRequest, com.dxx.game.dto.GuildProto.GuildBossEndBattleRequest.Builder>registerMessage((short)30211, com.dxx.game.dto.GuildProto.GuildBossEndBattleRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildBossEndBattleResponse, com.dxx.game.dto.GuildProto.GuildBossEndBattleResponse.Builder>registerMessage((short)30212, com.dxx.game.dto.GuildProto.GuildBossEndBattleResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.GuildProto.GuildBossBattleGRankRequest, com.dxx.game.dto.GuildProto.GuildBossBattleGRankRequest.Builder>registerMessage((short)30213, com.dxx.game.dto.GuildProto.GuildBossBattleGRankRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildBossBattleGRankResponse, com.dxx.game.dto.GuildProto.GuildBossBattleGRankResponse.Builder>registerMessage((short)30214, com.dxx.game.dto.GuildProto.GuildBossBattleGRankResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.ArtifactProto.ArtifactUpgradeRequest, com.dxx.game.dto.ArtifactProto.ArtifactUpgradeRequest.Builder>registerMessage((short)30241, com.dxx.game.dto.ArtifactProto.ArtifactUpgradeRequest.class, null, null);
		messageProto.<com.dxx.game.dto.ArtifactProto.ArtifactUpgradeResponse, com.dxx.game.dto.ArtifactProto.ArtifactUpgradeResponse.Builder>registerMessage((short)30242, com.dxx.game.dto.ArtifactProto.ArtifactUpgradeResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.ArtifactProto.ArtifactUpgradeAllRequest, com.dxx.game.dto.ArtifactProto.ArtifactUpgradeAllRequest.Builder>registerMessage((short)30243, com.dxx.game.dto.ArtifactProto.ArtifactUpgradeAllRequest.class, null, null);
		messageProto.<com.dxx.game.dto.ArtifactProto.ArtifactUpgradeAllResponse, com.dxx.game.dto.ArtifactProto.ArtifactUpgradeAllResponse.Builder>registerMessage((short)30244, com.dxx.game.dto.ArtifactProto.ArtifactUpgradeAllResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.ArtifactProto.ArtifactItemStarRequest, com.dxx.game.dto.ArtifactProto.ArtifactItemStarRequest.Builder>registerMessage((short)30245, com.dxx.game.dto.ArtifactProto.ArtifactItemStarRequest.class, null, null);
		messageProto.<com.dxx.game.dto.ArtifactProto.ArtifactItemStarResponse, com.dxx.game.dto.ArtifactProto.ArtifactItemStarResponse.Builder>registerMessage((short)30246, com.dxx.game.dto.ArtifactProto.ArtifactItemStarResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.ArtifactProto.ArtifactApplySkillRequest, com.dxx.game.dto.ArtifactProto.ArtifactApplySkillRequest.Builder>registerMessage((short)30247, com.dxx.game.dto.ArtifactProto.ArtifactApplySkillRequest.class, null, null);
		messageProto.<com.dxx.game.dto.ArtifactProto.ArtifactApplySkillResponse, com.dxx.game.dto.ArtifactProto.ArtifactApplySkillResponse.Builder>registerMessage((short)30248, com.dxx.game.dto.ArtifactProto.ArtifactApplySkillResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.ArtifactProto.ArtifactUnlockRequest, com.dxx.game.dto.ArtifactProto.ArtifactUnlockRequest.Builder>registerMessage((short)30249, com.dxx.game.dto.ArtifactProto.ArtifactUnlockRequest.class, null, null);
		messageProto.<com.dxx.game.dto.ArtifactProto.ArtifactUnlockResponse, com.dxx.game.dto.ArtifactProto.ArtifactUnlockResponse.Builder>registerMessage((short)30250, com.dxx.game.dto.ArtifactProto.ArtifactUnlockResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.MountProto.MountUpgradeRequest, com.dxx.game.dto.MountProto.MountUpgradeRequest.Builder>registerMessage((short)30281, com.dxx.game.dto.MountProto.MountUpgradeRequest.class, null, null);
		messageProto.<com.dxx.game.dto.MountProto.MountUpgradeResponse, com.dxx.game.dto.MountProto.MountUpgradeResponse.Builder>registerMessage((short)30282, com.dxx.game.dto.MountProto.MountUpgradeResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.MountProto.MountUpgradeAllRequest, com.dxx.game.dto.MountProto.MountUpgradeAllRequest.Builder>registerMessage((short)30283, com.dxx.game.dto.MountProto.MountUpgradeAllRequest.class, null, null);
		messageProto.<com.dxx.game.dto.MountProto.MountUpgradeAllResponse, com.dxx.game.dto.MountProto.MountUpgradeAllResponse.Builder>registerMessage((short)30284, com.dxx.game.dto.MountProto.MountUpgradeAllResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.MountProto.MountItemStarRequest, com.dxx.game.dto.MountProto.MountItemStarRequest.Builder>registerMessage((short)30285, com.dxx.game.dto.MountProto.MountItemStarRequest.class, null, null);
		messageProto.<com.dxx.game.dto.MountProto.MountItemStarResponse, com.dxx.game.dto.MountProto.MountItemStarResponse.Builder>registerMessage((short)30286, com.dxx.game.dto.MountProto.MountItemStarResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.MountProto.MountApplySkillRequest, com.dxx.game.dto.MountProto.MountApplySkillRequest.Builder>registerMessage((short)30287, com.dxx.game.dto.MountProto.MountApplySkillRequest.class, null, null);
		messageProto.<com.dxx.game.dto.MountProto.MountApplySkillResponse, com.dxx.game.dto.MountProto.MountApplySkillResponse.Builder>registerMessage((short)30288, com.dxx.game.dto.MountProto.MountApplySkillResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.MountProto.MountDressRequest, com.dxx.game.dto.MountProto.MountDressRequest.Builder>registerMessage((short)30289, com.dxx.game.dto.MountProto.MountDressRequest.class, null, null);
		messageProto.<com.dxx.game.dto.MountProto.MountDressResponse, com.dxx.game.dto.MountProto.MountDressResponse.Builder>registerMessage((short)30290, com.dxx.game.dto.MountProto.MountDressResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.MountProto.MountUnlockRequest, com.dxx.game.dto.MountProto.MountUnlockRequest.Builder>registerMessage((short)30291, com.dxx.game.dto.MountProto.MountUnlockRequest.class, null, null);
		messageProto.<com.dxx.game.dto.MountProto.MountUnlockResponse, com.dxx.game.dto.MountProto.MountUnlockResponse.Builder>registerMessage((short)30292, com.dxx.game.dto.MountProto.MountUnlockResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.GuildRaceProto.GuildRaceGuildApplyRequest, com.dxx.game.dto.GuildRaceProto.GuildRaceGuildApplyRequest.Builder>registerMessage((short)30301, com.dxx.game.dto.GuildRaceProto.GuildRaceGuildApplyRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GuildRaceProto.GuildRaceGuildApplyResponse, com.dxx.game.dto.GuildRaceProto.GuildRaceGuildApplyResponse.Builder>registerMessage((short)30302, com.dxx.game.dto.GuildRaceProto.GuildRaceGuildApplyResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.GuildRaceProto.GuildRaceUserApplyRequest, com.dxx.game.dto.GuildRaceProto.GuildRaceUserApplyRequest.Builder>registerMessage((short)30303, com.dxx.game.dto.GuildRaceProto.GuildRaceUserApplyRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GuildRaceProto.GuildRaceUserApplyResponse, com.dxx.game.dto.GuildRaceProto.GuildRaceUserApplyResponse.Builder>registerMessage((short)30304, com.dxx.game.dto.GuildRaceProto.GuildRaceUserApplyResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.GuildRaceProto.GuildRaceEditSeqRequest, com.dxx.game.dto.GuildRaceProto.GuildRaceEditSeqRequest.Builder>registerMessage((short)30305, com.dxx.game.dto.GuildRaceProto.GuildRaceEditSeqRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GuildRaceProto.GuildRaceEditSeqResponse, com.dxx.game.dto.GuildRaceProto.GuildRaceEditSeqResponse.Builder>registerMessage((short)30306, com.dxx.game.dto.GuildRaceProto.GuildRaceEditSeqResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.GuildRaceProto.GuildRaceInfoRequest, com.dxx.game.dto.GuildRaceProto.GuildRaceInfoRequest.Builder>registerMessage((short)30307, com.dxx.game.dto.GuildRaceProto.GuildRaceInfoRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GuildRaceProto.GuildRaceInfoResponse, com.dxx.game.dto.GuildRaceProto.GuildRaceInfoResponse.Builder>registerMessage((short)30308, com.dxx.game.dto.GuildRaceProto.GuildRaceInfoResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.GuildRaceProto.GuildRaceOwnerUserApplyListRequest, com.dxx.game.dto.GuildRaceProto.GuildRaceOwnerUserApplyListRequest.Builder>registerMessage((short)30309, com.dxx.game.dto.GuildRaceProto.GuildRaceOwnerUserApplyListRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GuildRaceProto.GuildRaceOwnerUserApplyListResponse, com.dxx.game.dto.GuildRaceProto.GuildRaceOwnerUserApplyListResponse.Builder>registerMessage((short)30310, com.dxx.game.dto.GuildRaceProto.GuildRaceOwnerUserApplyListResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.GuildRaceProto.GuildRaceOppUserApplyListRequest, com.dxx.game.dto.GuildRaceProto.GuildRaceOppUserApplyListRequest.Builder>registerMessage((short)30311, com.dxx.game.dto.GuildRaceProto.GuildRaceOppUserApplyListRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GuildRaceProto.GuildRaceOppUserApplyListResponse, com.dxx.game.dto.GuildRaceProto.GuildRaceOppUserApplyListResponse.Builder>registerMessage((short)30312, com.dxx.game.dto.GuildRaceProto.GuildRaceOppUserApplyListResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.GuildRaceProto.GuildRaceUserInfoRequest, com.dxx.game.dto.GuildRaceProto.GuildRaceUserInfoRequest.Builder>registerMessage((short)30313, com.dxx.game.dto.GuildRaceProto.GuildRaceUserInfoRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GuildRaceProto.GuildRaceUserInfoResponse, com.dxx.game.dto.GuildRaceProto.GuildRaceUserInfoResponse.Builder>registerMessage((short)30314, com.dxx.game.dto.GuildRaceProto.GuildRaceUserInfoResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.GuildRaceProto.GuildRaceGuildRecordRequest, com.dxx.game.dto.GuildRaceProto.GuildRaceGuildRecordRequest.Builder>registerMessage((short)30315, com.dxx.game.dto.GuildRaceProto.GuildRaceGuildRecordRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GuildRaceProto.GuildRaceGuildRecordResponse, com.dxx.game.dto.GuildRaceProto.GuildRaceGuildRecordResponse.Builder>registerMessage((short)30316, com.dxx.game.dto.GuildRaceProto.GuildRaceGuildRecordResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.GuildRaceProto.GuildRacePVPRecordRequest, com.dxx.game.dto.GuildRaceProto.GuildRacePVPRecordRequest.Builder>registerMessage((short)30317, com.dxx.game.dto.GuildRaceProto.GuildRacePVPRecordRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GuildRaceProto.GuildRacePVPRecordResponse, com.dxx.game.dto.GuildRaceProto.GuildRacePVPRecordResponse.Builder>registerMessage((short)30318, com.dxx.game.dto.GuildRaceProto.GuildRacePVPRecordResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.GuildProto.GuildTechUpgradeRequest, com.dxx.game.dto.GuildProto.GuildTechUpgradeRequest.Builder>registerMessage((short)30501, com.dxx.game.dto.GuildProto.GuildTechUpgradeRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildTechUpgradeResponse, com.dxx.game.dto.GuildProto.GuildTechUpgradeResponse.Builder>registerMessage((short)30502, com.dxx.game.dto.GuildProto.GuildTechUpgradeResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.GuildProto.GuildLogRequest, com.dxx.game.dto.GuildProto.GuildLogRequest.Builder>registerMessage((short)30503, com.dxx.game.dto.GuildProto.GuildLogRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildLogResponse, com.dxx.game.dto.GuildProto.GuildLogResponse.Builder>registerMessage((short)30504, com.dxx.game.dto.GuildProto.GuildLogResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.GuildProto.GuildContributeRequest, com.dxx.game.dto.GuildProto.GuildContributeRequest.Builder>registerMessage((short)30505, com.dxx.game.dto.GuildProto.GuildContributeRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildContributeResponse, com.dxx.game.dto.GuildProto.GuildContributeResponse.Builder>registerMessage((short)30506, com.dxx.game.dto.GuildProto.GuildContributeResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.GuildProto.GuildBossKilledRewardRequest, com.dxx.game.dto.GuildProto.GuildBossKilledRewardRequest.Builder>registerMessage((short)30507, com.dxx.game.dto.GuildProto.GuildBossKilledRewardRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildBossKilledRewardResponse, com.dxx.game.dto.GuildProto.GuildBossKilledRewardResponse.Builder>registerMessage((short)30508, com.dxx.game.dto.GuildProto.GuildBossKilledRewardResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		messageProto.<com.dxx.game.dto.GuildProto.GuildBossStartBattleRequest, com.dxx.game.dto.GuildProto.GuildBossStartBattleRequest.Builder>registerMessage((short)30601, com.dxx.game.dto.GuildProto.GuildBossStartBattleRequest.class, null, null);
		messageProto.<com.dxx.game.dto.GuildProto.GuildBossStartBattleResponse, com.dxx.game.dto.GuildProto.GuildBossStartBattleResponse.Builder>registerMessage((short)30602, com.dxx.game.dto.GuildProto.GuildBossStartBattleResponse.class, null, null);
		messageProto.<com.dxx.game.dto.SocketProto.SocketLoginRequest, com.dxx.game.dto.SocketProto.SocketLoginRequest.Builder>registerMessage((short)31101, com.dxx.game.dto.SocketProto.SocketLoginRequest.class, null, null);
		messageProto.<com.dxx.game.dto.SocketProto.SocketLoginResponse, com.dxx.game.dto.SocketProto.SocketLoginResponse.Builder>registerMessage((short)31102, com.dxx.game.dto.SocketProto.SocketLoginResponse.class, null, null);
		messageProto.<com.dxx.game.dto.SocketProto.SocketJoinGroupRequest, com.dxx.game.dto.SocketProto.SocketJoinGroupRequest.Builder>registerMessage((short)31103, com.dxx.game.dto.SocketProto.SocketJoinGroupRequest.class, null, null);
		messageProto.<com.dxx.game.dto.SocketProto.SocketJoinGroupResponse, com.dxx.game.dto.SocketProto.SocketJoinGroupResponse.Builder>registerMessage((short)31104, com.dxx.game.dto.SocketProto.SocketJoinGroupResponse.class, null, null);
		messageProto.<com.dxx.game.dto.SocketProto.SocketQuitGroupRequest, com.dxx.game.dto.SocketProto.SocketQuitGroupRequest.Builder>registerMessage((short)31105, com.dxx.game.dto.SocketProto.SocketQuitGroupRequest.class, null, null);
		messageProto.<com.dxx.game.dto.SocketProto.SocketQuitGroupResponse, com.dxx.game.dto.SocketProto.SocketQuitGroupResponse.Builder>registerMessage((short)31106, com.dxx.game.dto.SocketProto.SocketQuitGroupResponse.class, null, null);
		messageProto.<com.dxx.game.dto.SocketProto.SocketHeartBeatRequest, com.dxx.game.dto.SocketProto.SocketHeartBeatRequest.Builder>registerMessage((short)31107, com.dxx.game.dto.SocketProto.SocketHeartBeatRequest.class, null, null);
		messageProto.<com.dxx.game.dto.SocketProto.SocketHeartBeatResponse, com.dxx.game.dto.SocketProto.SocketHeartBeatResponse.Builder>registerMessage((short)31108, com.dxx.game.dto.SocketProto.SocketHeartBeatResponse.class, null, null);
		messageProto.<com.dxx.game.dto.SocketProto.SocketLoginRepeatMessage, com.dxx.game.dto.SocketProto.SocketLoginRepeatMessage.Builder>registerMessage((short)31202, com.dxx.game.dto.SocketProto.SocketLoginRepeatMessage.class, null, null);
		messageProto.<com.dxx.game.dto.SocketProto.SocketReconnectMessage, com.dxx.game.dto.SocketProto.SocketReconnectMessage.Builder>registerMessage((short)31204, com.dxx.game.dto.SocketProto.SocketReconnectMessage.class, null, null);
		messageProto.<com.dxx.game.dto.SocketProto.SocketPushMessage, com.dxx.game.dto.SocketProto.SocketPushMessage.Builder>registerMessage((short)31206, com.dxx.game.dto.SocketProto.SocketPushMessage.class, null, null);
		messageProto.<com.dxx.game.dto.SocketProto.SocketErrorMessage, com.dxx.game.dto.SocketProto.SocketErrorMessage.Builder>registerMessage((short)31299, com.dxx.game.dto.SocketProto.SocketErrorMessage.class, null, null);
		messageProto.<com.dxx.game.dto.ChatProto.ChatTranslateRequest, com.dxx.game.dto.ChatProto.ChatTranslateRequest.Builder>registerMessage((short)32101, com.dxx.game.dto.ChatProto.ChatTranslateRequest.class, null, null);
		messageProto.<com.dxx.game.dto.ChatProto.ChatTranslateResponse, com.dxx.game.dto.ChatProto.ChatTranslateResponse.Builder>registerMessage((short)32102, com.dxx.game.dto.ChatProto.ChatTranslateResponse.class, null, null);
		messageProto.<com.dxx.game.dto.ChatProto.ChatGuildRequest, com.dxx.game.dto.ChatProto.ChatGuildRequest.Builder>registerMessage((short)32103, com.dxx.game.dto.ChatProto.ChatGuildRequest.class, null, null);
		messageProto.<com.dxx.game.dto.ChatProto.ChatGuildResponse, com.dxx.game.dto.ChatProto.ChatGuildResponse.Builder>registerMessage((short)32104, com.dxx.game.dto.ChatProto.ChatGuildResponse.class, null, null);
		messageProto.<com.dxx.game.dto.ChatProto.ChatGuildShowItemRequest, com.dxx.game.dto.ChatProto.ChatGuildShowItemRequest.Builder>registerMessage((short)32105, com.dxx.game.dto.ChatProto.ChatGuildShowItemRequest.class, null, null);
		messageProto.<com.dxx.game.dto.ChatProto.ChatGuildShowItemResponse, com.dxx.game.dto.ChatProto.ChatGuildShowItemResponse.Builder>registerMessage((short)32106, com.dxx.game.dto.ChatProto.ChatGuildShowItemResponse.class, null, null);
		messageProto.<com.dxx.game.dto.ChatProto.ChatCommonRequest, com.dxx.game.dto.ChatProto.ChatCommonRequest.Builder>registerMessage((short)32107, com.dxx.game.dto.ChatProto.ChatCommonRequest.class, null, null);
		messageProto.<com.dxx.game.dto.ChatProto.ChatCommonResponse, com.dxx.game.dto.ChatProto.ChatCommonResponse.Builder>registerMessage((short)32108, com.dxx.game.dto.ChatProto.ChatCommonResponse.class, null, null);
		messageProto.<com.dxx.game.dto.ChatProto.ChatGetMessageRecordsRequest, com.dxx.game.dto.ChatProto.ChatGetMessageRecordsRequest.Builder>registerMessage((short)32109, com.dxx.game.dto.ChatProto.ChatGetMessageRecordsRequest.class, null, null);
		messageProto.<com.dxx.game.dto.ChatProto.ChatGetMessageRecordsResponse, com.dxx.game.dto.ChatProto.ChatGetMessageRecordsResponse.Builder>registerMessage((short)32110, com.dxx.game.dto.ChatProto.ChatGetMessageRecordsResponse.class, null, null);
		messageProto.<com.dxx.game.dto.ChatProto.ChatFunctionRequest, com.dxx.game.dto.ChatProto.ChatFunctionRequest.Builder>registerMessage((short)32111, com.dxx.game.dto.ChatProto.ChatFunctionRequest.class, null, null);
		messageProto.<com.dxx.game.dto.ChatProto.ChatFunctionResponse, com.dxx.game.dto.ChatProto.ChatFunctionResponse.Builder>registerMessage((short)32112, com.dxx.game.dto.ChatProto.ChatFunctionResponse.class, null, null);
		messageProto.<com.dxx.game.dto.ChatProto.ChatGetOnlineStatusRequest, com.dxx.game.dto.ChatProto.ChatGetOnlineStatusRequest.Builder>registerMessage((short)32113, com.dxx.game.dto.ChatProto.ChatGetOnlineStatusRequest.class, null, null);
		messageProto.<com.dxx.game.dto.ChatProto.ChatGetOnlineStatusResponse, com.dxx.game.dto.ChatProto.ChatGetOnlineStatusResponse.Builder>registerMessage((short)32114, com.dxx.game.dto.ChatProto.ChatGetOnlineStatusResponse.class, msg -> msg.hasCommonData() ? msg.getCommonData() : null, (builder, data) -> builder.setCommonData(data).build());
		
	}
}
